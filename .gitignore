.heft/
lib/
_api-extractor-temp/
temp/
.idea
pods/workspace/init-scripts/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*./rush-logs
*tests/sanity/screenshots

# Runtime data
*.pid
*.seed
*.pid.lock

# VS Code settings
.vscode/settings.json

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
# .env

# next.js build output
.next

# OS X temporary files
.DS_Store

# Rush temporary files
common/deploy/
common/temp/
common/autoinstallers/*/.npmrc
**/.rush/temp/
bundle.js
bundle/*.js
dist
.build
typings
types
.validate
tsconfig.tsbuildinfo
ingest-attachment-*.zip
tsdoc-metadata.json
pods/front/dist
*.cpuprofile
*.pyc
metrics.txt
dev/tool/report*.csv
tests/db_dump
.build
.format
tools/apm/apm.js
deploy
metrics.txt
services/github/pod-github/src/github.graphql
.build
.format
dev/tool/report.csv
bundle/*
bundle.js.map
tests/profiles
**/bundle/model.json
.wrangler
dump
**/logs/**
dev/tool/history.json
.aider*
/combined_dependencies
.tmp
ws-tests/docker-compose.override.yml

# Husky temporary files
.git/.husky-validation-*
.git/.husky-last-validation

# Rush build cache backup (created by pre-commit hook)
common/config/rush/build-cache.json.bak
