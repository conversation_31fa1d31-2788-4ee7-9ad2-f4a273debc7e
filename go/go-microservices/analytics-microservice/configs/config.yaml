server:
  port: 2020
  host: 0.0.0.0

environment:

logging:
  level: info  # debug, info, warn, error

observability:
  prometheus:
    enabled: true
    path: /metrics
  tracing:
    enabled: false
    jaeger: http://localhost:14268/api/traces

clickhouse:
  dsn: "tcp://localhost:9010/analytics"

grafana:
  url: "http://localhost:3002"
  api_key: ""  # Set via ANALYTICS_GRAFANA_API_KEY environment variable

dapr:
  enabled: true
  app_id: analytics-microservice
  http_port: 3500        # this is the side-car HTTP port
  grpc_port: 50001       # this is the side-car gRPC port
  state_store: ""        # No state store needed - analytics only uses pub/sub and ClickHouse
  pubsub_name: huly-pubsub
  secret_store: config-secret-store


kpi_config:
  path: "internal/infra/config/kpi-config.json"