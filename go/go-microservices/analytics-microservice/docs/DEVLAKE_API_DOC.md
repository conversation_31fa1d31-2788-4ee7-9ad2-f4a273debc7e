# Dashboard API Testing Guide

## Environment Variables

The service uses Viper with `ANALYTICS_` prefix. Set variables in `.env` file or as environment variables:

```bash
# In .env file (recommended for local development)
ANALYTICS_GRAFANA_URL="http://localhost:3002"
ANALYTICS_GRAFANA_API_KEY="your_grafana_api_key"
```

**Note:** The `.env` file should be in the analytics-microservice root directory (same level as `go.mod`). Environment variables take precedence over config file values.

### Getting Grafana API Key

1. Log into Grafana UI at `http://localhost:3002`
2. Go to Configuration → API Keys
3. Create a new API key with Admin role

Or via API:
```bash
curl -X POST *********************************/api/auth/keys \
  -H "Content-Type: application/json" \
  -d '{"name":"analytics-service","role":"Admin","secondsToLive":0}'
```

## API Endpoints

Base URL: `http://localhost:2020`

All endpoints are prefixed with `/api/v1/dashboards`.

### 1. Get All Dashboards

**Endpoint:** `GET /api/v1/dashboards`

Returns list of all dashboards (metadata only).

```bash
curl -X GET "http://localhost:2020/api/v1/dashboards"
```

**Response:**
```json
[
  {
    "id": 17,
    "uid": "d449042e-22f0-4357-b8b7-22083f47618d",
    "title": "Work Logs",
    "url": "/grafana/d/d449042e-22f0-4357-b8b7-22083f47618d/work-logs",
    "tags": ["Developer", "Highlights"]
  }
]
```

### 2. Get Dashboard Details

**Endpoint:** `GET /api/v1/dashboards/:uid`

Returns full dashboard metadata including panels and templating configuration.

**Path Parameters:**
- `uid` (string, required): Dashboard UID

```bash
curl -X GET "http://localhost:2020/api/v1/dashboards/IndividualEngX7z"
```

**Response:** Full dashboard object with `meta`, `dashboard.panels`, `dashboard.templating.list`, etc.

### 3. Get Templating Options

**Endpoint:** `GET /api/v1/dashboards/:uid/templating-options`

Returns options for all templating variables (populates dropdown filters).

**Path Parameters:**
- `uid` (string, required): Dashboard UID

```bash
curl -X GET "http://localhost:2020/api/v1/dashboards/IndividualEngX7z/templating-options"
```

**Response:**
```json
{
  "variables": {
    "project": {
      "label": "Project",
      "options": [
        {"text": "TMP JIRA", "value": "TMP JIRA"},
        {"text": "TMP GITHUB", "value": "TMP GITHUB"}
      ]
    },
    "engineer": {
      "label": "Engineer",
      "options": [
        {"text": "Mario Deda", "value": "Mario Deda"},
        {"text": "Rex Kqiku", "value": "Rex Kqiku"}
      ]
    }
  }
}
```

### 4. Get Dashboard Data

**Endpoint:** `POST /api/v1/dashboards/:uid/data`

Fetches data for all panels in a dashboard with filters and date range.

**Path Parameters:**
- `uid` (string, required): Dashboard UID

**Request Body:**
```json
{
  "from": "2025-11-01T00:00:00Z",    // RFC3339 format - required
  "to": "2025-11-30T23:59:59Z",      // RFC3339 format - required
  "filters": {                        // Optional - map of variable name to value(s)
    "project": "TMP JIRA",            // Single value
    "engineer": "Mario Deda, Rron Haxhiu",  // Comma-separated multiple values
    "sprint": ["MP Sprint 21", "MP Sprint 20"]  // Array format
  }
}
```

**Filter Value Formats:**
- Single value: `"project": "TMP JIRA"`
- Comma-separated: `"engineer": "Mario Deda, Rron Haxhiu"`
- Array: `"sprint": ["MP Sprint 21", "MP Sprint 20"]`

**Examples:**

**Option 1: No filters (select all values)**
```bash
curl -X POST "http://localhost:2020/api/v1/dashboards/IndividualEngX7z/data" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "2025-11-01T00:00:00Z",
    "to": "2025-11-30T23:59:59Z"
  }'
```
All `${variableName}` placeholders are replaced with all available option values (e.g., `${project}` → `'TMP JIRA', 'TMP GITHUB'`).

**Option 2: Single filter value**
```bash
curl -X POST "http://localhost:2020/api/v1/dashboards/IndividualEngX7z/data" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "2025-11-01T00:00:00Z",
    "to": "2025-11-30T23:59:59Z",
    "filters": {
      "project": "TMP JIRA",
      "engineer": "Mario Deda",
      "sprint": "MP Sprint 21"
    }
  }'
```
Each placeholder is replaced with the specified filter value (e.g., `${project}` → `'TMP JIRA'`).

**Option 3: Multiple filter values**
```bash
curl -X POST "http://localhost:2020/api/v1/dashboards/IndividualEngX7z/data" \
  -H "Content-Type: application/json" \
  -d '{
    "from": "2025-11-01T00:00:00Z",
    "to": "2025-11-30T23:59:59Z",
    "filters": {
      "project": "TMP JIRA",
      "engineer": "Mario Deda, Rron Haxhiu",
      "sprint": ["MP Sprint 21", "MP Sprint 20"]
    }
  }'
```
Placeholders are replaced with multiple values (e.g., `${engineer}` → `'Mario Deda', 'Rron Haxhiu'`).

**Response:**
```json
{
  "dashboardUid": "IndividualEngX7z",
  "from": "2025-11-01T00:00:00Z",
  "to": "2025-11-30T23:59:59Z",
  "panels": [
    {
      "panelId": 2,
      "title": "Issues Completed",
      "data": {
        "results": {
          "A": {
            "frames": [...],
            "refId": "A"
          }
        }
      }
    }
  ]
}
```

## How It Works

- Each panel's `rawSql` contains placeholders like `${project}`, `${engineer}`, `${sprint}`
- **If filter provided**: Placeholder replaced with filter value(s), properly escaped
- **If filter NOT provided**: Placeholder replaced with all available option values (comma-separated)
- Replaced SQL is executed against Grafana's datasource API
- Text panels are skipped automatically

## Notes

- Dates must be RFC3339 format: `YYYY-MM-DDTHH:MM:SSZ`
- Filter keys must match variable `name` from dashboard templating config
- Filter values are case-sensitive
- SQL values are automatically escaped to prevent injection
- Missing filters use all available option values (not subqueries)
