package app

import (
	"analytics-microservice/internal/domain"
	"context"
	"time"
)

type KPIConfigRepository interface {
	List(ctx context.Context, workspaceID string) ([]*domain.KPIConfiguration, error)
}
type EventPublisher interface {
}

type ClickHouseClient interface {
	AutoMigrate(models ...interface{}) error
	GetTableNames() ([]string, error)
	DropAllTables() error
	PopulateDateDimension() error
	Close() error
}

type JiraRepository interface {
	GetCalculatedKPIs(ctx context.Context, queries []string) (domain.KPIAggregatedValues, error)
	GetProjectSprints(ctx context.Context, projectKey string) ([]*domain.Sprint, error)
	GetProjectReleases(ctx context.Context, projectKey string) ([]*domain.Release, error)
	GetProjectIssues(ctx context.Context, projectIds []string, startDate, endDate time.Time, issueType string) ([]*domain.Issue, error)
	StoreWebhookEvent(ctx context.Context, event *domain.WebhookEvent) error // Store in the raw jira table
	GetEpicHealthRaw(ctx context.Context, projectKey string, start, end time.Time) ([]*domain.RawEpicHealth, error)
}

type GithubRepository interface {
	StoreWebhookEvent(ctx context.Context, event *domain.WebhookEvent) error // Store in the raw github table
}

type SeedJobRepository interface {
	Store(ctx context.Context, job *domain.SeedJob) error
	GetByID(ctx context.Context, jobID string) (*domain.SeedJob, error)
	Update(ctx context.Context, job *domain.SeedJob) error
	ListActive(ctx context.Context) ([]*domain.SeedJob, error)
	GetJobs(ctx context.Context) ([]*domain.SeedJob, error)
}

type GenericSeeder interface {
	Seed(ctx context.Context, job *domain.SeedJob, endpoint, project, wrapperKey, arrayKey string, requestBody []byte, method string) error
}

// DevLakeClient represents the DevLake/Grafana client interface
type DevLakeClient interface {
	GetDashboards(ctx context.Context) ([]map[string]interface{}, error)
	GetDashboard(ctx context.Context, uid string) (map[string]interface{}, error)
	GetPanelData(ctx context.Context, query, datasourceUID, from, to string, scopedVars map[string]ScopedVar) (*PanelQueryResponse, error)
	GetTemplatingOptions(ctx context.Context, query, datasourceUID string) (*PanelQueryResponse, error)
}
