package app

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"

	"analytics-microservice/internal/domain"
)

// Query structs

// GetDashboardsQuery represents a query to retrieve all dashboards
type GetDashboardsQuery struct{}

// GetDashboardQuery represents a query to retrieve a specific dashboard
type GetDashboardQuery struct {
	UID string
}

// GetTemplatingOptionsQuery represents a query to retrieve templating options for a dashboard
type GetTemplatingOptionsQuery struct {
	UID string
}

// GetDashboardDataQuery represents a query to retrieve panel data for a dashboard
type GetDashboardDataQuery struct {
	DashboardUID string
	From         string
	To           string
	Filters      map[string]string
}

// Service

type DashboardService struct {
	devlakeClient DevLakeClient
	logger        *slog.Logger
}

func NewDashboardService(devlakeClient DevLakeClient, logger *slog.Logger) *DashboardService {
	return &DashboardService{
		devlakeClient: devlakeClient,
		logger:        logger.WithGroup("DashboardService"),
	}
}

// HandleGetDashboards handles the GetDashboardsQuery
func (s *DashboardService) HandleGetDashboards(ctx context.Context, q GetDashboardsQuery) ([]*domain.Dashboard, error) {
	dashboards, err := s.devlakeClient.GetDashboards(ctx)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to fetch dashboards", "error", err)
		return nil, fmt.Errorf("failed to fetch dashboards: %w", err)
	}

	result := make([]*domain.Dashboard, 0, len(dashboards))
	for _, d := range dashboards {
		dashboard, err := s.mapToDashboard(d)
		if err != nil {
			s.logger.WarnContext(ctx, "Failed to map dashboard", "error", err)
			continue
		}
		result = append(result, dashboard)
	}

	return result, nil
}

// HandleGetDashboard handles the GetDashboardQuery
func (s *DashboardService) HandleGetDashboard(ctx context.Context, q GetDashboardQuery) (*domain.DashboardDetail, error) {
	if q.UID == "" {
		return nil, fmt.Errorf("dashboard UID is required")
	}

	dashboardData, err := s.devlakeClient.GetDashboard(ctx, q.UID)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to fetch dashboard", "uid", q.UID, "error", err)
		return nil, fmt.Errorf("failed to fetch dashboard: %w", err)
	}

	// Convert map to DashboardDetail struct
	detail, err := s.mapToDashboardDetail(dashboardData)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to parse dashboard", "uid", q.UID, "error", err)
		return nil, fmt.Errorf("failed to parse dashboard: %w", err)
	}

	return detail, nil
}

// HandleGetTemplatingOptions handles the GetTemplatingOptionsQuery
func (s *DashboardService) HandleGetTemplatingOptions(ctx context.Context, q GetTemplatingOptionsQuery) (*domain.TemplatingOptionsResponse, error) {
	if q.UID == "" {
		return nil, fmt.Errorf("dashboard UID is required")
	}

	// First get the dashboard to extract templating variables
	dashboard, err := s.HandleGetDashboard(ctx, GetDashboardQuery{UID: q.UID})
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to fetch dashboard for templating options", "uid", q.UID, "error", err)
		return nil, fmt.Errorf("failed to fetch dashboard: %w", err)
	}

	// Extract datasource UID from first panel or templating variable
	datasourceUID := s.extractDatasourceUID(dashboard)

	// Execute queries for each templating variable
	variables := make(map[string]domain.TemplatingVariableOptions)
	for _, variable := range dashboard.Dashboard.Templating.List {
		if variable.Query == "" {
			continue
		}

		result, err := s.devlakeClient.GetTemplatingOptions(ctx, variable.Query, datasourceUID)
		if err != nil {
			s.logger.ErrorContext(ctx, "Failed to fetch templating options", "variable", variable.Name, "error", err)
			continue
		}

		// Parse the result to extract options
		variableOptions := s.extractOptionsFromPanelQueryResponse(result)

		// Store with label
		variables[variable.Name] = domain.TemplatingVariableOptions{
			Label:   variable.Label,
			Options: variableOptions,
		}
	}

	return &domain.TemplatingOptionsResponse{Variables: variables}, nil
}

// HandleGetDashboardData handles the GetDashboardDataQuery
func (s *DashboardService) HandleGetDashboardData(ctx context.Context, q GetDashboardDataQuery) (*domain.DashboardDataResponse, error) {
	if q.DashboardUID == "" {
		return nil, fmt.Errorf("dashboard UID is required")
	}
	if q.From == "" || q.To == "" {
		return nil, fmt.Errorf("from and to dates are required")
	}

	// Get dashboard to extract panels and datasource
	dashboard, err := s.HandleGetDashboard(ctx, GetDashboardQuery{UID: q.DashboardUID})
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to fetch dashboard for panel data", "uid", q.DashboardUID, "error", err)
		return nil, fmt.Errorf("failed to fetch dashboard: %w", err)
	}

	datasourceUID := s.extractDatasourceUID(dashboard)

	// Fetch all templating options to use for missing filters
	templatingOptions, err := s.HandleGetTemplatingOptions(ctx, GetTemplatingOptionsQuery{UID: q.DashboardUID})
	allOptions := make(map[string][]domain.VariableOption)
	if err != nil {
		s.logger.WarnContext(ctx, "Failed to fetch templating options for 'select all'", "error", err)
	} else {
		for varName, varOpts := range templatingOptions.Variables {
			allOptions[varName] = varOpts.Options
		}
	}

	// Fetch data for each panel
	panels := make([]domain.PanelData, 0)
	for _, panel := range dashboard.Dashboard.Panels {
		// Skip non-data panels (like text panels)
		if panel.Type == "text" || panel.RawSQL == "" {
			continue
		}

		// Pass templating variables and all options for "select all" replacement
		panelData, err := s.fetchPanelData(ctx, panel, datasourceUID, q.From, q.To, q.Filters, dashboard.Dashboard.Templating.List, allOptions)
		if err != nil {
			s.logger.ErrorContext(ctx, "Failed to fetch panel data", "panel_id", panel.ID, "error", err)
			continue
		}

		panels = append(panels, *panelData)
	}

	return &domain.DashboardDataResponse{
		DashboardUID: q.DashboardUID,
		From:         q.From,
		To:           q.To,
		Panels:       panels,
	}, nil
}

// Helper methods

func (s *DashboardService) mapToDashboard(data map[string]interface{}) (*domain.Dashboard, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal dashboard data: %w", err)
	}

	var dashboard domain.Dashboard
	if err := json.Unmarshal(jsonData, &dashboard); err != nil {
		return nil, fmt.Errorf("failed to unmarshal dashboard data: %w", err)
	}

	return &dashboard, nil
}

func (s *DashboardService) mapToDashboardDetail(data map[string]interface{}) (*domain.DashboardDetail, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal dashboard detail data: %w", err)
	}

	var detail domain.DashboardDetail
	if err := json.Unmarshal(jsonData, &detail); err != nil {
		return nil, fmt.Errorf("failed to unmarshal dashboard detail data: %w", err)
	}

	// Extract RawSQL from panel targets
	for i := range detail.Dashboard.Panels {
		panel := &detail.Dashboard.Panels[i]
		if len(panel.Targets) > 0 {
			panel.RawSQL = panel.Targets[0].RawSQL
			panel.Datasource = panel.Targets[0].Datasource
		}
	}

	return &detail, nil
}

func (s *DashboardService) extractDatasourceUID(dashboard *domain.DashboardDetail) string {
	// Try to get datasource UID from panels (from targets)
	for _, panel := range dashboard.Dashboard.Panels {
		// Check panel datasource first
		if panel.Datasource.UID != "" {
			return panel.Datasource.UID
		}
		// Check targets if panel datasource is empty
		for _, target := range panel.Targets {
			if target.Datasource.UID != "" {
				return target.Datasource.UID
			}
		}
	}

	// Fallback: Hardcoded datasource UID (we only have one datasource)
	// TODO: In the future, query Grafana's /api/datasources endpoint to get UID dynamically
	return "P430005175C4C7810"
}

// escapeSQLString escapes single quotes in SQL string values by doubling them
func escapeSQLString(value string) string {
	return strings.ReplaceAll(value, "'", "''")
}

// parseFilterValues parses a filter value that may be comma-separated
func parseFilterValues(value string) []string {
	if value == "" {
		return []string{}
	}

	parts := strings.Split(value, ",")
	values := make([]string, 0, len(parts))

	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			values = append(values, trimmed)
		}
	}

	return values
}

func (s *DashboardService) replaceSQLPlaceholders(rawSQL string, filters map[string]string, templatingVars []domain.TemplatingVariable, allOptions map[string][]domain.VariableOption) string {
	if rawSQL == "" {
		return rawSQL
	}

	result := rawSQL

	// Create a map of variable names to their queries for fallback if options are not available
	varQueryMap := make(map[string]string)
	for _, v := range templatingVars {
		if v.Query != "" {
			varQueryMap[v.Name] = v.Query
		}
	}

	// Find all placeholders in the SQL and replace them
	for {
		startIdx := strings.Index(result, "${")
		if startIdx == -1 {
			break
		}

		endIdx := strings.Index(result[startIdx:], "}")
		if endIdx == -1 {
			break
		}
		endIdx += startIdx

		varName := result[startIdx+2 : endIdx]

		// Check if we have a filter value for this variable
		if value, ok := filters[varName]; ok && value != "" {
			values := parseFilterValues(value)

			escapedValues := make([]string, 0, len(values))
			for _, v := range values {
				escapedValue := escapeSQLString(v)
				escapedValues = append(escapedValues, "'"+escapedValue+"'")
			}
			sqlValue := strings.Join(escapedValues, ", ")
			result = result[:startIdx] + sqlValue + result[endIdx+1:]
		} else {
			// No filter provided - replace with all option values
			if options, ok := allOptions[varName]; ok && len(options) > 0 {
				values := make([]string, 0, len(options))
				for _, opt := range options {
					escapedValue := escapeSQLString(opt.Value)
					values = append(values, "'"+escapedValue+"'")
				}
				replacement := strings.Join(values, ", ")
				result = result[:startIdx] + replacement + result[endIdx+1:]
			} else if query, ok := varQueryMap[varName]; ok {
				replacement := "(" + query + ")"
				result = result[:startIdx] + replacement + result[endIdx+1:]
			} else {
				s.logger.Warn("No filter value, no options, and no query found for placeholder",
					"placeholder", "${"+varName+"}",
					"variable", varName,
				)
				result = result[:startIdx] + result[endIdx+1:]
			}
		}
	}

	return result
}

func (s *DashboardService) fetchPanelData(ctx context.Context, panel domain.Panel, datasourceUID, from, to string, filters map[string]string, templatingVars []domain.TemplatingVariable, allOptions map[string][]domain.VariableOption) (*domain.PanelData, error) {
	if panel.RawSQL == "" {
		return nil, fmt.Errorf("panel %d has no SQL query", panel.ID)
	}

	replacedSQL := s.replaceSQLPlaceholders(panel.RawSQL, filters, templatingVars, allOptions)

	result, err := s.devlakeClient.GetPanelData(ctx, replacedSQL, datasourceUID, from, to, nil)
	if err != nil {
		s.logger.ErrorContext(ctx, "Failed to fetch panel data", "panel_id", panel.ID, "error", err)
		return nil, fmt.Errorf("failed to fetch panel data: %w", err)
	}

	resultMap := s.panelQueryResponseToMap(result)

	return &domain.PanelData{
		PanelID: panel.ID,
		Title:   panel.Title,
		Data:    resultMap,
	}, nil
}

func (s *DashboardService) extractOptionsFromPanelQueryResponse(result *PanelQueryResponse) []domain.VariableOption {
	options := make([]domain.VariableOption, 0)

	if result == nil || result.Results == nil {
		return options
	}

	if resultA, ok := result.Results["A"]; ok {
		if len(resultA.Frames) > 0 {
			frame := resultA.Frames[0]
			if len(frame.Data.Values) > 0 {
				firstColumn := frame.Data.Values[0]
				for _, val := range firstColumn {
					if strVal, ok := val.(string); ok {
						options = append(options, domain.VariableOption{
							Text:  strVal,
							Value: strVal,
						})
					}
				}
			}
		}
	}

	return options
}

func (s *DashboardService) panelQueryResponseToMap(result *PanelQueryResponse) map[string]interface{} {
	if result == nil {
		return make(map[string]interface{})
	}

	jsonData, err := json.Marshal(result)
	if err != nil {
		return make(map[string]interface{})
	}

	var resultMap map[string]interface{}
	if err := json.Unmarshal(jsonData, &resultMap); err != nil {
		return make(map[string]interface{})
	}

	return resultMap
}
