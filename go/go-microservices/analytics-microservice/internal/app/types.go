package app

// ScopedVar represents a scoped variable for template substitution
type ScopedVar struct {
	Text  string
	Value string
}

// PanelQueryResponse represents the response from a panel query
type PanelQueryResponse struct {
	Results map[string]QueryResult
}

// QueryResult represents the result of a single query
type QueryResult struct {
	Frames []Frame
	RefID  string
}

// Frame represents a data frame in the query result
type Frame struct {
	Schema FrameSchema
	Data   FrameData
}

// FrameSchema represents the schema of a data frame
type FrameSchema struct {
	Fields []FieldSchema
}

// FieldSchema represents the schema of a field
type FieldSchema struct {
	Name     string
	Type     string
	TypeInfo map[string]interface{}
}

// FrameData represents the actual data in a frame
type FrameData struct {
	Values [][]interface{}
}
