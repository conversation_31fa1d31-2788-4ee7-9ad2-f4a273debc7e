package config

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server struct {
		Host string `mapstructure:"host" yaml:"host"`
		Port int    `mapstructure:"port" yaml:"port"`
	} `mapstructure:"server" yaml:"server"`

	Environment string `mapstructure:"environment" yaml:"environment"`

	Logging struct {
		Level string `mapstructure:"level" yaml:"level"`
	} `mapstructure:"logging" yaml:"logging"`

	Observability struct {
		Prometheus struct {
			Enabled bool   `mapstructure:"enabled" yaml:"enabled"`
			Path    string `mapstructure:"path" yaml:"path"`
		} `mapstructure:"prometheus" yaml:"prometheus"`

		Tracing struct {
			Enabled bool   `mapstructure:"enabled" yaml:"enabled"`
			Jaeger  string `mapstructure:"jaeger" yaml:"jaeger"`
		} `mapstructure:"tracing" yaml:"tracing"`
	} `mapstructure:"observability" yaml:"observability"`

	ClickHouse struct {
		DSN string `mapstructure:"dsn" yaml:"dsn"`
	} `mapstructure:"clickhouse" yaml:"clickhouse"`

	Grafana struct {
		URL    string `mapstructure:"url" yaml:"url"`
		APIKey string `mapstructure:"api_key" yaml:"api_key"`
	} `mapstructure:"grafana" yaml:"grafana"`

	MongoDB struct {
		URI string `mapstructure:"uri" yaml:"uri"`
	} `mapstructure:"mongodb" yaml:"mongodb"`

	Dapr struct {
		Enabled     bool   `mapstructure:"enabled"      yaml:"enabled"`
		AppID       string `mapstructure:"app_id"       yaml:"app_id"`
		HTTPPort    int    `mapstructure:"http_port"    yaml:"http_port"`
		GRPCPort    int    `mapstructure:"grpc_port"    yaml:"grpc_port"`
		StateStore  string `mapstructure:"state_store"  yaml:"state_store"`
		PubSubName  string `mapstructure:"pubsub_name"  yaml:"pubsub_name"`
		SecretStore string `mapstructure:"secret_store" yaml:"secret_store"`
	} `mapstructure:"dapr" yaml:"dapr"`

	Secrets struct {
		Database struct {
			ConnectionString string `mapstructure:"connection_string" yaml:"connection_string"`
			Username         string `mapstructure:"username"          yaml:"username"`
			Password         string `mapstructure:"password"          yaml:"password"`
		} `mapstructure:"database" yaml:"database"`

		API struct {
			Key    string `mapstructure:"key"    yaml:"key"`
			Secret string `mapstructure:"secret" yaml:"secret"`
		} `mapstructure:"api" yaml:"api"`

		External struct {
			ServiceAKey    string `mapstructure:"service_a_key"    yaml:"service_a_key"`
			ServiceBSecret string `mapstructure:"service_b_secret" yaml:"service_b_secret"`
		} `mapstructure:"external" yaml:"external"`
	} `mapstructure:"secrets" yaml:"secrets"`

	KPIConfig struct {
		Path string `mapstructure:"path" yaml:"path"`
	} `mapstructure:"kpi_config" yaml:"kpi_config"`
}

func Load(path string) (*Config, error) {
	v := viper.New()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.SetEnvPrefix("ANALYTICS")
	v.AutomaticEnv()

	v.SetDefault("server.host", "0.0.0.0")
	v.SetDefault("server.port", 2020)

	if path == "" {
		if p := os.Getenv("CONFIG_PATH"); p != "" {
			path = p
		} else {
			v.AddConfigPath("configs")
			v.SetConfigName("config")
		}
	}
	if path != "" {
		v.SetConfigFile(path)
	}
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("read config: %w", err)
	}


	var cfg Config
	if err := v.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("unmarshal: %w", err)
	}

	return &cfg, nil
}

// GetSecret returns a secret value by path (for Dapr secrets compatibility)
func (c *Config) GetSecret(secretPath string) (map[string]string, error) {
	secrets := make(map[string]string)

	switch secretPath {
	case "database":
		secrets["connection_string"] = c.Secrets.Database.ConnectionString
		secrets["username"] = c.Secrets.Database.Username
		secrets["password"] = c.Secrets.Database.Password
	case "api":
		secrets["key"] = c.Secrets.API.Key
		secrets["secret"] = c.Secrets.API.Secret
	case "external":
		secrets["service_a_key"] = c.Secrets.External.ServiceAKey
		secrets["service_b_secret"] = c.Secrets.External.ServiceBSecret
	default:
		return nil, fmt.Errorf("secret path '%s' not found", secretPath)
	}

	return secrets, nil
}
