package domain

import (
	"encoding/json"
	"fmt"
)

// Dashboard represents a DevLake/Grafana dashboard
type Dashboard struct {
	ID        int      `json:"id"`
	UID       string   `json:"uid"`
	OrgID     int      `json:"orgId"`
	Title     string   `json:"title"`
	URI       string   `json:"uri"`
	URL       string   `json:"url"`
	Slug      string   `json:"slug"`
	Type      string   `json:"type"`
	Tags      []string `json:"tags"`
	IsStarred bool     `json:"isStarred"`
	SortMeta  int      `json:"sortMeta"`
	IsDeleted bool     `json:"isDeleted"`
}

// DashboardDetail represents the full dashboard metadata including panels and templating
type DashboardDetail struct {
	Meta      DashboardMeta      `json:"meta"`
	Dashboard DashboardConfig    `json:"dashboard"`
}

// DashboardMeta represents dashboard metadata
type DashboardMeta struct {
	Type        string `json:"type"`
	CanSave     bool   `json:"canSave"`
	CanEdit     bool   `json:"canEdit"`
	CanAdmin    bool   `json:"canAdmin"`
	CanStar     bool   `json:"canStar"`
	CanDelete   bool   `json:"canDelete"`
	Slug        string `json:"slug"`
	URL         string `json:"url"`
	Expires     string `json:"expires"`
	Created     string `json:"created"`
	Updated     string `json:"updated"`
	UpdatedBy   string `json:"updatedBy"`
	CreatedBy   string `json:"createdBy"`
	Version     int    `json:"version"`
	HasAcl      bool   `json:"hasAcl"`
	IsFolder    bool   `json:"isFolder"`
	FolderID    int    `json:"folderId"`
	FolderUID   string `json:"folderUid"`
	FolderTitle string `json:"folderTitle"`
	FolderURL   string `json:"folderUrl"`
	Provisioned bool   `json:"provisioned"`
}

// DashboardConfig represents the dashboard configuration
type DashboardConfig struct {
	ID                int                    `json:"id"`
	UID               string                 `json:"uid"`
	Title             string                 `json:"title"`
	Editable          bool                   `json:"editable"`
	Panels            []Panel                `json:"panels"`
	Templating        TemplatingConfig        `json:"templating"`
	Time              TimeConfig              `json:"time"`
	Timezone          string                 `json:"timezone"`
	Annotations       map[string]interface{} `json:"annotations,omitempty"`
	Links             []interface{}           `json:"links,omitempty"`
	FiscalYearStartMonth int                  `json:"fiscalYearStartMonth,omitempty"`
	GraphTooltip      int                     `json:"graphTooltip,omitempty"`
}

// Panel represents a dashboard panel
type Panel struct {
	ID          int                    `json:"id"`
	Title       string                 `json:"title"`
	Type        string                 `json:"type"`
	GridPos     GridPosition           `json:"gridPos"`
	FieldConfig map[string]interface{} `json:"fieldConfig,omitempty"`
	Options     map[string]interface{} `json:"options,omitempty"`
	Targets     []PanelTarget          `json:"targets,omitempty"`
	RawSQL      string                 `json:"rawSql,omitempty"` // Extracted from targets
	Datasource  DatasourceInfo         `json:"datasource,omitempty"`
}

// GridPosition represents panel grid position
type GridPosition struct {
	H int `json:"h"`
	W int `json:"w"`
	X int `json:"x"`
	Y int `json:"y"`
}

// PanelTarget represents a panel query target
type PanelTarget struct {
	RefID      string         `json:"refId"`
	RawSQL     string         `json:"rawSql"`
	Format     string         `json:"format"`
	Datasource DatasourceInfo `json:"datasource"`
}

// DatasourceInfo represents datasource information
// Can be unmarshaled from either a string or an object
type DatasourceInfo struct {
	Type string `json:"type"`
	UID  string `json:"uid"`
}

// UnmarshalJSON implements custom unmarshaling to handle both string and object formats
func (d *DatasourceInfo) UnmarshalJSON(data []byte) error {
	// Try to unmarshal as string first
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		// If it's a string, determine if it's a type name or UID
		if str != "" {
			// Common datasource types are short strings (mysql, prometheus, etc.)
			// UIDs are typically longer alphanumeric strings
			// If it's a short string (< 20 chars) and looks like a type name, treat as type
			if len(str) < 20 && (str == "mysql" || str == "prometheus" || str == "influxdb" || str == "postgres" || str == "graphite") {
				d.Type = str
				// UID will be empty, caller should handle this
			} else {
				// Likely a UID - default type to mysql for DevLake
				d.UID = str
				d.Type = "mysql"
			}
		}
		return nil
	}

	// If not a string, try to unmarshal as object
	var obj struct {
		Type string `json:"type"`
		UID  string `json:"uid"`
	}
	if err := json.Unmarshal(data, &obj); err != nil {
		return fmt.Errorf("failed to unmarshal datasource: %w", err)
	}
	d.Type = obj.Type
	d.UID = obj.UID
	return nil
}

// TemplatingConfig represents dashboard templating configuration
type TemplatingConfig struct {
	List []TemplatingVariable `json:"list"`
}

// TemplatingVariable represents a templating variable (filter)
type TemplatingVariable struct {
	Name         string                 `json:"name"`
	Label        string                 `json:"label"`
	Type         string                 `json:"type"`
	Datasource   string                 `json:"datasource"`
	Query        string                 `json:"query"`
	Definition   string                 `json:"definition"`
	IncludeAll   bool                   `json:"includeAll"`
	Multi        bool                   `json:"multi"`
	Current      VariableCurrent        `json:"current"`
	Options      []VariableOption       `json:"options"`
	Refresh      int                    `json:"refresh"`
	Regex        string                 `json:"regex"`
	OtherOptions map[string]interface{} `json:",inline"`
}

// VariableCurrent represents the current value of a templating variable
// Can be unmarshaled from either arrays or single strings
type VariableCurrent struct {
	Text  []string `json:"text"`
	Value []string `json:"value"`
}

// UnmarshalJSON implements custom unmarshaling to handle both array and string formats
func (v *VariableCurrent) UnmarshalJSON(data []byte) error {
	// Try to unmarshal as object with arrays first
	var obj struct {
		Text  interface{} `json:"text"`
		Value interface{} `json:"value"`
	}
	if err := json.Unmarshal(data, &obj); err != nil {
		return fmt.Errorf("failed to unmarshal VariableCurrent: %w", err)
	}

	// Handle text field - can be string or []string
	if obj.Text != nil {
		switch t := obj.Text.(type) {
		case string:
			v.Text = []string{t}
		case []interface{}:
			v.Text = make([]string, 0, len(t))
			for _, item := range t {
				if str, ok := item.(string); ok {
					v.Text = append(v.Text, str)
				}
			}
		case []string:
			v.Text = t
		}
	}

	// Handle value field - can be string or []string
	if obj.Value != nil {
		switch val := obj.Value.(type) {
		case string:
			v.Value = []string{val}
		case []interface{}:
			v.Value = make([]string, 0, len(val))
			for _, item := range val {
				if str, ok := item.(string); ok {
					v.Value = append(v.Value, str)
				}
			}
		case []string:
			v.Value = val
		}
	}

	return nil
}

// VariableOption represents an option for a templating variable
type VariableOption struct {
	Text  string `json:"text"`
	Value string `json:"value"`
}

// TimeConfig represents dashboard time configuration
type TimeConfig struct {
	From string `json:"from"`
	To   string `json:"to"`
}

// PanelDataRequest represents a request to fetch panel data
type PanelDataRequest struct {
	DashboardUID string            `json:"dashboardUid"`
	PanelID      int               `json:"panelId"`
	From         string            `json:"from"` // RFC3339 format
	To           string            `json:"to"`   // RFC3339 format
	Filters      map[string]string `json:"filters"` // e.g., {"project": "TMP JIRA", "engineer": "Rex Kqiku"}
}

// DashboardDataRequest represents a request to fetch all panel data for a dashboard
type DashboardDataRequest struct {
	DashboardUID string            `json:"dashboardUid"`
	From         string            `json:"from"` // RFC3339 format
	To           string            `json:"to"`   // RFC3339 format
	Filters      map[string]string `json:"filters"` // e.g., {"project": "TMP JIRA", "engineer": "Rex Kqiku"}
}

// PanelData represents the data for a single panel
type PanelData struct {
	PanelID int                    `json:"panelId"`
	Title   string                 `json:"title"`
	Data    map[string]interface{} `json:"data"` // The actual query result
}

// DashboardDataResponse represents the response with all panel data
type DashboardDataResponse struct {
	DashboardUID string      `json:"dashboardUid"`
	From         string      `json:"from"`
	To           string      `json:"to"`
	Panels       []PanelData `json:"panels"`
}

// TemplatingVariableOptions represents options for a single templating variable
type TemplatingVariableOptions struct {
	Label   string          `json:"label"`
	Options []VariableOption `json:"options"`
}

// TemplatingOptionsResponse represents templating variable options
type TemplatingOptionsResponse struct {
	Variables map[string]TemplatingVariableOptions `json:"variables"`
}

