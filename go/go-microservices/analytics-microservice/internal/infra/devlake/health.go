package devlake

import (
	"context"
	"fmt"
	"net/http"
)

// HealthCheck checks if the Grafana API is accessible
// This is a convenience method to verify connectivity
func (c *Client) HealthCheck(ctx context.Context) error {
	// Try to hit a simple endpoint - we'll use the search endpoint as a health check
	resp, err := c.makeGrafanaRequest(ctx, http.MethodGet, "/api/search", nil)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		return nil
	}

	return fmt.Errorf("health check failed with status code: %d", resp.StatusCode)
}
