package devlake

import (
	"context"

	"analytics-microservice/internal/app"
)

// Adapter adapts the concrete DevLake client to the app.DevLakeClient interface
type Adapter struct {
	client *Client
}

// NewAdapter creates a new adapter for the DevLake client
func NewAdapter(client *Client) *Adapter {
	return &Adapter{client: client}
}

// GetDashboards implements app.DevLakeClient interface
func (a *Adapter) GetDashboards(ctx context.Context) ([]map[string]interface{}, error) {
	return a.client.GetDashboards(ctx)
}

// GetDashboard implements app.DevLakeClient interface
func (a *Adapter) GetDashboard(ctx context.Context, uid string) (map[string]interface{}, error) {
	return a.client.GetDashboard(ctx, uid)
}

// GetPanelData implements app.DevLakeClient interface
func (a *Adapter) GetPanelData(ctx context.Context, query, datasourceUID, from, to string, scopedVars map[string]app.ScopedVar) (*app.PanelQueryResponse, error) {
	// Convert app.ScopedVar to devlake.ScopedVar
	devlakeScopedVars := make(map[string]ScopedVar)
	for k, v := range scopedVars {
		devlakeScopedVars[k] = ScopedVar{
			Text:  v.Text,
			Value: v.Value,
		}
	}

	result, err := a.client.GetPanelData(ctx, query, datasourceUID, from, to, devlakeScopedVars)
	if err != nil {
		return nil, err
	}

	// Convert devlake.PanelQueryResponse to app.PanelQueryResponse
	return convertPanelQueryResponse(result), nil
}

// GetTemplatingOptions implements app.DevLakeClient interface
func (a *Adapter) GetTemplatingOptions(ctx context.Context, query, datasourceUID string) (*app.PanelQueryResponse, error) {
	result, err := a.client.GetTemplatingOptions(ctx, query, datasourceUID)
	if err != nil {
		return nil, err
	}

	return convertPanelQueryResponse(result), nil
}

// convertPanelQueryResponse converts devlake.PanelQueryResponse to app.PanelQueryResponse
func convertPanelQueryResponse(resp *PanelQueryResponse) *app.PanelQueryResponse {
	if resp == nil {
		return nil
	}

	results := make(map[string]app.QueryResult)
	for k, v := range resp.Results {
		frames := make([]app.Frame, len(v.Frames))
		for i, f := range v.Frames {
			fields := make([]app.FieldSchema, len(f.Schema.Fields))
			for j, field := range f.Schema.Fields {
				fields[j] = app.FieldSchema{
					Name:     field.Name,
					Type:     field.Type,
					TypeInfo: field.TypeInfo,
				}
			}

			frames[i] = app.Frame{
				Schema: app.FrameSchema{
					Fields: fields,
				},
				Data: app.FrameData{
					Values: f.Data.Values,
				},
			}
		}

		results[k] = app.QueryResult{
			Frames: frames,
			RefID:  v.RefID,
		}
	}

	return &app.PanelQueryResponse{
		Results: results,
	}
}

