package devlake

import (
	"context"
	"fmt"
	"net/http"
)

// GetDashboards retrieves all dashboards from Grafana
// Endpoint: GET /api/search
// Returns a list of dashboard metadata
func (c *Client) GetDashboards(ctx context.Context) ([]map[string]interface{}, error) {
	resp, err := c.makeGrafanaRequest(ctx, http.MethodGet, "/api/search", nil)
	if err != nil {
		return nil, err
	}

	var dashboards []map[string]interface{}
	if err := c.parseResponse(resp, &dashboards); err != nil {
		return nil, err
	}

	return dashboards, nil
}

// GetDashboard retrieves a specific dashboard by UID from Grafana
// Endpoint: GET /api/dashboards/uid/:uid
// Returns the full dashboard detail including panels and templating configuration
func (c *Client) GetDashboard(ctx context.Context, uid string) (map[string]interface{}, error) {
	endpoint := fmt.Sprintf("/api/dashboards/uid/%s", uid)
	resp, err := c.makeGrafanaRequest(ctx, http.MethodGet, endpoint, nil)
	if err != nil {
		return nil, err
	}

	var dashboard map[string]interface{}
	if err := c.parseResponse(resp, &dashboard); err != nil {
		return nil, err
	}

	return dashboard, nil
}
