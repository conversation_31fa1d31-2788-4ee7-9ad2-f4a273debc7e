package devlake

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client represents a Grafana API client
type Client struct {
	grafanaURL    string
	grafanaAPIKey string
	httpClient    *http.Client
}

// NewClient creates a new Grafana client from configuration
// Parameters:
//   - url: The base URL of the Grafana instance (e.g., http://localhost:3002)
//   - apiKey: The API key for Grafana authentication
func NewClient(url, apiKey string) (*Client, error) {
	if url == "" {
		return nil, fmt.Errorf("Grafana URL is required")
	}

	if apiKey == "" {
		return nil, fmt.Errorf("Grafana API key is required")
	}

	// Remove trailing slash from URL if present
	if len(url) > 0 && url[len(url)-1] == '/' {
		url = url[:len(url)-1]
	}

	client := &Client{
		grafanaURL:    url,
		grafanaAPIKey: apiKey,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	return client, nil
}

// makeGrafanaRequest performs an HTTP request to Grafana with API key authentication
func (c *Client) makeGrafanaRequest(ctx context.Context, method, endpoint string, body interface{}) (*http.Response, error) {
	url := fmt.Sprintf("%s%s", c.grafanaURL, endpoint)

	var reqBody io.Reader
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.grafanaAPIKey))

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}

	return resp, nil
}

// parseResponse parses the HTTP response body into the target struct
func (c *Client) parseResponse(resp *http.Response, target interface{}) error {
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if len(bodyBytes) == 0 {
		return nil
	}

	if err := json.Unmarshal(bodyBytes, target); err != nil {
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return nil
}

// Close closes any resources held by the client
// Currently a no-op but included for consistency with other clients
func (c *Client) Close() error {
	return nil
}
