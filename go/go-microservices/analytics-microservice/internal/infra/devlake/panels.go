package devlake

import (
	"context"
	"fmt"
	"net/http"
)

// GetPanelData queries a panel (KPI chart) on a dashboard using raw SQL
// This method queries Grafana's datasource API to execute SQL queries
// Parameters:
//   - query: The raw SQL query to execute
//   - datasourceUID: The UID of the datasource (from dashboard metadata)
//   - from: Start time in RFC3339 format (e.g., "2025-11-01T00:00:00Z")
//   - to: End time in RFC3339 format (e.g., "2025-11-30T23:59:59Z")
//   - scopedVars: Optional map of scoped variables for template substitution (e.g., {"project": {Text: "TMP JIRA", Value: "'TMP JIRA'"}})
//
// Endpoint: POST /api/ds/query
func (c *Client) GetPanelData(ctx context.Context, query, datasourceUID, from, to string, scopedVars map[string]ScopedVar) (*PanelQueryResponse, error) {
	request := PanelQueryRequest{
		Queries: []Query{
			{
				RefID: "A",
				Datasource: Datasource{
					Type: "mysql",
					UID:  datasourceUID,
				},
				RawSQL:        query,
				Format:        "table",
				MaxDataPoints: 1000,
			},
		},
		Range: Range{
			From: from,
			To:   to,
		},
	}

	// Add scoped variables if provided
	if len(scopedVars) > 0 {
		request.ScopedVars = scopedVars
	}

	resp, err := c.makeGrafanaRequest(ctx, http.MethodPost, "/api/ds/query", request)
	if err != nil {
		return nil, fmt.Errorf("failed to execute panel query: %w", err)
	}

	var queryResp PanelQueryResponse
	if err := c.parseResponse(resp, &queryResp); err != nil {
		return nil, fmt.Errorf("failed to parse panel query response: %w", err)
	}

	return &queryResp, nil
}

// GetTemplatingOptions executes a query to get options for a templating variable
// This is used to populate dropdown filters in dashboards
func (c *Client) GetTemplatingOptions(ctx context.Context, query, datasourceUID string) (*PanelQueryResponse, error) {
	request := PanelQueryRequest{
		Queries: []Query{
			{
				RefID: "A",
				Datasource: Datasource{
					Type: "mysql",
					UID:  datasourceUID,
				},
				RawSQL:        query,
				Format:        "table",
				MaxDataPoints: 1000,
			},
		},
		Range: Range{
			From: "now-1y", // Templating queries typically don't need a specific range
			To:   "now",
		},
	}

	resp, err := c.makeGrafanaRequest(ctx, http.MethodPost, "/api/ds/query", request)
	if err != nil {
		return nil, fmt.Errorf("failed to execute templating query: %w", err)
	}

	var queryResp PanelQueryResponse
	if err := c.parseResponse(resp, &queryResp); err != nil {
		return nil, fmt.Errorf("failed to parse templating query response: %w", err)
	}

	return &queryResp, nil
}
