package devlake

// Dashboard represents a DevLake dashboard
type Dashboard struct {
	UID   string                 `json:"uid"`
	Title string                 `json:"title"`
	Data  map[string]interface{} `json:"data,omitempty"`
}

// DashboardSearchResponse represents the response from the search endpoint
type DashboardSearchResponse struct {
	Dashboards []Dashboard `json:"dashboards"`
	Total      int         `json:"total,omitempty"`
}

// PanelQueryRequest represents a request to query panel data
type PanelQueryRequest struct {
	Queries    []Query              `json:"queries"`
	Range      Range                `json:"range"`
	ScopedVars map[string]ScopedVar `json:"scopedVars,omitempty"`
}

// ScopedVar represents a scoped variable for template substitution
type ScopedVar struct {
	Text  string `json:"text"`
	Value string `json:"value"`
}

// Query represents a single query in the panel query request
type Query struct {
	RefID         string     `json:"refId"`
	Datasource    Datasource `json:"datasource"`
	RawSQL        string     `json:"rawSql"`
	Format        string     `json:"format"`
	MaxDataPoints int        `json:"maxDataPoints"`
}

// Datasource represents the datasource configuration
type Datasource struct {
	Type string `json:"type"`
	UID  string `json:"uid"`
}

// Range represents the time range for the query
type Range struct {
	From string `json:"from"`
	To   string `json:"to"`
}

// PanelQueryResponse represents the response from a panel query
type PanelQueryResponse struct {
	Results map[string]QueryResult `json:"results"`
}

// QueryResult represents the result of a single query
type QueryResult struct {
	Frames []Frame `json:"frames"`
	RefID  string  `json:"refId"`
}

// Frame represents a data frame in the query result
type Frame struct {
	Schema FrameSchema `json:"schema"`
	Data   FrameData   `json:"data"`
}

// FrameSchema represents the schema of a data frame
type FrameSchema struct {
	Fields []FieldSchema `json:"fields"`
}

// FieldSchema represents the schema of a field
type FieldSchema struct {
	Name     string                 `json:"name"`
	Type     string                 `json:"type"`
	TypeInfo map[string]interface{} `json:"typeInfo,omitempty"`
}

// FrameData represents the actual data in a frame
type FrameData struct {
	Values [][]interface{} `json:"values"`
}
