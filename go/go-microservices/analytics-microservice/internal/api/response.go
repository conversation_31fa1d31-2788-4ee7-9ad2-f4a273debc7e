// internal/api/response.go
package api

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"analytics-microservice/internal/app"
	"analytics-microservice/internal/logging"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Response represents a standard API response
type Response struct {
	Data  interface{} `json:"data,omitempty"`
	Error *APIError   `json:"error,omitempty"`
}

// Error represents an API error
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// NewErrorResponse creates a new error response
func NewErrorResponse(code string, message string) *Response {
	return &Response{
		//Success: false,
		Error: &APIError{
			Code:    code,
			Message: message,
		},
	}
}

// JSON sends a JSON response
func JSON(c *gin.Context, status int, response interface{}) {
	c.JSON(status, response)
}

// Success sends a 200 OK
func Success(c *gin.Context, data interface{}) {
	JSON(c, http.StatusOK, data)
}

// Created sends a 201 Created
func Created(c *gin.Context, data interface{}) {
	JSON(c, http.StatusCreated, data)
}

// NoContent sends a 204 No Content
func NoContent(c *gin.Context) {
	c.Status(http.StatusNoContent)
}

// BadRequest sends a 400 Bad Request
func BadRequest(c *gin.Context, message string) {
	JSON(c, http.StatusBadRequest, NewErrorResponse("BAD_REQUEST", message))
}

// NotFound sends a 404 Not Found
func NotFound(c *gin.Context, message string) {
	JSON(c, http.StatusNotFound, NewErrorResponse("NOT_FOUND", message))
}

// InternalError sends a 500 Internal Server Error
func InternalError(c *gin.Context, message string) {
	JSON(c, http.StatusInternalServerError, NewErrorResponse("INTERNAL_ERROR", message))
}

// Error inspects any error and sends the appropriate HTTP response
func HandleError(c *gin.Context, err error) {
	var (
		inv  *app.InvalidArgumentError
		nf   *app.NotFoundError
		una  *app.UnauthorizedError
		conf *app.ConflictError
	)

	// Log the error with full details
	logger := logging.GetLogger()
	logger.Error("API error occurred",
		zap.String("path", c.Request.URL.Path),
		zap.String("method", c.Request.Method),
		zap.String("error", err.Error()),
		zap.String("error_type", fmt.Sprintf("%T", err)),
		zap.String("client_ip", c.ClientIP()),
	)

	switch {
	case errors.As(err, &inv):
		JSON(c, http.StatusBadRequest, NewErrorResponse("BAD_REQUEST", inv.Message))

	case errors.As(err, &nf):
		JSON(c, http.StatusNotFound, NewErrorResponse("NOT_FOUND", nf.Error()))

	case errors.As(err, &una):
		JSON(c, http.StatusUnauthorized, NewErrorResponse("UNAUTHORIZED", una.Message))

	case errors.As(err, &conf):
		JSON(c, http.StatusConflict, NewErrorResponse("CONFLICT", conf.Error()))

	case errors.Is(err, context.Canceled):
		JSON(c, 499, NewErrorResponse("REQUEST_CANCELED", "request canceled"))

	case errors.Is(err, context.DeadlineExceeded):
		JSON(c, http.StatusRequestTimeout, NewErrorResponse("TIMEOUT", "request timed out"))

	default:
		// Log full error details for internal errors
		logger.Error("Internal server error",
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
			zap.String("error", err.Error()),
			zap.String("error_type", fmt.Sprintf("%T", err)),
			zap.String("stack_trace", fmt.Sprintf("%+v", err)),
		)
		JSON(c, http.StatusInternalServerError, NewErrorResponse("INTERNAL_ERROR", fmt.Sprintf("internal server error: %v", err)))
	}
}
