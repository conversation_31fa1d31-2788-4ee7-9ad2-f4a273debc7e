package api

import (
	"analytics-microservice/internal/app"
	"analytics-microservice/internal/domain"
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type DashboardService interface {
	HandleGetDashboards(ctx context.Context, q app.GetDashboardsQuery) ([]*domain.Dashboard, error)
	HandleGetDashboard(ctx context.Context, q app.GetDashboardQuery) (*domain.DashboardDetail, error)
	HandleGetTemplatingOptions(ctx context.Context, q app.GetTemplatingOptionsQuery) (*domain.TemplatingOptionsResponse, error)
	HandleGetDashboardData(ctx context.Context, q app.GetDashboardDataQuery) (*domain.DashboardDataResponse, error)
}

type DashboardAPI struct {
	dashboardService DashboardService
	logger           *slog.Logger
}

func NewDashboardAPI(dashboardService DashboardService, logger *slog.Logger) *DashboardAPI {
	return &DashboardAPI{
		dashboardService: dashboardService,
		logger:           logger.WithGroup("DashboardHandler"),
	}
}

func (h *DashboardAPI) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/v1/dashboards")
	{
		api.GET("", h.getAllDashboards)
		api.GET("/:uid", h.getDashboard)
		api.GET("/:uid/templating-options", h.getTemplatingOptions)
		api.POST("/:uid/data", h.getDashboardData)
	}
}

// @Summary Get all dashboards
// @Description Retrieve list of all dashboards (metadata only)
// @Tags Dashboards
// @Produce json
// @Success 200 {array} dashboardResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboards [get]
func (h *DashboardAPI) getAllDashboards(c *gin.Context) {
	h.logger.Info("Getting all dashboards", "request_id", c.GetString("request_id"))

	dashboards, err := h.dashboardService.HandleGetDashboards(c.Request.Context(), app.GetDashboardsQuery{})
	if err != nil {
		h.logger.Error("Failed to get dashboards",
			"error", err.Error(),
			"error_type", fmt.Sprintf("%T", err),
			"request_id", c.GetString("request_id"),
		)
		HandleError(c, err)
		return
	}

	h.logger.Info("Successfully retrieved dashboards", "count", len(dashboards))

	response := make([]dashboardResponse, len(dashboards))
	for i, d := range dashboards {
		response[i] = toDashboardResponse(d)
	}

	Success(c, response)
}

// @Summary Get dashboard details
// @Description Retrieve full dashboard metadata including panels and templating configuration
// @Tags Dashboards
// @Produce json
// @Param uid path string true "Dashboard UID"
// @Success 200 {object} dashboardDetailResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboards/{uid} [get]
func (h *DashboardAPI) getDashboard(c *gin.Context) {
	uid := c.Param("uid")
	h.logger.Info("Getting dashboard", "uid", uid, "request_id", c.GetString("request_id"))

	if uid == "" {
		h.logger.Warn("Dashboard UID is empty")
		BadRequest(c, "dashboard UID is required")
		return
	}

	dashboard, err := h.dashboardService.HandleGetDashboard(c.Request.Context(), app.GetDashboardQuery{UID: uid})
	if err != nil {
		h.logger.Error("Failed to get dashboard",
			"uid", uid,
			"error", err.Error(),
			"error_type", fmt.Sprintf("%T", err),
			"request_id", c.GetString("request_id"),
		)
		HandleError(c, err)
		return
	}

	h.logger.Info("Successfully retrieved dashboard", "uid", uid)
	Success(c, toDashboardDetailResponse(dashboard))
}

// @Summary Get templating options
// @Description Retrieve options for all templating variables in a dashboard
// @Tags Dashboards
// @Produce json
// @Param uid path string true "Dashboard UID"
// @Success 200 {object} templatingOptionsResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboards/{uid}/templating-options [get]
func (h *DashboardAPI) getTemplatingOptions(c *gin.Context) {
	uid := c.Param("uid")
	h.logger.Info("Getting templating options", "uid", uid, "request_id", c.GetString("request_id"))

	if uid == "" {
		h.logger.Warn("Dashboard UID is empty")
		BadRequest(c, "dashboard UID is required")
		return
	}

	options, err := h.dashboardService.HandleGetTemplatingOptions(c.Request.Context(), app.GetTemplatingOptionsQuery{UID: uid})
	if err != nil {
		h.logger.Error("Failed to get templating options",
			"uid", uid,
			"error", err.Error(),
			"error_type", fmt.Sprintf("%T", err),
			"request_id", c.GetString("request_id"),
		)
		HandleError(c, err)
		return
	}

	h.logger.Info("Successfully retrieved templating options", "uid", uid, "variable_count", len(options.Variables))
	Success(c, toTemplatingOptionsResponse(options))
}

// @Summary Get dashboard data
// @Description Fetch data for all panels in a dashboard with filters and date range
// @Tags Dashboards
// @Accept json
// @Produce json
// @Param uid path string true "Dashboard UID"
// @Param request body dashboardDataRequest true "Dashboard data request"
// @Success 200 {object} dashboardDataResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboards/{uid}/data [post]
func (h *DashboardAPI) getDashboardData(c *gin.Context) {
	uid := c.Param("uid")
	h.logger.Info("Getting dashboard data", "uid", uid, "request_id", c.GetString("request_id"))

	if uid == "" {
		h.logger.Warn("Dashboard UID is empty")
		BadRequest(c, "dashboard UID is required")
		return
	}

	var req dashboardDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body", "error", err.Error(), "uid", uid)
		BadRequest(c, err.Error())
		return
	}

	// Validate date format
	if req.From == "" || req.To == "" {
		h.logger.Warn("Missing date range", "from", req.From, "to", req.To, "uid", uid)
		BadRequest(c, "from and to dates are required")
		return
	}

	// Validate RFC3339 format
	if _, err := time.Parse(time.RFC3339, req.From); err != nil {
		h.logger.Warn("Invalid from date format", "from", req.From, "error", err.Error(), "uid", uid)
		BadRequest(c, "from date must be in RFC3339 format (e.g., 2025-11-01T00:00:00Z)")
		return
	}
	if _, err := time.Parse(time.RFC3339, req.To); err != nil {
		h.logger.Warn("Invalid to date format", "to", req.To, "error", err.Error(), "uid", uid)
		BadRequest(c, "to date must be in RFC3339 format (e.g., 2025-11-30T23:59:59Z)")
		return
	}

	// Parse filters - convert interface{} to string, handling arrays and comma-separated strings
	parsedFilters := make(map[string]string)
	for key, value := range req.Filters {
		if value == nil {
			continue
		}

		var filterValue string
		switch v := value.(type) {
		case string:
			filterValue = v
		case []interface{}:
			// Array of values - join with comma
			values := make([]string, 0, len(v))
			for _, item := range v {
				if str, ok := item.(string); ok {
					values = append(values, str)
				}
			}
			filterValue = strings.Join(values, ", ")
		case []string:
			// Array of strings - join with comma
			filterValue = strings.Join(v, ", ")
		default:
			// Try to convert to string
			filterValue = fmt.Sprintf("%v", v)
		}

		if filterValue != "" {
			parsedFilters[key] = filterValue
		}
	}

	query := app.GetDashboardDataQuery{
		DashboardUID: uid,
		From:         req.From,
		To:           req.To,
		Filters:      parsedFilters,
	}

	h.logger.Info("Fetching dashboard data", "uid", uid, "from", req.From, "to", req.To, "filters", req.Filters)

	result, err := h.dashboardService.HandleGetDashboardData(c.Request.Context(), query)
	if err != nil {
		h.logger.Error("Failed to get dashboard data",
			"uid", uid,
			"from", req.From,
			"to", req.To,
			"error", err.Error(),
			"error_type", fmt.Sprintf("%T", err),
			"request_id", c.GetString("request_id"),
		)
		HandleError(c, err)
		return
	}

	h.logger.Info("Successfully retrieved dashboard data", "uid", uid, "panel_count", len(result.Panels))
	Success(c, toDashboardDataResponse(result))
}

// Response types

type dashboardResponse struct {
	ID        int      `json:"id"`
	UID       string   `json:"uid"`
	OrgID     int      `json:"orgId"`
	Title     string   `json:"title"`
	URI       string   `json:"uri"`
	URL       string   `json:"url"`
	Slug      string   `json:"slug"`
	Type      string   `json:"type"`
	Tags      []string `json:"tags"`
	IsStarred bool     `json:"isStarred"`
	SortMeta  int      `json:"sortMeta"`
	IsDeleted bool     `json:"isDeleted"`
}

type dashboardDetailResponse struct {
	Meta      dashboardMetaResponse   `json:"meta"`
	Dashboard dashboardConfigResponse `json:"dashboard"`
}

type dashboardMetaResponse struct {
	Type        string `json:"type"`
	CanSave     bool   `json:"canSave"`
	CanEdit     bool   `json:"canEdit"`
	CanAdmin    bool   `json:"canAdmin"`
	CanStar     bool   `json:"canStar"`
	CanDelete   bool   `json:"canDelete"`
	Slug        string `json:"slug"`
	URL         string `json:"url"`
	Expires     string `json:"expires"`
	Created     string `json:"created"`
	Updated     string `json:"updated"`
	UpdatedBy   string `json:"updatedBy"`
	CreatedBy   string `json:"createdBy"`
	Version     int    `json:"version"`
	HasAcl      bool   `json:"hasAcl"`
	IsFolder    bool   `json:"isFolder"`
	FolderID    int    `json:"folderId"`
	FolderUID   string `json:"folderUid"`
	FolderTitle string `json:"folderTitle"`
	FolderURL   string `json:"folderUrl"`
	Provisioned bool   `json:"provisioned"`
}

type dashboardConfigResponse struct {
	ID                   int                      `json:"id"`
	UID                  string                   `json:"uid"`
	Title                string                   `json:"title"`
	Editable             bool                     `json:"editable"`
	Panels               []panelResponse          `json:"panels"`
	Templating           templatingConfigResponse `json:"templating"`
	Time                 timeConfigResponse       `json:"time"`
	Timezone             string                   `json:"timezone"`
	Annotations          map[string]interface{}   `json:"annotations,omitempty"`
	Links                []interface{}            `json:"links,omitempty"`
	FiscalYearStartMonth int                      `json:"fiscalYearStartMonth,omitempty"`
	GraphTooltip         int                      `json:"graphTooltip,omitempty"`
}

type panelResponse struct {
	ID          int                    `json:"id"`
	Title       string                 `json:"title"`
	Type        string                 `json:"type"`
	GridPos     gridPositionResponse   `json:"gridPos"`
	FieldConfig map[string]interface{} `json:"fieldConfig,omitempty"`
	Options     map[string]interface{} `json:"options,omitempty"`
	RawSQL      string                 `json:"rawSql,omitempty"`
	Datasource  datasourceInfoResponse `json:"datasource,omitempty"`
}

type gridPositionResponse struct {
	H int `json:"h"`
	W int `json:"w"`
	X int `json:"x"`
	Y int `json:"y"`
}

type datasourceInfoResponse struct {
	Type string `json:"type"`
	UID  string `json:"uid"`
}

type templatingConfigResponse struct {
	List []templatingVariableResponse `json:"list"`
}

type templatingVariableResponse struct {
	Name       string                   `json:"name"`
	Label      string                   `json:"label"`
	Type       string                   `json:"type"`
	Datasource string                   `json:"datasource"`
	Query      string                   `json:"query"`
	Definition string                   `json:"definition"`
	IncludeAll bool                     `json:"includeAll"`
	Multi      bool                     `json:"multi"`
	Current    variableCurrentResponse  `json:"current"`
	Options    []variableOptionResponse `json:"options"`
	Refresh    int                      `json:"refresh"`
	Regex      string                   `json:"regex"`
}

type variableCurrentResponse struct {
	Text  []string `json:"text"`
	Value []string `json:"value"`
}

type variableOptionResponse struct {
	Text  string `json:"text"`
	Value string `json:"value"`
}

type timeConfigResponse struct {
	From string `json:"from"`
	To   string `json:"to"`
}

type templatingVariableOptionsResponse struct {
	Label   string                   `json:"label"`
	Options []variableOptionResponse `json:"options"`
}

type templatingOptionsResponse struct {
	Variables map[string]templatingVariableOptionsResponse `json:"variables"`
}

type dashboardDataRequest struct {
	From    string                 `json:"from" binding:"required"` // RFC3339 format
	To      string                 `json:"to" binding:"required"`   // RFC3339 format
	Filters map[string]interface{} `json:"filters"`                 // e.g., {"project": "TMP JIRA", "engineer": "Rex Kqiku"} or {"engineer": ["Mario Deda", "Rex Kqiku"]} or {"engineer": "Mario Deda, Rex Kqiku"}
}

type dashboardDataResponse struct {
	DashboardUID string              `json:"dashboardUid"`
	From         string              `json:"from"`
	To           string              `json:"to"`
	Panels       []panelDataResponse `json:"panels"`
}

type panelDataResponse struct {
	PanelID int                    `json:"panelId"`
	Title   string                 `json:"title"`
	Data    map[string]interface{} `json:"data"`
}

// Mappers

func toDashboardResponse(d *domain.Dashboard) dashboardResponse {
	return dashboardResponse{
		ID:        d.ID,
		UID:       d.UID,
		OrgID:     d.OrgID,
		Title:     d.Title,
		URI:       d.URI,
		URL:       d.URL,
		Slug:      d.Slug,
		Type:      d.Type,
		Tags:      d.Tags,
		IsStarred: d.IsStarred,
		SortMeta:  d.SortMeta,
		IsDeleted: d.IsDeleted,
	}
}

func toDashboardDetailResponse(d *domain.DashboardDetail) dashboardDetailResponse {
	return dashboardDetailResponse{
		Meta:      toDashboardMetaResponse(d.Meta),
		Dashboard: toDashboardConfigResponse(d.Dashboard),
	}
}

func toDashboardMetaResponse(m domain.DashboardMeta) dashboardMetaResponse {
	return dashboardMetaResponse{
		Type:        m.Type,
		CanSave:     m.CanSave,
		CanEdit:     m.CanEdit,
		CanAdmin:    m.CanAdmin,
		CanStar:     m.CanStar,
		CanDelete:   m.CanDelete,
		Slug:        m.Slug,
		URL:         m.URL,
		Expires:     m.Expires,
		Created:     m.Created,
		Updated:     m.Updated,
		UpdatedBy:   m.UpdatedBy,
		CreatedBy:   m.CreatedBy,
		Version:     m.Version,
		HasAcl:      m.HasAcl,
		IsFolder:    m.IsFolder,
		FolderID:    m.FolderID,
		FolderUID:   m.FolderUID,
		FolderTitle: m.FolderTitle,
		FolderURL:   m.FolderURL,
		Provisioned: m.Provisioned,
	}
}

func toDashboardConfigResponse(d domain.DashboardConfig) dashboardConfigResponse {
	panels := make([]panelResponse, len(d.Panels))
	for i, p := range d.Panels {
		panels[i] = toPanelResponse(p)
	}

	return dashboardConfigResponse{
		ID:                   d.ID,
		UID:                  d.UID,
		Title:                d.Title,
		Editable:             d.Editable,
		Panels:               panels,
		Templating:           toTemplatingConfigResponse(d.Templating),
		Time:                 toTimeConfigResponse(d.Time),
		Timezone:             d.Timezone,
		Annotations:          d.Annotations,
		Links:                d.Links,
		FiscalYearStartMonth: d.FiscalYearStartMonth,
		GraphTooltip:         d.GraphTooltip,
	}
}

func toPanelResponse(p domain.Panel) panelResponse {
	return panelResponse{
		ID:          p.ID,
		Title:       p.Title,
		Type:        p.Type,
		GridPos:     toGridPositionResponse(p.GridPos),
		FieldConfig: p.FieldConfig,
		Options:     p.Options,
		RawSQL:      p.RawSQL,
		Datasource:  toDatasourceInfoResponse(p.Datasource),
	}
}

func toGridPositionResponse(g domain.GridPosition) gridPositionResponse {
	return gridPositionResponse{
		H: g.H,
		W: g.W,
		X: g.X,
		Y: g.Y,
	}
}

func toDatasourceInfoResponse(d domain.DatasourceInfo) datasourceInfoResponse {
	return datasourceInfoResponse{
		Type: d.Type,
		UID:  d.UID,
	}
}

func toTemplatingConfigResponse(t domain.TemplatingConfig) templatingConfigResponse {
	list := make([]templatingVariableResponse, len(t.List))
	for i, v := range t.List {
		list[i] = toTemplatingVariableResponse(v)
	}
	return templatingConfigResponse{List: list}
}

func toTemplatingVariableResponse(v domain.TemplatingVariable) templatingVariableResponse {
	options := make([]variableOptionResponse, len(v.Options))
	for i, o := range v.Options {
		options[i] = variableOptionResponse{
			Text:  o.Text,
			Value: o.Value,
		}
	}

	return templatingVariableResponse{
		Name:       v.Name,
		Label:      v.Label,
		Type:       v.Type,
		Datasource: v.Datasource,
		Query:      v.Query,
		Definition: v.Definition,
		IncludeAll: v.IncludeAll,
		Multi:      v.Multi,
		Current: variableCurrentResponse{
			Text:  v.Current.Text,
			Value: v.Current.Value,
		},
		Options: options,
		Refresh: v.Refresh,
		Regex:   v.Regex,
	}
}

func toTimeConfigResponse(t domain.TimeConfig) timeConfigResponse {
	return timeConfigResponse{
		From: t.From,
		To:   t.To,
	}
}

func toTemplatingOptionsResponse(o *domain.TemplatingOptionsResponse) templatingOptionsResponse {
	variables := make(map[string]templatingVariableOptionsResponse)
	for key, varOpts := range o.Variables {
		options := make([]variableOptionResponse, len(varOpts.Options))
		for i, opt := range varOpts.Options {
			options[i] = variableOptionResponse{
				Text:  opt.Text,
				Value: opt.Value,
			}
		}
		variables[key] = templatingVariableOptionsResponse{
			Label:   varOpts.Label,
			Options: options,
		}
	}
	return templatingOptionsResponse{Variables: variables}
}

func toDashboardDataResponse(d *domain.DashboardDataResponse) dashboardDataResponse {
	panels := make([]panelDataResponse, len(d.Panels))
	for i, p := range d.Panels {
		panels[i] = panelDataResponse{
			PanelID: p.PanelID,
			Title:   p.Title,
			Data:    p.Data,
		}
	}

	return dashboardDataResponse{
		DashboardUID: d.DashboardUID,
		From:         d.From,
		To:           d.To,
		Panels:       panels,
	}
}
