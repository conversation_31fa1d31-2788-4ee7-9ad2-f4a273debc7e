# Huly Platform - Agent Guide

## Build & Test Commands

- **Install**: `rush install` (use this after git pull)
- **Build all**: `rush build` (or `rush rebuild` to clear cache)
- **Build & watch**: `rush build:watch` (includes build + validate in watch mode)
- **Bundle**: `rush bundle` (prepare webpack bundles)
- **Package**: `rush package` (build webpack packages)
- **Validate**: `rush validate` (TypeScript validation + d.ts generation)
- **Svelte check**: `rush svelte-check` (optional Svelte validation)
- **Test all**: `rush test` (run all Jest tests)
- **Test single package**: `cd <package-dir> && rushx test` (run tests in specific package)
- **Format**: `rushx format` (format code with Prettier in package)
- **Docker build**: `rush docker:build` (builds containers, runs build/bundle/package automatically)
- **Docker up**: `rush docker:up` (start all containers)
- **Quick start**: `sh ./scripts/fast-start.sh` or `sh ./scripts/build.sh`

## Architecture & Structure

**Monorepo**: Rush-based monorepo using pnpm (v10.15.1), Node.js v22 required
**Main directories**:
- `packages/` - Core platform packages (@hcengineering/platform, core, ui, text, etc.)
- `plugins/` - Feature plugins (hr, tracker, contact, chat, etc.) - each has 3 sub-packages: plugin, assets, resources
- `models/` - Data model definitions for plugins
- `server-plugins/` - Server-side plugin logic
- `services/` & `pods/` - Microservices (account, server/transactor, collaborator, fulltext, stats, mail, etc.)
- `dev/` - Development environment setup & Docker configs
- `tests/`, `ws-tests/`, `qms-tests/` - Testing suites
- `communication/` - Git submodule for communication features
- `go/` - Go microservices (analytics, webhook, annotation tool)

**Key concepts**: Event-sourced object model, Workspaces (multi-tenant), Spaces (containers), Transactions (all changes), Mixins (dynamic types), Domains (storage). See HULY-MODEL-ARCHITECTURE.md for details.

**Databases**: MongoDB (main storage), Elasticsearch (fulltext search), MinIO (blob storage), CockroachDB (communication submodule)

**Internal APIs**: Platform resources, client queries, collaborator client, API client (packages/api-client)

## Code Style & Conventions

**Formatting**: Prettier (single quotes, no semicolons, 120 char width, 2 space tabs). Config: `.prettierrc`
**Linting**: ESLint with TypeScript (@hcengineering/platform-rig profiles). Config: `.eslintrc.js` in each package
**TypeScript**: Strict mode enabled. Generate .d.ts files with `rush validate`
**Testing**: Jest framework. Test files: `*.test.ts`. Run with `rushx test` in package or `rush test` globally
**Imports**: Use `@hcengineering/` scoped packages. Follow existing import patterns in each package
**Naming**: 
  - Classes/Types: PascalCase
  - Variables/functions: camelCase
  - Files: kebab-case for components, camelCase for utilities
  - Plugins follow pattern: `plugin`, `plugin-assets`, `plugin-resources`
**Error handling**: Use platform error types, async/await with try-catch
**Svelte**: Used for UI components in `*-resources` packages
**Documentation**: Comprehensive docs in `documentation/` - see START-HERE.md for navigation

## Daily Workflow

After `git pull`: run `rush update && rush build` (or `rush rebuild` if build cache issues). See documentation/docs/getting-started/daily-workflow.md
