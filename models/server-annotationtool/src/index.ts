//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import core from '@hcengineering/core'
import { type Builder } from '@hcengineering/model'
import annotationTool from '@hcengineering/model-annotationtool'
import chunter from '@hcengineering/model-chunter'
import serverChunter from '@hcengineering/server-chunter'
import serverCore from '@hcengineering/server-core'
import serverNotification from '@hcengineering/server-notification'
import serverAnnotationTool from '@hcengineering/server-annotationtool'

export { serverAnnotationToolId } from '@hcengineering/server-annotationtool'

export function createModel (builder: Builder): void {
  // Configure HTML presenters for AnnotationActivity
  builder.mixin(annotationTool.class.AnnotationActivity, core.class.Class, serverNotification.mixin.HTMLPresenter, {
    presenter: serverAnnotationTool.function.AnnotationActivityHTMLPresenter
  })

  // Configure text presenters for AnnotationActivity
  builder.mixin(annotationTool.class.AnnotationActivity, core.class.Class, serverNotification.mixin.TextPresenter, {
    presenter: serverAnnotationTool.function.AnnotationActivityTextPresenter
  })

  // Configure notification content provider for AnnotationActivity
  builder.mixin(annotationTool.class.AnnotationActivity, core.class.Class, serverNotification.mixin.NotificationPresenter, {
    presenter: serverAnnotationTool.function.AnnotationActivityNotificationContentProvider
  })

  // Configure notification content provider for ChatMessage (reuse chunter's provider for chat messages)
  // This is needed so notifications for ChatMessage attached to AnnotationActivity can be displayed
  builder.mixin(chunter.class.ChatMessage, core.class.Class, serverNotification.mixin.NotificationPresenter, {
    presenter: serverChunter.function.ChunterNotificationContentProvider
  })

  // Configure search presenters for AnnotationActivity
  builder.mixin(annotationTool.class.AnnotationActivity, core.class.Class, serverCore.mixin.SearchPresenter, {
    shortTitle: [['episodeId']],
    title: [['episodeId']]
  })

  // Create trigger to auto-add all project members as collaborators
  builder.createDoc(serverCore.class.Trigger, core.space.Model, {
    trigger: serverAnnotationTool.trigger.OnAnnotationActivityCreate,
    txMatch: {
      _class: core.class.TxCreateDoc,
      objectClass: annotationTool.class.AnnotationActivity
    }
  })

  // Create trigger to handle chat message notifications
  builder.createDoc(serverCore.class.Trigger, core.space.Model, {
    trigger: serverAnnotationTool.trigger.ChatNotificationsHandler,
    txMatch: {
      _class: core.class.TxCreateDoc,
      objectClass: chunter.class.ChatMessage
    },
    isAsync: true
  })
}
