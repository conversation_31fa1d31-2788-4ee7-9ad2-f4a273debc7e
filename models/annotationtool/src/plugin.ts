import annotationTool, { annotationToolId } from '@hcengineering/annotationtool'
import { AnyComponent } from '@hcengineering/ui'
import { Asset, IntlString, mergeIds, Resource } from '@hcengineering/platform'
import { type LocationData } from '@hcengineering/workbench'
import { NotificationGroup, NotificationType } from '@hcengineering/notification'
import { ChatMessageViewlet } from '@hcengineering/chunter'
import { Ref } from '@hcengineering/core'
import { ActionCategory } from '@hcengineering/view'

export default mergeIds(annotationToolId, annotationTool, {
  string: {
    Name: '' as IntlString,
    Description: '' as IntlString,
    AnnotationTool: '' as IntlString,
    AnnotationActivity: '' as IntlString,
    AnnotationActivityMessages: '' as IntlString,
    ConfigLabel: '' as IntlString,
    ConfigDescription: '' as IntlString,
    ConfigValue: '' as IntlString,
    ConfigColor: '' as IntlString,
    ConfigAttachedToProject: '' as IntlString,
    ConfigAccount: '' as IntlString,
    ConfigAccessLevel: '' as IntlString,
    ConfigEpisodeId: '' as IntlString,
    ConfigStripId: '' as IntlString,
    ConfigProject: '' as IntlString,
    ConfigDatasetId: '' as IntlString,
    ConfigRules: '' as IntlString,
    ApplicationLabelAnnotationTool: '' as IntlString,
    DoneD: '' as IntlString,
    ClearX: '' as IntlString,
    ApplyToEntireStripE: '' as IntlString,
    SnapLeft: '' as IntlString,
    SnapRight: '' as IntlString,
    AddNewSegment: '' as IntlString,
    ConfigSegmentTimestamp: '' as IntlString,
    ConfigComment: '' as IntlString,
    ConfigShortcut: '' as IntlString,
  },
  function: {
    LocationDataResolver: '' as Resource<(loc: Location) => Promise<LocationData>>
  },
  ids: {
    AnnotationToolDocNotificationGroup: '' as Ref<NotificationGroup>,
    AnnotationActivityNotificationGroup: '' as Ref<NotificationGroup>,
    AnnotationToolDocChatMessageViewlet: '' as Ref<ChatMessageViewlet>,
    AnnotationActivityChatMessageViewlet: '' as Ref<ChatMessageViewlet>,
    AnnotationActivityChatMessageNotification: '' as Ref<NotificationType>
  },
  component: {
    ActivityNotificationLabel: '' as AnyComponent,
    ActivityPanel: '' as AnyComponent
  },
  category: {
    AnnotationTool: '' as Ref<ActionCategory>
  } 
})