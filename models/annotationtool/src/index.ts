import { Asset, Plugin } from "@hcengineering/platform";
import { Builder } from "@hcengineering/model";
import { TAnnotationToolDoc, <PERSON><PERSON><PERSON><PERSON>, T<PERSON>riageLabel, TAnnotator, TAnnotationActivity, TSegmentInteresting, TSegmentComment } from "./types";
import workbench from "@hcengineering/model-workbench";
import core from "@hcengineering/model-core";
import setting from '@hcengineering/setting'
import activity from '@hcengineering/activity'
import chunter from '@hcengineering/chunter'
import notification from '@hcengineering/notification'
import { generateClassNotificationTypes } from '@hcengineering/model-notification'
import view from '@hcengineering/view'

import annotationTool from "./plugin";
import { AnyComponent } from "@hcengineering/ui";
import { Ref, Doc, AccountRole, Domain, ClassCollaborators } from "@hcengineering/core";
import { defineActions } from "./actions";
import { AnnotationToolDoc, AnnotationActivity } from "@hcengineering/annotationtool";

export const DOMAIN_ANNOTATIONTOOL = 'annotationtool' as Domain

export const annotationToolId = 'annotationtool' as Plugin

function defineNotifications (builder: Builder): void {
  // Create notification group for AnnotationActivity
  builder.createDoc(
    notification.class.NotificationGroup,
    core.space.Model,
    {
      label: annotationTool.string.AnnotationActivity,
      icon: annotationTool.icon.AnnotationTool as Asset,
      objectClass: annotationTool.class.AnnotationActivity
    },
    annotationTool.ids.AnnotationActivityNotificationGroup
  )

  // Generate notification types for AnnotationActivity
  generateClassNotificationTypes(
    builder,
    annotationTool.class.AnnotationActivity,
    annotationTool.ids.AnnotationActivityNotificationGroup,
    [],
    ['comments']
  )

  // Create notification type for ChatMessage on AnnotationActivity
  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      label: annotationTool.string.AnnotationActivityMessages,
      generated: false,
      hidden: false,
      txClasses: [core.class.TxCreateDoc],
      objectClass: chunter.class.ChatMessage,
      attachedToClass: annotationTool.class.AnnotationActivity,
      defaultEnabled: false,
      group: annotationTool.ids.AnnotationActivityNotificationGroup,
      templates: {
        textTemplate: '{sender} has sent a message in {doc}: {message}',
        htmlTemplate: '<p><b>{sender}</b> has sent a message in {doc}</p> {message}',
        subjectTemplate: 'You have new message in {doc}'
      }
    },
    annotationTool.ids.AnnotationActivityChatMessageNotification
  )

  builder.mixin(annotationTool.class.AnnotationActivity, core.class.Class, notification.mixin.NotificationContextPresenter, {
    labelPresenter: annotationTool.component.ActivityNotificationLabel
  })

  builder.mixin(annotationTool.class.AnnotationActivity, core.class.Class, view.mixin.ObjectPanel, {
    component: annotationTool.component.ActivityPanel
  })

  // Enable notifications by default for inbox provider
  builder.createDoc(notification.class.NotificationProviderDefaults, core.space.Model, {
    provider: notification.providers.InboxNotificationProvider,
    ignoredTypes: [],
    enabledTypes: [annotationTool.ids.AnnotationActivityChatMessageNotification]
  })
}

export function createModel (builder: Builder): void {
  builder.createModel(TAnnotationToolDoc, TLabel, TTriageLabel, TAnnotator, TAnnotationActivity, TSegmentInteresting, TSegmentComment)

  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: annotationTool.string.ApplicationLabelAnnotationTool,
      // locationDataResolver: annotationTool.function.LocationDataResolver,
      icon: annotationTool.icon.AnnotationTool as Asset,
      alias: annotationToolId,
      hidden: false,
      component: annotationTool.component.AnnotationTool as AnyComponent
    },
    annotationTool.app.AnnotationToolApp as Ref<Doc>
  )
  
  builder.createDoc(
    setting.class.SettingsCategory, 
    core.space.Model, {
      name: annotationToolId,
      label: annotationTool.string.ApplicationLabelAnnotationTool,
      icon: annotationTool.icon.AnnotationTool as Asset,
      component: annotationTool.component.Settings as AnyComponent,
      group: 'settings-editor',
      role: AccountRole.Maintainer,
      order: 1700
    }
  )

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: annotationTool.class.AnnotationActivity,
    components: { input: { component: chunter.component.ChatMessageInput } }
  })

  builder.createDoc<ClassCollaborators<AnnotationToolDoc>>(core.class.ClassCollaborators, core.space.Model, {
    attachedTo: annotationTool.class.AnnotationToolDoc,
    fields: ['createdBy']
  })

  builder.createDoc<ClassCollaborators<AnnotationActivity>>(core.class.ClassCollaborators, core.space.Model, {
    attachedTo: annotationTool.class.AnnotationActivity,
    fields: ['createdBy']
  })

  // Create chat message viewlets for AnnotationToolDoc
  builder.createDoc(
    chunter.class.ChatMessageViewlet,
    core.space.Model,
    {
      messageClass: chunter.class.ChatMessage,
      objectClass: annotationTool.class.AnnotationToolDoc,
      label: chunter.string.LeftComment
    },
    annotationTool.ids.AnnotationToolDocChatMessageViewlet
  )

  // Create chat message viewlets for AnnotationActivity
  builder.createDoc(
    chunter.class.ChatMessageViewlet,
    core.space.Model,
    {
      messageClass: chunter.class.ChatMessage,
      objectClass: annotationTool.class.AnnotationActivity,
      label: chunter.string.LeftComment
    },
    annotationTool.ids.AnnotationActivityChatMessageViewlet
  )

  // Define notifications
  defineNotifications(builder)

  // Define actions
  defineActions(builder)
}

export default annotationTool