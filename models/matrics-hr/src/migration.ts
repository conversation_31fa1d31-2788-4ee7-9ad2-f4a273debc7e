//
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import {
  type Space,
  TxOperations,
  type Ref,
  type Class,
  type Doc,
  DOMAIN_MODEL,
  DOMAIN_MODEL_TX,
  type TxCUD
} from '@hcengineering/core'
import { type Department, RequestStatus } from '@hcengineering/matrics-hr'
import {
  migrateSpace,
  tryMigrate,
  tryUpgrade,
  type MigrateOperation,
  type MigrationClient,
  type MigrationUpgradeClient
} from '@hcengineering/model'
import core, { DOMAIN_SPACE, getAccountsFromTxes } from '@hcengineering/model-core'

import hr, { DOMAIN_MATRICS_HR, matricsHrId } from './index'

async function createDepartment (tx: TxOperations): Promise<void> {
  const current = await tx.findOne(hr.class.Department, {
    _id: hr.ids.Head
  })
  if (current === undefined) {
    await tx.createDoc(
      hr.class.Department,
      core.space.Workspace,
      {
        name: 'Organization',
        description: '',
        members: [],
        teamLead: null,
        managers: []
      },
      hr.ids.Head
    )
  }
}

// Move data from legacy HR domain (hr) to the new Matrics‑HR domain (matrics-hr)
async function migrateFromHrDomain (client: MigrationClient): Promise<void> {
  const HR_DOMAIN = 'hr' as any
  // Move Departments
  await client.move(HR_DOMAIN, { _class: 'hr:class:Department' as any }, DOMAIN_MATRICS_HR)
  // Move Requests
  await client.move(HR_DOMAIN, { _class: 'hr:class:Request' as any }, DOMAIN_MATRICS_HR)
  // Move Public Holidays
  await client.move(HR_DOMAIN, { _class: 'hr:class:PublicHoliday' as any }, DOMAIN_MATRICS_HR)

  // Rebind classes to Matrics‑HR equivalents
  await client.update(DOMAIN_MATRICS_HR, { _class: 'hr:class:Department' as any }, { _class: hr.class.Department as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: 'hr:class:Request' as any }, { _class: hr.class.Request as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: 'hr:class:PublicHoliday' as any }, { _class: hr.class.PublicHoliday as any })

  // Update references to the Head department id
  const OLD_HEAD = 'hr:ids:Head' as any
  await client.update(DOMAIN_MATRICS_HR, { _class: hr.class.Department as any, parent: OLD_HEAD }, { parent: hr.ids.Head as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: hr.class.Request as any, department: OLD_HEAD }, { department: hr.ids.Head as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: hr.class.PublicHoliday as any, department: OLD_HEAD }, { department: hr.ids.Head as any })

  // Update RequestType class ids in the model domain
  await client.update(DOMAIN_MODEL_TX as any, { 'tx.objectClass': 'hr:class:RequestType' as any }, { 'tx.objectClass': hr.class.RequestType as any } as any)
  await client.update(DOMAIN_MODEL as any, { _class: 'hr:class:RequestType' as any }, { _class: hr.class.RequestType as any } as any)

  // Ensure Request.attachedToClass references new mixin id
  await client.update(
    DOMAIN_MATRICS_HR,
    { _class: hr.class.Request as any, attachedToClass: 'hr:mixin:Staff' as any },
    { attachedToClass: hr.mixin.Staff as any }
  )
}

async function migrateDepartments (client: MigrationClient): Promise<void> {
  await client.update(
    DOMAIN_MATRICS_HR,
    { _class: hr.class.PublicHoliday, space: { $ne: core.space.Workspace } },
    { space: core.space.Workspace }
  )
  const objects = await client.find(DOMAIN_MATRICS_HR, { space: { $ne: core.space.Workspace }, _class: hr.class.Request })
  for (const obj of objects) {
    await client.update(DOMAIN_MATRICS_HR, { _id: obj._id }, { space: core.space.Workspace, department: obj.space })
  }
  await client.move(DOMAIN_SPACE, { _class: hr.class.Department }, DOMAIN_MATRICS_HR)
  const departments = await client.find<Department>(DOMAIN_MATRICS_HR, {
    _class: hr.class.Department,
    space: { $ne: core.space.Workspace }
  })
  for (const department of departments) {
    const upd: Partial<Department> = {
      space: core.space.Workspace
    }
    if (department._id !== hr.ids.Head) {
      upd.parent = department.space as unknown as Ref<Department>
    }
    await client.update(DOMAIN_MATRICS_HR, { _id: department._id }, upd)
  }
  await client.update(
    DOMAIN_MATRICS_HR,
    { _class: hr.class.Department },
    { $unset: { archived: true, private: true, owners: true, autoJoin: true } }
  )
}

async function migrateDepartmentMembersToEmployee (client: MigrationClient): Promise<void> {
  const departments = await client.find<Department>(DOMAIN_MATRICS_HR, { _class: hr.class.Department })

  for (const department of departments) {
    const accounts = department.members
    if (accounts === undefined || accounts.length === 0) continue

    const personAccountsTxes: any[] = await client.find<TxCUD<Doc>>(DOMAIN_MODEL_TX, {
      objectClass: 'contact:class:PersonAccount' as Ref<Class<Doc>>,
      objectId: { $in: accounts }
    })
    const personAccounts = getAccountsFromTxes(personAccountsTxes)

    await client.update(DOMAIN_MATRICS_HR, { _id: department._id }, { members: personAccounts.map((pAcc: any) => pAcc.person) })
  }
}

/**
 * Add status field to existing Request documents
 * Set all existing requests to 'approved' status by default
 */
async function addRequestStatus (client: MigrationClient): Promise<void> {
  await client.update(
    DOMAIN_MATRICS_HR,
    { _class: hr.class.Request as any, status: { $exists: false } },
    {
      status: RequestStatus.Approved as any,
      submittedDate: Date.now(),
      approvalDate: Date.now()
    } as any
  )
}

/**
 * Idempotent version of migrateFromHrDomain that re-applies the migration
 * for instances where the previous migration was skipped due to mode restrictions.
 * This migration safely handles both fresh installs and upgrade scenarios.
 */
async function migrateHrToMatricsHrV2 (client: MigrationClient): Promise<void> {
  const HR_DOMAIN = 'hr' as any

  // Check if there's data in the old HR domain that needs migrating
  const oldHrDepartments = await client.find(HR_DOMAIN, { _class: 'hr:class:Department' as any })
  const oldHrRequests = await client.find(HR_DOMAIN, { _class: 'hr:class:Request' as any })
  const oldHrHolidays = await client.find(HR_DOMAIN, { _class: 'hr:class:PublicHoliday' as any })

  // Only proceed if there's data to migrate
  if (oldHrDepartments.length > 0 || oldHrRequests.length > 0 || oldHrHolidays.length > 0) {
    // Move Departments
    if (oldHrDepartments.length > 0) {
      await client.move(HR_DOMAIN, { _class: 'hr:class:Department' as any }, DOMAIN_MATRICS_HR)
    }
    // Move Requests
    if (oldHrRequests.length > 0) {
      await client.move(HR_DOMAIN, { _class: 'hr:class:Request' as any }, DOMAIN_MATRICS_HR)
    }
    // Move Public Holidays
    if (oldHrHolidays.length > 0) {
      await client.move(HR_DOMAIN, { _class: 'hr:class:PublicHoliday' as any }, DOMAIN_MATRICS_HR)
    }
  }

  // Rebind classes to Matrics‑HR equivalents (safe to run even if already done)
  await client.update(DOMAIN_MATRICS_HR, { _class: 'hr:class:Department' as any }, { _class: hr.class.Department as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: 'hr:class:Request' as any }, { _class: hr.class.Request as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: 'hr:class:PublicHoliday' as any }, { _class: hr.class.PublicHoliday as any })

  // Update references to the Head department id (safe to run multiple times)
  const OLD_HEAD = 'hr:ids:Head' as any
  await client.update(DOMAIN_MATRICS_HR, { _class: hr.class.Department as any, parent: OLD_HEAD }, { parent: hr.ids.Head as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: hr.class.Request as any, department: OLD_HEAD }, { department: hr.ids.Head as any })
  await client.update(DOMAIN_MATRICS_HR, { _class: hr.class.PublicHoliday as any, department: OLD_HEAD }, { department: hr.ids.Head as any })

  // Update RequestType class ids in the model domain (safe to run multiple times)
  await client.update(DOMAIN_MODEL_TX as any, { 'tx.objectClass': 'hr:class:RequestType' as any }, { 'tx.objectClass': hr.class.RequestType as any } as any)
  await client.update(DOMAIN_MODEL as any, { _class: 'hr:class:RequestType' as any }, { _class: hr.class.RequestType as any } as any)

  // Ensure Request.attachedToClass references new mixin id (safe to run multiple times)
  await client.update(
    DOMAIN_MATRICS_HR,
    { _class: hr.class.Request as any, attachedToClass: 'hr:mixin:Staff' as any },
    { attachedToClass: hr.mixin.Staff as any }
  )
}

export const hrOperation: MigrateOperation = {
  async migrate (client: MigrationClient, mode): Promise<void> {
    await tryMigrate(mode, client, matricsHrId, [
      {
        state: 'migrateHrToMatricsHr',
        func: migrateFromHrDomain
      },
      {
        state: 'migrateDepartments',
        func: migrateDepartments
      },
      {
        state: 'removeDeprecatedSpace',
        func: async (client: MigrationClient) => {
          await migrateSpace(client, 'hr:space:HR' as Ref<Space>, core.space.Workspace, [DOMAIN_MATRICS_HR])
        }
      },
      {
        state: 'migrateDepartmentMembersToEmployee',
        func: migrateDepartmentMembersToEmployee
      },
      {
        state: 'addRequestStatus',
        func: addRequestStatus
      },
      {
        state: 'matrics-hr-reindex-v1',
        func: async (client) => {
          await client.reindex(DOMAIN_MATRICS_HR, [hr.class.Department, hr.class.Request, hr.class.PublicHoliday])
        }
      },
      {
        state: 'migrateHrToMatricsHr_v2',
        func: migrateHrToMatricsHrV2
      }
    ])
  },
  async upgrade (state: Map<string, Set<string>>, client: () => Promise<MigrationUpgradeClient>, mode): Promise<void> {
    await tryUpgrade(mode, state, client, matricsHrId, [
      {
        state: 'create-defaults-v2',
        func: async (client) => {
          const tx = new TxOperations(client, core.account.System)
          await createDepartment(tx)
        }
      }
    ])
  }
}
