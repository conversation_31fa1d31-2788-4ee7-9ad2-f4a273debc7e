//
// Copyright © 2022, 2023 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { type Ref, type Doc } from '@hcengineering/core'
import { matricsHrId } from '@hcengineering/matrics-hr'
import hr from '@hcengineering/matrics-hr-resources/src/plugin'
import { type NotificationGroup } from '@hcengineering/notification'
import { mergeIds } from '@hcengineering/platform'
import { type AnyComponent } from '@hcengineering/ui/src/types'
import { type Action, type ActionCategory, type ViewAction } from '@hcengineering/view'

export default mergeIds(matricsHrId, hr, {
  string: {},
  class: {},
  component: {
    Structure: '' as AnyComponent,
    CreateDepartment: '' as AnyComponent,
    EditDepartment: '' as AnyComponent,
    DepartmentStaff: '' as AnyComponent,
    DepartmentEditor: '' as AnyComponent,
    Schedule: '' as AnyComponent,
    EditRequest: '' as AnyComponent,
    TzDatePresenter: '' as AnyComponent,
    TzDateEditor: '' as AnyComponent,
    RequestPresenter: '' as AnyComponent,
    DepartmentPresenter: '' as AnyComponent,
    DepartmentRefPresenter: '' as AnyComponent,
    CreateOffice: '' as AnyComponent,
    EditOffice: '' as AnyComponent,
    OfficePresenter: '' as AnyComponent,
    OfficeEditor: '' as AnyComponent,
    RequestStatusPresenter: '' as AnyComponent,
    ApprovalButtons: '' as AnyComponent,
    PendingApprovals: '' as AnyComponent,
    CreatePolicy: '' as AnyComponent,
    EditPolicy: '' as AnyComponent,
    PolicyPresenter: '' as AnyComponent,
    PolicyList: '' as AnyComponent,
    CreateWorkflow: '' as AnyComponent,
    EditWorkflow: '' as AnyComponent,
    WorkflowPresenter: '' as AnyComponent,
    WorkflowList: '' as AnyComponent,
    WorkflowStepPresenter: '' as AnyComponent,
    PeopleCultureSettings: '' as AnyComponent,
    PeopleCultureSettingsNavigation: '' as AnyComponent,
    TimeOffPolicyList: '' as AnyComponent,
    EditTimeOffPolicy: '' as AnyComponent
  },
  category: {
    HR: '' as Ref<ActionCategory>
  },
  action: {
    CreateDepartment: '' as Ref<Action<Doc, any>>,
    EditDepartment: '' as Ref<Action>,
    ArchiveDepartment: '' as Ref<Action>,
    EditRequest: '' as Ref<Action>,
    EditRequestType: '' as Ref<Action>,
    DeleteRequest: '' as Ref<Action>,
    CreateOffice: '' as Ref<Action<Doc, any>>,
    EditOffice: '' as Ref<Action>,
    CreatePolicy: '' as Ref<Action<Doc, any>>,
    EditPolicy: '' as Ref<Action>,
    CreateWorkflow: '' as Ref<Action<Doc, any>>,
    EditWorkflow: '' as Ref<Action>
  },
  actionImpl: {
    EditRequestType: '' as ViewAction
  },
  ids: {
    HRNotificationGroup: '' as Ref<NotificationGroup>
  }
})
