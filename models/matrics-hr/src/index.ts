//
// Copyright © 2022, 2023 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { type Contact, type Employee } from '@hcengineering/contact'
import {
  AccountRole,
  DOMAIN_MODEL,
  IndexKind,
  type Arr,
  type Class,
  type Domain,
  type Markup,
  type Ref,
  type Type
} from '@hcengineering/core'
import {
  matricsHrId,
  type Department,
  type Office,
  type Policy,
  type PublicHoliday,
  type Request,
  type RequestType,
  type Staff,
  type TimeOffBalance,
  type TimeOffPolicy,
  type TimeOffTransaction,
  type TzDate,
  type Workflow,
  type WorkflowStep,
  type RequestStatus,
  type WorkflowStatus,
  type EmploymentType,
  type TimeOffAccrualFrequency,
  type TimeOffAccrualMethod,
  type TimeOffTransactionKind,
  type EmployeeDocument,
  type DocumentType,
  type DocumentStatus,
  type CompensationRecord,
  type CompensationType,
  type PayFrequency,
  type PerformanceReview,
  type ReviewType,
  type ReviewStatus,
  type EmployeeBenefit,
  type BenefitType,
  type BenefitStatus,
  type EmployeeLifecycleTask,
  type TaskCategory,
  type TaskStatus
} from '@hcengineering/matrics-hr'
import {
  ArrOf,
  Collection,
  Hidden,
  Index,
  Mixin,
  Model,
  Prop,
  TypeBoolean,
  TypeIntlString,
  TypeMarkup,
  TypeNumber,
  TypeRef,
  TypeString,
  TypeTimestamp,
  UX,
  type Builder
} from '@hcengineering/model'
import attachment from '@hcengineering/model-attachment'
import calendar from '@hcengineering/model-calendar'
import chunter from '@hcengineering/model-chunter'
import contact, { TEmployee } from '@hcengineering/model-contact'
import core, { TAttachedDoc, TDoc, TType } from '@hcengineering/model-core'
import setting from '@hcengineering/setting'
import view, { classPresenter, createAction } from '@hcengineering/model-view'
import workbench from '@hcengineering/model-workbench'
import notification from '@hcengineering/notification'
import { type Asset, type IntlString } from '@hcengineering/platform'
import { PaletteColorIndexes } from '@hcengineering/ui/src/colors'
import hr from './plugin'

export { matricsHrId } from '@hcengineering/matrics-hr'
export { hrOperation } from './migration'
export { default } from './plugin'

export const DOMAIN_MATRICS_HR = 'matrics-hr' as Domain

@Model(hr.class.Office, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.Office, hr.icon.Office)
export class TOffice extends TDoc implements Office {
  @Prop(TypeString(), core.string.Name)
  @Index(IndexKind.FullText)
    name!: string

  @Prop(TypeString(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: string

  @Prop(TypeString(), hr.string.Address)
    address!: string

  @Prop(TypeString(), hr.string.City)
    city!: string

  @Prop(TypeString(), hr.string.Country)
    country!: string

  @Prop(TypeString(), hr.string.Timezone)
    timezone!: string

  avatar?: string | null

  @Prop(Collection(hr.class.Department), hr.string.Departments)
    departments?: number

  @Prop(ArrOf(TypeRef(contact.mixin.Employee)), hr.string.Managers)
    managers!: Arr<Ref<Employee>>
}

@Model(hr.class.Department, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.Department, hr.icon.Department)
export class TDepartment extends TDoc implements Department {
  @Prop(TypeRef(hr.class.Department), hr.string.ParentDepartmentLabel)
  @Index(IndexKind.Indexed)
    parent?: Ref<Department>

  @Prop(TypeRef(hr.class.Office), hr.string.Office)
  @Index(IndexKind.Indexed)
    office?: Ref<Office>

  @Prop(TypeString(), core.string.Name)
  @Index(IndexKind.FullText)
    name!: string

  @Prop(TypeString(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: string

  @Prop(Collection(contact.class.Channel), contact.string.ContactInfo)
    channels?: number

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number

  avatar?: string | null

  @Prop(TypeRef(contact.mixin.Employee), hr.string.TeamLead)
    teamLead!: Ref<Employee> | null

  @Prop(ArrOf(TypeRef(contact.mixin.Employee)), contact.string.Members)
  @Index(IndexKind.Indexed)
    members!: Arr<Ref<Employee>>

  @Prop(ArrOf(TypeRef(contact.class.Contact)), hr.string.Subscribers)
    subscribers?: Arr<Ref<Contact>>

  @Prop(ArrOf(TypeRef(contact.mixin.Employee)), hr.string.Managers)
    managers!: Arr<Ref<Employee>>
}

@Mixin(hr.mixin.Staff, contact.mixin.Employee)
@UX(hr.string.Staff, hr.icon.HR, 'STFF', 'name')
export class TStaff extends TEmployee implements Staff {
  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department!: Ref<Department>

  @Prop(TypeString(), hr.string.JobTitle)
    jobTitle?: string | null

  @Prop(TypeString(), hr.string.EmploymentType)
    employmentType?: EmploymentType | null

  @Prop(TypeRef(contact.mixin.Employee), hr.string.Manager)
    manager?: Ref<Employee> | null

  @Prop(TypeString(), hr.string.Location)
    location?: string | null

  @Prop(TypeNumber(), hr.string.WorkHoursPerWeek)
    workHoursPerWeek?: number | null

  @Prop(TypeTimestamp(), hr.string.HireDate)
    hireDate?: number | null

  @Prop(TypeTimestamp(), hr.string.TerminationDate)
    terminationDate?: number | null

  @Prop(TypeTimestamp(), hr.string.ProbationEndDate)
    probationEndDate?: number | null

  @Prop(TypeString(), hr.string.EmergencyContact)
    emergencyContact?: string | null

  @Prop(TypeString(), hr.string.EmergencyPhone)
    emergencyPhone?: string | null

  @Prop(TypeString(), hr.string.EmergencyEmail)
    emergencyEmail?: string | null

  @Prop(TypeString(), hr.string.EmergencyRelationship)
    emergencyRelationship?: string | null

  @Prop(TypeNumber(), core.string.String)
    ftePercent?: number | null

  @Prop(TypeString(), core.string.String)
    costCenter?: string | null
}

@Model(hr.class.RequestType, core.class.Doc, DOMAIN_MODEL)
@UX(hr.string.RequestType)
export class TRequestType extends TDoc implements RequestType {
  @Prop(TypeIntlString(), core.string.Name)
    label!: IntlString

  icon!: Asset
  value!: number
  color!: number
}

@Model(hr.class.TzDate, core.class.Type)
@UX(core.string.Timestamp)
export class TTzDate extends TType {
  year!: number
  month!: number
  day!: number
  offset!: number
}

/**
 * @public
 */
export function TypeTzDate (): Type<TzDate> {
  return { _class: hr.class.TzDate, label: core.string.Timestamp }
}

@Model(hr.class.Request, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.Request, hr.icon.PTO)
export class TRequest extends TAttachedDoc implements Request {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department!: Ref<Department>

  @Prop(TypeRef(hr.class.RequestType), hr.string.RequestType)
  @Hidden()
    type!: Ref<RequestType>

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(TypeMarkup(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: Markup

  @Prop(TypeTzDate(), calendar.string.Date)
    tzDate!: TzDate

  @Prop(TypeTzDate(), calendar.string.DueTo)
    tzDueDate!: TzDate

  @Prop(TypeString(), hr.string.Status)
  @Index(IndexKind.Indexed)
    status!: RequestStatus

  submittedDate?: number

  @Prop(TypeRef(contact.mixin.Employee), hr.string.Approver)
    approver?: Ref<Employee>

  @Prop(TypeRef(contact.mixin.Employee), hr.string.ApprovedBy)
    approvedBy?: Ref<Employee>

  approvalDate?: number

  @Prop(TypeMarkup(), hr.string.ApprovalComment)
    approvalComment?: Markup

  @Prop(TypeNumber(), hr.string.RequestedDays)
    requestedDays!: number

  @Prop(TypeString(), hr.string.Payload)
    payload?: string

  @Prop(TypeBoolean(), hr.string.HalfDayStart)
    halfDayStart?: boolean

  @Prop(TypeBoolean(), hr.string.HalfDayEnd)
    halfDayEnd?: boolean
}

@Model(hr.class.PublicHoliday, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.PublicHoliday)
export class TPublicHoliday extends TDoc implements PublicHoliday {
  title!: string
  description!: string
  date!: TzDate
  @Index(IndexKind.Indexed)
    department!: Ref<Department>
}

@Model(hr.class.Policy, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.Policy, hr.icon.Policy)
export class TPolicy extends TDoc implements Policy {
  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeMarkup(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: Markup

  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department?: Ref<Department>

  @Prop(TypeString(), hr.string.Active)
    active!: boolean

  @Prop(TypeString(), hr.string.TriggerType)
    triggerType?: string

  @Prop(TypeString(), hr.string.TriggerConfig)
    triggerConfig?: string

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

@Model(hr.class.TimeOffPolicy, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.TimeOffPolicy, hr.icon.PTO)
export class TTimeOffPolicy extends TDoc implements TimeOffPolicy {
  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeMarkup(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: Markup

  @Prop(TypeRef(hr.class.RequestType), hr.string.RequestType)
  @Index(IndexKind.Indexed)
    requestType!: Ref<RequestType>

  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department?: Ref<Department>

  @Prop(TypeBoolean(), hr.string.Active)
    active!: boolean

  @Prop(TypeString(), hr.string.AccrualMethod)
    accrualMethod!: TimeOffAccrualMethod

  @Prop(TypeString(), hr.string.AccrualFrequency)
    accrualFrequency?: TimeOffAccrualFrequency

  @Prop(TypeNumber(), hr.string.AccrualRate)
    accrualRate?: number

  @Prop(TypeNumber(), hr.string.AccrualCap)
    accrualCap?: number

  @Prop(TypeNumber(), hr.string.AccrualStartMonth)
    accrualStartMonth?: number

  @Prop(TypeNumber(), hr.string.AccrualStartDay)
    accrualStartDay?: number

  @Prop(TypeNumber(), hr.string.CarryoverLimit)
    carryoverLimit?: number

  @Prop(TypeNumber(), hr.string.CarryoverExpiryDays)
    carryoverExpiryDays?: number

  @Prop(TypeBoolean(), hr.string.AllowNegativeBalance)
    allowNegativeBalance!: boolean

  @Prop(TypeBoolean(), hr.string.AllowHalfDays)
    allowHalfDays!: boolean

  @Prop(TypeNumber(), hr.string.WaitingPeriodDays)
    waitingPeriodDays?: number

  @Prop(TypeRef(contact.mixin.Employee), hr.string.DefaultApprover)
    defaultApprover?: Ref<Employee>

  @Prop(TypeBoolean(), hr.string.AutoApprove)
    autoApprove!: boolean

  @Prop(TypeNumber(), hr.string.AutoApproveMaxDays)
    autoApproveMaxDays?: number
}

@Model(hr.class.TimeOffBalance, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.TimeOffBalance, hr.icon.PTO)
export class TTimeOffBalance extends TDoc implements TimeOffBalance {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
    staff!: Ref<Staff>

  @Prop(TypeRef(hr.class.TimeOffPolicy), hr.string.TimeOffPolicy)
  @Index(IndexKind.Indexed)
    policy!: Ref<TimeOffPolicy>

  @Prop(TypeNumber(), hr.string.CurrentBalance)
    balance!: number

  @Prop(TypeNumber(), hr.string.PendingBalance)
    pending!: number

  @Prop(TypeNumber(), hr.string.Carryover)
    carryover!: number

  @Prop(TypeTimestamp(), hr.string.LastAccruedAt)
    lastAccruedAt?: number

  @Prop(TypeTimestamp(), hr.string.EffectiveDate)
    effectiveDate?: number
}

@Model(hr.class.TimeOffTransaction, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.TimeOffTransaction, hr.icon.PTO)
export class TTimeOffTransaction extends TDoc {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
    staff!: Ref<Staff>

  @Prop(TypeRef(hr.class.TimeOffPolicy), hr.string.TimeOffPolicy)
  @Index(IndexKind.Indexed)
    policy!: Ref<TimeOffPolicy>

  @Prop(TypeRef(hr.class.Request), hr.string.Request)
  @Index(IndexKind.Indexed)
    sourceRequest?: Ref<Request>

  @Prop(TypeString(), hr.string.TransactionKind)
    kind!: TimeOffTransactionKind

  @Prop(TypeNumber(), hr.string.Amount)
    amount!: number

  @Prop(TypeMarkup(), core.string.Description)
    note?: Markup

  @Prop(TypeTimestamp(), hr.string.EffectiveDate)
    effectiveDate!: number

  @Prop(TypeRef(contact.mixin.Employee), hr.string.RecordedBy)
    recordedBy?: Ref<Employee>
}

@Model(hr.class.AttendanceRecord, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.Attendance, hr.icon.Structure)
export class TAttendanceRecord extends TDoc {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
    staff!: Ref<Staff>

  @Prop(TypeRef(hr.class.Office), hr.string.Office)
  @Index(IndexKind.Indexed)
    office?: Ref<Office>

  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department?: Ref<Department>

  @Prop(TypeTimestamp(), hr.string.ClockIn)
  @Index(IndexKind.Indexed)
    clockIn!: number

  @Prop(TypeTimestamp(), hr.string.ClockOut)
    clockOut?: number

  @Prop(TypeString(), core.string.String)
    source!: 'manual' | 'device' | 'api'

  @Prop(TypeMarkup(), core.string.Description)
    notes?: Markup
}

@Model(hr.class.AttendanceSchedule, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.AttendanceSchedule, hr.icon.Structure)
export class TAttendanceSchedule extends TDoc {
  @Prop(TypeRef(hr.class.Office), hr.string.Office)
  @Index(IndexKind.Indexed)
    office?: Ref<Office>

  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department?: Ref<Department>

  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    label!: string

  @Prop(TypeString(), hr.string.Timezone)
    timezone?: string

  @Prop(TypeNumber(), hr.string.WorkdayStart)
    startMinutes!: number

  @Prop(TypeNumber(), hr.string.WorkdayEnd)
    endMinutes!: number

  @Prop(TypeNumber(), hr.string.LunchStart)
    lunchStartMinutes?: number

  @Prop(TypeNumber(), hr.string.LunchEnd)
    lunchEndMinutes?: number

  @Prop(ArrOf(TypeNumber()), hr.string.WorkingDays)
    workingDays?: Arr<number>
}

@Model(hr.class.AssignedAsset, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.Assets, hr.icon.Structure)
export class TAssignedAsset extends TAttachedDoc {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeString(), hr.string.AssetName)
    assetName!: string

  @Prop(TypeString(), hr.string.AssetTag)
    assetTag?: string

  @Prop(TypeString(), hr.string.SerialNumber)
    serialNumber?: string

  @Prop(TypeTimestamp(), hr.string.AssignedDate)
    assignedDate?: number

  @Prop(TypeTimestamp(), hr.string.ReturnDate)
    returnDate?: number

  @Prop(TypeString(), hr.string.Condition)
    condition?: string

  @Prop(TypeMarkup(), hr.string.Notes)
    notes?: Markup

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

@Model(hr.class.Workflow, core.class.Doc, DOMAIN_MATRICS_HR)
@UX(hr.string.Workflow, hr.icon.Workflow)
export class TWorkflow extends TDoc implements Workflow {
  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeMarkup(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: Markup

  @Prop(TypeString(), hr.string.Status)
  @Index(IndexKind.Indexed)
    status!: WorkflowStatus

  @Prop(TypeRef(hr.class.Department), hr.string.Department)
  @Index(IndexKind.Indexed)
    department?: Ref<Department>

  @Prop(TypeRef(contact.mixin.Employee), hr.string.Assignee)
  @Index(IndexKind.Indexed)
    assignee?: Ref<Employee>

  dueDate?: number

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number

  @Prop(Collection(hr.class.WorkflowStep), hr.string.Steps)
    steps?: number
}

@Model(hr.class.WorkflowStep, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.WorkflowStep, hr.icon.Workflow)
export class TWorkflowStep extends TAttachedDoc implements WorkflowStep {
  @Prop(TypeRef(hr.class.Workflow), hr.string.Workflow)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Workflow>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Workflow>>

  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeMarkup(), core.string.Description)
  @Index(IndexKind.FullText)
    description!: Markup

  @Prop(TypeString(), hr.string.Completed)
    completed!: boolean

  @Prop(TypeRef(contact.mixin.Employee), hr.string.CompletedBy)
    completedBy?: Ref<Employee>

  completedDate?: number

  dueDate?: number

  @Prop(TypeRef(contact.mixin.Employee), hr.string.Assignee)
  @Index(IndexKind.Indexed)
    assignee?: Ref<Employee>

  @Prop(TypeString(), hr.string.Order)
    order!: number
}

@Model(hr.class.EmployeeDocument, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.EmployeeDocument, hr.icon.Document)
export class TEmployeeDocument extends TAttachedDoc implements EmployeeDocument {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeMarkup(), core.string.Description)
    description?: Markup

  @Prop(TypeString(), hr.string.DocumentType)
    documentType!: DocumentType

  @Prop(TypeString(), hr.string.DocumentStatus)
    status!: DocumentStatus

  @Prop(TypeTimestamp(), hr.string.EffectiveDate)
    effectiveDate?: number

  @Prop(TypeTimestamp(), hr.string.ExpiryDate)
    expiryDate?: number

  @Prop(TypeTimestamp(), hr.string.SignedDate)
    signedDate?: number

  @Prop(TypeString(), core.string.String)
    fileUrl?: string

  @Prop(TypeRef(contact.mixin.Employee), hr.string.UploadedBy)
    uploadedBy!: Ref<Employee>

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

@Model(hr.class.CompensationRecord, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.CompensationRecord, hr.icon.Compensation)
export class TCompensationRecord extends TAttachedDoc implements CompensationRecord {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeString(), hr.string.CompensationType)
    compensationType!: CompensationType

  @Prop(TypeNumber(), hr.string.Amount)
    amount!: number

  @Prop(TypeString(), hr.string.Currency)
    currency!: string

  @Prop(TypeString(), hr.string.PayFrequency)
    payFrequency?: PayFrequency

  @Prop(TypeTimestamp(), hr.string.EffectiveDate)
    effectiveDate!: number

  @Prop(TypeTimestamp(), hr.string.EndDate)
    endDate?: number

  @Prop(TypeMarkup(), hr.string.Reason)
    reason?: Markup

  @Prop(TypeRef(contact.mixin.Employee), hr.string.ApprovedBy)
    approvedBy?: Ref<Employee>

  @Prop(TypeMarkup(), hr.string.Notes)
    notes?: Markup

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

@Model(hr.class.PerformanceReview, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.PerformanceReview, hr.icon.Performance)
export class TPerformanceReview extends TAttachedDoc implements PerformanceReview {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeString(), hr.string.ReviewType)
    reviewType!: ReviewType

  @Prop(TypeString(), hr.string.ReviewStatus)
    status!: ReviewStatus

  @Prop(TypeTimestamp(), hr.string.ReviewPeriodStart)
    reviewPeriodStart!: number

  @Prop(TypeTimestamp(), hr.string.ReviewPeriodEnd)
    reviewPeriodEnd!: number

  @Prop(TypeTimestamp(), hr.string.DueDate)
    dueDate?: number

  @Prop(TypeTimestamp(), hr.string.CompletedDate)
    completedDate?: number

  @Prop(TypeRef(contact.mixin.Employee), hr.string.Reviewer)
    reviewer!: Ref<Employee>

  @Prop(TypeNumber(), hr.string.OverallRating)
    overallRating?: number

  @Prop(TypeMarkup(), hr.string.Strengths)
    strengths?: Markup

  @Prop(TypeMarkup(), hr.string.AreasForImprovement)
    areasForImprovement?: Markup

  @Prop(TypeMarkup(), hr.string.Goals)
    goals?: Markup

  @Prop(TypeMarkup(), hr.string.Feedback)
    feedback?: Markup

  @Prop(TypeMarkup(), hr.string.EmployeeComments)
    employeeComments?: Markup

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

@Model(hr.class.EmployeeBenefit, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.EmployeeBenefit, hr.icon.Benefits)
export class TEmployeeBenefit extends TAttachedDoc implements EmployeeBenefit {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeString(), hr.string.BenefitName)
  @Index(IndexKind.FullText)
    benefitName!: string

  @Prop(TypeString(), hr.string.BenefitType)
    benefitType!: BenefitType

  @Prop(TypeString(), hr.string.BenefitStatus)
    status!: BenefitStatus

  @Prop(TypeString(), hr.string.Provider)
    provider?: string

  @Prop(TypeTimestamp(), hr.string.EnrollmentDate)
    enrollmentDate?: number

  @Prop(TypeTimestamp(), hr.string.EffectiveDate)
    effectiveDate?: number

  @Prop(TypeTimestamp(), hr.string.TerminationDate)
    terminationDate?: number

  @Prop(TypeNumber(), hr.string.EmployeeContribution)
    employeeContribution?: number

  @Prop(TypeNumber(), hr.string.EmployerContribution)
    employerContribution?: number

  @Prop(TypeString(), hr.string.Currency)
    currency?: string

  @Prop(TypeMarkup(), hr.string.Notes)
    notes?: Markup

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

@Model(hr.class.EmployeeLifecycleTask, core.class.AttachedDoc, DOMAIN_MATRICS_HR)
@UX(hr.string.LifecycleTask, hr.icon.Task)
export class TEmployeeLifecycleTask extends TAttachedDoc implements EmployeeLifecycleTask {
  @Prop(TypeRef(hr.mixin.Staff), contact.string.Employee)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<Staff>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<Staff>>

  @Prop(TypeString(), hr.string.TaskCategory)
    category!: TaskCategory

  @Prop(TypeString(), hr.string.Title)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeMarkup(), core.string.Description)
    description?: Markup

  @Prop(TypeString(), hr.string.TaskStatus)
    status!: TaskStatus

  @Prop(TypeRef(contact.mixin.Employee), hr.string.Assignee)
    assignee?: Ref<Employee>

  @Prop(TypeTimestamp(), hr.string.DueDate)
    dueDate?: number

  @Prop(TypeTimestamp(), hr.string.CompletedDate)
    completedDate?: number

  @Prop(TypeRef(contact.mixin.Employee), hr.string.CompletedBy)
    completedBy?: Ref<Employee>

  @Prop(TypeNumber(), hr.string.Order)
    order!: number

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments, { shortLabel: attachment.string.Files })
    attachments?: number

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
}

export function createModel (builder: Builder): void {
  builder.createModel(
    TOffice,
    TDepartment,
    TRequest,
    TRequestType,
    TPublicHoliday,
    TStaff,
    TTzDate,
    TPolicy,
    TTimeOffPolicy,
    TTimeOffBalance,
    TTimeOffTransaction,
    TAttendanceRecord,
    TAttendanceSchedule,
    TAssignedAsset,
    TWorkflow,
    TWorkflowStep,
    TEmployeeDocument,
    TCompensationRecord,
    TPerformanceReview,
    TEmployeeBenefit,
    TEmployeeLifecycleTask
  )

  builder.createDoc(
    view.class.ActionCategory,
    core.space.Model,
    { label: hr.string.HRApplication, visible: true },
    hr.category.HR
  )

  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: hr.string.HRApplication,
      icon: hr.icon.HR,
      accessLevel: AccountRole.User,
      alias: matricsHrId,
      hidden: false,
      component: hr.component.Schedule
    },
    hr.app.HR
  )

  builder.createDoc(
    setting.class.WorkspaceSettingCategory,
    core.space.Model,
    {
      name: 'peopleCulture',
      label: hr.string.ConfigLabel,
      icon: hr.icon.HR,
      component: hr.component.PeopleCultureSettings,
      extraComponents: {
        navigation: hr.component.PeopleCultureSettingsNavigation
      },
      expandable: true,
      order: 2100,
      role: AccountRole.Maintainer
    },
    hr.ids.PeopleCultureSettings
  )

  builder.mixin(hr.class.Department, core.class.Class, view.mixin.AttributeEditor, {
    inlineEditor: hr.component.DepartmentEditor
  })

  builder.mixin(hr.class.Department, core.class.Class, view.mixin.AttributePresenter, {
    presenter: hr.component.DepartmentRefPresenter
  })

  builder.mixin(hr.class.Department, core.class.Class, view.mixin.ObjectEditor, {
    editor: hr.component.EditDepartment
  })

  builder.mixin(hr.class.Department, core.class.Class, view.mixin.ArrayEditor, {
    inlineEditor: view.component.ArrayEditor
  })

  builder.mixin(hr.class.Request, core.class.Class, view.mixin.ObjectEditor, {
    editor: hr.component.EditRequest
  })

  classPresenter(builder, hr.class.TzDate, hr.component.TzDatePresenter, hr.component.TzDateEditor)

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.Vacation,
      icon: hr.icon.Vacation,
      color: 2,
      value: -1
    },
    hr.ids.Vacation
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.Sick,
      icon: hr.icon.Sick,
      color: PaletteColorIndexes.Turquoise,
      value: -1
    },
    hr.ids.Sick
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.PTO,
      icon: hr.icon.PTO,
      color: PaletteColorIndexes.Firework,
      value: -1
    },
    hr.ids.PTO
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.PTO2,
      icon: hr.icon.PTO2,
      color: PaletteColorIndexes.Watermelon,
      value: -0.5
    },
    hr.ids.PTO2
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.Overtime,
      icon: hr.icon.Overtime,
      color: PaletteColorIndexes.Waterway,
      value: 1
    },
    hr.ids.Overtime
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.Overtime2,
      icon: hr.icon.Overtime2,
      color: PaletteColorIndexes.Cerulean,
      value: 0.5
    },
    hr.ids.Overtime2
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: hr.string.Remote,
      icon: hr.icon.Remote,
      color: PaletteColorIndexes.Coin,
      value: 0
    },
    hr.ids.Remote
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: (hr.string as any).ProfileUpdate,
      icon: hr.icon.Structure,
      color: PaletteColorIndexes.Cerulean,
      value: 0
    },
    (hr.ids as any).ProfileUpdate
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: (hr.string as any).EmergencyInfoUpdate,
      icon: hr.icon.Structure,
      color: PaletteColorIndexes.Waterway,
      value: 0
    },
    (hr.ids as any).EmergencyInfoUpdate
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: (hr.string as any).AssetAssignment,
      icon: hr.icon.Structure,
      color: PaletteColorIndexes.Firework,
      value: 0
    },
    (hr.ids as any).AssetAssignment
  )

  builder.createDoc(
    hr.class.RequestType,
    core.space.Model,
    {
      label: (hr.string as any).AssetReturn,
      icon: hr.icon.Structure,
      color: PaletteColorIndexes.Watermelon,
      value: 0
    },
    (hr.ids as any).AssetReturn
  )

  createAction(
    builder,
    {
      action: view.actionImpl.ShowPanel,
      actionProps: { element: 'content' },
      label: view.string.Open,
      icon: view.icon.Open,
      keyBinding: ['e'],
      input: 'any',
      category: hr.category.HR,
      target: hr.class.Department,
      context: { mode: 'context', application: hr.app.HR, group: 'create' }
    },
    hr.action.EditDepartment
  )

  createAction(
    builder,
    {
      action: view.actionImpl.ShowPopup,
      actionProps: {
        component: hr.component.CreateDepartment,
        element: 'top',
        fillProps: {
          _id: 'parent'
        }
      },
      label: hr.string.CreateDepartment,
      icon: hr.icon.Department,
      input: 'focus',
      category: hr.category.HR,
      target: hr.class.Department,
      context: { mode: 'context', application: hr.app.HR, group: 'create' }
    },
    hr.action.CreateDepartment
  )

  createAction(
    builder,
    {
      action: view.actionImpl.Delete,
      label: view.string.Delete,
      icon: view.icon.Delete,
      input: 'any',
      category: hr.category.HR,
      keyBinding: ['Meta + Backspace'],
      query: {
        'members.length': 0,
        _id: { $nin: [hr.ids.Head] }
      },
      target: hr.class.Department,
      context: { mode: ['context', 'browser'], group: 'tools' },
      override: [view.action.Delete]
    },
    hr.action.ArchiveDepartment
  )

  createAction(
    builder,
    {
      action: view.actionImpl.ShowPanel,
      actionProps: { element: 'content' },
      label: view.string.Open,
      icon: view.icon.Open,
      keyBinding: ['e'],
      input: 'any',
      category: hr.category.HR,
      target: hr.class.Request,
      context: { mode: 'context', application: hr.app.HR, group: 'create' },
      override: [view.action.Open]
    },
    hr.action.EditRequest
  )

  createAction(
    builder,
    {
      action: hr.actionImpl.EditRequestType,
      actionProps: {},
      label: hr.string.EditRequestType,
      icon: view.icon.Edit,
      input: 'any',
      category: hr.category.HR,
      target: hr.class.Request,
      context: { mode: 'context', application: hr.app.HR, group: 'edit' }
    },
    hr.action.EditRequestType
  )

  // Office browse action
  builder.mixin(hr.class.Office, core.class.Class, view.mixin.ClassFilters, {
    filters: []
  })

  builder.mixin(hr.class.Office, core.class.Class, view.mixin.IgnoreActions, {
    actions: [view.action.Delete]
  })

  // Office actions
  createAction(
    builder,
    {
      action: view.actionImpl.ShowPopup,
      actionProps: {
        component: hr.component.CreateOffice,
        element: 'top'
      },
      label: hr.string.CreateOffice,
      icon: hr.icon.Office,
      input: 'none',
      category: hr.category.HR,
      target: core.class.Doc,
      context: { mode: 'browser', application: hr.app.HR, group: 'create' }
    },
    hr.action.CreateOffice
  )

  createAction(
    builder,
    {
      action: view.actionImpl.ShowPanel,
      actionProps: { element: 'content' },
      label: view.string.Open,
      icon: view.icon.Open,
      input: 'any',
      category: hr.category.HR,
      target: hr.class.Office,
      context: { mode: 'context', application: hr.app.HR, group: 'edit' }
    },
    hr.action.EditOffice
  )

  // Policy browse setup
  builder.mixin(hr.class.Policy, core.class.Class, view.mixin.ClassFilters, {
    filters: ['active', 'department']
  })

  builder.mixin(hr.class.Policy, core.class.Class, view.mixin.IgnoreActions, {
    actions: [view.action.Delete]
  })

  // Policy actions
  createAction(
    builder,
    {
      action: view.actionImpl.ShowPopup,
      actionProps: {
        component: hr.component.CreatePolicy,
        element: 'top'
      },
      label: hr.string.CreatePolicy,
      icon: hr.icon.Policy,
      input: 'none',
      category: hr.category.HR,
      target: core.class.Doc,
      context: { mode: 'browser', application: hr.app.HR, group: 'create' }
    },
    hr.action.CreatePolicy
  )

  createAction(
    builder,
    {
      action: view.actionImpl.ShowPanel,
      actionProps: { element: 'content' },
      label: view.string.Open,
      icon: view.icon.Open,
      input: 'any',
      category: hr.category.HR,
      target: hr.class.Policy,
      context: { mode: 'context', application: hr.app.HR, group: 'edit' }
    },
    hr.action.EditPolicy
  )

  // Workflow browse setup
  builder.mixin(hr.class.Workflow, core.class.Class, view.mixin.ClassFilters, {
    filters: ['status', 'assignee', 'department']
  })

  builder.mixin(hr.class.Workflow, core.class.Class, view.mixin.IgnoreActions, {
    actions: [view.action.Delete]
  })

  // Workflow actions
  createAction(
    builder,
    {
      action: view.actionImpl.ShowPopup,
      actionProps: {
        component: hr.component.CreateWorkflow,
        element: 'top'
      },
      label: hr.string.CreateWorkflow,
      icon: hr.icon.Workflow,
      input: 'none',
      category: hr.category.HR,
      target: core.class.Doc,
      context: { mode: 'browser', application: hr.app.HR, group: 'create' }
    },
    hr.action.CreateWorkflow
  )

  createAction(
    builder,
    {
      action: view.actionImpl.ShowPanel,
      actionProps: { element: 'content' },
      label: view.string.Open,
      icon: view.icon.Open,
      input: 'any',
      category: hr.category.HR,
      target: hr.class.Workflow,
      context: { mode: 'context', application: hr.app.HR, group: 'edit' }
    },
    hr.action.EditWorkflow
  )

  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: hr.mixin.Staff,
      descriptor: view.viewlet.Table,
      config: [
        '',
        {
          key: '$lookup.channels',
          label: contact.string.ContactInfo,
          sortingKey: ['$lookup.channels.lastMessage', 'channels']
        },
        'modifiedOn'
      ]
    },
    hr.viewlet.TableMember
  )

  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: hr.mixin.Staff,
      descriptor: view.viewlet.Table,
      config: ['']
    },
    hr.viewlet.StaffStats
  )

  // Office viewlet
  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: hr.class.Office,
      descriptor: view.viewlet.Table,
      config: [
        '',
        'city',
        'country',
        'timezone',
        'modifiedOn'
      ]
    },
    hr.viewlet.OfficeTable
  )

  // Policy viewlet
  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: hr.class.Policy,
      descriptor: view.viewlet.Table,
      config: [
        '',
        'active',
        'department',
        'modifiedOn'
      ]
    },
    hr.viewlet.PolicyTable
  )

  // Workflow viewlet
  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: hr.class.Workflow,
      descriptor: view.viewlet.Table,
      config: [
        '',
        'status',
        'assignee',
        'department',
        'dueDate',
        'modifiedOn'
      ]
    },
    hr.viewlet.WorkflowTable
  )

  createAction(builder, {
    action: view.actionImpl.ValueSelector,
    actionPopup: view.component.ValueSelector,
    actionProps: {
      attribute: 'department',
      _class: hr.class.Department,
      query: {},
      searchField: 'name',
      placeholder: hr.string.Department,
      castRequest: hr.mixin.Staff
    },
    label: hr.string.Department,
    icon: hr.icon.Department,
    input: 'none',
    category: hr.category.HR,
    target: hr.mixin.Staff,
    context: {
      mode: ['context'],
      application: hr.app.HR,
      group: 'associate'
    }
  })

  builder.mixin(hr.class.Request, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: hr.component.RequestPresenter
  })

  builder.createDoc(
    notification.class.NotificationGroup,
    core.space.Model,
    {
      label: hr.string.HRApplication,
      icon: hr.icon.HR
    },
    hr.ids.HRNotificationGroup
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      generated: false,
      label: hr.string.RequestCreated,
      group: hr.ids.HRNotificationGroup,
      // will be created with different trigger
      txClasses: [],
      objectClass: hr.class.Request,
      defaultEnabled: true,
      templates: {
        textTemplate: 'New request: {doc}',
        htmlTemplate: 'New request: {doc}',
        subjectTemplate: 'New request'
      }
    },
    hr.ids.CreateRequestNotification
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      generated: false,
      group: hr.ids.HRNotificationGroup,
      label: hr.string.RequestUpdated,
      // will be created with different trigger
      txClasses: [],
      objectClass: hr.class.Request,
      defaultEnabled: true,
      templates: {
        textTemplate: 'Request updated: {doc}',
        htmlTemplate: 'Request updated: {doc}',
        subjectTemplate: 'Request updated'
      }
    },
    hr.ids.UpdateRequestNotification
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      group: hr.ids.HRNotificationGroup,
      generated: false,
      label: hr.string.RequestRemoved,
      // will be created with different trigger
      txClasses: [],
      objectClass: hr.class.Request,
      defaultEnabled: true,
      templates: {
        textTemplate: 'Request removed: {doc}',
        htmlTemplate: 'Request removed: {doc}',
        subjectTemplate: 'Request removed'
      }
    },
    hr.ids.RemoveRequestNotification
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      generated: false,
      group: hr.ids.HRNotificationGroup,
      label: hr.string.PublicHoliday,
      // will be created with different trigger
      txClasses: [],
      objectClass: hr.class.PublicHoliday,
      defaultEnabled: true,
      templates: {
        textTemplate: 'New public holiday: {doc}',
        htmlTemplate: 'New public holiday: {doc}',
        subjectTemplate: 'New public holiday'
      }
    },
    hr.ids.CreatePublicHolidayNotification
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      generated: false,
      group: hr.ids.HRNotificationGroup,
      label: hr.string.RequestApproved,
      txClasses: [],
      objectClass: hr.class.Request,
      defaultEnabled: true,
      templates: {
        textTemplate: 'Your request has been approved: {doc}',
        htmlTemplate: 'Your request has been approved: {doc}',
        subjectTemplate: 'Time off request approved'
      }
    },
    hr.ids.RequestApprovedNotification
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      generated: false,
      group: hr.ids.HRNotificationGroup,
      label: hr.string.RequestRejected,
      txClasses: [],
      objectClass: hr.class.Request,
      defaultEnabled: true,
      templates: {
        textTemplate: 'Your request has been rejected: {doc}',
        htmlTemplate: 'Your request has been rejected: {doc}',
        subjectTemplate: 'Time off request rejected'
      }
    },
    hr.ids.RequestRejectedNotification
  )

  builder.createDoc(
    notification.class.NotificationType,
    core.space.Model,
    {
      hidden: false,
      generated: false,
      group: hr.ids.HRNotificationGroup,
      label: hr.string.RequestSubmitted,
      txClasses: [],
      objectClass: hr.class.Request,
      defaultEnabled: true,
      templates: {
        textTemplate: 'New request pending approval: {doc}',
        htmlTemplate: 'New request pending approval: {doc}',
        subjectTemplate: 'Time off request - approval needed'
      }
    },
    hr.ids.RequestSubmittedNotification
  )

  builder.createDoc(core.class.DomainIndexConfiguration, core.space.Model, {
    domain: DOMAIN_MATRICS_HR,
    disabled: [{ modifiedOn: 1 }, { modifiedBy: 1 }, { createdBy: 1 }, { attachedToClass: 1 }, { createdOn: -1 }]
  })

  builder.mixin(hr.class.Department, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: hr.component.DepartmentPresenter
  })

  // Office mixins
  builder.mixin(hr.class.Office, core.class.Class, view.mixin.AttributeEditor, {
    inlineEditor: hr.component.OfficeEditor
  })

  builder.mixin(hr.class.Office, core.class.Class, view.mixin.AttributePresenter, {
    presenter: hr.component.OfficePresenter
  })

  builder.mixin(hr.class.Office, core.class.Class, view.mixin.ObjectEditor, {
    editor: hr.component.EditOffice
  })

  builder.mixin(hr.class.Office, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: hr.component.OfficePresenter
  })

  // Policy mixins
  builder.mixin(hr.class.Policy, core.class.Class, view.mixin.ObjectEditor, {
    editor: hr.component.EditPolicy
  })

  builder.mixin(hr.class.Policy, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: hr.component.PolicyPresenter
  })

  // Workflow mixins
  builder.mixin(hr.class.Workflow, core.class.Class, view.mixin.ObjectEditor, {
    editor: hr.component.EditWorkflow
  })

  builder.mixin(hr.class.Workflow, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: hr.component.WorkflowPresenter
  })

  builder.mixin(hr.class.WorkflowStep, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: hr.component.WorkflowStepPresenter
  })
}
