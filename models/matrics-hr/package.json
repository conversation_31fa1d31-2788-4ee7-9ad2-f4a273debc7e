{"name": "@hcengineering/model-matrics-hr", "version": "0.6.0", "main": "lib/index.js", "svelte": "src/index.ts", "types": "types/index.d.ts", "author": "Anticrm Platform Contributors", "template": "@hcengineering/model-package", "license": "EPL-2.0", "scripts": {"build": "compile", "build:watch": "compile", "format": "format src", "_phase:build": "compile transpile src", "_phase:format": "format src", "_phase:validate": "compile validate", "_phase:test": "jest --passWithNoTests --silent --forceExit", "test": "jest --passWithNoTests --silent --forceExit"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-n": "^15.4.0", "eslint": "^8.54.0", "@typescript-eslint/parser": "^6.11.0", "eslint-config-standard-with-typescript": "^40.0.0", "prettier": "^3.1.0", "typescript": "^5.8.3", "@types/node": "^22.15.29", "jest": "^29.7.0", "@types/jest": "^29.5.5", "ts-jest": "^29.1.1"}, "dependencies": {"@hcengineering/contact": "^0.6.24", "@hcengineering/core": "^0.6.32", "@hcengineering/matrics-hr": "^0.6.19", "@hcengineering/matrics-hr-resources": "^0.6.0", "@hcengineering/model": "^0.6.11", "@hcengineering/model-attachment": "^0.6.0", "@hcengineering/model-calendar": "^0.6.0", "@hcengineering/model-chunter": "^0.6.0", "@hcengineering/model-contact": "^0.6.1", "@hcengineering/model-core": "^0.6.0", "@hcengineering/model-view": "^0.6.0", "@hcengineering/model-workbench": "^0.6.1", "@hcengineering/notification": "^0.6.23", "@hcengineering/platform": "^0.6.11", "@hcengineering/setting": "^0.6.17", "@hcengineering/ui": "^0.6.15", "@hcengineering/view": "^0.6.13"}}