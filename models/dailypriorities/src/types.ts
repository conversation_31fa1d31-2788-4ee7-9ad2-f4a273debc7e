//
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import {
  Collection as PropCollection,
  Index,
  Mixin,
  Model,
  Prop,
  TypeMarkup,
  TypeRef,
  TypeString,
  TypeDate,
  UX
} from '@hcengineering/model'
import core, { TAttachedDoc, TClass, TDoc, TSpace } from '@hcengineering/model-core'
import type {
  Channel,
  ChatMessage,
  ChatMessageViewlet,
  ChatSyncInfo,
  ChunterExtension,
  ChunterSpace,
  InlineButton,
  InlineButtonAction,
  ObjectChatPanel,
  ThreadMessage,
  TypingInfo,
  ChunterExtensionPoint,
  TimeEntry,
  RssFeed
} from '@hcengineering/dailypriorities'
import {
  type Class,
  type Doc,
  type Domain,
  DOMAIN_MODEL,
  DOMAIN_TRANSIENT,
  IndexKind,
  type Ref,
  type Timestamp,
  DateRangeMode
} from '@hcengineering/core'
import contact, { type ChannelProvider as SocialChannelProvider, type Person } from '@hcengineering/contact'
import activity, { type ActivityMessage } from '@hcengineering/activity'
import { TActivityMessage } from '@hcengineering/model-activity'
import attachment from '@hcengineering/model-attachment'
import type { IntlString, Resource } from '@hcengineering/platform'
import type { DocNotifyContext } from '@hcengineering/notification'

import dailyPriorities from './plugin'
import type { AnyComponent } from '@hcengineering/ui/src/types'

export const DOMAIN_DAILY_PRIORITIES = 'dailyPriorities' as Domain

@Model(dailyPriorities.class.ChunterSpace, core.class.Space)
export class TChunterSpace extends TSpace implements ChunterSpace {
  @Prop(PropCollection(activity.class.ActivityMessage), dailyPriorities.string.Messages)
    messages?: number
}

@Model(dailyPriorities.class.Channel, dailyPriorities.class.ChunterSpace)
@UX(dailyPriorities.string.Channel, dailyPriorities.icon.Hashtag, undefined, undefined, undefined, dailyPriorities.string.Channels)
export class TChannel extends TChunterSpace implements Channel {
  @Prop(TypeString(), dailyPriorities.string.Topic)
  @Index(IndexKind.FullText)
    topic?: string
}

@Model(dailyPriorities.class.ChatMessage, activity.class.ActivityMessage)
@UX(dailyPriorities.string.Message, dailyPriorities.icon.Thread, undefined, undefined, undefined, dailyPriorities.string.Threads)
export class TChatMessage extends TActivityMessage implements ChatMessage {
  @Prop(TypeMarkup(), dailyPriorities.string.Message)
  @Index(IndexKind.FullText)
    message!: string

  @Prop(PropCollection(attachment.class.Attachment), attachment.string.Attachments, {
    shortLabel: attachment.string.Files
  })
    attachments?: number

  @Prop(TypeRef(contact.class.ChannelProvider), core.string.Object)
    provider?: Ref<SocialChannelProvider>

  @Prop(PropCollection(dailyPriorities.class.InlineButton), core.string.Object)
    inlineButtons?: number

  @Prop(TypeDate(DateRangeMode.DATETIME), dailyPriorities.string.DueDate)
    dueDate?: Timestamp
}

@Model(dailyPriorities.class.ThreadMessage, dailyPriorities.class.ChatMessage)
@UX(dailyPriorities.string.ThreadMessage, dailyPriorities.icon.Thread, undefined, undefined, undefined, dailyPriorities.string.Threads)
export class TThreadMessage extends TChatMessage implements ThreadMessage {
  @Prop(TypeRef(activity.class.ActivityMessage), core.string.AttachedTo)
  @Index(IndexKind.Indexed)
  declare attachedTo: Ref<ActivityMessage>

  @Prop(TypeRef(activity.class.ActivityMessage), core.string.AttachedToClass)
  @Index(IndexKind.Indexed)
  declare attachedToClass: Ref<Class<ActivityMessage>>

  @Prop(TypeRef(core.class.Doc), core.string.Object)
  @Index(IndexKind.Indexed)
    objectId!: Ref<Doc>

  @Prop(TypeRef(core.class.Class), core.string.Class)
  @Index(IndexKind.Indexed)
    objectClass!: Ref<Class<Doc>>
}

@Model(dailyPriorities.class.ChatMessageViewlet, core.class.Doc, DOMAIN_MODEL)
export class TChatMessageViewlet extends TDoc implements ChatMessageViewlet {
  @Prop(TypeRef(core.class.Doc), core.string.Class)
  @Index(IndexKind.Indexed)
    objectClass!: Ref<Class<Doc>>

  @Prop(TypeRef(core.class.Doc), core.string.Class)
  @Index(IndexKind.Indexed)
    messageClass!: Ref<Class<Doc>>

  label?: IntlString
  onlyWithParent?: boolean
}

@Mixin(dailyPriorities.mixin.ObjectChatPanel, core.class.Class)
export class TObjectChatPanel extends TClass implements ObjectChatPanel {
  openByDefault?: boolean
  ignoreKeys!: string[]
}

@Model(dailyPriorities.class.ChatSyncInfo, core.class.Doc, DOMAIN_DAILY_PRIORITIES)
export class TChatSyncInfo extends TDoc implements ChatSyncInfo {
  user!: Ref<Person>
  hidden!: Ref<DocNotifyContext>[]
  timestamp!: Timestamp
}

@Model(dailyPriorities.class.InlineButton, core.class.Doc, DOMAIN_DAILY_PRIORITIES)
export class TInlineButton extends TAttachedDoc implements InlineButton {
  name!: string
  titleIntl?: IntlString
  title?: string
  action!: Resource<InlineButtonAction>
}

@Model(dailyPriorities.class.TypingInfo, core.class.Doc, DOMAIN_TRANSIENT)
export class TTypingInfo extends TDoc implements TypingInfo {
  objectId!: Ref<Doc>
  objectClass!: Ref<Class<Doc>>
  person!: Ref<Person>
  lastTyping!: Timestamp
}

@Model(dailyPriorities.class.ChunterExtension, core.class.Doc, DOMAIN_MODEL)
export class TChunterExtension extends TDoc implements ChunterExtension {
  ofClass!: Ref<Class<Doc>>
  point!: ChunterExtensionPoint
  component!: AnyComponent
}

@Model(dailyPriorities.class.TimeEntry, core.class.Doc, DOMAIN_DAILY_PRIORITIES)
@UX(dailyPriorities.string.TimeTracker, dailyPriorities.icon.Clock)
export class TTimeEntry extends TDoc implements TimeEntry {
  @Prop(TypeString(), core.string.String)
  @Index(IndexKind.FullText)
    title!: string

  @Prop(TypeDate(DateRangeMode.DATETIME), core.string.Modified)
  @Index(IndexKind.Indexed)
    startTime!: Timestamp

  @Prop(TypeDate(DateRangeMode.DATETIME), core.string.Modified)
    endTime?: Timestamp

  @Prop(TypeRef(contact.class.Person), core.string.Modified)
  @Index(IndexKind.Indexed)
    user!: Ref<Person>

  duration?: number
}

@Model(dailyPriorities.class.RssFeed, core.class.Doc, DOMAIN_DAILY_PRIORITIES)
@UX(dailyPriorities.string.News, dailyPriorities.icon.News)
export class TRssFeed extends TDoc implements RssFeed {
  @Prop(TypeString(), core.string.String)
  @Index(IndexKind.FullText)
    url!: string

  @Prop(TypeString(), core.string.String)
  @Index(IndexKind.FullText)
    title?: string

  @Prop(TypeRef(contact.class.Person), core.string.Modified)
  @Index(IndexKind.Indexed)
    user!: Ref<Person>
}
