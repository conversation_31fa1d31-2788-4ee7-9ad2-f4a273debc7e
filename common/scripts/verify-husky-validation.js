#!/usr/bin/env node
const { execSync } = require('child_process');

function checkLatestCommit() {
  try {
    // Get latest commit hash and parent count
    const commitInfo = execSync(`git log -1 --pretty=format:"%H%x00%P"`, {
      encoding: 'utf-8'
    }).trim();

    if (!commitInfo) {
      console.log('No commits found, skipping Husky validation check');
      return true;
    }

    const [hash, parents] = commitInfo.split('\x00');
    const parentCount = parents.trim().split(/\s+/).length;

    if (parentCount > 1) {
      console.log(`Merge commit ${hash.substring(0, 7)} detected, skipping Husky validation check.`);
      return true;
    }

    // Get the full commit message separately
    const fullMessage = execSync(`git log -1 --pretty=format:"%B"`, {
      encoding: 'utf-8'
    }).trim();

    const subject = fullMessage.split('\n')[0];

    console.log(`Checking latest commit (${hash.substring(0, 7)}) for Husky validation marker...`);

    if (!fullMessage.includes('[husky-validated]')) {
      console.error('\n❌ ERROR: Latest commit is missing the Husky validation marker!');
      console.error(`Commit: ${hash.substring(0, 7)} — ${subject}`);
      console.error('\nThis commit appears to have bypassed Husky hooks (possibly using --no-verify).');
      console.error('Please ensure all commits pass through Husky hooks.');
      console.error('If you need to skip validation, you must run "rush validate" manually and update the commit message.');
      return false;
    }

    console.log('✓ Latest commit has Husky validation marker');
    return true;

  } catch (error) {
    console.error('Error checking latest commit:', error.message);
    try {
      execSync('node common/scripts/install-run-rush.js validate', {
        stdio: 'inherit'
      });
      return true;
    } catch (validateError) {
      console.error('Validation failed:', validateError.message);
      return false;
    }
  }
}

const isValid = checkLatestCommit();
process.exit(isValid ? 0 : 1);
