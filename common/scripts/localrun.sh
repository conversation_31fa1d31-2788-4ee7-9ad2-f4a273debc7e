#!/usr/bin/env bash

set -euo pipefail

# Usage:
#  ./common/scripts/localrun.sh           # start 3 panes/windows
#  ./common/scripts/localrun.sh --update  # run rush update && rush validate first

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"

maybe_update() {
  if [[ "${1-}" == "--update" ]]; then
    echo "Running: rush update && rush validate"
    pushd "$ROOT_DIR" >/dev/null
    rush update
    rush validate
    popd >/dev/null
  fi
}

run_with_tmux() {
  local session_name="huly-local"

  # Reset stale sessions to ensure the pane layout is always correct
  if command -v tmux >/dev/null 2>&1 && tmux has-session -t "$session_name" 2>/dev/null; then
    echo "Existing tmux session '$session_name' detected. Resetting it for fresh tiled layout."
    tmux kill-session -t "$session_name"
  fi

  echo "Starting tmux session '$session_name' with a tiled pane layout..."

  tmux new-session -d -s "$session_name" -n local -c "$ROOT_DIR/pods/account" \
    'npm run run-local'

  tmux split-window -h -t "$session_name:local" -c "$ROOT_DIR/pods/server" \
    'npm run run-local'

  tmux select-pane -t "$session_name:local.0"
  tmux split-window -v -c "$ROOT_DIR/pods/workspace" \
    'npm run run-local'

  tmux select-pane -t "$session_name:local.1"
  tmux split-window -v -c "$ROOT_DIR/dev/prod" \
    'rushx dev-server'

  tmux select-layout -t "$session_name:local" tiled
  tmux select-pane -t "$session_name:local.0"
  tmux attach -t "$session_name"
}

run_with_macos_terminal() {
  # Fallback for macOS if tmux is not available
  if [[ "$(uname -s)" != "Darwin" ]]; then
    return 1
  fi

  if ! command -v osascript >/dev/null 2>&1; then
    return 1
  fi

  echo "Launching macOS Terminal in one window with multiple tabs..."
  osascript <<OSA
tell application "Terminal"
  if (count of windows) is 0 then
    do script "cd \"$ROOT_DIR/pods/account\"; npm run run-local"
    set w to front window
  else
    set w to front window
    do script "cd \"$ROOT_DIR/pods/account\"; npm run run-local" in w
  end if
  do script "cd \"$ROOT_DIR/pods/server\"; npm run run-local" in w
  do script "cd \"$ROOT_DIR/pods/workspace\"; npm run run-local" in w
  do script "cd \"$ROOT_DIR/dev/prod\"; rushx dev-server" in w
  activate
end tell
OSA
}

main() {
  maybe_update "${1-}"

  if command -v tmux >/dev/null 2>&1; then
    run_with_tmux
    exit 0
  fi

  if run_with_macos_terminal; then
    exit 0
  fi

  echo "tmux not found and macOS Terminal fallback unavailable."
  echo "Running all processes in the current shell (use Ctrl-C to stop):"
  echo " - pods/account: npm run run-local"
  echo " - pods/server:  npm run run-local"
  echo " - pods/workspace: npm run run-local"
  echo " - dev/prod:     rushx dev-server"

  # As a last resort, run them sequentially in background; logs mixed in this shell.
  (cd "$ROOT_DIR/pods/account" && npm run run-local) &
  (cd "$ROOT_DIR/pods/server" && npm run run-local) &
  (cd "$ROOT_DIR/pods/workspace" && npm run run-local) &
  (cd "$ROOT_DIR/dev/prod" && rushx dev-server) &

  wait
}

main "$@"


