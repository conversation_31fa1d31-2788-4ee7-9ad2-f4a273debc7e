#!/usr/bin/env node
/**
 * Post-install script to set up Husky git hooks
 * This script runs after rush install completes
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const repoRoot = path.resolve(__dirname, '../..');

console.log('Setting up Husky git hooks...');

try {
  // Check if package.json exists
  const packageJsonPath = path.join(repoRoot, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.log('No root package.json found, skipping Husky setup');
    process.exit(0);
  }

  // Check if this is a git repository
  const gitDir = path.join(repoRoot, '.git');
  if (!fs.existsSync(gitDir)) {
    console.log('Not a git repository, skipping Husky setup');
    process.exit(0);
  }

  // Install Husky dependencies
  console.log('Installing Husky dependencies...');
  execSync('npm install', {
    cwd: repoRoot,
    stdio: 'inherit'
  });

  // Run husky install to set up hooks
  console.log('Running husky install...');
  execSync('npx husky install', {
    cwd: repoRoot,
    stdio: 'inherit'
  });

  console.log('✓ Husky setup complete!');
} catch (error) {
  console.error('Error setting up Husky:', error.message);
  // Don't fail the rush install if Husky setup fails
  process.exit(0);
}

