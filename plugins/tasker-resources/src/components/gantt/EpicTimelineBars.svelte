<script lang="ts">
  import { tooltip } from '@hcengineering/ui'
  import DetailsPanel from './DetailsPanel.svelte'
  import EpicBar from './EpicBar.svelte'
  import { formatDate, sortDates, toLocalDay, addOneDay } from './utils/dateUtils'
  import { getEpicPosition, getDueDatePosition } from './utils/timelineUtils'
  import tracker from '../../plugin'

  export let row: any
  export let values: any[]
  export let columnWidthRem: number
  export let dueBarWidth: number
  export let handleMouseMove: (e: MouseEvent) => void

  $: missedRedDates = Array.isArray(row.redDates) && row.redDates.length > 0 ? sortDates(row.redDates) : null
  $: dueLeft = row.dueDate ? getDueDatePosition(values, row.dueDate, columnWidthRem, dueBarWidth) : -1
  $: greenPos =
    row.startDate && row.greenDate ? getEpicPosition(values, row.startDate, row.greenDate, columnWidthRem) : undefined
  $: blueStartDate = row.greenDate
    ? toLocalDay(addOneDay(row.greenDate)) // next day after green
    : row.startDate
  $: bluePos =
    row.blueDate && (row.greenDate ?? row.startDate)
      ? getEpicPosition(values, blueStartDate, row.blueDate, columnWidthRem)
      : undefined
  $: redStartDate = row.blueDate
    ? toLocalDay(addOneDay(row.blueDate)) //next day after blue
    : row.greenDate
      ? toLocalDay(addOneDay(row.greenDate)) // next day after green
      : row.startDate
  $: redPos =
    row.redDate && !missedRedDates && (row.blueDate ?? row.greenDate ?? row.startDate)
      ? getEpicPosition(values, redStartDate, row.redDate, columnWidthRem)
      : undefined

  // Track popup state to disable tooltips
  let isPopupOpen = false
  let isHoveringMissedIcon = false

  // Custom conditional tooltip action
  function conditionalTooltip(node: HTMLElement, params: { enabled: boolean; config?: any }) {
    let tooltipAction: any = null
    let isHovered = false

    // Create tooltip
    function apply(config: any) {
      if (!tooltipAction) {
        tooltipAction = tooltip(node, config)
      }
    }

    // Remove tooltip
    function clear() {
      tooltipAction?.destroy?.()
      tooltipAction = null
    }

    function update(p: { enabled: boolean; config?: any }) {
      if (p.enabled && p.config && isHovered) {
        apply(p.config) // Show tooltip
      } else {
        clear() // Hide tooltip
      }
    }

    // Update hover state and check if tooltip should show
    function onEnter() {
      isHovered = true
      update(params)
    }

    // Update hover state and hide tooltip
    function onLeave() {
      isHovered = false
      clear()
    }

    node.addEventListener('mouseenter', onEnter)
    node.addEventListener('mouseleave', onLeave)

    // Initital check
    update(params)

    return {
      update, // Called when params change
      destroy() {
        // Called when element is removed from DOM
        node.removeEventListener('mouseenter', onEnter)
        node.removeEventListener('mouseleave', onLeave)
        clear()
      }
    }
  }
</script>

<div class="epic-container">
  {#if greenPos}
    <EpicBar
      {row}
      date={row.greenDate}
      pos={greenPos}
      type="green"
      {conditionalTooltip}
      isFirst={true}
      isLast={!row.blueDate && !row.redDate}
      popupText={tracker.string.Green}
      {isPopupOpen}
      {isHoveringMissedIcon}
      {handleMouseMove}
    />
  {/if}

  {#if bluePos}
    <EpicBar
      {row}
      date={row.blueDate}
      pos={bluePos}
      type="blue"
      {conditionalTooltip}
      isFirst={!row.greenDate}
      isLast={!row.redDate}
      popupText={tracker.string.Blue}
      {isPopupOpen}
      {isHoveringMissedIcon}
      {handleMouseMove}
    />
  {/if}

  {#if redPos}
    <EpicBar
      {row}
      date={row.redDate}
      pos={redPos}
      type="red"
      {conditionalTooltip}
      isFirst={!row.blueDate && !row.greenDate}
      isLast={true}
      popupText={tracker.string.Red}
      {isPopupOpen}
      {isHoveringMissedIcon}
      {handleMouseMove}
    />
  {:else if missedRedDates && (row.blueDate ?? row.greenDate ?? row.startDate)}
    {@const lastIndex = missedRedDates.length - 1}
    {#each missedRedDates as missedRedDate, index}
      {@const redPos = getEpicPosition(
        values,
        index == 0 ? redStartDate : toLocalDay(addOneDay(missedRedDates[index - 1])),
        missedRedDate,
        columnWidthRem
      )}
      <EpicBar
        {row}
        date={missedRedDate}
        pos={redPos}
        type="red"
        {conditionalTooltip}
        isFirst={!row.blueDate && !row.greenDate && index == 0}
        isLast={index == lastIndex}
        popupText={tracker.string.Red}
        showMissedIcon={index == lastIndex}
        {isPopupOpen}
        {isHoveringMissedIcon}
        {handleMouseMove}
      />
    {/each}
  {/if}

  {#if dueLeft >= 0}
    <div
      class="due-dot"
      style="left: {dueLeft}rem; width: {dueBarWidth}rem;"
      use:conditionalTooltip={{
        enabled: !isPopupOpen,
        config: {
          component: DetailsPanel,
          props: {
            variant: 'chart',
            title: row.label,
            assignee: row.assignee,
            type: tracker.string.DueDate,
            startDate: formatDate(row.dueDate)
          }
        }
      }}
    />
  {/if}
</div>

<style lang="scss">
  .epic-container {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    height: 1.8rem;
    width: 100%;
    pointer-events: all;
    cursor: pointer;
    z-index: 2;
  }

  .due-dot {
    position: absolute;
    top: 0;
    bottom: 0;
    height: 100%;
    background-color: var(--theme-state-warning-color, #ffc107);
    pointer-events: auto;
    z-index: 15;
    cursor: pointer;

    &:hover {
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.25),
        0 0 15px rgba(255, 255, 255, 0.2);
      z-index: 10;
    }
  }

  .due-dot::before {
    content: '';
    position: absolute;
    left: -5px;
    right: -5px;
    top: 0;
    bottom: 0;
    pointer-events: auto;
  }
</style>
