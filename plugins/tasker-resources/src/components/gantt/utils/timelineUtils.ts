import { toLocalDay, addOneDay } from './dateUtils'

// Calculate column width from space and column count
// Never smaller than minimum width
export const getColumnWidth = (gridWidth: number, numColumns: number, minWidth: number): number => {
  const width = gridWidth / numColumns
  return Math.max(width, minWidth)
}

// Get the start of the column in milliseconds
const colStartInMs = (c: any) => toLocalDay(c.date).getTime()
// Get the end of the column plus one day. Columns are treated as [start, end), end being exclusive
const colEndExclusiveInMs = (c: any) => addOneDay(toLocalDay(c.endDate)).getTime()
// How many milliseconds the column spans
const colDurationInMs = (c: any) => colEndExclusiveInMs(c) - colStartInMs(c)
// Returns a number from 0 to 1 which tells how far across the column a particular date is
// Anything outside [0, 1] gets clamped
const offsetInColumn = (date: Date, column: any) => {
  const start = colStartInMs(column)
  const end = colEndExclusiveInMs(column)
  const frac = (date.getTime() - start) / (end - start)

  return Math.max(0, Math.min(1, frac))
}
// Return the index of the column that contains the timestamp
const findColumnIndex = (t: number, values: any[]) =>
  values.findIndex((c) => t >= colStartInMs(c) && t < colEndExclusiveInMs(c))

// Calculate epic bar left position and width in rem
// Return { left, width }
export const getEpicPosition = (
  values: any[],
  startDate: Date,
  endDate: Date,
  columnWidthRem: number
): { left: number; width: number } => {
  const epicStart = toLocalDay(startDate)
  const epicEnd = toLocalDay(endDate)
  // Inclusive end date
  const epicEndInclusive = addOneDay(epicEnd)

  let left = 0
  let width = 0

  // Case 1: Epic bar is only one day long
  if (epicStart.getTime() === epicEnd.getTime()) {
    const columnIndex = findColumnIndex(epicStart.getTime(), values)

    if (columnIndex !== -1) {
      const column = values[columnIndex]
      const offset = offsetInColumn(epicStart, column)
      const oneDayInMs = 24 * 60 * 60 * 1000

      left = columnIndex * columnWidthRem + offset * columnWidthRem
      width = (oneDayInMs / colDurationInMs(column)) * columnWidthRem
    }
  }
  // Case 2: Multi-day epic bar (only gets displayed if epic start date is before the epic end date)
  else if (epicStart < epicEnd) {
    const startIdx = findColumnIndex(epicStart.getTime(), values)
    const endIdx = findColumnIndex(epicEnd.getTime(), values)

    const realStartIdx = startIdx !== -1 ? startIdx : 0
    const realEndIdx = endIdx !== -1 ? endIdx : values.length - 1

    const startCol = values[realStartIdx]
    const endCol = values[realEndIdx]

    const startOffset = offsetInColumn(epicStart, startCol)
    const endOffset = offsetInColumn(epicEndInclusive, endCol)

    left = realStartIdx * columnWidthRem + startOffset * columnWidthRem
    width = (realEndIdx - realStartIdx) * columnWidthRem + (endOffset - startOffset) * columnWidthRem
  }

  return { left, width }
}

// Get the due date’s horizontal position in rem units within timeline columns
// Return -1 if the date doesn’t fit in any column
export const getDueDatePosition = (values: any[], date: Date, columnWidthRem: number, dueBarWidth: number): number => {
  const d = toLocalDay(date)
  const dInclusive = addOneDay(d)

  const idx = findColumnIndex(d.getTime(), values)
  if (idx === -1) return -1

  const col = values[idx]
  const start = colStartInMs(col)
  const endEx = colEndExclusiveInMs(col)
  const offset = (dInclusive.getTime() - start) / (endEx - start)

  // Position at the right edge of the date, then shift left by due bar width
  const left = idx * columnWidthRem + offset * columnWidthRem - dueBarWidth

  return left
}

// Generate CSS style string for timeline cell width
export const getCellStyle = (columnWidthRem: number): string => {
  return `width: ${columnWidthRem}rem;`
}

// Generate CSS style string for timeline row height
export const getRowStyle = (
  isProjectHeader: boolean = false,
  projectHeaderHeightRem: number = 2.5,
  epicRowHeightRem: number = 3.0
): string => {
  return `height: ${isProjectHeader ? projectHeaderHeightRem : epicRowHeightRem}rem;`
}

// Split text into parts for search term highlighting
// Return object with before/match/after parts or null if no match found
// Used for search result highlighting in epic labels
export const getHighlightParts = (
  text: string,
  searchTerm: string
): { before: string; match: string; after: string } | null => {
  if (!searchTerm) return null
  const index = text.toLowerCase().indexOf(searchTerm.toLowerCase())
  if (index === -1) return null
  return {
    before: text.slice(0, index),
    match: text.slice(index, index + searchTerm.length),
    after: text.slice(index + searchTerm.length)
  }
}
