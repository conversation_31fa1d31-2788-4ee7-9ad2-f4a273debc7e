import { getMetadata } from '@hcengineering/platform'
import kpis from '@hcengineering/kpis'
import { EpicData, EpicRow } from '../types'
import { isSentinel, toLocalDay, formatYMD } from './dateUtils'

// Fetch Epics from the KPI service by project ID and optional date range
// Throws if KPI URL or project ID is missing, or if request fails
// Returns an array of EpicData (empty if response has no valid data)
async function fetchEpics(projectId: string, startDate?: string, endDate?: string): Promise<EpicData[]> {
  const kpiUrl = getMetadata(kpis.metadata.KpiUrl)

  if (typeof kpiUrl !== 'string' || kpiUrl === '') {
    throw new Error('KPI service URL is not configured')
  }

  if (projectId === '') {
    throw new Error('ProjectId missing!')
  }

  const params = new URLSearchParams({ projectIds: projectId.toUpperCase(), issueType: 'Epic' })

  if (startDate != null && startDate !== '') {
    params.append('startDate', startDate)
  }
  if (endDate != null && endDate !== '') {
    params.append('endDate', endDate)
  }

  const url = `${kpiUrl}/api/v1/jira/issues?${params.toString()}`
  const res = await fetch(url)
  const content = await res.text()
  if (!res.ok) {
    throw new Error(`Failed to load epics: ${res.status} ${res.statusText} - ${content}`)
  }

  try {
    const json: unknown = JSON.parse(content)
    if (typeof json === 'object' && json !== null && Array.isArray((json as { data?: unknown }).data)) {
      const data = (json as { data: EpicData[] }).data
      return data
    }
    if (Array.isArray(json)) {
      return json as EpicData[]
    }
    return []
  } catch (e) {
    throw new Error('Invalid JSON response')
  }
}

// Fetch and transform Epic issues into EpicRow objects for timeline display
// Returns a flat array of EpicRow entries (one per epic)
export async function mapEpicsToRows(
  customStartDate: Date | null,
  customEndDate: Date | null,
  projectCodes: string[]
): Promise<EpicRow[]> {
  // Use dates from the custom filter if both are provided; otherwise don't pass dates
  let startIso: string | undefined
  let endIso: string | undefined
  if (customStartDate != null && customEndDate != null) {
    startIso = formatYMD(customStartDate)
    endIso = formatYMD(customEndDate)
  }

  const allProjectRows = await Promise.all(
    projectCodes.map(async (project) => {
      try {
        const raw: any = await fetchEpics(project, startIso, endIso)
        const items: any[] = Array.isArray(raw) ? raw : Array.isArray(raw?.data) ? raw.data : []
        const mapped = items.map((item: any): EpicRow => {
          const start = item.startDate && !isSentinel(item.startDate) ? toLocalDay(new Date(item.startDate)) : undefined
          const end = item.endDate && !isSentinel(item.endDate) ? toLocalDay(new Date(item.endDate)) : undefined
          const green = item.greenDate && !isSentinel(item.greenDate) ? toLocalDay(new Date(item.greenDate)) : undefined
          const blue = item.blueDate && !isSentinel(item.blueDate) ? toLocalDay(new Date(item.blueDate)) : undefined
          const red = item.redDate && !isSentinel(item.redDate) ? toLocalDay(new Date(item.redDate)) : undefined
          const due = item.dueDate && !isSentinel(item.dueDate) ? toLocalDay(new Date(item.dueDate)) : undefined

          // Align missed red dates with reasons, preferring backend key-value pairs if present
          let redDatesArr: Date[] | undefined
          let redReasonsArr: string[] | undefined
          const hasMissHistory = Array.isArray(item.missedRed) && item.missedRed.length > 0
          if (hasMissHistory) {
            const pairs = item.missedRed as Array<{ date?: string; reason?: string }>
            const filtered = pairs
              .map((p) => ({
                d: p.date && !isSentinel(p.date) ? toLocalDay(new Date(p.date)) : undefined,
                r: p?.reason ?? ''
              }))
              .filter((x): x is { d: Date; r: string } => x.d instanceof Date)
            if (filtered.length > 0) {
              redDatesArr = filtered.map((x) => x.d)
              redReasonsArr = filtered.map((x) => x.r)
            }
          } else if (Array.isArray(item.redDates)) {
            redDatesArr = []
            redReasonsArr = []
            for (let i = 0; i < item.redDates.length; i++) {
              const d =
                item.redDates[i] && !isSentinel(item.redDates[i]) ? toLocalDay(new Date(item.redDates[i])) : undefined
              if (d) {
                redDatesArr.push(d)
                if (Array.isArray(item.redReasons)) {
                  redReasonsArr.push(item.redReasons[i] ?? '')
                }
              }
            }
            if (redDatesArr.length === 0) redDatesArr = undefined
            if (redReasonsArr.length === 0) redReasonsArr = undefined
          }

          return {
            label: item.summary ?? item.epicName ?? item.key ?? '',
            project,
            id: item.key ?? item.epicName ?? '',
            businessDescription: item.description,
            status: item.status,
            startDate: start,
            greenDate: green,
            blueDate: blue,
            redDate: red,
            dueDate: due,
            endDate: end,
            assignee: item.assignee,
            redDates: redDatesArr,
            redReasons: redReasonsArr,
            hasMissHistory
          }
        })
        const visible = mapped.filter((row) => row.greenDate != null || row.blueDate != null || row.redDate != null)
        return visible
      } catch (err) {
        console.warn('Failed to load KPIs for project', project, err)
        return []
      }
    })
  )

  return allProjectRows.flat()
}

// Filter EpicRow data against a custom date range
// If no range is provided, returns all rows unchanged
export function applyMockData(rows: EpicRow[], start: Date | null, end: Date | null): EpicRow[] {
  if (start != null && end != null) {
    const startMs = start.getTime()
    const endMs = end.getTime()
    return rows.filter((row) => {
      if (row.dueDate == null) return false
      const dueMs = row.dueDate.getTime()
      return dueMs >= startMs && dueMs <= endMs
    })
  }
  return rows
}

// Calculate a simple relevance score between a label and a search term
// - Exact match returns 100
// - Substring match returns 75
// - Partial match scores up to 50, proportional to how many search words appear in the label
export function calculateMatchScore(label: string, searchTerm: string): number {
  const normalizedLabel = label.toLowerCase()
  const normalizedSearch = searchTerm.toLowerCase()
  if (normalizedLabel === normalizedSearch) return 100
  if (normalizedLabel.includes(normalizedSearch)) return 75
  const searchWords = normalizedSearch.split(' ')
  const matchedWords = searchWords.filter((word) => normalizedLabel.includes(word))
  return (matchedWords.length / searchWords.length) * 50
}
