// Used to identify placeholder/null date values in the system
export const isSentinel = (s?: string): boolean => typeof s === 'string' && s.startsWith('1970-01-01')

// Return a new Date set to local midnight on the same calendar day.
export const toLocalDay = (d: Date): Date => {
  return new Date(d.getFullYear(), d.getMonth(), d.getDate())
}

// Format date for timeline display (e.g., "15 Oct")
export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('default', { day: 'numeric', month: 'short' })
}

// Find the latest (most recent) date from an array of dates
export const getMaxDate = (dates: Date[]): Date => {
  return new Date(Math.max(...dates.map((d) => d.getTime())))
}

// Format missed dates (with optional reasons) for tooltip display
// Return undefined if no valid dates are given
export const buildMissedDatesList = (
  dates?: Date[] | null,
  reasons?: string[] | null
): { date: string; reason?: string }[] | undefined => {
  if (!Array.isArray(dates)) return undefined
  return dates.map((d, i) => ({
    date: formatDate(d),
    reason: Array.isArray(reasons) ? reasons[i] : undefined
  }))
}

// Check if two dates represent the same calendar day (ignoring time)
// Return false if either date is undefined/null
export const isSameDay = (a?: Date, b?: Date): boolean => {
  if (!a || !b) return false
  return a.getFullYear() === b.getFullYear() && a.getMonth() === b.getMonth() && a.getDate() === b.getDate()
}

// Sort an array of dates in chronological order (earliest to latest)
export const sortDates = (dates: Date[]): Date[] => {
  return [...dates].sort((a, b) => a.getTime() - b.getTime())
}

// Check if a timeline column represents "today" by checking if the current date falls within its range
export const isToday = (value: any, todayDate: Date): boolean => {
  return todayDate >= value.date && todayDate <= value.endDate
}

// Create timeline columns for the Gantt chart from a date range and view mode
// Extends range by 12 months on each side for smooth scrolling
// Returns array of { type: 'week'|'month', date, endDate }
export const getTimeValues = (start: Date, end: Date, timeRange: string): any[] => {
  const extraMonths = 12
  if (timeRange === '12Months') {
    // For 12 months view, show months instead of weeks
    const months = []
    const extendedStartMonth = new Date(start.getFullYear(), start.getMonth() - extraMonths, 1)
    const originalTotalMonths = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth() + 1
    const totalMonths = originalTotalMonths + 2 * extraMonths

    for (let i = 0; i < totalMonths; i++) {
      const monthStart = new Date(extendedStartMonth.getFullYear(), extendedStartMonth.getMonth() + i, 1)
      const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0)

      months.push({
        type: 'month',
        date: monthStart,
        endDate: monthEnd
      })
    }
    return months
  } else {
    // For 3/6 months view, show weeks (Monday to Sunday)
    const weeks = []
    const viewStart = new Date(start.getFullYear(), start.getMonth() - extraMonths, 1)
    const viewEnd = new Date(end.getFullYear(), end.getMonth() + extraMonths, 0)
    const viewStartWeek = new Date(viewStart)
    viewStartWeek.setDate(viewStartWeek.getDate() - ((viewStartWeek.getDay() + 6) % 7))

    const totalDays = Math.ceil((viewEnd.getTime() - viewStartWeek.getTime()) / (1000 * 60 * 60 * 24))
    const totalWeeks = Math.ceil(totalDays / 7)

    for (let i = 0; i < totalWeeks; i++) {
      const weekStart = new Date(viewStartWeek.getTime() + i * 7 * 24 * 60 * 60 * 1000)
      const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000)

      weeks.push({
        type: 'week',
        date: weekStart,
        endDate: weekEnd
      })
    }
    return weeks
  }
}

// Calculate how many columns fit in the original date range
// Used for layout and column width
export const getVisibleColumnsCount = (start: Date, end: Date, timeRange: string): number => {
  if (timeRange === '12Months') {
    return (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth() + 1
  } else {
    const startWeek = new Date(start)
    startWeek.setDate(startWeek.getDate() - ((startWeek.getDay() + 6) % 7))
    const totalDays = Math.ceil((end.getTime() - startWeek.getTime()) / (1000 * 60 * 60 * 24))
    return Math.ceil(totalDays / 7)
  }
}

// Format calendar header text
// Monthly view: month abbrev (e.g. "Oct")
// Weekly view: day number (e.g. "15")
export const getDisplayText = (value: any, timeRange: string): string => {
  if (timeRange === '12Months') {
    return value.date.toLocaleDateString('default', { month: 'short' })
  }
  return value.date.getDate().toString()
}

// Format calendar header subtext
// Monthly view: year (e.g. "2024")
// Weekly view: month abbrev (e.g. "Oct")
export const getSubText = (value: any, timeRange: string): string => {
  if (timeRange === '12Months') {
    return value.date.getFullYear().toString()
  }
  return value.date.toLocaleDateString('default', { month: 'short' })
}

// Check if a milestone date is within a column’s time range
// Return true if between start and end (inclusive)
const isMilestoneInColumn = (value: any, milestone: { date: Date; label: string }): boolean => {
  return milestone.date >= value.date && milestone.date <= value.endDate
}

// Calculate milestone position in rem within timeline columns
// Return -1 if date not in any column
export const getMilestonePosition = (
  values: any[],
  milestone: { date: Date; label: string },
  columnWidthRem: number
): number => {
  const columnIndex = values.findIndex((value) => isMilestoneInColumn(value, milestone))
  if (columnIndex === -1) return -1

  const column = values[columnIndex]
  const offset = (milestone.date.getTime() - column.date.getTime()) / (column.endDate.getTime() - column.date.getTime())

  return (columnIndex + offset) * columnWidthRem
}

// Format Date object as YYYY-MM-DD string
export const formatYMD = (d: Date): string => {
  const y = d.getFullYear()
  const m = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${y}-${m}-${day}`
}
// Given a start date and range (3/6/12 months), return the last date in that range
export function getEndDateForRange(date: Date, range: '3Months' | '6Months' | '12Months'): Date {
  const start = new Date(date.getFullYear(), date.getMonth(), 1)
  switch (range) {
    case '3Months':
      return new Date(start.getFullYear(), start.getMonth() + 3, 0)
    case '6Months':
      return new Date(start.getFullYear(), start.getMonth() + 6, 0)
    case '12Months':
      return new Date(start.getFullYear(), start.getMonth() + 12, 0)
  }
}

// Return the full month name (e.g. "October") for a given date
function getMonthName(date: Date): string {
  return new Intl.DateTimeFormat('default', { month: 'long' }).format(date)
}

// Format a date range for display (e.g. "Jan - Mar 2024" or "Dec 2023 - Jan 2024")
// Return empty string if either date is null
export function getDateRangeDisplay(startDate: Date | null, endDate: Date | null): string {
  if (startDate == null || endDate == null) return ''

  const start = getMonthName(startDate)
  const end = getMonthName(endDate)
  const startYear = startDate.getFullYear()
  const endYear = endDate.getFullYear()

  if (startYear === endYear) {
    return `${start} - ${end} ${startYear}`
  } else {
    return `${start} ${startYear} - ${end} ${endYear}`
  }
}

export function addOneDay(date: Date): Date {
  return new Date(date.getTime() + 24 * 60 * 60 * 1000)
}
