<script lang="ts">
  import { Scroller, resizeObserver, deviceOptionsStore as deviceInfo, Label } from '@hcengineering/ui'
  import CalendarDateHeaders from './CalendarDateHeaders.svelte'
  import TimelineResourcePanel from './TimelineResourcePanel.svelte'
  import TimelineHeaderLabel from './TimelineHeaderLabel.svelte'
  import TimelineGrid from './TimelineGrid.svelte'
  import tracker from '../../plugin'
  import { getTimeValues, getVisibleColumnsCount, toLocalDay } from './utils/dateUtils'
  import { getColumnWidth, getCellStyle, getRowStyle } from './utils/timelineUtils'

  const headerHeightRem = 4.375
  const minColWidthRem = 2.5

  export let currentDate: Date = new Date()
  export let startDate: Date = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  export let endDate: Date = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
  export let timeRange: '3Months' | '6Months' | '12Months' = '3Months'
  export let search: string = ''
  export let useMock: boolean = false
  export let handleMouseMove: (e: MouseEvent) => void

  export let rows: any[] = []

  // Collapsible projects state
  let collapsed: Set<string> = new Set<string>()

  $: visibleRows = (() => {
    const result: any[] = []
    let currentProject: string | null = null
    let hide = false
    for (const r of rows) {
      if (r?.type === 'header') {
        currentProject = r.label
        hide = collapsed.has(r.label)
        result.push(r)
      } else {
        if (!hide) result.push(r)
      }
    }
    return result
  })()

  let didInitialScroll = false

  let headerWidth: number = 0
  let containerWidth: number = 0
  let scrollContainer: HTMLDivElement | undefined

  $: headerWidthRem = headerWidth / $deviceInfo.fontSize
  $: containerWidthRem = containerWidth / $deviceInfo.fontSize
  $: values = getTimeValues(startDate, endDate, timeRange)
  $: visibleColumnsCount = getVisibleColumnsCount(startDate, endDate, timeRange)
  $: columnWidthRem = getColumnWidth(containerWidthRem - headerWidthRem, visibleColumnsCount, minColWidthRem)

  function handleHeaderWidthChange(event: CustomEvent<number>) {
    headerWidth = event.detail
  }

  function toggleProject(project: string): void {
    if (collapsed.has(project)) collapsed.delete(project)
    else collapsed.add(project)
    // force reactivity by reassigning
    collapsed = new Set(collapsed)
  }

  $: if (scrollContainer && values.length > 0 && columnWidthRem) {
    const pxPerRem = $deviceInfo.fontSize
    let targetIndex = -1
    if (!didInitialScroll) {
      targetIndex = values.findIndex(
        (v) => toLocalDay(currentDate) >= toLocalDay(v.date) && toLocalDay(v.date) <= toLocalDay(v.endDate)
      )
      didInitialScroll = true
    } else {
      const centerDate = new Date(startDate.getTime() + (endDate.getTime() - startDate.getTime()) / 2)
      targetIndex = values.findIndex(
        (v) => toLocalDay(centerDate) >= toLocalDay(v.date) && toLocalDay(centerDate) <= toLocalDay(v.endDate)
      )
    }
    if (targetIndex !== -1) {
      const targetX = targetIndex * columnWidthRem * pxPerRem
      const desired = Math.max(0, headerWidth + targetX - scrollContainer.clientWidth / 2)
      scrollContainer.scrollTo({ left: desired, behavior: 'auto' })
    }
  }
</script>

<div class="timeline-container">
  {#if rows.length}
    <Scroller fade={{ multipler: { top: headerHeightRem, left: headerWidthRem } }} noFade>
      <div
        bind:this={scrollContainer}
        class="vertical-scroll"
        use:resizeObserver={(evt) => {
          containerWidth = evt.clientWidth
        }}
      >
        <div class="timeline">
          {#key [containerWidthRem, columnWidthRem, headerWidthRem, timeRange]}
            <TimelineHeaderLabel on:headerWidthChange={handleHeaderWidthChange} />
            <TimelineResourcePanel
              {rows}
              {search}
              {collapsed}
              on:projectToggle={(e) => toggleProject(e.detail)}
              {handleMouseMove}
            />
            <CalendarDateHeaders {values} {timeRange} {currentDate} getCellStyle={() => getCellStyle(columnWidthRem)} />
            <TimelineGrid
              {visibleRows}
              {values}
              {getRowStyle}
              getCellStyle={() => getCellStyle(columnWidthRem)}
              {columnWidthRem}
              dueBarWidth={0.3125}
              todayDate={currentDate}
              {useMock}
              {handleMouseMove}
            />
          {/key}
        </div>
      </div>
    </Scroller>
  {:else}
    <div class="flex-center h-full w-full flex-grow fs-title">
      <Label label={tracker.string.NoItemsAvailable} />
    </div>
  {/if}
</div>

<style lang="scss">
  .timeline-container {
    flex: 1;
    min-height: 0;
    position: relative;
    overflow: hidden;
  }

  .vertical-scroll {
    height: 100%;
    overflow-y: auto;
    overflow-x: auto;
  }

  .timeline {
    min-width: 100%;
    width: max-content;
    min-height: 100%;
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: auto 1fr;
    grid-template-rows: auto 1fr;
    position: relative;
  }

  :global(.tooltip-container) {
    background: var(--theme-popup-background);
    border-radius: 4px;
    padding: 8px 12px;
    min-width: 150px;
  }

  :global(.tooltip-header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  :global(.tooltip-type) {
    font-weight: 500;
  }

  :global(.tooltip-dates) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
