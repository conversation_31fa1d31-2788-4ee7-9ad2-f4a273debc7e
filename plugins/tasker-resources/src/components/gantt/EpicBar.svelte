<script lang="ts">
  import DetailsPanel from './DetailsPanel.svelte'
  import { formatDate, buildMissedDatesList } from './utils/dateUtils'
  import tracker from '../../plugin'
  import { IntlString } from '@hcengineering/platform'
  import { Icon, showPopup } from '@hcengineering/ui'

  export let row: any
  export let date: Date
  export let pos: { left: number; width: number }
  export let type: 'green' | 'blue' | 'red'
  export let popupText: IntlString
  export let conditionalTooltip: (node: HTMLElement, params: { enabled: boolean; config?: any }) => any
  export let isPopupOpen: boolean = false
  export let isHoveringMissedIcon: boolean = false
  export let isFirst: boolean = false
  export let isLast: boolean = false
  export let showMissedIcon: boolean = false
  export let handleMouseMove: (e: MouseEvent) => void
</script>

<div
  class={`epic-section epic-${type}`}
  class:first={isFirst}
  class:last={isLast}
  style="left: {pos.left}rem; width: {pos.width}rem;"
  role="cell"
  tabindex={0}
  on:mouseenter={handleMouseMove}
  on:mousemove={handleMouseMove}
  use:conditionalTooltip={{
    enabled: !isPopupOpen && !isHoveringMissedIcon,
    config: {
      component: DetailsPanel,
      props: {
        variant: 'chart',
        title: row.label,
        assignee: row.assignee,
        type: popupText,
        startDate: formatDate(date)
      }
    }
  }}
>
  <div class="epic-content">
    {#if showMissedIcon}
      <div
        class="missed-icon"
        role="button"
        tabindex={0}
        on:mouseenter={() => {
          isHoveringMissedIcon = true
        }}
        on:mouseleave={() => {
          isHoveringMissedIcon = false
        }}
        on:click={(event) => {
          event.stopPropagation()
          isPopupOpen = true
          showPopup(
            DetailsPanel,
            {
              variant: 'missed',
              title: row.label,
              assignee: row.assignee,
              type: tracker.string.CurrentRed,
              startDate: formatDate(date),
              missedList: buildMissedDatesList(row.redDates, row.redReasons)
            },
            event.currentTarget,
            () => {
              isPopupOpen = false
            }
          )
        }}
        on:keydown={(event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault()
            event.stopPropagation()
            isPopupOpen = true
            showPopup(
              DetailsPanel,
              {
                variant: 'missed',
                title: row.label,
                assignee: row.assignee,
                type: tracker.string.CurrentRed,
                startDate: formatDate(date),
                missedList: buildMissedDatesList(row.redDates, row.redReasons)
              },
              event.currentTarget,
              () => {
                isPopupOpen = false
              }
            )
          }
        }}
      >
        <Icon icon={tracker.icon.Warning} size="small" iconProps={{ style: 'width: 21px; height: 21px;' }} />
      </div>
    {/if}
  </div>
</div>

<style lang="scss">
  .epic-section {
    position: absolute;
    height: 100%;
    border-radius: 4px;

    .epic-content {
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 0.75rem;
      font-weight: 500;
      color: var(--white-color);
      display: flex;
      align-items: center;
      justify-content: flex-end;
      transition: none;
      box-shadow: none;
      position: relative;
    }

    &.epic-green .epic-content {
      background: var(--theme-state-positive-color);
      margin-right: 0; // No right margin to connect with blue
    }

    &.epic-blue .epic-content {
      background: var(--theme-state-primary-color);
      margin: 0 -1px; // Negative margin on both sides
    }

    &.epic-red .epic-content {
      background: var(--theme-state-negative-color);
      margin-left: 0; // No left margin to connect with blue
    }

    &:hover:not(:has(.missed-icon:hover)) {
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.25),
        0 0 15px rgba(255, 255, 255, 0.2);
      z-index: 10;
    }
  }

  .first {
    .epic-content {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
  }

  .last {
    .epic-content {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }

  .missed-icon {
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 20;
    cursor: pointer;
    border-radius: 4px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px;

    &:hover {
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.25),
        0 0 15px rgba(255, 255, 255, 0.2);
    }
  }

  :global(.epic-content .popup) {
    padding: 8px;
    background-color: var(--theme-dialog-background-color) !important;
    border-radius: 6px;
    box-shadow: var(--theme-popup-shadow);
  }
</style>
