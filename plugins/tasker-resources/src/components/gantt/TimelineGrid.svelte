<script lang="ts">
  import EpicTimelineBars from './EpicTimelineBars.svelte'
  import MilestoneLine from './MilestoneLine.svelte'
  import { milestone } from './constants'
  import { toLocalDay } from './utils/dateUtils'

  export let visibleRows: any[] = []
  export let values: any[] = []
  export let getRowStyle: (isHeader: boolean) => string
  export let getCellStyle: () => string
  export let todayDate: Date
  export let columnWidthRem: number
  export let dueBarWidth: number
  export let useMock: boolean = false
  export let handleMouseMove: (e: MouseEvent) => void
</script>

<div class="timeline-grid-content">
  <div class="timeline-rows-wrapper">
    {#each visibleRows as row (row?.type ? `header-${row.label}` : row.id)}
      <div class="timeline-row flex" style={getRowStyle(row.type === 'header')}>
        {#each values as value}
          {@const today =
            toLocalDay(todayDate) >= toLocalDay(value.date) && toLocalDay(todayDate) <= toLocalDay(value.endDate)}
          <div
            class="timeline-cell"
            class:today-column={today}
            class:project-header-cell={row.type === 'header'}
            style={getCellStyle()}
          ></div>
        {/each}
        {#if !row.type}
          <EpicTimelineBars {row} {values} {columnWidthRem} {dueBarWidth} {handleMouseMove} />
        {/if}
      </div>
    {/each}
    {#if useMock}
      <MilestoneLine {values} {milestone} {columnWidthRem} />
    {/if}
  </div>
</div>

<style lang="scss">
  $timeline-row-height: 3rem;
  $timeline-column-width: 2rem;
  $timeline-bg-color: var(--theme-comp-header-color);
  $timeline-border-color: var(--theme-bg-divider-color);
  $timeline-border: 1px solid $timeline-border-color;

  .timeline-grid-content {
    position: relative;
    z-index: 1;
    margin: 0;
    padding: 0;

    .timeline-rows-wrapper {
      position: relative;
    }

    .timeline-row {
      position: relative;
      height: $timeline-row-height;
      background-color: var(--theme-button-hovered);

      .timeline-cell {
        box-shadow:
          inset 0 -1px 0 0 $timeline-border-color,
          inset -1px 0 0 0 $timeline-border-color;
        background-color: var(--theme-button-hovered);
        width: $timeline-column-width;
        height: 100%;
        position: relative;
        z-index: 1;
        padding: 0;
        margin: 0;

        &.today-column {
          background-image: linear-gradient(
            135deg,
            var(--theme-calendar-weekend-color) 10%,
            $timeline-bg-color 10%,
            $timeline-bg-color 50%,
            var(--theme-calendar-weekend-color) 50%,
            var(--theme-calendar-weekend-color) 60%,
            $timeline-bg-color 60%,
            $timeline-bg-color 100%
          );
          background-size: 7px 7px;
          opacity: 0.3;
        }

        &.project-header-cell {
          background-color: $timeline-bg-color !important;
          box-shadow: inset 0 -1px 0 0 $timeline-border-color !important;
        }
      }
    }
  }
</style>
