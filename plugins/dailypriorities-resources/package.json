{"name": "@hcengineering/dailypriorities-resources", "version": "0.6.0", "main": "src/index.ts", "author": "Matrics", "license": "EPL-2.0", "scripts": {"build": "compile ui", "build:docs": "api-extractor run --local", "format": "format src", "svelte-check": "do-svelte-check", "_phase:svelte-check": "do-svelte-check", "build:watch": "compile ui", "_phase:build": "compile ui", "_phase:format": "format src", "_phase:validate": "compile validate"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@types/html-to-text": "^8.1.1", "eslint": "^8.54.0", "eslint-config-standard-with-typescript": "^40.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.4.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-svelte": "^2.35.1", "jest": "^29.7.0", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.2.2", "sass": "1.77.5", "svelte-check": "^3.6.9", "svelte-eslint-parser": "^0.33.1", "svelte-loader": "^3.2.0", "svelte-preprocess": "^5.1.3", "ts-jest": "^29.1.1", "typescript": "^5.8.3"}, "dependencies": {"@hcengineering/chunter": "^0.6.20", "@hcengineering/activity": "^0.6.32", "@hcengineering/activity-resources": "^0.6.1", "@hcengineering/ai-bot": "^0.6.0", "@hcengineering/ai-bot-resources": "^0.6.0", "@hcengineering/analytics": "^0.6.0", "@hcengineering/attachment": "^0.6.14", "@hcengineering/attachment-resources": "^0.6.0", "@hcengineering/dailypriorities": "^0.6.20", "@hcengineering/contact": "^0.6.24", "@hcengineering/contact-resources": "^0.6.0", "@hcengineering/core": "^0.6.32", "@hcengineering/login": "^0.6.12", "@hcengineering/notification": "^0.6.23", "@hcengineering/notification-resources": "^0.6.0", "@hcengineering/platform": "^0.6.11", "@hcengineering/preference": "^0.6.13", "@hcengineering/presentation": "^0.6.3", "@hcengineering/text": "^0.6.5", "@hcengineering/ui": "^0.6.15", "@hcengineering/view": "^0.6.13", "@hcengineering/view-resources": "^0.6.0", "@hcengineering/workbench": "^0.6.16", "@hcengineering/workbench-resources": "^0.6.1", "@hcengineering/presence-resources": "^0.6.0", "@hcengineering/emoji": "^0.6.0", "fast-equals": "^5.2.2", "svelte": "^4.2.20", "@hcengineering/text-editor-resources": "^0.6.0", "@hcengineering/text-editor": "^0.6.0", "@hcengineering/task": "^0.6.20", "@hcengineering/tracker": "^0.7.0", "@hcengineering/tasker": "^0.6.24", "@hcengineering/tasker-resources": "^0.6.0", "@hcengineering/matrics-hr": "^0.6.19", "@hcengineering/love": "^0.6.0", "@hcengineering/love-resources": "^0.6.0"}}