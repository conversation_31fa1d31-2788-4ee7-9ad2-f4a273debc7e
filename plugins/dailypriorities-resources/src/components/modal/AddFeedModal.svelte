<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Label, EditBox, Button } from '@hcengineering/ui'
  import dailyPriorities from '../../plugin'

  export let defaultUrl: string = 'https://telegrafi.com/feeds/feed.rss'

  const dispatch = createEventDispatcher()

  let feedUrl = defaultUrl
  let loading = false

  function close (): void {
    dispatch('close')
  }

  async function handleAdd (): Promise<void> {
    if (!feedUrl.trim()) return
    
    loading = true
    dispatch('add', { url: feedUrl.trim() })
  }

  function handleKeydown (e: KeyboardEvent): void {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      void handleAdd()
    } else if (e.key === 'Escape') {
      close()
    }
  }
</script>

<div class="modal-overlay" role="button" tabindex="0" on:click={close} on:keydown={handleKeydown}>
  <div class="modal-content" role="dialog" aria-modal="true" on:click|stopPropagation on:keydown|stopPropagation>
    <div class="modal-header">
      <h3><Label label={dailyPriorities.string.AddFeed} /></h3>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label class="form-label">
          <Label label={dailyPriorities.string.EnterFeedUrl} />
        </label>
        <EditBox
          bind:value={feedUrl}
          placeholder="https://example.com/feed.rss"
          maxWidth="100%"
          autoFocus={true}
          on:keydown={handleKeydown}
        />
      </div>
    </div>

    <div class="modal-footer">
      <Button
        label={dailyPriorities.string.AddFeed}
        kind="primary"
        size="large"
        disabled={!feedUrl.trim() || loading}
        loading={loading}
        on:click={handleAdd}
      />
      <Button
        label={dailyPriorities.string.EditCancel}
        kind="secondary"
        size="large"
        on:click={close}
      />
    </div>
  </div>
</div>

<style lang="scss">
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
  }

  .modal-content {
    background-color: var(--theme-popup-color);
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--theme-divider-color);

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--theme-caption-color);
    }
  }

  .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--theme-content-color);
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--theme-divider-color);
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
  }
</style>
