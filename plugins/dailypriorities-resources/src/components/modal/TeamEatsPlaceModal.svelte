<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Modal, EditBox, Toggle, RadioGroup, Label } from '@hcengineering/ui'
  import ui from '@hcengineering/ui'
  import dailyPriorities from '../../plugin'

  export let hidden: boolean = false

  // Form state
  let name = ''
  let isOrdering = false
  let walk: 0 | 5 | 10 | 15 | 30 = 0
  let price: '' | '$' | '$$' | '$$$' = ''
  let mapUrl: string = ''
  let menuUrl: string = ''

  const dispatch = createEventDispatcher()

  $: canSave = name.trim().length > 0

  function handleCancel (): void {
    dispatch('close')
  }

  function handleOk (): void {
    if (!canSave) return
    const payload = {
      name: name.trim(),
      isOrdering,
      walkTime: walk === 0 ? undefined : walk,
      price: price === '' ? undefined : price,
      mapUrl: mapUrl.trim().length > 0 ? mapUrl.trim() : undefined,
      menuUrl: menuUrl.trim().length > 0 ? menuUrl.trim() : undefined
    }
    dispatch('create', payload)
    dispatch('close', payload)
  }

  const walkItems = [
    { id: 'none', label: 'No walk', value: 0 },
    { id: '5', label: '5 min', value: 5 },
    { id: '10', label: '10 min', value: 10 },
    { id: '15', label: '15 min', value: 15 },
    { id: '30', label: '30 min', value: 30 }
  ]
  const priceItems = [
    { id: 'none', label: 'No preference', value: '' },
    { id: '$', label: '$', value: '$' },
    { id: '$$', label: '$$', value: '$$' },
    { id: '$$$', label: '$$$', value: '$$$' }
  ]
</script>

<div class="te-modal-portal">
  <Modal
    type={'type-popup'}
    width={'medium'}
    label={dailyPriorities.string.AddPlace}
    {hidden}
    onCancel={handleCancel}
    okAction={handleOk}
    okLabel={dailyPriorities.string.AddPlace}
    {canSave}
  >
  <div class="content">
    <div class="field">
      <div class="label"><Label label={dailyPriorities.string.WhereToEat} /></div>
      <EditBox bind:value={name} placeholder={dailyPriorities.string.WhereToEat} fullSize kind={'large-style'} />
    </div>

    <div class="row">
      <div class="toggle">
        <Toggle bind:on={isOrdering} />
        <span class="ml-2"><Label label={dailyPriorities.string.Ordering} /></span>
      </div>
    </div>

    <div class="field">
      <div class="label">Walk time</div>
      <RadioGroup items={walkItems} bind:selected={walk} disabled={isOrdering} />
    </div>

    <div class="field">
      <div class="label">Price</div>
      <RadioGroup items={priceItems} bind:selected={price} />
    </div>

    <div class="field">
      <div class="label">Google Maps link (optional)</div>
      <EditBox bind:value={mapUrl} placeholder={ui.string.EditBoxPlaceholder} fullSize />
    </div>

    <div class="field">
      <div class="label">Menu link (optional)</div>
      <EditBox bind:value={menuUrl} placeholder={ui.string.EditBoxPlaceholder} fullSize />
    </div>
  </div>
  </Modal>
</div>

<style lang="scss">
  .content { 
    display: flex; 
    flex-direction: column; 
    gap: 1rem; 
    padding: 0.5rem 0;
  }
  .field { 
    display: flex; 
    flex-direction: column; 
    gap: 0.5rem; 
  }
  .label { 
    font-weight: 600; 
    font-size: 0.875rem;
    color: var(--theme-caption-color); 
  }
  .row { 
    display: flex; 
    align-items: center; 
    gap: 1rem; 
  }
  .toggle { 
    display: inline-flex; 
    align-items: center; 
    gap: 0.5rem;
  }
  .te-modal-portal {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    pointer-events: all;
  }
</style>
