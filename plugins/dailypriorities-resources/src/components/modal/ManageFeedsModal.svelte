<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Label, Button, IconDelete, IconAdd, ButtonIcon, SectionEmpty } from '@hcengineering/ui'
  import dailyPriorities from '../../plugin'
  import type { RssFeed } from '@hcengineering/dailypriorities'

  export let feeds: RssFeed[] = []

  const dispatch = createEventDispatcher()

  function close (): void {
    dispatch('close')
  }

  function handleAddFeed (): void {
    dispatch('addFeed')
  }

  function handleDeleteFeed (feed: RssFeed): void {
    dispatch('deleteFeed', { feed })
  }

  function handleKeydown (e: KeyboardEvent): void {
    if (e.key === 'Escape') {
      close()
    }
  }
</script>

<div class="modal-overlay" role="button" tabindex="0" on:click={close} on:keydown={handleKeydown}>
  <div class="modal-content" role="dialog" aria-modal="true" on:click|stopPropagation on:keydown|stopPropagation>
    <div class="modal-header">
      <h3><Label label={dailyPriorities.string.ManageFeeds} /></h3>
      <ButtonIcon
        icon={IconAdd}
        size="medium"
        kind="primary"
        on:click={handleAddFeed}
      />
    </div>
    
    <div class="modal-body">
      {#if feeds.length === 0}
        <SectionEmpty icon={IconAdd} label={dailyPriorities.string.NoFeedsYet} />
      {:else}
        <div class="feeds-list">
          <div class="section-header"><Label label={dailyPriorities.string.YourFeeds} /> ({feeds.length})</div>
          {#each feeds as feed}
            <div class="feed-item">
              <div class="feed-info">
                <div class="feed-title">{feed.title ?? feed.url}</div>
                <div class="feed-url">{feed.url}</div>
              </div>
              <button
                class="action-btn"
                title="Delete"
                on:click={() => handleDeleteFeed(feed)}
              >
                <IconDelete size="small" />
              </button>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <div class="modal-footer">
      <Button
        label={dailyPriorities.string.EditCancel}
        kind="secondary"
        size="large"
        on:click={close}
      />
    </div>
  </div>
</div>

<style lang="scss">
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
  }

  .modal-content {
    background-color: var(--theme-popup-color);
    border-radius: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
    display: flex;
    align-items: center;
    justify-content: space-between;

    h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--theme-caption-color);
    }
  }

  .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
  }

  .section-header {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--theme-content-color);
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .feeds-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .feed-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background-color: var(--theme-button-default);
    border-radius: 0.5rem;
    border: 1px solid var(--theme-divider-color);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-border-color);
    }
  }

  .feed-info {
    flex: 1;
    min-width: 0;
  }

  .feed-title {
    font-weight: 500;
    color: var(--theme-caption-color);
    font-size: 0.9375rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .feed-url {
    font-size: 0.75rem;
    color: var(--theme-dark-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    color: var(--theme-content-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      color: var(--theme-caption-color);
    }
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--theme-divider-color);
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
  }
</style>
