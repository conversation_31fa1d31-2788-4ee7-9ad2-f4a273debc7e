<!--
// Copyright © 2024 Hardcore Engineering Inc.
// Licensed under EPL-2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Modal, Label, StateTag, Chip, ButtonIcon, StateType } from '@hcengineering/ui'
  import { getClient } from '@hcengineering/presentation'
  import type { PersonAccount } from '@hcengineering/contact'
  import type { Ref } from '@hcengineering/core'
  import contact from '@hcengineering/contact'
  import { Avatar } from '@hcengineering/contact-resources'
  import dailyPriorities from '../../plugin'

  export let item: any
  export let hidden: boolean = false

  const dispatch = createEventDispatcher()

  function handleClose (): void {
    dispatch('close')
  }

  function handleMapClick (): void {
    if (item.mapUrl) {
      dispatch('showMap', { url: item.mapUrl })
    }
  }

  function walkLabel (walkTime?: number): string | undefined {
    if (walkTime === 5) return '5 min'
    if (walkTime === 10) return '10 min'
    if (walkTime === 15) return '15 min'
    if (walkTime === 30) return '30 min'
    return undefined
  }

  function priceLabel (price?: string): string | undefined {
    if (price === '$') return '$'
    if (price === '$$') return '$$'
    if (price === '$$$') return '$$$'
    return undefined
  }

  const GOING_EMOJI = '🍽️'

  $: goingReactions = ((item.$lookup?.reactions ?? []) as Array<{ emoji: string, createBy: string }>).filter((r: any) => r.emoji === GOING_EMOJI)
  $: goingCount = goingReactions.length

  let goingUsers: PersonAccount[] = []

  $: if (goingReactions.length > 0) {
    loadGoingUsers()
  }

  async function loadGoingUsers (): Promise<void> {
    try {
      const client = getClient()
      const userIds = goingReactions.map(r => r.createBy as Ref<PersonAccount>)
      if (userIds.length > 0) {
        goingUsers = await client.findAll(contact.class.PersonAccount, { _id: { $in: userIds } })
      }
    } catch (e) {
      console.error('Failed to load going users:', e)
      goingUsers = []
    }
  }
</script>

<div class="te-modal-portal">
  <Modal
    type={'type-popup'}
    width={'medium'}
    label={dailyPriorities.string.PlaceDetails}
    {hidden}
    onCancel={handleClose}
    showOkButton={false}
  >
    <div class="details-content">
      <div class="place-header">
        <h2 class="place-name">{item.message || 'Unknown Place'}</h2>
        <div class="place-meta">
          {#if item.te_isOrdering}
            <StateTag type={StateType.Primary} label={dailyPriorities.string.Ordering} />
          {/if}
          {#if walkLabel(item.te_walkTime)}
            <Chip size="small" label={`🚶 ${walkLabel(item.te_walkTime)}`} />
          {/if}
          {#if priceLabel(item.te_price)}
            <Chip size="small" label={`💰 ${priceLabel(item.te_price)}`} />
          {/if}
        </div>
      </div>

      {#if goingCount > 0}
        <div class="going-section">
          <div class="going-label"><Label label={dailyPriorities.string.Going} /> ({goingCount})</div>
          <div class="going-indicator">{GOING_EMOJI} {goingCount} {goingCount === 1 ? 'person' : 'people'} going</div>
          {#if goingUsers.length > 0}
            <div class="going-users">
              {#each goingUsers as user}
                {#await getClient().findOne(contact.class.Person, { _id: user.person }) then person}
                  {#if person}
                    <div class="user-chip">
                      <Avatar {person} size="x-small" />
                      <span class="user-name">{person.name || user.email || 'Unknown'}</span>
                    </div>
                  {:else}
                    <div class="user-chip">
                      <span class="user-name">{user.email || 'Unknown'}</span>
                    </div>
                  {/if}
                {/await}
              {/each}
            </div>
          {/if}
        </div>
      {/if}

      {#if item.mapUrl || item.menuUrl}
        <div class="links-section">
          <div class="section-title"><Label label={dailyPriorities.string.Links} /></div>
          <div class="links-grid">
            {#if item.mapUrl}
              <button class="link-card map-link" on:click={handleMapClick}>
                <div class="link-icon">🗺️</div>
                <div class="link-info">
                  <div class="link-title"><Label label={dailyPriorities.string.ViewOnMap} /></div>
                  <div class="link-subtitle"><Label label={dailyPriorities.string.OpenLocation} /></div>
                </div>
              </button>
            {/if}
            {#if item.menuUrl}
              <a class="link-card menu-link" href={item.menuUrl} target="_blank" rel="noopener noreferrer">
                <div class="link-icon">📋</div>
                <div class="link-info">
                  <div class="link-title"><Label label={dailyPriorities.string.ViewMenu} /></div>
                  <div class="link-subtitle"><Label label={dailyPriorities.string.ExternalLink} /></div>
                </div>
              </a>
            {/if}
          </div>
        </div>
      {:else}
        <div class="empty-section">
          <div class="empty-text"><Label label={dailyPriorities.string.NoLinksAvailable} /></div>
        </div>
      {/if}
    </div>
  </Modal>
</div>

<style lang="scss">
  .te-modal-portal {
    position: fixed;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  :global(.te-modal-portal .hulyModal-container.type-popup) {
    width: auto;
    max-width: 42rem;
    min-width: 32rem;
  }

  .details-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0.25rem 0;
  }

  .place-header {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .place-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    margin: 0;
  }

  .place-meta {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .going-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: var(--theme-comp-BackgroundColor);
    border-radius: 0.5rem;
    border: 1px solid var(--theme-divider-color);
  }

  .going-label {
    font-weight: 600;
    color: var(--theme-caption-color);
    font-size: 0.875rem;
  }

  .going-indicator {
    color: var(--theme-content-color);
    font-size: 0.875rem;
  }

  .going-users {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .user-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.25rem 0.5rem;
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: 1rem;
    font-size: 0.75rem;
  }

  .user-name {
    color: var(--theme-caption-color);
    font-weight: 500;
  }

  .links-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .section-title {
    font-weight: 600;
    color: var(--theme-caption-color);
    font-size: 0.875rem;
  }

  .links-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  @media (max-width: 36rem) {
    .links-grid {
      grid-template-columns: 1fr;
    }
  }

  .link-card {
    all: unset;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: var(--theme-comp-BackgroundColor);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 120ms ease;
  }

  .link-card:hover {
    background-color: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.03));
    border-color: var(--theme-button-border);
    box-shadow: 0 1px 3px rgba(0,0,0,0.06);
  }

  .link-icon {
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: var(--theme-button-default);
    border-radius: 0.375rem;
  }

  .link-info {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    flex: 1;
  }

  .link-title {
    font-weight: 500;
    color: var(--theme-caption-color);
    font-size: 0.875rem;
  }

  .link-subtitle {
    color: var(--theme-content-color);
    font-size: 0.75rem;
  }

  .empty-section {
    padding: 1rem;
    text-align: center;
  }

  .empty-text {
    color: var(--theme-content-color);
    font-size: 0.875rem;
  }
</style>
