<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import type { Person } from '@hcengineering/contact'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import { Label, ButtonBase, Icon } from '@hcengineering/ui'
  import type { TimeEntry } from '@hcengineering/dailypriorities'
  import dailyPriorities from '../plugin'
  import { onMount, onDestroy } from 'svelte'
  import love from '@hcengineering/love'
  import type { Room } from '@hcengineering/love'
  import { addNotification, NotificationSeverity } from '@hcengineering/ui'
  import SimpleTextNotification from './notifications/SimpleTextNotification.svelte'

  export let object: Person
  export let disabled: boolean = false

  const client = getClient()
  let activeEntry: TimeEntry | null = null
  let elapsedSeconds = 0
  let timerInterval: any = null
  let userOffice: Room | null = null

  const query = createQuery()
  const officeQuery = createQuery()

  function formatTime (seconds: number): string {
    const h = Math.floor(seconds / 3600)
    const m = Math.floor((seconds % 3600) / 60)
    const s = seconds % 60
    return `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}:${String(s).padStart(2, '0')}`
  }

  async function startHuddle (): Promise<void> {
    if (!userOffice) {
      addNotification(
        'Office',
        'User does not have an office',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Warning
      )
      return
    }

    try {
      // Import meeting functions dynamically to avoid circular dependencies
      const { joinMeeting } = await import('@hcengineering/love-resources/src/meetings')
      await joinMeeting(userOffice)
      addNotification(
        'Office',
        `Joining huddle with ${object.name}`,
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Success
      )
    } catch (e) {
      console.error('Failed to start huddle:', e)
      addNotification(
        'Office',
        'Failed to start huddle',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Error
      )
    }
  }

  async function startVideoCall (): Promise<void> {
    if (!userOffice) {
      addNotification(
        'Office',
        'User does not have an office',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Warning
      )
      return
    }

    try {
      // Import meeting functions dynamically to avoid circular dependencies
      const { createMeeting } = await import('@hcengineering/love-resources/src/meetings')
      await createMeeting(userOffice)
      addNotification(
        'Office',
        `Starting video call with ${object.name}`,
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Success
      )
    } catch (e) {
      console.error('Failed to start video call:', e)
      addNotification(
        'Office',
        'Failed to start video call',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Error
      )
    }
  }

  onMount(() => {
    // Query for active time entry for this person
    query.query(
      dailyPriorities.class.TimeEntry,
      {
        user: object._id as unknown as Ref<Person>,
        endTime: { $exists: false }
      },
      (res) => {
        activeEntry = res[0] as any ?? null
        if (activeEntry) {
          elapsedSeconds = Math.floor((Date.now() - (activeEntry.startTime ?? Date.now())) / 1000)
          if (timerInterval) clearInterval(timerInterval)
          timerInterval = setInterval(() => {
            if (activeEntry) {
              elapsedSeconds = Math.floor((Date.now() - activeEntry.startTime) / 1000)
            }
          }, 1000)
        } else {
          if (timerInterval) {
            clearInterval(timerInterval)
            timerInterval = null
          }
        }
      }
    )

    // Query for user's office
    officeQuery.query(
      love.class.Office,
      {
        person: object._id
      },
      (res) => {
        userOffice = res[0] ?? null
      }
    )
  })

  onDestroy(() => {
    if (timerInterval) {
      clearInterval(timerInterval)
    }
  })
</script>

{#if object}
  <div class="person-tracker-card">
    <!-- Office Integration Buttons -->
    <div class="office-section">
      <div class="office-buttons">
        <button class="office-btn huddle-btn" disabled={!userOffice} on:click={startHuddle}>
          <Icon icon={love.icon.Mic} size="small" />
          <span><Label label={dailyPriorities.string.Huddle} /></span>
        </button>

        <button class="office-btn video-btn" disabled={!userOffice} on:click={startVideoCall}>
          <Icon icon={love.icon.Camera} size="small" />
          <span><Label label={dailyPriorities.string.VideoCall} /></span>
        </button>
      </div>
    </div>

    <!-- Time Tracking Status -->
    {#if activeEntry}
      <div class="status-section">
        <div class="status-header">
          <Label label={dailyPriorities.string.CurrentlyWorking} />
        </div>
        <div class="status-task">{activeEntry.title}</div>
        <div class="status-time">{formatTime(elapsedSeconds)}</div>
      </div>
    {:else}
      <div class="status-section idle">
        <Label label={dailyPriorities.string.NotWorkingNow} />
      </div>
    {/if}
  </div>
{/if}

<style lang="scss">
  .person-tracker-card {
    margin-top: 1rem;
  }

  .office-section {
    margin-bottom: 1rem;
  }

  .office-buttons {
    display: flex;
    gap: 0.5rem;
  }

  .office-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    border: 1px solid var(--theme-button-border);
    background: var(--theme-button-default);
    color: var(--theme-content-color);
    font-size: 0.8125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover:not(:disabled) {
      background: var(--theme-button-hovered);
      border-color: var(--theme-button-border-hover, var(--theme-button-border));
    }

    &:active:not(:disabled) {
      background: var(--theme-button-pressed);
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    span {
      white-space: nowrap;
    }
  }

  .huddle-btn {
    background: var(--theme-button-positive, var(--theme-button-default));
    border-color: var(--theme-button-positive-border, var(--theme-button-border));
    color: var(--theme-button-positive-color, var(--theme-content-color));

    &:hover:not(:disabled) {
      background: var(--theme-button-positive-hovered, var(--theme-button-hovered));
    }
  }

  .video-btn {
    background: var(--theme-button-default);
    border-color: var(--theme-button-border);
  }

  .status-section {
    padding: 0.75rem;
    background: var(--theme-navpanel-color, var(--theme-bg-color));
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.25rem;

    &.idle {
      text-align: center;
      color: var(--theme-dark-color);
      font-size: 0.8125rem;
    }
  }

  .status-header {
    font-size: 0.6875rem;
    font-weight: 500;
    text-transform: uppercase;
    color: var(--theme-dark-color);
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
  }

  .status-task {
    font-size: 0.9375rem;
    font-weight: 500;
    color: var(--theme-caption-color);
    margin-bottom: 0.375rem;
  }

  .status-time {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    font-variant-numeric: tabular-nums;
  }
</style>
