<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import type { Widget } from '@hcengineering/workbench'
  import dailyPriorities from '../plugin'
  import { onMount, createEventDispatcher } from 'svelte'
  import { Label, Loading, SectionEmpty, ListView, Header, ButtonBase, EditBox, IconDelete, ButtonIcon, IconAdd, IconActivity, addNotification, NotificationSeverity, IconSettings } from '@hcengineering/ui'
  import { getClient, createQuery } from '@hcengineering/presentation'
  import { type Ref, getCurrentAccount } from '@hcengineering/core'
  import type { RssFeed } from '@hcengineering/dailypriorities'
  import contact, { getCurrentEmployee, type Person } from '@hcengineering/contact'
  import core from '@hcengineering/core'
  import SimpleTextNotification from './notifications/SimpleTextNotification.svelte'
  import AddFeedModal from './modal/AddFeedModal.svelte'
  import ManageFeedsModal from './modal/ManageFeedsModal.svelte'

  export let widget: Widget | undefined
  export let height: string
  export let width: string
  $: void widget

  const dispatch = createEventDispatcher()
  const client = getClient()
  const currentEmployeeRef = getCurrentEmployee()
  const currentPersonRef: Ref<Person> | undefined = currentEmployeeRef as unknown as Ref<Person> | undefined

  let loading = true
  let error: string | null = null
  let feeds: RssFeed[] = []
  let showAddModal = false
  let showManageModal = false
  let articles: Array<{ title: string, link: string, pubDate?: string, description?: string, feedTitle?: string }> = []
  let fetchingArticles = false

  const query = createQuery()
  const CORS_PROXY = 'https://cors.erzen.tk/'

  // Parse RSS feed using native browser DOMParser with CORS proxy
  async function parseFeed(url: string): Promise<{ title: string, items: any[] }> {
    try {
      // Use CORS proxy to fetch the RSS feed
      const proxiedUrl = CORS_PROXY + url
      const response = await fetch(proxiedUrl, {
        headers: {
          'Accept': 'application/rss+xml, application/xml, text/xml, application/atom+xml'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const text = await response.text()
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(text, 'text/xml')
      
      // Check for parsing errors
      const parserError = xmlDoc.querySelector('parsererror')
      if (parserError) {
        throw new Error('Failed to parse XML')
      }

      // Handle both RSS and Atom feeds
      const isAtom = xmlDoc.querySelector('feed') !== null
      
      let feedTitle = ''
      let items: any[] = []

      if (isAtom) {
        feedTitle = xmlDoc.querySelector('feed > title')?.textContent ?? 'Unknown Feed'
        const entries = xmlDoc.querySelectorAll('entry')
        items = Array.from(entries).map((entry) => ({
          title: entry.querySelector('title')?.textContent ?? 'No title',
          link: entry.querySelector('link')?.getAttribute('href') ?? '#',
          pubDate: entry.querySelector('published, updated')?.textContent,
          description: entry.querySelector('summary, content')?.textContent
        }))
      } else {
        feedTitle = xmlDoc.querySelector('channel > title, rss > title')?.textContent ?? 'Unknown Feed'
        const itemsNodes = xmlDoc.querySelectorAll('item, entry')
        items = Array.from(itemsNodes).map((item) => ({
          title: item.querySelector('title')?.textContent ?? 'No title',
          link: item.querySelector('link')?.textContent ?? '#',
          pubDate: item.querySelector('pubDate, published')?.textContent,
          description: item.querySelector('description, summary')?.textContent
        }))
      }

      return { title: feedTitle, items }
    } catch (e) {
      console.error('Failed to parse feed:', e)
      throw e
    }
  }

  async function fetchAllFeeds(): Promise<void> {
    if (feeds.length === 0) {
      articles = []
      return
    }

    fetchingArticles = true
    const allArticles: typeof articles = []

    for (const feed of feeds) {
      try {
        const { title: feedTitle, items } = await parseFeed(feed.url)
        const feedArticles = items.slice(0, 10).map((item) => ({
          ...item,
          feedTitle: feed.title ?? feedTitle
        }))
        allArticles.push(...feedArticles)
      } catch (e) {
        console.error(`Failed to fetch feed ${feed.url}:`, e)
      }
    }

    // Sort by date (most recent first)
    allArticles.sort((a, b) => {
      const dateA = a.pubDate ? new Date(a.pubDate).getTime() : 0
      const dateB = b.pubDate ? new Date(b.pubDate).getTime() : 0
      return dateB - dateA
    })

    articles = allArticles.slice(0, 50) // Limit to 50 most recent articles
    fetchingArticles = false
  }

  async function addFeed(e: CustomEvent<{ url: string }>): Promise<void> {
    const feedUrl = e.detail.url
    
    if (currentPersonRef === undefined) {
      error = dailyPriorities.string.TimeTrackerUnavailable
      showAddModal = false
      return
    }
    if (!feedUrl.trim()) {
      addNotification(
        'News',
        'Please enter a feed URL',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Warning
      )
      showAddModal = false
      return
    }

    // Validate URL
    try {
      new URL(feedUrl.trim())
    } catch {
      addNotification(
        'News',
        'Invalid URL format',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Error
      )
      showAddModal = false
      return
    }

    try {
      // Test if feed is valid
      const { title } = await parseFeed(feedUrl.trim())
      
      await client.createDoc(
        dailyPriorities.class.RssFeed,
        core.space.Space,
        {
          url: feedUrl.trim(),
          title,
          user: currentPersonRef
        }
      )
      
      showAddModal = false
      addNotification(
        'News',
        'Feed added successfully',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Success
      )
    } catch (e) {
      console.error('Failed to add feed:', e)
      showAddModal = false
      addNotification(
        'News',
        'Failed to add feed. Make sure the URL is a valid RSS/Atom feed.',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Error
      )
    }
  }

  async function deleteFeed(feed: RssFeed): Promise<void> {
    try {
      await client.remove(feed)
    } catch (e) {
      console.error('Failed to delete feed:', e)
    }
  }

  function formatDate(dateStr?: string): string {
    if (!dateStr) return ''
    try {
      const date = new Date(dateStr)
      const now = new Date()
      const diffMs = now.getTime() - date.getTime()
      const diffMins = Math.floor(diffMs / 60000)
      const diffHours = Math.floor(diffMs / 3600000)
      const diffDays = Math.floor(diffMs / 86400000)

      if (diffMins < 60) return `${diffMins}m ago`
      if (diffHours < 24) return `${diffHours}h ago`
      if (diffDays < 7) return `${diffDays}d ago`
      return date.toLocaleDateString()
    } catch {
      return ''
    }
  }

  function stripHtml(html?: string): string {
    if (!html) return ''
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent ?? tmp.innerText ?? ''
  }

  onMount(async () => {
    try {
      loading = true
      error = null

      if (currentPersonRef === undefined) {
        error = 'User not authenticated'
        return
      }

      // Query all feeds for current user
      query.query(
        dailyPriorities.class.RssFeed,
        { user: currentPersonRef },
        (res) => {
          feeds = res as any[]
          // Fetch articles whenever feeds change
          void fetchAllFeeds()
        },
        { sort: { _id: 1 } }
      )
    } catch (e: any) {
      error = e?.message ?? 'Failed to load feeds'
    } finally {
      loading = false
    }
  })
</script>

<div class="news-widget" style={`height:${height}; width:${width}`}>
  <Header
    allowFullsize={false}
    type="type-aside"
    hideBefore={true}
    hideActions={false}
    hideDescription={true}
    hideExtra={true}
    adaptive="disabled"
    closeOnEscape={false}
    on:close={() => dispatch('close')}
  >
    <div class="header-content">
      <div class="title">
        <Label label={dailyPriorities.string.News} />
        {#if articles.length > 0}
          <span class="articles-badge">{articles.length}</span>
        {/if}
      </div>
      <div class="header-actions">
        <ButtonIcon
          icon={IconSettings}
          size="small"
          kind="tertiary"
          tooltip={{ label: dailyPriorities.string.ManageFeeds }}
          on:click={() => { showManageModal = true }}
        />
        <ButtonIcon
          icon={IconActivity}
          size="small"
          kind="tertiary"
          tooltip={{ label: dailyPriorities.string.RefreshFeeds }}
          disabled={fetchingArticles || feeds.length === 0}
          on:click={() => fetchAllFeeds()}
        />
      </div>
    </div>
  </Header>

  <div class="content-body">
    {#if loading}
      <div class="content">
        <Loading shrink>
          <Label label={dailyPriorities.string.Loading} />
        </Loading>
      </div>
    {:else if error}
      <div class="content error">{error}</div>
    {:else}
      {#if feeds.length === 0}
        <div class="empty-state">
          <SectionEmpty icon={IconAdd} label={dailyPriorities.string.NoFeedsYet} />
          <ButtonBase
            type="type-button"
            kind="primary"
            size="large"
            label={dailyPriorities.string.AddFeed}
            icon={IconAdd}
            on:click={() => { showAddModal = true }}
          />
        </div>
      {:else}
        {#if fetchingArticles}
          <div class="loading-articles">
            <Loading shrink>
              <Label label={dailyPriorities.string.Loading} />
            </Loading>
          </div>
        {:else if articles.length === 0}
          <SectionEmpty icon={IconAdd} label={dailyPriorities.string.NoArticlesYet} />
        {:else}
          <div class="articles-section">
            <div class="section-header">Latest Articles ({articles.length})</div>
            <ListView count={articles.length} addClass={'news-card'}>
              <svelte:fragment slot="item" let:item={item}>
                {@const article = articles[item]}
                <a href={article.link} target="_blank" rel="noopener noreferrer" class="article-item">
                  <div class="article-header">
                    <div class="article-title">{article.title}</div>
                    {#if article.pubDate}
                      <div class="article-date">{formatDate(article.pubDate)}</div>
                    {/if}
                  </div>
                  {#if article.description}
                    <div class="article-description">
                      {stripHtml(article.description).slice(0, 150)}{stripHtml(article.description).length > 150 ? '...' : ''}
                    </div>
                  {/if}
                  <div class="article-source">{article.feedTitle}</div>
                </a>
              </svelte:fragment>
            </ListView>
          </div>
        {/if}
      {/if}
    {/if}
  </div>
</div>

{#if showAddModal}
  <AddFeedModal
    defaultUrl="https://telegrafi.com/feeds/feed.rss"
    on:close={() => { showAddModal = false }}
    on:add={addFeed}
  />
{/if}

{#if showManageModal}
  <ManageFeedsModal
    {feeds}
    on:close={() => { showManageModal = false }}
    on:addFeed={() => { showManageModal = false; showAddModal = true }}
    on:deleteFeed={(e) => deleteFeed(e.detail.feed)}
  />
{/if}

<style lang="scss">
  .news-widget {
    display: flex;
    flex-direction: column;
  }

  .content.error {
    color: var(--theme-danger-color);
    font-size: 0.9rem;
  }

  .content-body {
    padding: 0.25rem 0.5rem 0.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    width: 100%;
  }

  .title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .articles-badge {
    font-size: 0.75rem;
    color: var(--theme-content-color);
    background-color: var(--theme-button-default);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
  }

  .loading-articles {
    padding: 2rem;
    display: flex;
    justify-content: center;
  }

  .articles-section {
    margin-top: 0.5rem;
  }

  :global(.list-item.news-card) {
    padding: 0;
    border: none;
    background: none;
    margin: 0;
  }

  :global(.list-item.news-card + .list-item.news-card) {
    margin-top: 0.5rem;
  }

  .article-item {
    display: block;
    padding: 1rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    background-color: var(--theme-comp-BackgroundColor);
    text-decoration: none;
    color: inherit;
    transition: all 120ms ease;
  }

  .article-item:hover {
    background-color: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.03));
    border-color: var(--theme-button-border);
    box-shadow: 0 1px 3px rgba(0,0,0,0.06);
  }

  .article-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .article-title {
    font-weight: 600;
    color: var(--theme-caption-color);
    line-height: 1.4;
    flex: 1;
  }

  .article-date {
    font-size: 0.75rem;
    color: var(--theme-disabled-text-color);
    white-space: nowrap;
  }

  .article-description {
    font-size: 0.85rem;
    color: var(--theme-content-color);
    line-height: 1.5;
    margin-bottom: 0.5rem;
  }

  .article-source {
    font-size: 0.75rem;
    color: var(--theme-disabled-text-color);
    font-style: italic;
  }
</style>
