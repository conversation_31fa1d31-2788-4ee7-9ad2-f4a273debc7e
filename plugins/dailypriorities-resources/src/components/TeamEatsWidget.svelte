<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import type { Widget } from '@hcengineering/workbench'
  import dailyPriorities from '../plugin'
  import { onMount, createEventDispatcher } from 'svelte'
  import { Label, SearchInput, Loading, SectionEmpty, ListView, Header, Menu, ButtonIcon, IconCalendar, IconSearch, IconAdd, IconMoreH, IconCheck, showPopup, type Action, addNotification, NotificationSeverity, ButtonBase } from '@hcengineering/ui'
  import SimpleTextNotification from './notifications/SimpleTextNotification.svelte'
  import { ReactionsPresenter } from '@hcengineering/activity-resources'
  import activity from '@hcengineering/activity'
  import core from '@hcengineering/core'
  import chunter from '@hcengineering/chunter'
  import { getClient, createQuery, MessageViewer } from '@hcengineering/presentation'
  import { getDay, type Ref, type Space, type Doc, getCurrentAccount } from '@hcengineering/core'
  import TeamEatsPlaceModal from './modal/TeamEatsPlaceModal.svelte'
  import MapPreviewModal from './modal/MapPreviewModal.svelte'
  import TeamEatsDetailsModal from './modal/TeamEatsDetailsModal.svelte'

  export let widget: Widget | undefined
  export let height: string
  export let width: string
  $: void widget

  interface TeamEatsReaction {
    emoji: string
    createBy: string
  }

  interface TeamEatsMessage {
    _id: string
    _class: string
    message: string
    createdOn?: number
    space: string
    te_type?: string
    te_isOrdering?: boolean
    te_walkTime?: 5 | 10 | 15 | 30
    te_price?: '$' | '$$' | '$$$'
    mapUrl?: string
    menuUrl?: string
    reactions?: number
    $lookup?: {
      reactions?: TeamEatsReaction[]
    }
  }

  const TEAM_EATS_TYPE = 'team-eats'
  const CHANNEL_ID = 'team-eats-channel' // dedicated Team Eats channel

  let loading = true
  let error: string | null = null
  // Store all fetched items (could include yesterday)
  let itemsRaw: TeamEatsMessage[] = []
  // Reactive subset containing only today's items
  $: items = todayFilter(itemsRaw)
  let searchValue = ''
  let debouncedSearchValue = ''
  let searchDebounceTimer: any = null

  // Change default sort to 'mostGoing'
  let selectedSort: 'mostVoted' | 'mostGoing' | 'newest' = 'mostGoing'

  const dispatch = createEventDispatcher()

  const query = createQuery()

  // Modal state
  let showCreateModal = false
  let showSearchInput = false

  function todayFilter (messages: TeamEatsMessage[]): TeamEatsMessage[] {
    const today = getDay(Date.now())
    return messages.filter((m) => getDay(m.createdOn) === today)
  }

  // Adjust sorting: make 'mostVoted' sort by going first, swap with 'mostGoing' logic
  function toDisplay (
    messages: TeamEatsMessage[],
    qValue: string,
    sort: 'mostVoted' | 'mostGoing' | 'newest'
  ): TeamEatsMessage[] {
    const q = (qValue ?? '').trim().toLowerCase()
    let r = messages
      .filter((m) => (m.message ?? '').toLowerCase().includes(q))

    const score = (m: TeamEatsMessage): { up: number, going: number } => {
      return getScoreCache(m)
    }

    console.log(`Team Eats: Sorting ${r.length} items by ${sort}, query: "${qValue}"`)

    // Debug: Log all reactions for first few items
    if (r.length > 0) {
      const firstItem = r[0]
      const allReactions = firstItem.$lookup?.reactions ?? []
      console.log('First item reactions debug:', {
        item: firstItem.message,
        allReactions,
        goingEmoji: GOING_EMOJI,
        goingEmojiCode: GOING_EMOJI.codePointAt(0)?.toString(16),
        uniqueEmojis: [...new Set(allReactions.map((r: any) => r.emoji))],
        emojiCodes: allReactions.map((r: any) => ({ emoji: r.emoji, code: r.emoji.codePointAt(0)?.toString(16) }))
      })
    }

    switch (sort) {
      case 'mostVoted':
        r = r.sort((a, b) => {
          const sa = score(a); const sb = score(b)
          const goingDiff = sb.going - sa.going
          const upDiff = sb.up - sa.up
          const timeDiff = (b.createdOn ?? 0) - (a.createdOn ?? 0)
          return goingDiff !== 0 ? goingDiff : (upDiff !== 0 ? upDiff : timeDiff)
        })
        break
      case 'mostGoing':
        r = r.sort((a, b) => {
          const sa = score(a); const sb = score(b)
          const upDiff = sb.up - sa.up
          const goingDiff = sb.going - sa.going
          const timeDiff = (b.createdOn ?? 0) - (a.createdOn ?? 0)
          return upDiff !== 0 ? upDiff : (goingDiff !== 0 ? goingDiff : timeDiff)
        })
        break
      case 'newest':
        r = r.sort((a, b) => (b.createdOn ?? 0) - (a.createdOn ?? 0))
        break
    }

    return r
  }

  // Debounce search input
  $: {
    if (searchDebounceTimer) clearTimeout(searchDebounceTimer)
    searchDebounceTimer = setTimeout(() => {
      debouncedSearchValue = searchValue
    }, 300)
  }

  let filtered: TeamEatsMessage[] = []

  $: {
    filtered = toDisplay(items, debouncedSearchValue, selectedSort)
    // Debug logging to verify sorting
    if (filtered.length > 0) {
      const sortedData = filtered.map((item, index) => {
        const reactions = (item.$lookup?.reactions ?? []) as Array<{ emoji: string, createBy: string }>
        const upvotes = reactions.filter((r: any) => r.emoji === '👍' || r.emoji === '⬆️').length
        const going = reactions.filter((r: any) => r.emoji === GOING_EMOJI).length
        const score = { upvotes, going }
        return {
          position: index + 1,
          name: item.message,
          score,
          created: new Date(item.createdOn ?? 0).toLocaleTimeString()
        }
      })
      console.log(`Team Eats: Sorted by ${selectedSort} (${filtered.length} items):`, sortedData)
    }
  }

  onMount(async () => {
    try {
      loading = true
      error = null
      // Ensure dedicated Team Eats channel exists and current user is a member
      try {
        const client = getClient()
        const currentAccountId = (getCurrentAccount() as any)._id
        const existing = await client.findOne(chunter.class.Channel, { _id: CHANNEL_ID as any })
        if (existing === undefined) {
          await client.createDoc(
            chunter.class.Channel,
            core.space.Space,
            {
              name: 'Team Eats',
              description: '',
              private: false,
              archived: false,
              autoJoin: true,
              members: [currentAccountId],
              owners: []
            },
            CHANNEL_ID as any
          )
        } else {
          if (!(existing.members ?? []).includes(currentAccountId)) {
            await client.update(existing, { $push: { members: currentAccountId } })
          }
        }
      } catch (err) {
        console.warn('TeamEats: ensure channel failed', err)
      }
      query.query(
        activity.class.ActivityMessage,
        { attachedTo: CHANNEL_ID, space: CHANNEL_ID, te_type: TEAM_EATS_TYPE } as any,
        (res: any[]) => {
          if (Array.isArray(res)) {
            // Keep only today's entries
            itemsRaw = Array.isArray(res) ? res : []
            // Clear cache when data updates
            reactionCountCache.clear()
          } else {
            itemsRaw = []
          }
        },
        {
          sort: { createdOn: -1 },
          lookup: {
            _id: {
              reactions: activity.class.Reaction
            }
          }
        } as any
      )
    } catch (e: any) {
      error = e?.message ?? 'Failed to load Team Eats'
    } finally {
      loading = false
    }
  })

  // Inline add form removed in favor of modal-only creation

  async function createPlaceFromModal (e: CustomEvent<{ name: string, isOrdering: boolean, walkTime?: 5 | 10 | 15 | 30, price?: '$' | '$$' | '$$$', mapUrl?: string, menuUrl?: string }>): Promise<void> {
    const { name, isOrdering, walkTime, price, mapUrl, menuUrl } = e.detail
    try {
      const client = getClient()
      await client.addCollection(
        chunter.class.ChatMessage,
        CHANNEL_ID as unknown as Ref<Space>,
        CHANNEL_ID as unknown as Ref<Doc>,
        chunter.class.Channel,
        'messages',
        {
          message: name,
          te_type: TEAM_EATS_TYPE,
          te_isOrdering: isOrdering,
          te_walkTime: walkTime,
          te_price: price,
          mapUrl,
          menuUrl
        } as any
      )
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('TeamEats: failed to create place from modal', e)
    } finally {
      showCreateModal = false
    }
  }

  function walkLabel (m: TeamEatsMessage): string | undefined {
    const w = m.te_walkTime
    if (w === 5) return '5m'
    if (w === 10) return '10m'
    if (w === 15) return '15m'
    if (w === 30) return '30m'
    return undefined
  }

  let mapUrlToShow: string | undefined = undefined
  let detailsItem: TeamEatsMessage | undefined = undefined

  const GOING_EMOJI = '🍽️'

  // Cache reaction counts
  const reactionCountCache = new Map<string, { going: number, up: number }>()

  function getGoingCount (item: TeamEatsMessage): number {
    const cached = reactionCountCache.get(item._id)
    if (cached) return cached.going

    const reactions = (item.$lookup?.reactions ?? [])
    const going = reactions.filter((r) => r.emoji === GOING_EMOJI).length
    const up = reactions.filter((r: any) => r.emoji === '👍' || r.emoji === '⬆️').length

    reactionCountCache.set(item._id, { going, up })
    return going
  }

  function getScoreCache (item: TeamEatsMessage): { up: number, going: number } {
    const cached = reactionCountCache.get(item._id)
    if (cached) return cached

    const reactions = (item.$lookup?.reactions ?? [])
    const going = reactions.filter((r) => r.emoji === GOING_EMOJI).length
    const up = reactions.filter((r: any) => r.emoji === '👍' || r.emoji === '⬆️').length

    const result = { going, up }
    reactionCountCache.set(item._id, result)
    return result
  }

  async function toggleGoing (it: TeamEatsMessage, e?: Event): Promise<void> {
    e?.stopPropagation?.()
    try {
      const client = getClient()
      const currentAccount = getCurrentAccount()

      // Always fetch fresh reactions to ensure we have the latest data
      let current: any[] = []
      if ((it.reactions ?? 0) > 0) {
        current = await client.findAll(activity.class.Reaction, {
          attachedTo: it._id,
          space: it.space
        })
      }

      console.log('Toggling going for item:', it._id, 'current reactions:', current)

      // Check if current user already has this reaction
      const existingReaction = current.find((r: any) =>
        r.emoji === GOING_EMOJI && r.createBy === (currentAccount as any)._id
      )

      if (existingReaction !== undefined) {
        // Remove existing reaction
        console.log('Removing existing reaction:', existingReaction._id)
        await client.remove(existingReaction)
      } else {
        // Enforce single 'going' per day: block if there's already one on another item
        try {
          const todayIds = new Set(items.map((m: any) => m._id))
          const others: any[] = await client.findAll(activity.class.Reaction, {
            emoji: GOING_EMOJI,
            createBy: (currentAccount as any)._id
          })
          const alreadyOnOther = others.find((r: any) => r.attachedTo !== it._id && todayIds.has(r.attachedTo))
          if (alreadyOnOther !== undefined) {
            addNotification(
              'Team Eats',
              "You can't select two places for the same day. Remove your previous 'going' first.",
              SimpleTextNotification,
              undefined,
              NotificationSeverity.Warning,
              'team-eats-going-limit'
            )
            return
          }
        } catch (err) {
          console.warn('Failed to check previous going reactions (will continue):', err)
        }

        // Add new reaction
        console.log('Adding new reaction')
        await client.addCollection(
          activity.class.Reaction,
          it.space,
          it._id,
          it._class,
          'reactions',
          {
            emoji: GOING_EMOJI,
            createBy: (currentAccount as any)._id
          }
        )
      }

      console.log('Successfully toggled going reaction')

      // Force refresh the query to see updated reactions
      // Note: query.refresh() doesn't exist, we need to re-run the query
      // Clear cache before refresh
      reactionCountCache.clear()
      query.query(
        activity.class.ActivityMessage,
        { attachedTo: CHANNEL_ID, space: CHANNEL_ID, te_type: TEAM_EATS_TYPE } as any,
        (res: any[]) => {
          if (Array.isArray(res)) {
            itemsRaw = Array.isArray(res) ? res : []
            console.log('Refreshed items after reaction toggle:', itemsRaw.length)
          }
        },
        {
          sort: { createdOn: -1 },
          lookup: {
            _id: {
              reactions: activity.class.Reaction
            }
          }
        } as any
      )
    } catch (error) {
      console.error('Failed to toggle going reaction:', error)
    }
  }

  // Remove async from actions and fix any trailing spaces
  function showOptionsMenu (e: MouseEvent): void {
    const actions: Action[] = [
      {
        label: dailyPriorities.string.SearchPlaces,
        icon: IconSearch,
        action: async () => {
          showSearchInput = !showSearchInput
          if (showSearchInput) {
            // Focus search input after it appears
            setTimeout(() => {
              const searchInput = document.querySelector('.team-eats-search input') as HTMLInputElement
              searchInput?.focus()
            }, 100)
          } else {
            searchValue = ''
          }
        }
      },
      {
        label: dailyPriorities.string.MostVoted,
        icon: selectedSort === 'mostVoted' ? IconCheck : undefined,
        action: async () => { selectedSort = 'mostVoted' },
        group: 'sort'
      },
      {
        label: dailyPriorities.string.MostGoing,
        icon: selectedSort === 'mostGoing' ? IconCheck : undefined,
        action: async () => { selectedSort = 'mostGoing' },
        group: 'sort'
      },
      {
        label: dailyPriorities.string.Newest,
        icon: selectedSort === 'newest' ? IconCheck : undefined,
        action: async () => { selectedSort = 'newest' },
        group: 'sort'
      }
    ]

    showPopup(
      Menu,
      { actions },
      e.target as HTMLElement
    )
  }

</script>

<div class="teameats" style={`height:${height}; width:${width}`}>
  <Header
    allowFullsize={false}
    type="type-aside"
    hideBefore={true}
    hideActions={false}
    hideDescription={true}
    hideExtra={true}
    adaptive="disabled"
    closeOnEscape={false}
    on:close={() => dispatch('close')}
  >
    <div class="title">
      <Label label={dailyPriorities.string.TeamEats} />
      <span class="sort-indicator">
        {#if selectedSort === 'mostVoted'}
          (Most Voted)
        {:else if selectedSort === 'mostGoing'}
          (Most Going)
        {:else}
          (Newest)
        {/if}
      </span>
    </div>
    <svelte:fragment slot="actions">
      <div class="header-actions">
        <ButtonIcon icon={IconMoreH} size="small" kind="tertiary" tooltip={{ nonIntlLabel: 'Options' }} on:click={showOptionsMenu} />
        <ButtonIcon icon={IconAdd} size="small" kind="primary" tooltip={{ label: dailyPriorities.string.AddPlace }} on:click={() => { showCreateModal = true }} />
      </div>
    </svelte:fragment>
  </Header>

  {#if showSearchInput}
    <div class="search-section team-eats-search">
      <SearchInput bind:value={searchValue} placeholder={dailyPriorities.string.SearchPlaces} />
    </div>
  {/if}

  <div class="content-body">
    {#if loading}
      <div class="content"><Loading shrink><Label label={dailyPriorities.string.Loading} /></Loading></div>
    {:else if error}
      <div class="content error">{error}</div>
    {:else}
      {#if items.length === 0}
        <SectionEmpty icon={IconCalendar} label={dailyPriorities.string.NoTeamEatsYet} />
      {:else}
        {#if filtered.length === 0 && searchValue.trim().length > 0}
          <SectionEmpty icon={IconSearch} label={dailyPriorities.string.NoResults} />
        {/if}
        {#key selectedSort + searchValue + filtered.length}
          <ListView count={filtered.length} addClass={'clean-card'}>
            <svelte:fragment slot="item" let:item={item}>
              {#each [filtered[item]] as it}
                {#each [getGoingCount(it)] as goingCount}
                  <div class="place-item" role="button" tabindex="0" on:click={() => { detailsItem = it }} on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); detailsItem = it } }}>
                    <div class="place-content">
                      <div class="place-main">
                        <div class="place-name">
                          <MessageViewer message={it.message} />
                        </div>
                        <div class="place-indicators">
                          {#if it.te_isOrdering}
                            <span class="indicator ordering">📦 Ordering</span>
                          {/if}
                          {#if walkLabel(it)}
                            <span class="indicator walk">🚶 {walkLabel(it)}</span>
                          {/if}
                          {#if it.te_price}
                            <span class="indicator price">{it.te_price}</span>
                          {/if}
                        </div>
                      </div>
                      <div class="place-actions">
                        <div class="reactions-section">
                          <ReactionsPresenter object={it} readonly={true} />
                        </div>
                        <div class="going-btn-wrapper">
                          <ButtonBase
                            type="type-button"
                            kind={goingCount > 0 ? 'primary' : 'secondary'}
                            size="small"
                            on:click={async (e) => {
                              console.log('Going button clicked for:', it.message)
                              await toggleGoing(it, e)
                            }}
                            tooltip={{ nonIntlLabel: "I'm going" }}
                          >
                            <div class="going-btn-content">
                              <span class="emoji">{GOING_EMOJI}</span>
                              {#if goingCount > 0}
                                <span class="count">{goingCount}</span>
                              {/if}
                            </div>
                          </ButtonBase>
                        </div>
                      </div>
                    </div>
                  </div>
                {/each}
              {/each}
            </svelte:fragment>
          </ListView>
        {/key}
      {/if}
    {/if}
  </div>
  {#if mapUrlToShow}
    <MapPreviewModal url={mapUrlToShow} on:close={() => { mapUrlToShow = undefined }} />
  {/if}
</div>

{#if showCreateModal}
  <TeamEatsPlaceModal on:close={() => { showCreateModal = false }} on:create={createPlaceFromModal} />
{/if}

{#if detailsItem}
  <TeamEatsDetailsModal
    item={detailsItem}
    on:close={() => { detailsItem = undefined }}
    on:showMap={(e) => { mapUrlToShow = e.detail.url; detailsItem = undefined }}
  />
{/if}

<style lang="scss">
  .teameats {
    display: flex;
    flex-direction: column;
  }
  .content.error {
    color: var(--theme-danger-color);
    font-size: 0.9rem;
  }
  :global(.list-item.clean-card) {
    padding: 0;
    border: none;
    background: none;
    margin: 0;
  }
  :global(.list-item.clean-card + .list-item.clean-card) { margin-top: 0.5rem; }

  .place-item {
    padding: 1rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    background-color: var(--theme-comp-BackgroundColor);
    cursor: pointer;
    transition: all 120ms ease;
  }
  .place-item:hover {
    background-color: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.03));
    border-color: var(--theme-button-border);
    box-shadow: 0 1px 3px rgba(0,0,0,0.06);
  }

  .place-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
  }

  .place-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .place-name {
    font-weight: 600;
    color: var(--theme-caption-color);
    font-size: 1rem;
  }

  .place-indicators {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .indicator {
    font-size: 0.75rem;
    color: var(--theme-content-color);
    background-color: var(--theme-button-default);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
  }

  .indicator.ordering {
    background-color: var(--primary-button-default);
    color: var(--primary-button-color);
  }

  .place-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .reactions-section {
    display: flex;
    align-items: center;
  }

  .going-btn-wrapper {
    display: flex;
    align-items: center;
  }

  .going-btn-content {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .emoji {
    font-size: 1rem;
    line-height: 1;
  }

  .count {
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 1rem;
    text-align: center;
  }
  .content-body {
    padding: 0.25rem 0.5rem 0.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }
  .header-actions { display: flex; align-items: center; gap: .5rem; }

  .title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .sort-indicator {
    font-size: 0.75rem;
    color: var(--theme-content-color);
    font-weight: 400;
    opacity: 0.8;
  }

  .search-section {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color);
  }
</style>
