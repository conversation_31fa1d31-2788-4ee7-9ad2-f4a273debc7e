<script lang="ts">
  import { <PERSON>View, Label, ButtonBase, showPopup, IconClose, IconEdit, EditBox, addNotification, NotificationSeverity } from '@hcengineering/ui'
  import { getDay } from '@hcengineering/core'
  import type { TimeEntry } from '@hcengineering/dailypriorities'
  import dailyPriorities from '../plugin'
  import { getClient } from '@hcengineering/presentation'
  import SimpleTextNotification from './notifications/SimpleTextNotification.svelte'

  export let entries: TimeEntry[] = []

  const client = getClient()
  let selectedEntry: TimeEntry | null = null
  let showStatsModal = false
  let showEditModal = false
  let editingTitle = ''
  let editingEntry: TimeEntry | null = null

  $: sorted = [...entries].sort((a, b) => (b.startTime ?? 0) - (a.startTime ?? 0))

  function dateLabel (ts: number): string {
    const d = new Date(ts)
    return d.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' })
  }

  function formatDuration (seconds: number): string {
    const h = Math.floor(seconds / 3600)
    const m = Math.floor((seconds % 3600) / 60)
    if (h > 0) return `${h}h ${m}m`
    return `${m}m`
  }

  function formatTimeRange (start: number, end?: number): string {
    const startDate = new Date(start)
    const endDate = end ? new Date(end) : new Date()
    const startTime = startDate.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })
    const endTime = endDate.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })
    return `${startTime} - ${endTime}`
  }

  $: groups = sorted.reduce((acc, e) => {
    const day = getDay(e.startTime)
    let g = acc.find((x) => x.day === day)
    if (g == null) {
      g = { day, dateLabel: dateLabel(e.startTime), items: [] as TimeEntry[] }
      acc.push(g)
    }
    g.items.push(e)
    return acc
  }, [] as Array<{ day: number, dateLabel: string, items: TimeEntry[] }>)

  function totalDuration (items: TimeEntry[]): number {
    return items.reduce((sum, it) => sum + (it.duration ?? Math.floor((Date.now() - it.startTime) / 1000)), 0)
  }

  function showStats (): void {
    showStatsModal = true
  }

  function closeStats (): void {
    showStatsModal = false
  }

  function exportToCSV (): void {
    const csvData = [['Date', 'Task', 'Start Time', 'End Time', 'Duration (hours)']]
    
    sorted.forEach((entry) => {
      const startDate = new Date(entry.startTime)
      const endDate = entry.endTime ? new Date(entry.endTime) : new Date()
      const durationHours = ((entry.duration ?? 0) / 3600).toFixed(2)
      
      csvData.push([
        startDate.toLocaleDateString(),
        entry.title,
        startDate.toLocaleTimeString(),
        endDate.toLocaleTimeString(),
        durationHours
      ])
    })

    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `timetracker-history-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)

    addNotification(
      'Time Tracker',
      'History exported to CSV',
      SimpleTextNotification,
      undefined,
      NotificationSeverity.Success
    )
  }

  function openEditModal (entry: TimeEntry): void {
    editingEntry = entry
    editingTitle = entry.title
    showEditModal = true
  }

  function closeEditModal (): void {
    showEditModal = false
    editingEntry = null
    editingTitle = ''
  }

  async function saveEdit (): Promise<void> {
    if (!editingEntry || !editingTitle.trim()) return

    try {
      await client.update(editingEntry, { title: editingTitle.trim() })
      addNotification(
        'Time Tracker',
        'Entry updated successfully',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Success
      )
      closeEditModal()
    } catch (e) {
      console.error('Failed to update entry:', e)
      addNotification(
        'Time Tracker',
        'Failed to update entry',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Error
      )
    }
  }

  $: totalHours = (sorted.reduce((sum, e) => sum + (e.duration ?? 0), 0) / 3600).toFixed(1)
  $: uniqueDays = [...new Set(sorted.map(e => getDay(e.startTime)))].length
  $: avgHoursPerDay = uniqueDays > 0 ? (parseFloat(totalHours) / uniqueDays).toFixed(1) : '0'
</script>

<div class="history-popup">
  <div class="popup-header">
    <div class="header-left">
      <h2 class="header-title"><Label label={dailyPriorities.string.ViewHistory} /></h2>
      <div class="header-stats">
        <span class="stat-badge">{totalHours}h total</span>
        <span class="stat-badge">{entries.length} <Label label={dailyPriorities.string.Entries} /></span>
      </div>
    </div>
    <div class="header-actions">
      <ButtonBase
        type="type-button"
        kind="secondary"
        size="small"
        label={dailyPriorities.string.Statistics}
        on:click={showStats}
      />
      <ButtonBase
        type="type-button"
        kind="secondary"
        size="small"
        label={dailyPriorities.string.ExportToCSV}
        on:click={exportToCSV}
      />
    </div>
  </div>

  <div class="popup-body">
    {#each groups as g}
      <div class="day-group">
        <div class="day-header">
          <div class="day-date">{g.dateLabel}</div>
          <div class="day-total">{formatDuration(totalDuration(g.items))}</div>
        </div>
        <div class="day-entries">
          {#each g.items as entry}
            <button class="entry-card" on:click={() => openEditModal(entry)}>
              <div class="entry-content">
                <div class="entry-title">{entry.title}</div>
                <div class="entry-meta">
                  {formatTimeRange(entry.startTime, entry.endTime)} · 
                  <Label label={dailyPriorities.string.Duration} />: {formatDuration(entry.duration ?? 0)}
                </div>
              </div>
              <div class="entry-action">
                <IconEdit size="small" />
              </div>
            </button>
          {/each}
        </div>
      </div>
    {/each}
  </div>
</div>

{#if showStatsModal}
  <div class="modal-overlay" on:click={closeStats}>
    <div class="modal-content" on:click={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h3><Label label={dailyPriorities.string.TimeStatistics} /></h3>
        <button class="modal-close" on:click={closeStats}>
          <IconClose size="small" />
        </button>
      </div>
      <div class="modal-body">
        <div class="stat-grid">
          <div class="stat-item">
            <div class="stat-label"><Label label={dailyPriorities.string.TotalHours} /></div>
            <div class="stat-value">{totalHours}h</div>
          </div>
          <div class="stat-item">
            <div class="stat-label"><Label label={dailyPriorities.string.Entries} /></div>
            <div class="stat-value">{entries.length}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label"><Label label={dailyPriorities.string.AveragePerDay} /></div>
            <div class="stat-value">{avgHoursPerDay}h</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">Days tracked</div>
            <div class="stat-value">{uniqueDays}</div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <ButtonBase
          type="type-button"
          kind="primary"
          size="medium"
          label={dailyPriorities.string.Close}
          on:click={closeStats}
        />
      </div>
    </div>
  </div>
{/if}

{#if showEditModal && editingEntry}
  <div class="modal-overlay" on:click={closeEditModal}>
    <div class="modal-content" on:click={(e) => e.stopPropagation()}>
      <div class="modal-header">
        <h3><Label label={dailyPriorities.string.EditEntry} /></h3>
        <button class="modal-close" on:click={closeEditModal}>
          <IconClose size="small" />
        </button>
      </div>
      <div class="modal-body">
        <div class="edit-field">
          <label class="field-label">Task Name</label>
          <EditBox
            bind:value={editingTitle}
            placeholder="Task name"
            maxWidth="100%"
          />
        </div>
        <div class="edit-info">
          <div class="info-row">
            <span class="info-label">Date:</span>
            <span>{dateLabel(editingEntry.startTime)}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Time:</span>
            <span>{formatTimeRange(editingEntry.startTime, editingEntry.endTime)}</span>
          </div>
          <div class="info-row">
            <span class="info-label"><Label label={dailyPriorities.string.Duration} />:</span>
            <span>{formatDuration(editingEntry.duration ?? 0)}</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <ButtonBase
          type="type-button"
          kind="tertiary"
          size="medium"
          label={dailyPriorities.string.EditCancel}
          on:click={closeEditModal}
        />
        <ButtonBase
          type="type-button"
          kind="primary"
          size="medium"
          label={dailyPriorities.string.SaveChanges}
          disabled={!editingTitle.trim()}
          on:click={saveEdit}
        />
      </div>
    </div>
  </div>
{/if}

<style lang="scss">
  .history-popup {
    display: flex;
    flex-direction: column;
    min-width: 600px;
    max-width: 800px;
    max-height: 80vh;
    background: var(--theme-comp-BackgroundColor);
    border-radius: 0.75rem;
    overflow: hidden;
  }

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
    background: var(--theme-ui-BackgroundColor);
    gap: 1rem;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
  }

  .header-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .header-stats {
    display: flex;
    gap: 0.5rem;
  }

  .stat-badge {
    font-size: 0.75rem;
    color: var(--theme-content-color);
    background-color: var(--theme-button-default);
    padding: 0.25rem 0.625rem;
    border-radius: 1rem;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 0.5rem;
  }

  .popup-body {
    padding: 1rem 1.5rem 1.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
    background: var(--theme-bg-color, var(--theme-comp-BackgroundColor));
  }

  .day-group {
    margin-bottom: 1.5rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .day-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    background: var(--theme-ui-BackgroundColor);
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .day-date {
    font-size: 0.9375rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .day-total {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--theme-content-color);
  }

  .day-entries {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .entry-card {
    all: unset;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.875rem 1rem;
    background: var(--theme-comp-BackgroundColor);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 150ms ease;

    &:hover {
      background: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.03));
      border-color: var(--theme-button-border);
      box-shadow: 0 2px 6px rgba(0,0,0,0.08);
      
      .entry-action {
        opacity: 1;
      }
    }
  }

  .entry-content {
    flex: 1;
    text-align: left;
  }

  .entry-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
    margin-bottom: 0.25rem;
  }

  .entry-meta {
    font-size: 0.8125rem;
    color: var(--theme-content-color);
  }

  .entry-action {
    opacity: 0;
    color: var(--theme-content-color);
    transition: opacity 150ms ease;
  }

  .modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 1rem;
  }

  .modal-content {
    background: var(--theme-popup-color, var(--theme-comp-header-color, #fff));
    border: 1px solid var(--theme-popup-divider, var(--theme-divider-color, rgba(0,0,0,0.1)));
    border-radius: 0.75rem;
    min-width: 400px;
    max-width: 600px;
    box-shadow: 0 12px 32px rgba(0,0,0,0.25), 0 2px 8px rgba(0,0,0,0.15);
    display: flex;
    flex-direction: column;
    max-height: 80vh;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--theme-divider-color);

    h3 {
      margin: 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--theme-caption-color);
    }
  }

  .modal-close {
    all: unset;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    cursor: pointer;
    border-radius: 0.25rem;
    color: var(--theme-content-color);
    transition: all 120ms ease;

    &:hover {
      background: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.05));
      color: var(--theme-caption-color);
    }
  }

  .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
  }

  .modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--theme-divider-color);
  }

  .stat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-item {
    padding: 1.25rem;
    background: var(--theme-ui-BackgroundColor);
    border-radius: 0.5rem;
    border: 1px solid var(--theme-divider-color);
  }

  .stat-label {
    font-size: 0.8125rem;
    color: var(--theme-content-color);
    margin-bottom: 0.5rem;
  }

  .stat-value {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .edit-field {
    margin-bottom: 1.5rem;
  }

  .field-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--theme-content-color);
    margin-bottom: 0.5rem;
  }

  .edit-info {
    padding: 1rem;
    background: var(--theme-ui-BackgroundColor);
    border-radius: 0.5rem;
    border: 1px solid var(--theme-divider-color);
  }

  .info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
    font-size: 0.875rem;
    
    &:not(:last-child) {
      border-bottom: 1px solid var(--theme-divider-color);
    }
  }

  .info-label {
    font-weight: 500;
    color: var(--theme-content-color);
  }
</style>
