<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import type { Widget } from '@hcengineering/workbench'
  import dailyPriorities from '../plugin'
  import { onMount, onDestroy, createEventDispatcher } from 'svelte'
  import { Label, Loading, SectionEmpty, IconCalendar, ListView, Header, ButtonBase, EditBox, IconDelete, showPopup, addNotification, NotificationSeverity, Icon } from '@hcengineering/ui'
  import { getClient, createQuery } from '@hcengineering/presentation'
  import { ObjectBox } from '@hcengineering/view-resources'
  import { getDay, type Ref, type Doc } from '@hcengineering/core'
  import type { TimeEntry } from '@hcengineering/dailypriorities'
  import contact, { getCurrentEmployee, type Person } from '@hcengineering/contact'
  import core from '@hcengineering/core'
  import task from '@hcengineering/task'
  import tracker from '@hcengineering/tracker'
  import TimeTrackerHistoryPopup from './TimeTrackerHistoryPopup.svelte'
  import SimpleTextNotification from './notifications/SimpleTextNotification.svelte'
  import view from '@hcengineering/view'

  export let widget: Widget | undefined
  export let height: string
  export let width: string
  $: void widget

  const dispatch = createEventDispatcher()
  const client = getClient()
  const currentEmployeeRef = getCurrentEmployee()
  const currentPersonRef: Ref<Person> | undefined = currentEmployeeRef as unknown as Ref<Person> | undefined

  let loading = true
  let error: string | null = null
  let entries: TimeEntry[] = []
  let activeEntry: TimeEntry | null = null
  let taskName = ''
  let selectedTask: Ref<Doc> | undefined = undefined
  let selectedTaskObject: Doc | undefined = undefined
  let elapsedSeconds = 0
  let timerInterval: any = null
  let historyPopupOpen = false

  const query = createQuery()
  const taskQuery = createQuery()

  $: if (selectedTask) {
    taskQuery.query(tracker.class.Issue, { _id: selectedTask }, (res) => {
      selectedTaskObject = res[0]
    })
  } else {
    selectedTaskObject = undefined
  }

  $: todayEntries = entries.filter((e) => getDay(e.startTime) === getDay(Date.now()))
  $: totalSeconds = todayEntries.reduce((sum, e) => sum + (e.duration ?? 0), 0)

  function formatTime (seconds: number): string {
    const h = Math.floor(seconds / 3600)
    const m = Math.floor((seconds % 3600) / 60)
    const s = seconds % 60
    return `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}:${String(s).padStart(2, '0')}`
  }

  function formatDuration (seconds: number): string {
    const h = Math.floor(seconds / 3600)
    const m = Math.floor((seconds % 3600) / 60)
    if (h > 0) return `${h}h ${m}m`
    return `${m}m`
  }

  function formatTimeRange (start: number, end?: number): string {
    const startDate = new Date(start)
    const endDate = end ? new Date(end) : new Date()
    const startTime = startDate.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })
    const endTime = endDate.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })
    return `${startTime} - ${endTime}`
  }

  async function startTimer (): Promise<void> {
    if (currentPersonRef === undefined) {
      error = dailyPriorities.string.TimeTrackerUnavailable
      return
    }
    if (!taskName.trim()) {
      addNotification(
        'Time Tracker',
        'Please enter a task name',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Warning
      )
      return
    }

    try {
      const data: any = {
        title: taskName.trim(),
        startTime: Date.now(),
        user: currentPersonRef
      }
      
      if (selectedTask) {
        data.task = selectedTask
      }

      const newEntry = await client.createDoc(
        dailyPriorities.class.TimeEntry,
        core.space.Space,
        data
      )
      activeEntry = await client.findOne(dailyPriorities.class.TimeEntry, { _id: newEntry }) as any
      elapsedSeconds = 0
      taskName = ''
      selectedTask = undefined

      // Start timer update
      timerInterval = setInterval(() => {
        if (activeEntry) {
          elapsedSeconds = Math.floor((Date.now() - activeEntry.startTime) / 1000)
        }
      }, 1000)
    } catch (e) {
      console.error('Failed to start timer:', e)
      error = 'Failed to start timer'
    }
  }

  async function resumeEntry (entry: TimeEntry): Promise<void> {
    if (currentPersonRef === undefined) return
    if (activeEntry) {
      addNotification(
        'Time Tracker',
        'Stop current timer before resuming another task',
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Warning
      )
      return
    }

    try {
      const data: any = {
        title: entry.title,
        startTime: Date.now(),
        user: currentPersonRef
      }
      
      if (entry.task) {
        data.task = entry.task
      }

      const newEntry = await client.createDoc(
        dailyPriorities.class.TimeEntry,
        core.space.Space,
        data
      )
      activeEntry = await client.findOne(dailyPriorities.class.TimeEntry, { _id: newEntry }) as any
      elapsedSeconds = 0

      // Start timer update
      timerInterval = setInterval(() => {
        if (activeEntry) {
          elapsedSeconds = Math.floor((Date.now() - activeEntry.startTime) / 1000)
        }
      }, 1000)

      addNotification(
        'Time Tracker',
        `Resumed: ${entry.title}`,
        SimpleTextNotification,
        undefined,
        NotificationSeverity.Success
      )
    } catch (e) {
      console.error('Failed to resume timer:', e)
    }
  }

  async function stopTimer (): Promise<void> {
    if (!activeEntry) return

    try {
      const duration = Math.floor((Date.now() - activeEntry.startTime) / 1000)
      await client.update(activeEntry, {
        endTime: Date.now(),
        duration
      })

      activeEntry = null
      elapsedSeconds = 0
      if (timerInterval) {
        clearInterval(timerInterval)
        timerInterval = null
      }
    } catch (e) {
      console.error('Failed to stop timer:', e)
      error = 'Failed to stop timer'
    }
  }

  async function deleteEntry (entry: TimeEntry): Promise<void> {
    try {
      await client.remove(entry)
    } catch (e) {
      console.error('Failed to delete entry:', e)
    }
  }

  function openHistory (): void {
    if (historyPopupOpen || entries.length === 0) return
    historyPopupOpen = true
    showPopup(
      TimeTrackerHistoryPopup,
      {
        entries
      },
      undefined,
      undefined,
      () => {
        historyPopupOpen = false
      }
    )
  }

  onMount(async () => {
    try {
      loading = true
      error = null

      if (currentPersonRef === undefined) {
        error = dailyPriorities.string.TimeTrackerUnavailable
        return
      }

      // Check for active timer
      const active = await client.findOne(
        dailyPriorities.class.TimeEntry,
        {
          user: currentPersonRef,
          endTime: { $exists: false }
        }
      ) as any

      if (active) {
        activeEntry = active
        elapsedSeconds = Math.floor((Date.now() - (active.startTime ?? Date.now())) / 1000)
        timerInterval = setInterval(() => {
          if (activeEntry) {
            elapsedSeconds = Math.floor((Date.now() - activeEntry.startTime) / 1000)
          }
        }, 1000)
      }

      // Query all entries for current user
      query.query(
        dailyPriorities.class.TimeEntry,
        { user: currentPersonRef },
        (res) => {
          entries = res as any[]
        },
        { sort: { startTime: -1 } }
      )
    } catch (e: any) {
      error = e?.message ?? 'Failed to load time entries'
    } finally {
      loading = false
    }
  })

  onDestroy(() => {
    if (timerInterval) {
      clearInterval(timerInterval)
    }
  })
</script>

<div class="timetracker" style={`height:${height}; width:${width}`}>
  <Header
    allowFullsize={false}
    type="type-aside"
    hideBefore={true}
    hideActions={false}
    hideDescription={true}
    hideExtra={true}
    adaptive="disabled"
    closeOnEscape={false}
    on:close={() => dispatch('close')}
  >
    <div class="header-content">
      <div class="title">
        <Label label={dailyPriorities.string.TimeTracker} />
        {#if totalSeconds > 0}
          <span class="total-badge">{formatDuration(totalSeconds)}</span>
        {/if}
      </div>
      <ButtonBase
        type="type-button"
        kind="tertiary"
        size="small"
        label={dailyPriorities.string.ViewHistory}
        disabled={entries.length === 0}
        on:click={openHistory}
      />
    </div>
  </Header>

  <div class="content-body">
    {#if loading}
      <div class="content">
        <Loading shrink>
          <Label label={dailyPriorities.string.Loading} />
        </Loading>
      </div>
    {:else if error}
      <div class="content error">{error}</div>
    {:else}
      <div class="timer-section">
        {#if activeEntry}
          <div class="active-timer">
            <div class="timer-display">{formatTime(elapsedSeconds)}</div>
            <div class="timer-info">
              <div class="timer-label">
                <Label label={dailyPriorities.string.CurrentlyWorking} />
              </div>
              <div class="timer-task">{activeEntry.title}</div>
              {#if activeEntry.task}
                {@const taskDoc = entries.find(e => e._id === activeEntry._id)}
                <div class="timer-task-ref">
                  <ObjectBox
                    _class={tracker.class.Issue}
                    value={activeEntry.task}
                    kind={'link'}
                    size={'small'}
                    readonly
                    showNavigate={true}
                    docProps={{ showTitle: true, shouldShowAvatar: false }}
                  />
                </div>
              {/if}
            </div>
            <ButtonBase
              type="type-button"
              kind="negative"
              size="large"
              label={dailyPriorities.string.StopTimer}
              on:click={stopTimer}
            />
          </div>
        {:else}
          <div class="start-timer">
            <div class="start-row">
              <div class="start-input">
                <EditBox
                  bind:value={taskName}
                  placeholder={dailyPriorities.string.ActiveTask}
                  fullSize
                  maxWidth="100%"
                  on:keydown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      startTimer()
                    }
                  }}
                />
              </div>
              <div class="start-button">
                <ButtonBase
                  type="type-button"
                  kind="primary"
                  size="small"
                  label={dailyPriorities.string.StartTimer}
                  disabled={!taskName.trim()}
                  on:click={startTimer}
                />
              </div>
            </div>

            <div class="link-row">
              <ObjectBox
                _class={tracker.class.Issue}
                bind:value={selectedTask}
                kind={'no-border'}
                size={'small'}
                searchField={'title'}
                allowDeselect
                showNavigate={false}
                placeholder={dailyPriorities.string.NoTaskSelected}
                label={dailyPriorities.string.SelectTask}
                docProps={{ showTitle: true, shouldShowAvatar: false }}
              />
            </div>
          </div>
        {/if}
      </div>

      <div class="entries-section">
        {#if todayEntries.length === 0}
          <SectionEmpty icon={IconCalendar} label={dailyPriorities.string.NoEntriesYet} />
        {:else}
          <ListView count={todayEntries.length} addClass={'card bordered'}>
            <svelte:fragment slot="item" let:item={item}>
              {@const entry = todayEntries[item]}
              <div class="entry-row">
                <div class="entry-main">
                  <div class="entry-title">{entry.title}</div>
                  {#if entry.task}
                    <div class="entry-task-ref">
                      <ObjectBox
                        _class={tracker.class.Issue}
                        value={entry.task}
                        kind={'link'}
                        size={'small'}
                        readonly
                        showNavigate={true}
                        docProps={{ showTitle: true, shouldShowAvatar: false }}
                      />
                    </div>
                  {/if}
                  <div class="entry-meta">
                    {formatTimeRange(entry.startTime, entry.endTime)} ·
                    <Label label={dailyPriorities.string.Duration} />: {formatDuration(entry.duration ?? 0)}
                  </div>
                </div>
                <div class="entry-actions">
                  {#if !activeEntry}
                    <button
                      class="action-btn resume-icon"
                      title="Resume"
                      on:click={() => resumeEntry(entry)}
                    >
                      <Icon icon={view.icon.ArrowRight} size="small" />
                    </button>
                  {/if}
                  <button
                    class="action-btn delete-icon"
                    title="Delete"
                    on:click={() => deleteEntry(entry)}
                  >
                    <IconDelete size="small" />
                  </button>
                </div>
              </div>
            </svelte:fragment>
          </ListView>
        {/if}
      </div>
    {/if}
  </div>
</div>

<style lang="scss">
  .timetracker {
    display: flex;
    flex-direction: column;
  }

  .content.error {
    color: var(--theme-danger-color);
    font-size: 0.9rem;
  }

  .content-body {
    padding: 0.25rem 0.5rem 0.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }

  .title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
    width: 100%;
  }

  

  .total-badge {
    font-size: 0.75rem;
    color: var(--theme-content-color);
    background-color: var(--theme-button-default);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 600;
  }

  .timer-section {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    background-color: var(--theme-comp-BackgroundColor);
  }

  .active-timer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .timer-display {
    font-size: 3rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    font-variant-numeric: tabular-nums;
  }

  .timer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
  }

  .timer-label {
    font-size: 0.6875rem;
    font-weight: 500;
    text-transform: uppercase;
    color: var(--theme-dark-color);
    letter-spacing: 0.5px;
  }

  .timer-task {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    text-align: center;
  }

  .timer-task-ref {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.875rem;
  }

  .start-timer {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
  }

  .start-row {
    display: grid;
    grid-template-columns: minmax(0, 1fr) auto;
    gap: 0.5rem;
    align-items: center;
  }

  .start-input :global(.antiEditBoxInput) {
    padding: 0.5rem 0.65rem;
    font-size: 0.9rem;
  }

  .start-button :global(button) {
    min-height: 2rem;
    padding: 0 0.85rem;
  }

  .link-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .link-row :global(button) {
    min-height: 2rem;
    padding: 0 0.5rem;
  }

  .entries-section {
    margin-top: 0.5rem;
  }

  :global(.list-item.card) {
    padding: 0.75rem 0.875rem;
    border: 1px solid var(--theme-button-border);
    border-radius: 0.5rem;
    background-color: var(--theme-comp-BackgroundColor);
    transition: background-color 120ms ease, border-color 120ms ease, box-shadow 120ms ease;
  }

  :global(.list-item.bordered) {
    border-color: var(--theme-divider-color);
  }

  :global(.list-item.card + .list-item.card) {
    margin-top: 0.5rem;
  }

  :global(.list-item.card:hover) {
    background-color: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.03));
    border-color: var(--theme-divider-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.06);
  }

  .entry-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.875rem;
  }

  .entry-main {
    flex: 1;
    min-width: 0;
  }

  .entry-title {
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.25rem;
  }

  .entry-task-ref {
    font-size: 0.8125rem;
    margin-bottom: 0.25rem;
    opacity: 0.9;
  }

  .entry-meta {
    font-size: 0.85rem;
    color: var(--theme-disabled-text-color);
  }

  .entry-actions {
    display: flex;
    gap: 0.25rem;
  }

  .action-btn {
    all: unset;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    cursor: pointer;
    border-radius: 0.25rem;
    color: var(--theme-content-color);
    transition: all 120ms ease;
  }

  .action-btn:hover {
    background-color: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.05));
    color: var(--theme-caption-color);
  }

  .resume-icon {
    color: var(--theme-accent-color, var(--theme-content-color));
    
    &:hover {
      color: var(--theme-caption-color);
      background-color: var(--theme-button-hovered, rgba(0,0,0,0.05));
    }
  }

  .delete-icon {
    color: var(--theme-content-color);
    
    &:hover {
      color: var(--theme-danger-color);
      background-color: var(--theme-ui-hover-BackgroundColor, rgba(0,0,0,0.05));
    }
  }
</style>
