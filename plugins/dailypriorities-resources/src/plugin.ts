//
// Copyright © 2020 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import dailyPriorities, { dailyPrioritiesId } from '@hcengineering/dailypriorities'
import type { Client, Doc, Ref, Space } from '@hcengineering/core'
import type { IntlString, Resource } from '@hcengineering/platform'
import { mergeIds } from '@hcengineering/platform'
import type { AnyComponent } from '@hcengineering/ui/src/types'
import { type ViewAction } from '@hcengineering/view'
import { type DocNotifyContext, type InboxNotification } from '@hcengineering/notification'

export default mergeIds(dailyPrioritiesId, dailyPriorities, {
  component: {
    ChannelHeader: '' as AnyComponent,
    ChannelPanel: '' as AnyComponent,
    ThreadViewPanel: '' as AnyComponent,
    ThreadParentPresenter: '' as AnyComponent,
    ChannelPreview: '' as AnyComponent,
    MessagePreview: '' as AnyComponent,
    CreateDocChannel: '' as AnyComponent,
    Threads: '' as AnyComponent,
    DailyPrioritiesBrowser: '' as AnyComponent,
    ChannelIcon: '' as AnyComponent
  },
  function: {
    GetDmName: '' as Resource<(client: Client, space: Space) => Promise<string>>,
    DirectTitleProvider: '' as Resource<(client: Client, id: Ref<Doc>) => Promise<string>>,
    ChannelTitleProvider: '' as Resource<(client: Client, id: Ref<Doc>) => Promise<string>>,
    DailyPrioritiesBrowserVisible: '' as Resource<(spaces: Space[]) => Promise<boolean>>,
    GetUnreadThreadsCount: '' as Resource<
    (inboxNotificationsByContext: Map<Ref<DocNotifyContext>, InboxNotification[]>) => number
    >
  },
  actionImpl: {
    SubscribeMessage: '' as ViewAction,
    UnsubscribeMessage: '' as ViewAction,
    SubscribeComment: '' as ViewAction,
    UnsubscribeComment: '' as ViewAction,
    LeaveChannel: '' as ViewAction,
    RemoveChannel: '' as ViewAction
  },
  string: {
    OutToday: '' as IntlString,
    SelectDate: '' as IntlString,
    MyProjectsUpdates: '' as IntlString,
    OtherProjectsUpdates: '' as IntlString,
    AllProjectsUpdates: '' as IntlString,
    AllDailyUpdatesName: '' as IntlString,
    NewDailyPriorities: '' as IntlString,
    AddNewDailyPriorities: '' as IntlString,
    UpdateDailyPriorities: '' as IntlString,
    PostDailyPriorities: '' as IntlString,
    YouAreNotAMemberOfAnyProjects: '' as IntlString,
    NoPrioritiesAddedFor: '' as IntlString,
    AddPriority: '' as IntlString,
    PriorityAddedFromLastMessage: '' as IntlString,
    Done: '' as IntlString,
    NotDone: '' as IntlString,
    EmojiText: '' as IntlString,
    SetPriority: '' as IntlString,
    DirectMessage: '' as IntlString,
    DirectMessages: '' as IntlString,
    NewDirectMessage: '' as IntlString,
    ChannelName: '' as IntlString,
    ChannelNamePlaceholder: '' as IntlString,
    ChannelDescription: '' as IntlString,
    About: '' as IntlString,
    Members: '' as IntlString,
    NoMembers: '' as IntlString,
    In: '' as IntlString,
    Replies: '' as IntlString,
    Topic: '' as IntlString,
    Threads: '' as IntlString,
    New: '' as IntlString,
    GetNewReplies: '' as IntlString,
    TurnOffReplies: '' as IntlString,
    PinMessage: '' as IntlString,
    UnpinMessage: '' as IntlString,
    Pinned: '' as IntlString,
    DeleteMessage: '' as IntlString,
    EditMessage: '' as IntlString,
    Edited: '' as IntlString,
    AndYou: '' as IntlString,
    ShowMoreReplies: '' as IntlString,
    AddToSaved: '' as IntlString,
    RemoveFromSaved: '' as IntlString,
    EmptySavedHeader: '' as IntlString,
    EmptySavedText: '' as IntlString,
    SharedBy: '' as IntlString,
    LeaveChannel: '' as IntlString,
    ChannelBrowser: '' as IntlString,
    Saved: '' as IntlString,
    MessagesBrowser: '' as IntlString,
    DailyPrioritiesBrowser: '' as IntlString,
    Messages: '' as IntlString,
    NoResults: '' as IntlString,
    CopyLink: '' as IntlString,
    You: '' as IntlString,
    YouHaveJoinedTheConversation: '' as IntlString,
    NoMessages: '' as IntlString,
    On: '' as IntlString,
    Mentioned: '' as IntlString,
    SentMessage: '' as IntlString,
    PinnedCount: '' as IntlString,
    LoadingHistory: '' as IntlString,
    UnpinChannels: '' as IntlString,
    ArchiveActivityConfirmationTitle: '' as IntlString,
    ArchiveActivityConfirmationMessage: '' as IntlString,
    JoinChannelHeader: '' as IntlString,
    JoinChannelText: '' as IntlString,
    LatestMessages: '' as IntlString,
    ResolveThread: '' as IntlString,
    NoThreadsYet: '' as IntlString,
    ItemsPerPage: '' as IntlString,
    CardsPerRow: '' as IntlString,
    WorkFromHome: '' as IntlString,
    Vacation: '' as IntlString,
    SickLeave: '' as IntlString,
    AbsenceInHours: '' as IntlString,
    Marriage: '' as IntlString,
    Parental: '' as IntlString,
    PassingAwayRelatives: '' as IntlString,
    BloodDonation: '' as IntlString,
    Other: '' as IntlString,
    NoEmployeesOutSubtitle: '' as IntlString,
    NoEmployeesOutSubtitle2: '' as IntlString,
    // Team Eats widget
    TeamEats: '' as IntlString,
    WhereToEat: '' as IntlString,
    AddPlace: '' as IntlString,
    Ordering: '' as IntlString,
    Walk5min: '' as IntlString,
    Walk10min: '' as IntlString,
    NoTeamEatsYet: '' as IntlString,
    SearchPlaces: '' as IntlString,
    MostVoted: '' as IntlString,
    MostGoing: '' as IntlString,
    Newest: '' as IntlString,
    Map: '' as IntlString,
    PlaceDetails: '' as IntlString,
    Going: '' as IntlString,
    PersonGoing: '' as IntlString,
    PeopleGoing: '' as IntlString,
    Links: '' as IntlString,
    ViewOnMap: '' as IntlString,
    OpenLocation: '' as IntlString,
    ViewMenu: '' as IntlString,
    ExternalLink: '' as IntlString,
    NoLinksAvailable: '' as IntlString
    // Time Tracker widget strings are defined in base dailypriorities plugin
    // News widget strings are defined in base dailypriorities plugin
  }
})
