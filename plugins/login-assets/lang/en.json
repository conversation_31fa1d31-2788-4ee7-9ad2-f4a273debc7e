{"status": {"RequiredField": "Required field {field}", "FieldsDoNotMatch": "{field} don't match {field2}", "ConnectingToServer": "Connecting to server....", "IncorrectValue": "Incorrect value {field}"}, "string": {"LogIn": "Log In", "SignUp": "Sign Up", "CreateWorkspace": "Create workspace", "HaveWorkspace": "Already have a workspace?", "LastName": "Last name", "FirstName": "First name", "Join": "Accept invitation to join workspace", "Email": "Email", "Password": "Password", "Workspace": "Workspace name", "DoNotHaveAnAccount": "Do not have an account?", "PasswordRepeat": "Repeat password", "HaveAccount": "Already have an account?", "LoadingAccount": "Loading...", "SelectWorkspace": "Select workspace", "Copy": "Copy", "Copied": "<PERSON>pied", "Close": "Close", "InviteDescription": "Share this link to invite other users", "WantAnotherWorkspace": "Want to create another workspace?", "ChangeAccount": "Change account", "NotSeeingWorkspace": "Not seeing your workspace?", "ForgotPassword": "Forgot your password?", "KnowPassword": "Know your password?", "Recover": "Recover", "PasswordRecovery": "Password recovery", "RecoveryLinkSent": "Password recovery link sent to email", "UseWorkspaceInviteSettings": "Use workspace invite settings", "LinkValidHours": "Link valid (hours):", "EmailMask": "Email mask:", "InviteLimit": "Invite limit:", "GetLink": "Get invite link", "NoLimit": "No limit", "AlreadyJoined": "Already joined?", "ConfirmationSent": "A message has been sent to your email containing a link to confirm your address.", "ConfirmationSent2": "Please follow the link to complete your sign up.", "ContinueWith": "Continue with 91/Matrics", "HaventReceivedCode": "Haven't received the code?", "ResendCode": "Resend code", "WrongEmail": "Wrong email?", "ChangeEmail": "Change email", "SentTo": "We've sent a code to ", "CanFindCode": "Can't find your code? Check your spam folder.", "LoginWithPassword": "Login with password", "LoginWithCode": "Login with code", "SignUpWithPassword": "Sign up with password", "SignUpWithCode": "Sign up with code", "FillInProfile": "Fill in your profile", "SetUpPassword": "Set up your password", "Next": "Next", "Skip": "<PERSON><PERSON>", "SignUpCompleted": "Sign up completed", "StartUsingHuly": "Start using Huly", "PasswordMinLength": "{count, plural, =1 {Password must be at least # character long} other {Password must be at least # characters long}}", "PasswordMinSpecialChars": "{count, plural, =1 {Password must contain at least # special character} other {Password must contain at least # special characters}}", "PasswordMinDigits": "{count, plural, =1 {Password must contain at least # number} other {Password must contain at least # numbers}}", "PasswordMinUpperChars": "{count, plural, =1 {Password must contain at least # uppercase letter} other {Password must contain at least # uppercase letters}}", "PasswordMinLowerChars": "{count, plural, =1 {Password must contain at least # lowercase letter} other {Password must contain at least # lowercase letters}}", "WorkspaceArchivedDesc": "Workspace is archived because of being unused.", "RestoreArchivedWorkspace": "Unarchive", "Hello": "Hello {name},", "ProcessingInvite": "Processing invite, please wait...", "SignToProceed": "Please sign in to proceed", "Proceed": "Proceed", "SetPasswordLater": "I'll set a password later", "SetPasswordNow": "I'll set a password now", "LoginAsGuest": "Continue as a guest", "SignUpToCreateWorkspace": "There are no workspaces with guest access. Please sign up to create your own.", "WhatIsYourName": "What is your name?", "AccessExpired": "Access time expired. Please ask for a new access link.", "AccessNotActive": "Access time hasn't started yet. It is starting in: "}}