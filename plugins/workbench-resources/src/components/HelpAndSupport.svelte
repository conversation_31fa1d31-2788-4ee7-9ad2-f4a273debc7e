<!--
// Copyright © 2023 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Asset, IntlString } from '@hcengineering/platform'
  import { getClient } from '@hcengineering/presentation'
  import setting, { settingId } from '@hcengineering/setting'
  import support, { docsLink, reportBugLink, supportLink, privacyPolicyLink } from '@hcengineering/support'
  import {
    AnySvelteComponent,
    Button,
    Icon,
    IconArrowLeft,
    Label,
    ListView,
    Scroller,
    capitalizeFirstLetter,
    closePopup,
    formatKey,
    getCurrentResolvedLocation,
    navigate,
    topSP
  } from '@hcengineering/ui'
  import view, { Action, ActionCategory } from '@hcengineering/view'
  import workbench from '../plugin'
  import RightArrowIcon from './icons/Collapsed.svelte'
  import DocumentationIcon from './icons/Documentation.svelte'
  import KeyboardIcon from './icons/Keyboard.svelte'
  import { WorkbenchEvents } from '@hcengineering/workbench'
  import { Analytics } from '@hcengineering/analytics'
  import { userManuals } from '../userManuals'

  let shortcuts = false
  let currentManual: string | null = null
  let actions: Action[] = []
  let categories: ActionCategory[] = []
  let selection: number = 0
  let manualSelection: number = 0

  $: filteredManuals = userManuals.filter(
    (category) => category.type === currentManual && category.class === currentApp
  )
  $: flatManualItems = filteredManuals.flatMap(category =>
    category.steps.flatMap(step =>
      step.instructions.map(instruction => ({
        stepTitle: step.title,
        instruction,
        category: category.type
      }))
    )
  )

  const client = getClient()

  const currentApp = getCurrentAppAlias()

  function getCurrentAppAlias (): string | undefined {
    const loc = getCurrentResolvedLocation()
    return loc.path[2] as string | undefined
  }

  function navigateToSettings () {
    closePopup()
    const loc = getCurrentResolvedLocation()
    loc.path[2] = loc.path[3] = settingId
    loc.path.length = 4
    navigate(loc)
  }

  function getManualTitle (manual: string): IntlString {
    switch (manual) {
      case workbench.string.UserManuals: return workbench.string.UserManuals
      default: return workbench.string.HelpCenter
    }
  }

  async function getActions (): Promise<void> {
    categories = await getClient().findAll(view.class.ActionCategory, [])
    const rawActions = await client.findAll(view.class.Action, [])

    // Find the category for the current app
    const currentAppCategory = categories.find((cat) => {
      return currentApp && cat._id.includes(currentApp)
    })

    // Filter out general categories (not plugin-specific)
    const generalCategories = categories.filter((cat) => cat._id.startsWith('view:'))

    const openAction = rawActions.find(
      (action) => action.label === view.string.Open && action.category === view.category.General
    )
    const deleteAction = rawActions.find(
      (action) => action.label === view.string.Delete && action.category === view.category.General
    )

    actions = rawActions.filter((action) => {
      if (!action.keyBinding || action.keyBinding.length === 0) return false
      if (action.label === view.string.Open || action.label === view.string.Delete) return false

      // Always display actions in general categories (not plugin-specific)
      if (generalCategories.find(cat => cat._id === action.category)) return true

      // Only display actions in the current app's category
      if (currentAppCategory && action.category === currentAppCategory._id) return true

      return false
    })

    deleteAction && actions.unshift(deleteAction)
    openAction && actions.unshift(openAction)

    actions.sort((a, b) => a.category.localeCompare(b.category))
  }
  getActions()

  interface HelpCard {
    class?: string
    icon: Asset | AnySvelteComponent
    title: IntlString
    description: IntlString
    onClick: () => void
    disabled?: boolean
  }

  const userManualCards: HelpCard[] = [
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.ProjectManagement,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.ProjectManagement
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.IssueManagement,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.IssueManagement
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.Components,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.Components
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.Milestones,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.Milestones
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.Labels,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.Labels
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.CustomFields,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.CustomFields
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'tracker',
      icon: DocumentationIcon,
      title: workbench.string.Workflows,
      description: workbench.string.OpenUserManuals,
      onClick: () => {
        currentManual = workbench.string.Workflows
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      class: 'chunter',
      icon: DocumentationIcon,
      title: workbench.string.MessageActions,
      description: workbench.string.MessageActionDescription,
      onClick: () => {
        currentManual = workbench.string.MessageActions
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    }
  ]

  const cards: HelpCard[] = [
    {
      icon: DocumentationIcon,
      title: workbench.string.Documentation,
      description: workbench.string.OpenPlatformGuide,
      onClick: () => {
        window.open(docsLink, '_blank')
        Analytics.handleEvent(WorkbenchEvents.DocumentationOpened)
      }
    },
    {
      icon: view.icon.Setting,
      title: setting.string.Settings,
      description: workbench.string.AccessWorkspaceSettings,
      onClick: navigateToSettings
    },
    {
      icon: KeyboardIcon,
      title: workbench.string.KeyboardShortcuts,
      description: workbench.string.HowToWorkFaster,
      onClick: () => {
        shortcuts = true
        Analytics.handleEvent(WorkbenchEvents.KeyboardShortcutsOpened)
      }
    }
  ]
</script>

<div class="helpAndSupportPopup">
  <div class="header">
    {#if shortcuts || currentManual}
      <!-- svelte-ignore a11y-click-events-have-key-events -->
      <!-- svelte-ignore a11y-no-static-element-interactions -->
      <div class="mr-4 cursor-pointer" on:click={() => {
        shortcuts = false
        currentManual = null
      }}>
        <Icon icon={IconArrowLeft} size={'medium'} fill={'var(--content-color)'} />
      </div>
    {/if}
    <span class="fs-title overflow-label">
      {#if shortcuts}
        <Label label={workbench.string.KeyboardShortcuts} />
      {:else if currentManual}
        <Label label={getManualTitle(currentManual)} />
      {:else}
        <Label label={workbench.string.HelpCenter} />
      {/if}
    </span>
  </div>
  {#if !shortcuts && !currentManual}
    {#each userManualCards as card}
      {#if card.class === currentApp}
        <div class="clear-mins card {!card.disabled ? 'cursor-pointer focused-button' : ''}">
          <!-- svelte-ignore a11y-click-events-have-key-events -->
          <!-- svelte-ignore a11y-no-static-element-interactions -->
          <div class="container" on:click={card.onClick}>
            <Icon icon={card.icon} size={'small'} fill={'var(--content-color)'} />
            <div class="content">
              <div class="fs-title">
                <Label label={card.title} />
              </div>
              <div class="text-sm content-dark-color"><Label label={card.description} /></div>
            </div>
            <div class="rightIcon">
              <Icon icon={RightArrowIcon} size={'small'} />
            </div>
          </div>
        </div>
      {/if}
    {/each}
    {#each cards as card}
      <div class="clear-mins card {!card.disabled ? 'cursor-pointer focused-button' : ''}">
        <!-- svelte-ignore a11y-click-events-have-key-events -->
        <!-- svelte-ignore a11y-no-static-element-interactions -->
        <div class="container" on:click={card.onClick}>
          <Icon icon={card.icon} size={'small'} fill={'var(--content-color)'} />
          <div class="content">
            <div class="fs-title">
              <Label label={card.title} />
            </div>
            <div class="text-sm content-dark-color"><Label label={card.description} /></div>
          </div>
          <div class="rightIcon">
            <Icon icon={RightArrowIcon} size={'small'} />
          </div>
        </div>
      </div>
    {/each}
    <div class="flex-grow" />
  {:else if currentManual}
    <!-- Manual content -->
    <Scroller padding={'0 .5rem'} fade={topSP} noStretch checkForHeaders>
      <ListView count={flatManualItems.length} noScroll addClass={'rounded'} bind:selection={manualSelection}>
        <svelte:fragment slot="category" let:item>
          {@const currentItem = flatManualItems[item]}
          {#if item === 0 || (item > 0 && flatManualItems[item - 1].stepTitle !== currentItem.stepTitle)}
            <div class="category-box font-semi-bold text-base categoryHeader clear-mins">
              <Label label={currentItem.stepTitle} />
            </div>
          {/if}
        </svelte:fragment>

        <svelte:fragment slot="item" let:item>
          {@const currentItem = flatManualItems[item]}
          <div class="flex-row-center flex-between flex-grow ml-2 p-3 text-base clear-mins">
            <div class="flex-grow {manualSelection === item ? 'caption-color' : 'content-color'} manual-text">
              <Label label={currentItem.instruction} />
            </div>
          </div>
        </svelte:fragment>
      </ListView>
    </Scroller>
  {:else}
    <!-- Keyboard shortcuts -->
    <Scroller padding={'0 .5rem'} fade={topSP} noStretch checkForHeaders>
      <ListView count={actions.length} noScroll addClass={'rounded'} bind:selection>
        <svelte:fragment slot="category" let:item>
          {@const action = actions[item]}
          {#if item === 0 || (item > 0 && actions[item - 1].category !== action.category)}
            {#if action.category}
              {@const category = categories.find((cat) => cat._id === action.category)}
              {#if category?.label && category.label !== categories.find((cat) => cat._id === actions[item - 1]?.category)?.label}
                <div class="category-box font-semi-bold text-base categoryHeader clear-mins">
                  <Label label={category.label} />
                </div>
              {/if}
            {/if}
          {/if}
        </svelte:fragment>
        <svelte:fragment slot="item" let:item>
          {@const action = actions[item]}
          <div class="flex-row-center flex-between flex-grow ml-2 p-3 text-base clear-mins">
            <div class="mr-4 {selection === item ? 'caption-color' : 'dark-color'}">
              <Icon icon={action.icon ?? IconArrowLeft} size={'small'} />
            </div>
            <div class="flex-grow {selection === item ? 'caption-color' : 'content-color'}">
              <Label label={action.label} />
            </div>
            <div class="mr-2 text-md flex-row-center">
              {#if action.keyBinding}
                {#each action.keyBinding as key, i}
                  {#if i !== 0}
                    <div class="ml-2 mr-2 lower"><Label label={view.string.Or} /></div>
                  {/if}
                  <div class="flex-row-center">
                    {#each formatKey(key) as k, jj}
                      {#if jj !== 0}
                        <div class="ml-1 mr-1 lower"><Label label={view.string.Then} /></div>
                      {/if}
                      {#each k as kk}
                        <div class="flex-center text-sm key-box">
                          {capitalizeFirstLetter(kk.trim())}
                        </div>
                      {/each}
                    {/each}
                  </div>
                {/each}
              {/if}
            </div>
          </div>
        </svelte:fragment>
      </ListView>
    </Scroller>
  {/if}
  {#if !shortcuts && !currentManual}
    <div class="footer">
      <a href={privacyPolicyLink} target="_blank">
        <Button id="privacy-policy" kind={'ghost'} label={support.string.PrivacyPolicy} stopPropagation={false} />
      </a>
      <a href={reportBugLink} target="_blank">
        <Button id="report-a-bug" kind={'primary'} label={support.string.ReportBug} stopPropagation={false} />
      </a>
      <a href={supportLink}>
        <Button
          id="contact-us"
          icon={support.icon.Support}
          kind={'ghost'}
          label={support.string.ContactUs}
          stopPropagation={false}
        />
      </a>
    </div>
  {/if}
</div>

<style lang="scss">
  .card {
    margin: 1rem 1rem 0 1rem;
    border: 1px solid var(--theme-button-border);
    border-radius: 0.75rem;
  }
  .container {
    display: flex;
    flex-direction: row;
    padding: 1rem;
    width: 100%;
  }
  .content {
    padding: 0 10px;
    width: 100%;
  }
  .rightIcon {
    align-self: center;
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    margin: 0 0.5rem 0.5rem;
    padding-top: 0.625rem;
    gap: 0.25rem;
  }
  .key-box {
    padding: 0 0.5rem;
    min-width: 1.5rem;
    color: var(--theme-caption-color);
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.25rem;
  }
  .key-box + .key-box {
    margin-left: 0.5rem;
  }
  .category-box {
    position: sticky;
    padding: 0.5rem 1rem;
    top: 0;
    min-height: 2.5rem;
    color: var(--theme-caption-color);
    background-color: var(--theme-comp-header-color);
    border-radius: 0.25rem;
  }

  .manual-text {
    max-width: 400px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    line-height: 1.4;
  }
</style>
