//
// Copyright © 2020 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { mergeIds } from '@hcengineering/platform'
import type { IntlString, Metadata } from '@hcengineering/platform'

import workbench, { workbenchId } from '@hcengineering/workbench'
import { type AnyComponent } from '@hcengineering/ui/src/types'

export default mergeIds(workbenchId, workbench, {
  string: {
    More: '' as IntlString,
    Delete: '' as IntlString,
    ShowMenu: '' as IntlString,
    HideMenu: '' as IntlString,
    Archived: '' as IntlString,
    Open: '' as IntlString,
    General: '' as IntlString,
    Members: '' as IntlString,
    BrowseSpaces: '' as IntlString,
    AccountDisabled: '' as IntlString,
    AccountDisabledDescr: '' as IntlString,
    HelpAndSupport: '' as IntlString,
    HelpCenter: '' as IntlString,
    KeyboardShortcuts: '' as IntlString,
    Documentation: '' as IntlString,
    OpenPlatformGuide: '' as IntlString,
    OpenUserManuals: '' as IntlString,
    ProjectManagement: '' as IntlString,
    IssueManagement: '' as IntlString,
    Components: '' as IntlString,
    Milestones: '' as IntlString,
    Labels: '' as IntlString,
    ActionItems: '' as IntlString,
    UserManuals: '' as IntlString,
    CreateProject: '' as IntlString,
    CreateProjectInstructionOne: '' as IntlString,
    CreateProjectInstructionTwo: '' as IntlString,
    CreateProjectInstructionThree: '' as IntlString,
    ArchiveProject: '' as IntlString,
    ArchiveProjectInstructionOne: '' as IntlString,
    DeleteProject: '' as IntlString,
    DeleteProjectInstructionOne: '' as IntlString,
    CreateIssue: '' as IntlString,
    CreateIssueInstructionOne: '' as IntlString,
    CreateIssueInstructionTwo: '' as IntlString,
    CreateIssueInstructionThree: '' as IntlString,
    IssuePeek: '' as IntlString,
    IssuePeekInstructionOne: '' as IntlString,
    SortingFilteringSearching: '' as IntlString,
    SortingFilteringSearchingInstructionOne: '' as IntlString,
    SortingFilteringSearchingInstructionTwo: '' as IntlString,
    SortingFilteringSearchingInstructionThree: '' as IntlString,
    ReferencingExistingIssue: '' as IntlString,
    ReferencingExistingIssueInstructionOne: '' as IntlString,
    ReferencingExistingIssueInstructionTwo: '' as IntlString,
    ReferencingExistingIssueInstructionThree: '' as IntlString,
    BlockingIssues: '' as IntlString,
    BlockingIssuesInstructionOne: '' as IntlString,
    BlockingIssuesInstructionTwo: '' as IntlString,
    CreatingIssueTemplates: '' as IntlString,
    CreatingIssueTemplatesInstructionOne: '' as IntlString,
    CreatingIssueTemplatesInstructionTwo: '' as IntlString,
    CreatingIssueTemplatesInstructionThree: '' as IntlString,
    AddingIssueTypes: '' as IntlString,
    IssueTypesInstructionOne: '' as IntlString,
    IssueTypesInstructionTwo: '' as IntlString,
    IssueTypesInstructionThree: '' as IntlString,
    IssueTypesInstructionFour: '' as IntlString,
    CreatingSubTasks: '' as IntlString,
    CreatingSubTasksInstructionOne: '' as IntlString,
    CreatingSubTasksInstructionTwo: '' as IntlString,
    CreatingComponents: '' as IntlString,
    CreatingComponentsInstructionOne: '' as IntlString,
    CreatingComponentsInstructionTwo: '' as IntlString,
    CreatingComponentsInstructionThree: '' as IntlString,
    ConnectingIssuesToComponents: '' as IntlString,
    ConnectingIssuesToComponentsInstructionOne: '' as IntlString,
    CreatingNewMilestone: '' as IntlString,
    CreatingNewMilestoneInstructionOne: '' as IntlString,
    CreatingNewMilestoneInstructionTwo: '' as IntlString,
    CreatingNewMilestoneInstructionThree: '' as IntlString,
    AddingIssuesToMilestone: '' as IntlString,
    AddingIssuesToMilestoneInstructionOne: '' as IntlString,
    AddingIssuesToMilestoneInstructionTwo: '' as IntlString,
    AddingIssuesToMilestoneInstructionThree: '' as IntlString,
    UsingLabels: '' as IntlString,
    UsingLabelsInstructionOne: '' as IntlString,
    UsingLabelsInstructionTwo: '' as IntlString,
    ManagingLabels: '' as IntlString,
    ManagingLabelsInstructionOne: '' as IntlString,
    ManagingLabelsInstructionTwo: '' as IntlString,
    ManagingLabelsInstructionThree: '' as IntlString,
    ManagingLabelsInstructionFour: '' as IntlString,
    CustomFields: '' as IntlString,
    CreatingCustomFields: '' as IntlString,
    CreatingCustomFieldsInstructionOne: '' as IntlString,
    CreatingCustomFieldsInstructionTwo: '' as IntlString,
    CreatingCustomFieldsInstructionThree: '' as IntlString,
    CreatingCustomFieldsInstructionFour: '' as IntlString,
    CreatingCustomFieldsInstructionFive: '' as IntlString,
    CreatingCustomFieldsInstructionSix: '' as IntlString,
    Workflows: '' as IntlString,
    ConfiguringWorkflows: '' as IntlString,
    ConfiguringWorkflowsInstructionOne: '' as IntlString,
    ConfiguringWorkflowsInstructionTwo: '' as IntlString,
    ConfiguringWorkflowsInstructionThree: '' as IntlString,
    ConfiguringWorkflowsInstructionFour: '' as IntlString,
    ConfiguringWorkflowsInstructionFive: '' as IntlString,
    ConfiguringWorkflowsInstructionSix: '' as IntlString,
    ConfiguringWorkflowsInstructionSeven: '' as IntlString,
    AddingNewStatus: '' as IntlString,
    AddingNewStatusInstructionOne: '' as IntlString,
    AddingNewStatusInstructionTwo: '' as IntlString,
    AddingNewStatusInstructionThree: '' as IntlString,
    AddingNewStatusInstructionFour: '' as IntlString,
    AddingNewStatusInstructionFive: '' as IntlString,
    AccessWorkspaceSettings: '' as IntlString,
    HowToWorkFaster: '' as IntlString,
    OpenInNewTab: '' as IntlString,
    NewVersionAvailable: '' as IntlString,
    PleaseUpdate: '' as IntlString,
    MobileNotSupported: '' as IntlString,
    LogInAnyway: '' as IntlString,
    WorkspaceCreating: '' as IntlString,
    AccessDenied: '' as IntlString,
    Widget: '' as IntlString,
    WidgetPreference: '' as IntlString,
    Tab: '' as IntlString,
    MessageActions: '' as IntlString,
    MessageActionDescription: '' as IntlString,
    EditMessage: '' as IntlString,
    EditMessageStepOne: '' as IntlString,
    EditMessageStepTwo: '' as IntlString,
    DeleteMessage: '' as IntlString,
    DeleteMessageStepOne: '' as IntlString,
    DeleteMessageStepTwo: '' as IntlString,
    PinMessage: '' as IntlString,
    PinMessageStepOne: '' as IntlString,
    PinMessageStepTwo: '' as IntlString,
    PinMessageStepThree: '' as IntlString,
    SaveMessage: '' as IntlString,
    SaveMessageStepOne: '' as IntlString,
    SaveMessageStepTwo: '' as IntlString,
    SaveMessageStepThree: '' as IntlString,
    ShareMessage: '' as IntlString,
    ShareMessageStepOne: '' as IntlString,
    ShareMessageStepTwo: '' as IntlString,
    MarkAsUnread: '' as IntlString,
    MarkAsUnreadStepOne: '' as IntlString,
    MarkAsUnreadStepTwo: '' as IntlString
  },
  metadata: {
    MobileAllowed: '' as Metadata<boolean>
  },
  component: {
    SpacePanel: '' as AnyComponent,
    Workbench: '' as AnyComponent,
    WorkbenchTabs: '' as AnyComponent
  }
})
