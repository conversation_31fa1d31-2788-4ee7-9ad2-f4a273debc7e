import type { IntlString } from '@hcengineering/platform'
import workbench from './plugin'

export interface ManualStep {
  title: IntlString
  instructions: IntlString[]
}

export interface ManualCategory {
  class: string
  type: IntlString
  steps: ManualStep[]
}

export const userManuals: ManualCategory[] = [
  {
    class: 'tracker',
    type: workbench.string.ProjectManagement,
    steps: [
      {
        title: workbench.string.CreateProject,
        instructions: [
          workbench.string.CreateProjectInstructionOne,
          workbench.string.CreateProjectInstructionTwo,
          workbench.string.CreateProjectInstructionThree
        ]
      },
      {
        title: workbench.string.ArchiveProject,
        instructions: [
          workbench.string.ArchiveProjectInstructionOne
        ]
      },
      {
        title: workbench.string.DeleteProject,
        instructions: [
          workbench.string.DeleteProjectInstructionOne
        ]
      }
    ]
  },
  {
    class: 'tracker',
    type: workbench.string.IssueManagement,
    steps: [
      {
        title: workbench.string.CreateIssue,
        instructions: [
          workbench.string.CreateIssueInstructionOne,
          workbench.string.CreateIssueInstructionTwo,
          workbench.string.CreateIssueInstructionThree
        ]
      },
      {
        title: workbench.string.IssuePeek,
        instructions: [
          workbench.string.IssuePeekInstructionOne
        ]
      },
      {
        title: workbench.string.SortingFilteringSearching,
        instructions: [
          workbench.string.SortingFilteringSearchingInstructionOne,
          workbench.string.SortingFilteringSearchingInstructionTwo,
          workbench.string.SortingFilteringSearchingInstructionThree
        ]
      },
      {
        title: workbench.string.ReferencingExistingIssue,
        instructions: [
          workbench.string.ReferencingExistingIssueInstructionOne,
          workbench.string.ReferencingExistingIssueInstructionTwo,
          workbench.string.ReferencingExistingIssueInstructionThree
        ]
      },
      {
        title: workbench.string.BlockingIssues,
        instructions: [
          workbench.string.BlockingIssuesInstructionOne,
          workbench.string.BlockingIssuesInstructionTwo
        ]
      },
      {
        title: workbench.string.CreatingIssueTemplates,
        instructions: [
          workbench.string.CreatingIssueTemplatesInstructionOne,
          workbench.string.CreatingIssueTemplatesInstructionTwo,
          workbench.string.CreatingIssueTemplatesInstructionThree
        ]
      },
      {
        title: workbench.string.AddingIssueTypes,
        instructions: [
          workbench.string.IssueTypesInstructionOne,
          workbench.string.IssueTypesInstructionTwo,
          workbench.string.IssueTypesInstructionThree,
          workbench.string.IssueTypesInstructionFour
        ]
      },
      {
        title: workbench.string.CreatingSubTasks,
        instructions: [
          workbench.string.CreatingSubTasksInstructionOne,
          workbench.string.CreatingSubTasksInstructionTwo
        ]
      }
    ]
  },
  {
    class: 'tracker',
    type: workbench.string.Components,
    steps: [
      {
        title: workbench.string.CreatingComponents,
        instructions: [
          workbench.string.CreatingComponentsInstructionOne,
          workbench.string.CreatingComponentsInstructionTwo,
          workbench.string.CreatingComponentsInstructionThree
        ]
      },
      {
        title: workbench.string.ConnectingIssuesToComponents,
        instructions: [
          workbench.string.ConnectingIssuesToComponentsInstructionOne
        ]
      }
    ]
  },
  {
    class: 'tracker',
    type: workbench.string.Milestones,
    steps: [
      {
        title: workbench.string.CreatingNewMilestone,
        instructions: [
          workbench.string.CreatingNewMilestoneInstructionOne,
          workbench.string.CreatingNewMilestoneInstructionTwo,
          workbench.string.CreatingNewMilestoneInstructionThree
        ]
      },
      {
        title: workbench.string.AddingIssuesToMilestone,
        instructions: [
          workbench.string.AddingIssuesToMilestoneInstructionOne,
          workbench.string.AddingIssuesToMilestoneInstructionTwo,
          workbench.string.AddingIssuesToMilestoneInstructionThree
        ]
      }
    ]
  },
  {
    class: 'tracker',
    type: workbench.string.Labels,
    steps: [
      {
        title: workbench.string.UsingLabels,
        instructions: [
          workbench.string.UsingLabelsInstructionOne,
          workbench.string.UsingLabelsInstructionTwo
        ]
      },
      {
        title: workbench.string.ManagingLabels,
        instructions: [
          workbench.string.ManagingLabelsInstructionOne,
          workbench.string.ManagingLabelsInstructionTwo,
          workbench.string.ManagingLabelsInstructionThree,
          workbench.string.ManagingLabelsInstructionFour
        ]
      }
    ]
  },
  {
    class: 'tracker',
    type: workbench.string.CustomFields,
    steps: [
      {
        title: workbench.string.CreatingCustomFields,
        instructions: [
          workbench.string.CreatingCustomFieldsInstructionOne,
          workbench.string.CreatingCustomFieldsInstructionTwo,
          workbench.string.CreatingCustomFieldsInstructionThree,
          workbench.string.CreatingCustomFieldsInstructionFour,
          workbench.string.CreatingCustomFieldsInstructionFive,
          workbench.string.CreatingCustomFieldsInstructionSix
        ]
      }
    ]
  },
  {
    class: 'tracker',
    type: workbench.string.Workflows,
    steps: [
      {
        title: workbench.string.ConfiguringWorkflows,
        instructions: [
          workbench.string.ConfiguringWorkflowsInstructionOne,
          workbench.string.ConfiguringWorkflowsInstructionTwo,
          workbench.string.ConfiguringWorkflowsInstructionThree,
          workbench.string.ConfiguringWorkflowsInstructionFour,
          workbench.string.ConfiguringWorkflowsInstructionFive,
          workbench.string.ConfiguringWorkflowsInstructionSix,
          workbench.string.ConfiguringWorkflowsInstructionSeven
        ]
      },
      {
        title: workbench.string.AddingNewStatus,
        instructions: [
          workbench.string.AddingNewStatusInstructionOne,
          workbench.string.AddingNewStatusInstructionTwo,
          workbench.string.AddingNewStatusInstructionThree,
          workbench.string.AddingNewStatusInstructionFour,
          workbench.string.AddingNewStatusInstructionFive
        ]
      }
    ]
  },
  {
    class: 'chunter',
    type: workbench.string.MessageActions,
    steps: [
      {
        title: workbench.string.EditMessage,
        instructions: [
          workbench.string.EditMessageStepOne,
          workbench.string.EditMessageStepTwo
        ]
      }
    ]
  },
  {
    class: 'chunter',
    type: workbench.string.MessageActions,
    steps: [
      {
        title: workbench.string.DeleteMessage,
        instructions: [
          workbench.string.DeleteMessageStepOne,
          workbench.string.DeleteMessageStepTwo
        ]
      }
    ]
  },
  {
    class: 'chunter',
    type: workbench.string.MessageActions,
    steps: [
      {
        title: workbench.string.PinMessage,
        instructions: [
          workbench.string.PinMessageStepOne,
          workbench.string.PinMessageStepTwo,
          workbench.string.PinMessageStepThree
        ]
      }
    ]
  },
  {
    class: 'chunter',
    type: workbench.string.MessageActions,
    steps: [
      {
        title: workbench.string.SaveMessage,
        instructions: [
          workbench.string.SaveMessageStepOne,
          workbench.string.SaveMessageStepTwo,
          workbench.string.SaveMessageStepThree
        ]
      }
    ]
  },
  {
    class: 'chunter',
    type: workbench.string.MessageActions,
    steps: [
      {
        title: workbench.string.ShareMessage,
        instructions: [
          workbench.string.ShareMessageStepOne,
          workbench.string.ShareMessageStepTwo
        ]
      }
    ]
  },
  {
    class: 'chunter',
    type: workbench.string.MessageActions,
    steps: [
      {
        title: workbench.string.MarkAsUnread,
        instructions: [
          workbench.string.MarkAsUnreadStepOne,
          workbench.string.MarkAsUnreadStepTwo
        ]
      }
    ]
  }
]
