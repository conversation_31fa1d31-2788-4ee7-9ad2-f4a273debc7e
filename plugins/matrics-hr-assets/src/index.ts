//
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import hr from '@hcengineering/matrics-hr'
import { loadMetadata } from '@hcengineering/platform'

const icons = require('../assets/icons.svg') as string // eslint-disable-line
loadMetadata(hr.icon, {
  HR: `${icons}#hr`,
  Department: `${icons}#department`,
  Office: `${icons}#department`, // Reuse department icon for now
  Structure: `${icons}#structure`,
  OrgChart: `${icons}#org-chart`,
  Members: `${icons}#members`,
  Vacation: `${icons}#vacation`,
  Sick: `${icons}#sick`,
  PTO: `${icons}#pto`,
  PTO2: `${icons}#pto2`,
  Overtime: `${icons}#overtime`,
  Overtime2: `${icons}#overtime2`,
  Remote: `${icons}#remote`,
  Policy: `${icons}#structure`, // Reuse structure icon for policies
  Workflow: `${icons}#structure`, // Reuse structure icon for workflows
  PublicHoliday: `${icons}#vacation`, // Reuse vacation icon for holidays
  Approved: `${icons}#vacation`, // Reuse vacation icon (green checkmark style)
  Rejected: `${icons}#sick`, // Reuse sick icon (red x style)
  Pending: `${icons}#pto`, // Reuse PTO icon (clock/waiting style)
  Document: `${icons}#structure`, // Reuse structure icon for documents
  Compensation: `${icons}#structure`, // Reuse structure icon for compensation
  Performance: `${icons}#structure`, // Reuse structure icon for performance
  Benefits: `${icons}#structure`, // Reuse structure icon for benefits
  Task: `${icons}#structure` // Reuse structure icon for tasks
})
