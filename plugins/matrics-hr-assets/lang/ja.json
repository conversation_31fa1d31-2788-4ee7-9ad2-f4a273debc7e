{"string": {"Department": "部署", "ParentDepartmentLabel": "親部署", "Structure": "組織図", "OrgChart": "組織チャート", "CreateDepartment": "部署を作成", "CreateDepartmentLabel": "部署", "DepartmentPlaceholder": "部署", "TeamLead": "チームリーダー", "UnAssignLead": "チームリーダーを解除", "MemberCount": "{count, plural, =0 {従業員なし} =1 {1人の従業員} other {#人の従業員}}", "AssignLead": "チームリーダーを割り当て", "TeamLeadTooltip": "{value}", "HRApplication": "人事 (ベータ)", "MoveStaff": "社員異動", "MoveStaffDescr": "{current}から{department}へ社員を異動させますか？", "Departments": "部署", "Positions": "役職", "ShowEmployees": "従業員を表示", "AddEmployee": "従業員を追加", "SelectEmployee": "従業員を選択", "Schedule": "スケジュール", "RequestType": "種類", "CreateRequest": "{type}を作成", "Today": "今日", "Summary": "合計", "NoEmployeesInDepartment": "選択された部署には従業員がいません", "Vacation": "休暇", "Sick": "病気", "PTO": "有給休暇", "PTOs": "有給休暇", "Remote": "リモート", "Overtime": "残業", "PTO2": "半日有給休暇", "Overtime2": "半日残業", "EditRequest": "{type} を編集", "EditRequestType": "種類を編集", "ChooseNewType": "新しい種類を選択:", "UnchangeableType": "この種類は変更できません", "Request": "リクエスト", "ExistingRequests": "設定された日付には既存のリクエストがあります", "Staff": "従業員", "Member": "メンバー", "Members": "メンバー", "NoMembers": "メンバーは追加されていません", "AddMember": "メンバーを追加", "Subscribers": "購読者", "PublicHoliday": "祝日", "Title": "タイトル", "Description": "説明", "PublicHolidays": "祝日", "MarkAsPublicHoliday": "祝日としてマーク", "EditPublicHoliday": "祝日を編集", "Manager": "Manager", "Managers": "マネージャー", "Dashboard": "Dashboard", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Export": "エクスポート", "Separator": "区切り文字", "ChooseSeparator": "区切り文字を選択", "RequestCreated": "リクエストを作成しました", "RequestUpdated": "リクエストを更新しました", "RequestRemoved": "リクエストを削除しました", "ConfigLabel": "人事", "ConfigDescription": "組織構造と従業員の勤務カレンダーを管理するための拡張機能", "WorkingDays": "勤務日", "ReportedDays": "報告された日数", "Tasks": "タスク", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "福利厚生", "AddHoliday": "休日を追加", "NoHolidays": "休日が定義されていません", "HolidayName": "休日名", "HolidayNamePlaceholder": "例：クリスマス", "HolidayDescription": "説明", "HolidayDescriptionPlaceholder": "例：オフィス閉鎖", "DepartmentOptional": "部署（任意）", "EditHoliday": "休日を編集", "Date": "日付"}}