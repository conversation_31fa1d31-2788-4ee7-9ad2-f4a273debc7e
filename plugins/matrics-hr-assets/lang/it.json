{"string": {"Department": "Dipartimento", "ParentDepartmentLabel": "Dipartimento genitore", "Structure": "Struttura", "OrgChart": "Organigramma", "CreateDepartment": "<PERSON><PERSON>o", "CreateDepartmentLabel": "Dipartimento", "DepartmentPlaceholder": "Dipartimento", "TeamLead": "Team lead", "UnAssignLead": "Rimuovi team lead", "MemberCount": "{count, plural, =0 {nessun dipendente} =1 {1 dipendente} other {# dipendenti}}", "AssignLead": "Assegna team lead", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Trasferimento dipendenti", "MoveStaffDescr": "Vuoi trasferire il dipendente da {current} a {department}?", "Departments": "<PERSON><PERSON><PERSON><PERSON>", "Positions": "Posizioni", "ShowEmployees": "<PERSON><PERSON> dipendenti", "AddEmployee": "Aggiungi dipendente", "SelectEmployee": "Seleziona dipendente", "Schedule": "Programma", "RequestType": "Tipo", "CreateRequest": "Crea {type}", "Today": "<PERSON><PERSON><PERSON>", "Summary": "Totale", "NoEmployeesInDepartment": "Non ci sono dipendenti nel dipartimento selezionato", "Vacation": "Vacanza", "Sick": "Malattia", "PTO": "PTO", "PTOs": "PTOs", "Remote": "<PERSON><PERSON>", "Overtime": "Straordinario", "PTO2": "PTO/2", "Overtime2": "Straordinario/2", "EditRequest": "Modifica {type}", "EditRequestType": "Modifica tipo", "ChooseNewType": "Scegli nuovo tipo:", "UnchangeableType": "Questo tipo non può essere cambiato", "Request": "<PERSON><PERSON>", "ExistingRequests": "Ci sono già richieste esistenti per le date impostate", "Staff": "Lavoratore", "Member": "Membro", "Members": "<PERSON><PERSON><PERSON>", "NoMembers": "<PERSON><PERSON><PERSON> membro aggiunto", "AddMember": "Aggiungi membro", "Subscribers": "<PERSON><PERSON><PERSON><PERSON>", "PublicHoliday": "Festività", "Title": "<PERSON><PERSON>", "Description": "Descrizione", "PublicHolidays": "Public holidays", "MarkAsPublicHoliday": "Contrassegna come festività", "EditPublicHoliday": "Modifica festività", "Manager": "Manager", "Managers": "Manager", "Dashboard": "Dashboard", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Export": "Esporta", "Separator": "Separatore", "ChooseSeparator": "Scegli separatore", "RequestCreated": "<PERSON><PERSON> creata", "RequestUpdated": "Richiesta aggiornata", "RequestRemoved": "<PERSON><PERSON> rim<PERSON>", "ConfigLabel": "Risorse umane", "ConfigDescription": "Estensione per gestire la struttura organizzativa e il calendario lavorativo dei dipendenti", "WorkingDays": "Working days", "ReportedDays": "Reported days", "Tasks": "Tasks", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "Benefici", "AddHoliday": "Aggiungi festività", "NoHolidays": "Nessuna festività definita ancora", "HolidayName": "Nome della festività", "HolidayNamePlaceholder": "<PERSON><PERSON>", "HolidayDescription": "Descrizione", "HolidayDescriptionPlaceholder": "es. Ufficio chiuso", "DepartmentOptional": "Dipartimento (facoltativo)", "EditHoliday": "Modifica festività", "Date": "Data"}}