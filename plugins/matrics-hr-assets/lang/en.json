{"string": {"Department": "Department", "ParentDepartmentLabel": "Parent department", "Structure": "Structure", "OrgChart": "Org chart", "CreateDepartment": "Create department", "CreateDepartmentLabel": "Department", "DepartmentPlaceholder": "Department", "TeamLead": "Team lead", "UnAssignLead": "Unassign team lead", "MemberCount": "{count, plural, =0 {no employees} =1 {1 employee} other {# employees}}", "AssignLead": "Assign team lead", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Employee transfer", "MoveStaffDescr": "Do you want to transfer employee from {current} to {department}", "Departments": "Departments", "Positions": "Positions", "ShowEmployees": "Show employees", "AddEmployee": "Add employee", "SelectEmployee": "Select employee", "Schedule": "Schedule", "RequestType": "Type", "CreateRequest": "Create {type}", "Today": "Today", "Summary": "Total", "NoEmployeesInDepartment": "There are no employees in the selected department", "Vacation": "Vacation", "Sick": "Sick", "PTO": "PTO", "PTOs": "PTOs", "Remote": "Remote", "Overtime": "Overtime", "PTO2": "PTO/2", "Overtime2": "Overtime/2", "EditRequest": "Edit {type}", "EditRequestType": "Edit type", "ChooseNewType": "Choose new type:", "UnchangeableType": "This type cannot be changed", "Request": "Request", "ExistingRequests": "There are already existing requests for set dates", "Staff": "Worker", "Member": "Member", "Members": "Members", "NoMembers": "No members added", "AddMember": "Add member", "Subscribers": "Subscribers", "Inactive": "Inactive", "PublicHoliday": "Public holiday", "Title": "Title", "Description": "Description", "PublicHolidays": "Public holidays", "MarkAsPublicHoliday": "Mark as public holiday", "EditPublicHoliday": "Edit public holiday", "Manager": "Manager", "Managers": "Managers", "Dashboard": "Dashboard", "Overview": "Overview", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "NoProfileData": "No profile data available yet.", "NoEmergencyInfo": "No emergency information added yet.", "NoAssignedAssets": "No assigned assets yet.", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Hello": "Hello", "DashboardSubtitle": "Stay on top of key HR updates for you and your team.", "Export": "Export", "Separator": "Separator", "ChooseSeparator": "Choose separator", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "FTE": "FTE (%)", "CostCenter": "Cost center", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "Attendance": "Attendance", "AttendanceSchedule": "Attendance schedule", "ClockIn": "Clock in", "ClockOut": "Clock out", "CurrentlyInOffice": "Currently in office", "NotInOffice": "Not in office", "WorkdayStart": "Workday start", "WorkdayEnd": "Workday end", "LunchStart": "Lunch break start", "LunchEnd": "Lunch break end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "PTOBalance": "PTO balance", "VacationBalance": "Vacation balance", "SickBalance": "Sick balance", "PersonalBalance": "Personal balance", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "PendingRequests": "Pending requests", "ApprovedTimeOff": "Approved time off", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOffPolicy": "Time-off policy", "TimeOffPolicies": "Time-off policies", "TimeOffTransaction": "Time-off transaction", "AccrualSettings": "Accrual settings", "CarryoverRules": "Carryover rules", "ApprovalRules": "Approval rules", "AccrualMethod": "Accrual method", "AccrualMethodNone": "No automatic accrual", "AccrualMethodLumpSum": "Lump sum", "AccrualMethodPeriodic": "Per period", "AccrualMethodHourly": "Per hour worked", "AccrualFrequency": "Accrual frequency", "AccrualFrequencyMonthly": "Monthly", "AccrualFrequencyQuarterly": "Quarterly", "AccrualFrequencyYearly": "Yearly", "AccrualFrequencyAnniversary": "Work anniversary", "AccrualFrequencyPerPayPeriod": "Per pay period", "AccrualRate": "Accrual rate (days)", "AccrualCap": "Accrual cap", "AccrualStartMonth": "Accrual starts (month)", "AccrualStartDay": "Accrual starts (day)", "CarryoverLimit": "Carryover limit", "CarryoverExpiryDays": "Carryover expires after (days)", "CurrentBalance": "Current balance", "PendingBalance": "Pending balance", "LastAccruedAt": "Last accrued on", "EffectiveDate": "Effective date", "AllowNegativeBalance": "Allow negative balances", "AllowHalfDays": "Allow half days", "WaitingPeriodDays": "Waiting period (days)", "DefaultApprover": "De<PERSON>ult approver", "AutoApprove": "Auto approve small requests", "AutoApproveMaxDays": "Auto approve up to (days)", "HalfDayStart": "Half-day on start date", "HalfDayEnd": "Half-day on end date", "TransactionKind": "Transaction kind", "Amount": "Amount", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "RecordedBy": "Recorded by", "CreateTimeOffPolicy": "Create time-off policy", "EditTimeOffPolicy": "Edit time-off policy", "RequestCreated": "Request created", "RequestUpdated": "Request updated", "RequestRemoved": "Request removed", "Payload": "Payload", "RequestProfileChange": "Request profile change", "RequestEmergencyInfoChange": "Request emergency info change", "RequestAssetAssignment": "Request asset assignment", "RequestAssetReturn": "Request asset return", "ConfigLabel": "People & Culture", "ConfigDescription": "Extension to manage organization structure and employee work calendar", "WorkingDays": "Working days", "ReportedDays": "Reported days", "Tasks": "Tasks", "TPD": "TPD", "EXTRa": "EXTRa", "Office": "Office", "Offices": "Offices", "CreateOffice": "Create office", "EditOffice": "Edit office", "Address": "Address", "City": "City", "Country": "Country", "Timezone": "Timezone", "Status": "Status", "Approver": "Approver", "ApprovedBy": "Approved by", "ApprovalComment": "Approval comment", "Approve": "Approve", "Reject": "Reject", "Submit": "Submit for approval", "Cancel": "Cancel request", "StatusPending": "Pending approval", "StatusApproved": "Approved", "StatusRejected": "Rejected", "StatusDraft": "Draft", "StatusCancelled": "Cancelled", "RequestApproved": "Request approved", "RequestRejected": "Request rejected", "RequestSubmitted": "Request submitted for approval", "PendingApprovals": "Pending approvals", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "Benefits", "Policy": "Policy", "Policies": "Policies", "CreatePolicy": "Create policy", "EditPolicy": "Edit policy", "Active": "Active", "TriggerType": "Trigger type", "TriggerConfig": "Trigger configuration", "Workflow": "Workflow", "Workflows": "Workflows", "CreateWorkflow": "Create workflow", "EditWorkflow": "Edit workflow", "WorkflowStep": "Workflow step", "Steps": "Steps", "Assignee": "Assignee", "Completed": "Completed", "CompletedBy": "Completed by", "Order": "Order", "All": "All", "ProfileUpdate": "Profile update", "EmergencyInfoUpdate": "Emergency info update", "AssetAssignment": "Asset assignment", "AssetReturn": "Asset return", "EmergencyInfo": "Emergency info", "EmergencyContact": "Emergency contact", "EmergencyPhone": "Emergency phone", "EmergencyEmail": "Emergency email", "EmergencyRelationship": "Relationship", "AssetName": "Asset name", "AssetTag": "Asset tag", "SerialNumber": "Serial number", "AssignedDate": "Assigned date", "AssignedShort": "Assigned", "ReturnDate": "Return date", "ReturnShort": "Return", "Condition": "Condition", "Notes": "Notes", "AddHoliday": "Add holiday", "NoHolidays": "No holidays defined yet", "HolidayName": "Holiday name", "HolidayNamePlaceholder": "e.g., Christmas Day", "HolidayDescription": "Description", "HolidayDescriptionPlaceholder": "e.g., Office closed", "DepartmentOptional": "Department (optional)", "EditHoliday": "Edit holiday", "Date": "Date", "Job": "Job", "EmploymentInformation": "Employment information", "EmploymentDates": "Employment dates", "JobHistory": "Job history", "Compensation": "Compensation", "Salary": "Salary", "BaseSalary": "Base salary", "PayFrequency": "Pay frequency", "PayFrequencyMonthly": "Monthly", "PayFrequencyBiWeekly": "Bi-weekly", "PayFrequencyWeekly": "Weekly", "PayFrequencyAnnual": "Annual", "Bonus": "Bonus", "Commission": "Commission", "RequestChange": "Request change", "RequestEmploymentChange": "Request employment change", "RequestCompensationChange": "Request compensation change", "RequestJobChange": "Request job change", "EmploymentChangeRequest": "Employment change request", "CompensationChangeRequest": "Compensation change request", "JobChangeRequest": "Job change request", "EffectiveFrom": "Effective from", "ProposedChanges": "Proposed changes", "CurrentValue": "Current value", "NewValue": "New value", "ChangeReason": "Reason for change", "Employees": "Employees", "EmployeeManagement": "Employee management", "EditEmployee": "Edit employee", "ViewEmployee": "View employee", "EmployeeDetails": "Employee details", "PersonalInformation": "Personal information", "EmployeeDirectory": "Employee directory", "SearchEmployees": "Search employees", "FilterByDepartment": "Filter by department", "EmployeeCount": "{count, plural, =0 {No employees} =1 {1 employee} other {# employees}}", "UpdateEmployeeInfo": "Update employee information", "ManageTimeOff": "Manage time off", "AdjustBalance": "Adjust balance", "BalanceAdjustment": "Balance adjustment", "AdjustmentReason": "Adjustment reason", "AddDays": "Add days", "SubtractDays": "Subtract days", "ManualAdjustment": "Manual adjustment", "BalanceHistory": "Balance history", "NoEmployeesFound": "No employees found", "ContractsDocuments": "Contracts & Documents", "EmployeeDocument": "Employee Document", "EmployeeDocuments": "Employee Documents", "AddDocument": "Add document", "UploadDocument": "Upload document", "DocumentType": "Document Type", "DocumentTypeContract": "Contract", "DocumentTypeOfferLetter": "Offer Letter", "DocumentTypeNDA": "NDA", "DocumentTypeAgreement": "Agreement", "DocumentTypePolicy": "Policy", "DocumentTypeCertificate": "Certificate", "DocumentTypeOther": "Other", "DocumentStatus": "Document Status", "DocumentStatusDraft": "Draft", "DocumentStatusActive": "Active", "DocumentStatusExpired": "Expired", "DocumentStatusRevoked": "Revoked", "ExpiryDate": "Expiry Date", "SignedDate": "Signed Date", "UploadedBy": "Uploaded By", "ViewDocument": "View Document", "DownloadDocument": "Download", "CompensationRecord": "Compensation Record", "CompensationRecords": "Compensation Records", "CompensationHistory": "Compensation History", "CompensationType": "Compensation Type", "CompensationTypeSalary": "Salary", "CompensationTypeBonus": "Bonus", "CompensationTypeCommission": "Commission", "CompensationTypeEquity": "Equity", "CompensationTypeOther": "Other", "EndDate": "End Date", "Reason": "Reason", "AddCompensation": "Add Compensation", "EditCompensation": "Edit Compensation", "PerformanceReview": "Performance Review", "PerformanceReviews": "Performance Reviews", "ReviewType": "Review Type", "ReviewTypeAnnual": "Annual Review", "ReviewTypeMidYear": "Mid-Year Review", "ReviewTypeProbation": "Probation Review", "ReviewTypeProjectBased": "Project-Based Review", "ReviewTypeCustom": "Custom Review", "ReviewStatus": "Review Status", "ReviewStatusNotStarted": "Not Started", "ReviewStatusInProgress": "In Progress", "ReviewStatusCompleted": "Completed", "ReviewStatusCancelled": "Cancelled", "ReviewPeriodStart": "Review Period Start", "ReviewPeriodEnd": "Review Period End", "DueDate": "Due Date", "CompletedDate": "Completed Date", "Reviewer": "Reviewer", "OverallRating": "Overall Rating", "Strengths": "Strengths", "AreasForImprovement": "Areas for Improvement", "Goals": "Goals", "Feedback": "<PERSON><PERSON><PERSON>", "EmployeeComments": "Employee Comments", "AddReview": "Add Review", "EditReview": "Edit Review", "StartReview": "Start Review", "CompleteReview": "Complete Review", "EmployeeBenefit": "Employee Benefit", "EmployeeBenefits": "Employee Benefits", "BenefitType": "Benefit Type", "BenefitTypeHealthInsurance": "Health Insurance", "BenefitTypeDentalInsurance": "Dental Insurance", "BenefitTypeVisionInsurance": "Vision Insurance", "BenefitTypeLifeInsurance": "Life Insurance", "BenefitTypeRetirement401k": "401(k) Retirement", "BenefitTypeStockOptions": "Stock Options", "BenefitTypeGymMembership": "Gym Membership", "BenefitTypeOther": "Other", "BenefitStatus": "Benefit Status", "BenefitStatusEligible": "Eligible", "BenefitStatusEnrolled": "Enrolled", "BenefitStatusDeclined": "Declined", "BenefitStatusTerminated": "Terminated", "BenefitName": "Benefit Name", "Provider": "Provider", "EnrollmentDate": "Enrollment Date", "EmployeeContribution": "Employee Contribution", "EmployerContribution": "Employer Contribution", "AddBenefit": "Add Benefit", "EditBenefit": "Edit Benefit", "EnrollInBenefit": "Enroll in Benefit", "LifecycleTask": "Lifecycle Task", "LifecycleTasks": "Lifecycle Tasks", "Onboarding": "Onboarding", "Offboarding": "Offboarding", "TaskCategory": "Task Category", "TaskCategoryOnboarding": "Onboarding", "TaskCategoryOffboarding": "Offboarding", "TaskStatus": "Task Status", "TaskStatusNotStarted": "Not Started", "TaskStatusInProgress": "In Progress", "TaskStatusCompleted": "Completed", "TaskStatusSkipped": "Skipped", "AddTask": "Add Task", "EditTask": "Edit Task", "CompleteTask": "Complete Task", "SkipTask": "Skip Task", "OnboardingChecklist": "Onboarding Checklist", "OffboardingChecklist": "Offboarding Checklist"}}