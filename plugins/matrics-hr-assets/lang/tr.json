{"string": {"Department": "<PERSON><PERSON><PERSON>", "ParentDepartmentLabel": "Üst departman", "Structure": "Yap<PERSON>", "OrgChart": "Organizasyon şeması", "CreateDepartment": "<PERSON><PERSON><PERSON> olu<PERSON>", "CreateDepartmentLabel": "<PERSON><PERSON><PERSON>", "DepartmentPlaceholder": "<PERSON><PERSON><PERSON>", "TeamLead": "<PERSON><PERSON><PERSON>m <PERSON>", "UnAssignLead": "Tak<PERSON>m liderini kaldır", "MemberCount": "{count, plural, =0 {çalışan yok} =1 {1 çalışan} other {# çalışan}}", "AssignLead": "Ta<PERSON><PERSON>m lideri ata", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Çalışan transferi", "MoveStaffDescr": "Çalışanı {current}'ten {department}'e transfer etmek istiyor musunuz?", "Departments": "Depar<PERSON><PERSON>", "Positions": "Pozisyonlar", "ShowEmployees": "Çalışanları göster", "AddEmployee": "<PERSON>alış<PERSON>", "SelectEmployee": "Çalışan seç", "Schedule": "Program", "RequestType": "Tip", "CreateRequest": "{type} oluştur", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Summary": "Toplam", "NoEmployeesInDepartment": "Seçilen departmanda çalışan yok", "Vacation": "<PERSON><PERSON>", "Sick": "Hastalık", "PTO": "İzin", "PTOs": "<PERSON><PERSON><PERSON>", "Remote": "Uzaktan", "Overtime": "<PERSON><PERSON><PERSON> mesai", "PTO2": "İzin/2", "Overtime2": "<PERSON>azla mesai/2", "EditRequest": "{type} düzenle", "EditRequestType": "<PERSON><PERSON> d<PERSON>", "ChooseNewType": "Yeni tip seç:", "UnchangeableType": "Bu tip değiştirilemez", "Request": "<PERSON><PERSON>", "ExistingRequests": "Belirlenen tarihler için zaten mevcut talepler var", "Staff": "Çalışan", "Member": "Üye", "Members": "<PERSON><PERSON><PERSON>", "NoMembers": "Üye eklenmedi", "AddMember": "<PERSON><PERSON> e<PERSON>", "Subscribers": "<PERSON><PERSON><PERSON>", "PublicHoliday": "<PERSON><PERSON><PERSON>", "Title": "Başlık", "Description": "<PERSON><PERSON>ı<PERSON><PERSON>", "PublicHolidays": "<PERSON><PERSON><PERSON>", "MarkAsPublicHoliday": "<PERSON><PERSON><PERSON>", "EditPublicHoliday": "<PERSON><PERSON><PERSON> ta<PERSON>", "Manager": "Manager", "Managers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dashboard": "Dashboard", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Export": "Dışa aktar", "Separator": "Ayırıcı", "ChooseSeparator": "Ayırıcı seç", "RequestCreated": "<PERSON><PERSON>", "RequestUpdated": "<PERSON><PERSON>", "RequestRemoved": "Talep kaldırıldı", "ConfigLabel": "İnsan kaynakları", "ConfigDescription": "Organizasyon yapısını ve çalışan iş takvimini yönetmek için uzantı", "WorkingDays": "Çalışma günleri", "ReportedDays": "<PERSON><PERSON><PERSON><PERSON>", "Tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "<PERSON><PERSON><PERSON>", "AddHoliday": "<PERSON><PERSON>", "NoHolidays": "<PERSON>n<PERSON>z tatil <PERSON>", "HolidayName": "<PERSON><PERSON> adı", "HolidayNamePlaceholder": "<PERSON><PERSON><PERSON>", "HolidayDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "HolidayDescriptionPlaceholder": "örn. <PERSON><PERSON>", "DepartmentOptional": "Departman (isteğe bağlı)", "EditHoliday": "<PERSON><PERSON><PERSON>", "Date": "<PERSON><PERSON><PERSON>"}}