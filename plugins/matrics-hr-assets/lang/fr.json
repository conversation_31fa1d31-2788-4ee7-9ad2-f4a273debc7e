{"string": {"Department": "Département", "ParentDepartmentLabel": "Département parent", "Structure": "Structure", "OrgChart": "Organigramme", "CreateDepartment": "<PERSON><PERSON>er un département", "CreateDepartmentLabel": "Département", "DepartmentPlaceholder": "Département", "TeamLead": "Chef d'é<PERSON>pe", "UnAssignLead": "<PERSON><PERSON><PERSON><PERSON><PERSON> le chef d'équipe", "MemberCount": "{count, plural, =0 {aucun employé} =1 {1 employé} other {# employés}}", "AssignLead": "Assigner le chef d'équipe", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Transfert d'employé", "MoveStaffDescr": "<PERSON><PERSON><PERSON><PERSON>-vous transférer l'employé de {current} à {department}", "Departments": "Départements", "Positions": "<PERSON><PERSON>", "ShowEmployees": "Afficher les employés", "AddEmployee": "Ajouter un employé", "SelectEmployee": "Sélectionner un employé", "Schedule": "Planning", "RequestType": "Type", "CreateRequest": "<PERSON><PERSON><PERSON> {type}", "Today": "<PERSON><PERSON><PERSON>'hui", "Summary": "Total", "NoEmployeesInDepartment": "Il n'y a aucun employé dans le département sélectionné", "Vacation": "Vacances", "Sick": "Malade", "PTO": "<PERSON><PERSON> payé", "PTOs": "PTOs", "Remote": "Télétravail", "Overtime": "Heures supplémentaires", "PTO2": "Congé payé/2", "Overtime2": "Heures supplémentaires/2", "EditRequest": "Modifier {type}", "EditRequestType": "Modifier le type", "ChooseNewType": "Choisir un nouveau type :", "UnchangeableType": "Ce type ne peut pas être modifié", "Request": "<PERSON><PERSON><PERSON>", "ExistingRequests": "Il y a déjà des demandes existantes pour les dates définies", "Staff": "Employé", "Member": "Membre", "Members": "Me<PERSON><PERSON>", "NoMembers": "Aucun membre ajouté", "AddMember": "Ajouter un membre", "Subscribers": "Abonnés", "PublicHoliday": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Title": "Titre", "Description": "Description", "PublicHolidays": "Jours fériés", "MarkAsPublicHoliday": "Marquer comme jour férié", "EditPublicHoliday": "Modifier le jour férié", "Manager": "Responsable", "Managers": "Responsables", "Dashboard": "Tableau de bord", "UpcomingHolidays": "Jours fériés à venir", "NoUpcomingHolidays": "Aucun jour férié à venir", "ManageHolidays": "<PERSON><PERSON><PERSON> les jours fériés", "CurrentEmployee": "Mes informations", "Export": "Exporter", "Separator": "Séparateur", "ChooseSeparator": "<PERSON><PERSON> le séparateur", "RequestCreated": "<PERSON><PERSON><PERSON>", "RequestUpdated": "<PERSON><PERSON><PERSON> mise à jour", "RequestRemoved": "<PERSON><PERSON><PERSON> supprimée", "ConfigLabel": "<PERSON><PERSON><PERSON><PERSON> humaines", "ConfigDescription": "Extension pour gérer la structure de l'organisation et le calendrier de travail des employés", "WorkingDays": "Jours travaill<PERSON>", "ReportedDays": "<PERSON><PERSON>", "Tasks": "Tâches", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Intitulé du poste", "EmploymentType": "Type d'emploi", "EmploymentTypeFullTime": "Temps plein", "EmploymentTypePartTime": "Temps partiel", "EmploymentTypeContractor": "Presta<PERSON>", "EmploymentTypeIntern": "Stagiaire", "EmploymentTypeTemporary": "Temporaire", "Location": "<PERSON><PERSON>", "WorkHoursPerWeek": "Heures hebdomadaires", "HireDate": "Date d'embauche", "TerminationDate": "Date de fin", "ProbationEndDate": "Fin de période d'essai", "VacationAllowance": "Droit aux congés", "VacationCarryover": "Report de congés", "SickAllowance": "Droit au congé maladie", "SickCarryover": "Report de congé maladie", "PersonalAllowance": "Droit aux jours personnels", "PersonalCarryover": "Report des jours personnels", "PTOAllowance": "Droit PTO", "PTOCarryover": "Report PTO", "Allowance": "<PERSON><PERSON>", "Carryover": "Report", "TimeOffBalance": "Solde des absences", "TimeOffUsed": "<PERSON><PERSON><PERSON><PERSON>", "TimeOffPending": "En attente", "TimeOffRemaining": "Restant", "TimeOffRequested": "<PERSON><PERSON><PERSON>", "InsufficientBalance": "Jours disponibles insuffisants", "RequestedDays": "Jours demandés", "AvailableDays": "Jours disponibles", "TimeOff": "<PERSON><PERSON><PERSON>", "Assets": "Actifs", "Documents": "Documents", "Performance": "Performance", "Training": "Formation", "Benefits": "Avantages", "AddHoliday": "A<PERSON>ter un jour férié", "NoHolidays": "Aucun jour férié défini pour le moment", "HolidayName": "Nom du jour férié", "HolidayNamePlaceholder": "ex. <PERSON><PERSON>", "HolidayDescription": "Description", "HolidayDescriptionPlaceholder": "ex. Bureau fermé", "DepartmentOptional": "Département (optionnel)", "EditHoliday": "Modifier le jour férié", "Date": "Date"}}