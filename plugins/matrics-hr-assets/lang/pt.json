{"string": {"Department": "Departamento", "ParentDepartmentLabel": "Departamento Superior", "Structure": "Estrutura", "OrgChart": "Organograma", "CreateDepartment": "Criar departamento", "CreateDepartmentLabel": "Departamento", "DepartmentPlaceholder": "Departamento", "TeamLead": "Líder de equipa", "UnAssignLead": "Retirar líder de equipa", "MemberCount": "{count, plural, =0 {sem colaboradores} =1 {1 colaborador} other {# colaboradores}}", "AssignLead": "Atribuir líder de equipa", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Transferência de colaborador", "MoveStaffDescr": "Pretende transferir o colaborador de {current} para {department}?", "Departments": "Departamentos", "Positions": "Cargos", "ShowEmployees": "Mostrar colaboradores", "AddEmployee": "Adicionar cola<PERSON>", "SelectEmployee": "Selecionar colaborador", "Schedule": "<PERSON><PERSON><PERSON><PERSON>", "RequestType": "Tipo", "CreateRequest": "Criar {type}", "Today": "Hoje", "Summary": "Total", "NoEmployeesInDepartment": "Não existem colaboradores no departamento selecionado", "Vacation": "<PERSON><PERSON><PERSON><PERSON>", "Sick": "Doença", "PTO": "Ausência remunerada", "PTOs": "PTOs", "Remote": "<PERSON><PERSON>", "Overtime": "<PERSON><PERSON> extra", "PTO2": "Ausência remunerada/2", "Overtime2": "Horas extra/2", "EditRequest": "Editar {type}", "EditRequestType": "<PERSON>ar tipo", "ChooseNewType": "Escolher novo tipo:", "UnchangeableType": "Este tipo não pode ser alterado", "Request": "Pedido", "ExistingRequests": "Já existem pedidos para as datas selecionadas", "Staff": "Colaborador", "Member": "Membro", "Members": "Me<PERSON><PERSON>", "NoMembers": "Nenhum membro adicionado", "AddMember": "<PERSON><PERSON><PERSON><PERSON> membro", "Subscribers": "Subscritores", "PublicHoliday": "<PERSON><PERSON><PERSON>", "Title": "<PERSON><PERSON><PERSON><PERSON>", "Description": "Descrição", "PublicHolidays": "Public holidays", "MarkAsPublicHoliday": "Marcar como feriado", "EditPublicHoliday": "<PERSON>ar feriado", "Manager": "Manager", "Managers": "Gestores", "Dashboard": "Dashboard", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Export": "Exportar", "Separator": "Separador", "ChooseSeparator": "Escolher separador", "RequestCreated": "Pedido criado", "RequestUpdated": "Pedido atualizado", "RequestRemoved": "Pedido removido", "ConfigLabel": "Recursos humanos", "ConfigDescription": "Extensão para gestão da estrutura organizacional e calendário de trabalho dos colaboradors", "WorkingDays": "Working days", "ReportedDays": "Reported days", "Tasks": "Tasks", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "Benefí<PERSON>s", "AddHoliday": "Adicionar feriado", "NoHolidays": "Nenhum feriado definido ainda", "HolidayName": "Nome do feriado", "HolidayNamePlaceholder": "ex. Natal", "HolidayDescription": "Descrição", "HolidayDescriptionPlaceholder": "ex. Escritório fechado", "DepartmentOptional": "Departamento (opcional)", "EditHoliday": "<PERSON>ar feriado", "Date": "Data"}}