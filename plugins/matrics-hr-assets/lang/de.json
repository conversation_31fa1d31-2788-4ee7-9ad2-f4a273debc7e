{"string": {"Department": "Abteilung", "ParentDepartmentLabel": "Übergeordnete Abteilung", "Structure": "Struktur", "OrgChart": "Organigramm", "CreateDepartment": "Abteilung erstellen", "CreateDepartmentLabel": "Abteilung", "DepartmentPlaceholder": "Abteilung", "TeamLead": "Teamleiter", "UnAssignLead": "Teamleiter entfernen", "MemberCount": "{count, plural, =0 {k<PERSON><PERSON>} =1 {1 <PERSON><PERSON><PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>}}", "AssignLead": "<PERSON><PERSON><PERSON>", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Mitarbeiterversetzung", "MoveStaffDescr": "Möchten Sie den Mitarbeiter von {current} nach {department} versetzen", "Departments": "Abteilungen", "Positions": "Positionen", "ShowEmployees": "Mitarbeiter anzeigen", "AddEmployee": "Mitarbeiter hinzufügen", "SelectEmployee": "Mitarbeiter auswählen", "Schedule": "Zeitplan", "RequestType": "<PERSON><PERSON>", "CreateRequest": "{type} erstellen", "Today": "<PERSON><PERSON>", "Summary": "Gesamt", "NoEmployeesInDepartment": "In der ausgewählten Abteilung gibt es keine Mitarbeiter", "Vacation": "<PERSON><PERSON><PERSON><PERSON>", "Sick": "Krankheit", "PTO": "<PERSON><PERSON><PERSON><PERSON>", "PTOs": "Bezahlte Urlaubstage", "Remote": "Homeoffice", "Overtime": "Überstunden", "PTO2": "Bezahlter Urlaub/2", "Overtime2": "Überstunden/2", "EditRequest": "{type} bearbeiten", "EditRequestType": "<PERSON><PERSON> bear<PERSON>ten", "ChooseNewType": "Neuen Typ wählen:", "UnchangeableType": "<PERSON>ser Ty<PERSON> kann nicht geändert werden", "Request": "<PERSON><PERSON><PERSON>", "ExistingRequests": "<PERSON>ür die gewählten Daten existieren bereits Anträge", "Staff": "<PERSON><PERSON><PERSON><PERSON>", "Member": "<PERSON><PERSON><PERSON><PERSON>", "Members": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NoMembers": "<PERSON><PERSON>", "AddMember": "<PERSON><PERSON><PERSON><PERSON> hinz<PERSON>", "Subscribers": "Abonnenten", "PublicHoliday": "<PERSON><PERSON><PERSON>", "Title": "Titel", "Description": "Beschreibung", "PublicHolidays": "<PERSON><PERSON><PERSON>", "MarkAsPublicHoliday": "Als Feiertag markieren", "EditPublicHoliday": "<PERSON><PERSON><PERSON> bearbeiten", "Manager": "Manager", "Managers": "Manager", "Dashboard": "Dashboard", "UpcomingHolidays": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NoUpcomingHolidays": "<PERSON><PERSON> be<PERSON>", "ManageHolidays": "<PERSON><PERSON><PERSON> verwalten", "CurrentEmployee": "<PERSON><PERSON>", "Export": "Exportieren", "Separator": "Trennzeichen", "ChooseSeparator": "Trennzeichen wählen", "RequestCreated": "<PERSON><PERSON><PERSON> er<PERSON>t", "RequestUpdated": "<PERSON><PERSON><PERSON>", "RequestRemoved": "<PERSON><PERSON><PERSON> entfernt", "Payload": "Nutzdaten", "ConfigLabel": "Personalwesen", "ConfigDescription": "Erweiterung zur Verwaltung der Organisationsstruktur und des Mitarbeiter-Arbeitskalenders", "WorkingDays": "Arbeitstage", "ReportedDays": "Gemeldete Tage", "Tasks": "Aufgaben", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Berufsbezeichnung", "EmploymentType": "Beschäftigungsart", "EmploymentTypeFullTime": "Vollzeit", "EmploymentTypePartTime": "Teilzeit", "EmploymentTypeContractor": "Auftragnehm<PERSON>", "EmploymentTypeIntern": "Praktikant", "EmploymentTypeTemporary": "<PERSON><PERSON><PERSON><PERSON>", "Location": "<PERSON><PERSON>", "WorkHoursPerWeek": "Wöchentliche Stunden", "HireDate": "Einstellungsdatum", "TerminationDate": "Enddatum", "ProbationEndDate": "Ende der Probezeit", "VacationAllowance": "Urlaubskontingent", "VacationCarryover": "Urlaubsübertrag", "SickAllowance": "Krankenurlaubskontingent", "SickCarryover": "Übertrag Krankheitstage", "PersonalAllowance": "Kontingent persönliche Tage", "PersonalCarryover": "Übertrag persönliche Tage", "PTOAllowance": "PTO‑Kontingent", "PTOCarryover": "PTO‑Übertrag", "Allowance": "<PERSON><PERSON><PERSON>", "Carryover": "Übertrag", "TimeOffBalance": "Zeitsaldo", "TimeOffUsed": "Ver<PERSON><PERSON>t", "TimeOffPending": "<PERSON><PERSON><PERSON><PERSON>", "TimeOffRemaining": "Verbleibend", "TimeOffRequested": "<PERSON><PERSON><PERSON><PERSON>", "InsufficientBalance": "Nicht genügend verfügbare Tage", "RequestedDays": "Angeforderte Tage", "AvailableDays": "Verfügbare Tage", "TimeOff": "Freizeit", "Assets": "Vermögenswerte", "Documents": "Dokumente", "Performance": "Le<PERSON><PERSON>", "Training": "Schulung", "Benefits": "<PERSON><PERSON><PERSON><PERSON>", "AddHoliday": "<PERSON><PERSON><PERSON> hi<PERSON>ufü<PERSON>", "NoHolidays": "<PERSON>ch keine Feier<PERSON> definiert", "HolidayName": "Name des Feiertags", "HolidayNamePlaceholder": "z.<PERSON><PERSON>", "HolidayDescription": "Beschreibung", "HolidayDescriptionPlaceholder": "z.B. Büro g<PERSON>chlossen", "DepartmentOptional": "Abteilung (optional)", "EditHoliday": "<PERSON><PERSON><PERSON> bearbeiten", "Date": "Datum"}}