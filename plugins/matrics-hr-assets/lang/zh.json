{"string": {"Department": "部门", "ParentDepartmentLabel": "上级部门", "Structure": "结构", "OrgChart": "组织架构图", "CreateDepartment": "创建部门", "CreateDepartmentLabel": "部门", "DepartmentPlaceholder": "部门", "TeamLead": "团队负责人", "UnAssignLead": "取消分配团队负责人", "MemberCount": "{count, plural, =0 {无员工} =1 {1 名员工} other {# 名员工}}", "AssignLead": "分配团队负责人", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "员工调动", "MoveStaffDescr": "您是否要将员工从 {current} 调动到 {department}", "Departments": "部门", "Positions": "职位", "ShowEmployees": "显示员工", "AddEmployee": "添加员工", "SelectEmployee": "选择员工", "Schedule": "日程安排", "RequestType": "类型", "CreateRequest": "创建 {type}", "Today": "今天", "Summary": "总计", "NoEmployeesInDepartment": "所选部门没有员工", "Vacation": "休假", "Sick": "病假", "PTO": "带薪休假", "PTOs": "PTOs", "Remote": "远程工作", "Overtime": "加班", "PTO2": "带薪休假/2", "Overtime2": "加班/2", "EditRequest": "编辑 {type}", "EditRequestType": "编辑类型", "ChooseNewType": "选择新类型：", "UnchangeableType": "此类型无法更改", "Request": "请求", "ExistingRequests": "已存在设定日期的请求", "Staff": "员工", "Member": "成员", "Members": "成员", "NoMembers": "没有添加成员", "AddMember": "添加成员", "Subscribers": "订阅者", "PublicHoliday": "公共假期", "Title": "标题", "Description": "描述", "PublicHolidays": "Public holidays", "MarkAsPublicHoliday": "标记为公共假期", "EditPublicHoliday": "编辑公共假期", "Manager": "Manager", "Managers": "管理人", "Dashboard": "Dashboard", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Export": "导出", "Separator": "分隔符", "ChooseSeparator": "选择分隔符", "RequestCreated": "请求已创建", "RequestUpdated": "请求已更新", "RequestRemoved": "请求已移除", "ConfigLabel": "人力资源", "ConfigDescription": "用于管理组织结构和员工工作日历的扩展", "WorkingDays": "Working days", "ReportedDays": "Reported days", "Tasks": "Tasks", "TPD": "TPD", "EXTRa": "EXTRa", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "福利", "AddHoliday": "添加节假日", "NoHolidays": "尚未定义节假日", "HolidayName": "节假日名称", "HolidayNamePlaceholder": "例如：圣诞节", "HolidayDescription": "描述", "HolidayDescriptionPlaceholder": "例如：办公室关闭", "DepartmentOptional": "部门（可选）", "EditHoliday": "编辑节假日", "Date": "日期"}}