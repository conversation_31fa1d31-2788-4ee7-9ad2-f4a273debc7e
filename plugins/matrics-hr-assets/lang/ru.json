{"string": {"Department": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "ParentDepartmentLabel": "Родительский департамент", "Structure": "Структура", "OrgChart": "Оргструктура", "CreateDepartment": "Создать департамент", "CreateDepartmentLabel": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "DepartmentPlaceholder": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "TeamLead": "Мен<PERSON>д<PERSON><PERSON><PERSON>", "UnAssignLead": "Отменить назначение менеджера", "MemberCount": "{count, plural, =0 {нет сотрудников} one {# сотрудник} few {# сотрудника} other {# сотрудников}}", "AssignLead": "Назначить менеджера", "TeamLeadTooltip": "{value}", "HRApplication": "People & Culture", "MoveStaff": "Перевод сотрудника", "MoveStaffDescr": "Вы действительно хотите перевести сотрудника из {current} в {department}", "Departments": "Департаменты", "Positions": "Позиции", "ShowEmployees": "Просмотреть сотрудников", "AddEmployee": "Добавить сотрудника", "SelectEmployee": "Выберите сотрудника", "Schedule": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RequestType": "Тип", "CreateRequest": "Создать {type}", "Today": "Сегодня", "Summary": "Итого", "NoEmployeesInDepartment": "Нет сотрудников в выбранном департаменте", "Vacation": "Отпуск", "Sick": "Больничный", "PTO": "Отгул(PTO)", "PTOs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Remote": "Удаленно", "Overtime": "Переработка", "PTO2": "Отгул(PTO)/2", "Overtime2": "Переработка/2", "EditRequest": "Редактировать {type}", "EditRequestType": "Редактировать тип", "ChooseNewType": "Выберите новый тип:", "UnchangeableType": "Этот тип нельзя поменять", "Request": "Запрос", "ExistingRequests": "На данные даты уже существуют запросы", "Staff": "Работник", "Member": "Сотрудник", "Members": "Сотрудники", "NoMembers": "Нет добавленных сотрудников", "AddMember": "Добавить сотрудника", "Subscribers": "Подписчики", "PublicHoliday": "Праздничный день", "Title": "Название", "Description": "Описание", "PublicHolidays": "Праздничных дней", "MarkAsPublicHoliday": "Отметить как праздничный день", "EditPublicHoliday": "Редактировать праздничный день", "Manager": "Manager", "Managers": "Менеджера", "Dashboard": "Dashboard", "UpcomingHolidays": "Upcoming holidays", "NoUpcomingHolidays": "No upcoming holidays", "ManageHolidays": "Manage holidays", "CurrentEmployee": "My details", "Export": "Экспортировать", "Separator": "Разделитель", "ChooseSeparator": "Выберите разделитель", "RequestCreated": "Запрос создан", "RequestUpdated": "Запрос изменен", "RequestRemoved": "Запрос удален", "ConfigLabel": "Производственный календарь", "ConfigDescription": "Расширение реализующее производственный календарь", "WorkingDays": "Рабочих дней", "ReportedDays": "Отчетных дней", "Tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TPD": "Оплачиваемых дней", "EXTRa": "EXTRa", "JobTitle": "Job title", "EmploymentType": "Employment type", "EmploymentTypeFullTime": "Full-time", "EmploymentTypePartTime": "Part-time", "EmploymentTypeContractor": "Contractor", "EmploymentTypeIntern": "Intern", "EmploymentTypeTemporary": "Temporary", "Location": "Location", "WorkHoursPerWeek": "Weekly hours", "HireDate": "Hire date", "TerminationDate": "End date", "ProbationEndDate": "Probation end", "VacationAllowance": "Vacation allowance", "VacationCarryover": "Vacation carryover", "SickAllowance": "Sick leave allowance", "SickCarryover": "Sick leave carryover", "PersonalAllowance": "Personal days allowance", "PersonalCarryover": "Personal days carryover", "PTOAllowance": "PTO allowance", "PTOCarryover": "PTO carryover", "Allowance": "Allowance", "Carryover": "Carryover", "TimeOffBalance": "Time-off balance", "TimeOffUsed": "Used", "TimeOffPending": "Pending", "TimeOffRemaining": "Remaining", "TimeOffRequested": "Requested", "InsufficientBalance": "Not enough available days", "RequestedDays": "Requested days", "AvailableDays": "Available days", "TimeOff": "Time off", "Assets": "Assets", "Documents": "Documents", "Performance": "Performance", "Training": "Training", "Benefits": "Льготы", "AddHoliday": "Добавить праздник", "NoHolidays": "Праздники еще не определены", "HolidayName": "Название праздника", "HolidayNamePlaceholder": "напр. Рождество", "HolidayDescription": "Описание", "HolidayDescriptionPlaceholder": "напр. <PERSON><PERSON><PERSON><PERSON> закрыт", "DepartmentOptional": "Отдел (опционально)", "EditHoliday": "Редактировать праздник", "Date": "Дата"}}