{"name": "@hcengineering/annotationtool", "version": "0.1.0", "main": "lib/index.js", "svelte": "src/index.ts", "types": "types/index.d.ts", "files": ["lib/**/*", "types/**/*", "tsconfig.json"], "author": "Hardcore Engineering Inc.", "license": "EPL-2.0", "scripts": {"build": "compile", "build:watch": "compile", "format": "format src", "test": "jest --passWithNoTests --silent", "svelte-check": "do-svelte-check", "_phase:svelte-check": "do-svelte-check", "_phase:build": "compile transpile src", "_phase:test": "jest --passWithNoTests --silent", "_phase:format": "format src", "_phase:validate": "compile validate"}, "dependencies": {"@hcengineering/platform": "^0.6.11", "@hcengineering/core": "^0.6.32", "@hcengineering/ui": "^0.6.15", "@hcengineering/view": "^0.6.13", "@hcengineering/tasker": "^0.6.24", "@hcengineering/notification": "^0.6.23", "@hcengineering/chunter": "^0.6.20"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@types/jest": "^29.5.5", "jest": "^29.7.0", "typescript": "^5.8.3"}}