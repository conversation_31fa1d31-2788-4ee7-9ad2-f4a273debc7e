<!--
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { AttachmentStyledBox } from '@hcengineering/attachment-resources'
  import calendar from '@hcengineering/calendar'
  import { Employee } from '@hcengineering/contact'
  import { EmployeeBox } from '@hcengineering/contact-resources'
  import core, { DocumentQuery, generateId, Markup, Ref } from '@hcengineering/core'
import { Request, RequestType, Staff, timeToTzDate, RequestStatus, computeRequestedDays, type TimeOffPolicy, type TimeOffBalance } from '@hcengineering/matrics-hr'
  import { translate } from '@hcengineering/platform'
  import { Card, createQuery, getClient } from '@hcengineering/presentation'
  import { EmptyMarkup } from '@hcengineering/text'
import ui, {
    Button,
    DateRangePresenter,
    DropdownLabelsIntl,
    IconAttachment,
    Label,
    Toggle,
    themeStore
  } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import hr from '../plugin'
  import { getRequests } from '../utils'

  export let staff: Staff
  export let date: Date
  export let readonly: boolean
  export let docQuery: DocumentQuery<Employee> | undefined
  export let employeeRequests: Map<Ref<Staff>, Request[]>

  let description: Markup = EmptyMarkup
  let employee: Ref<Employee> = staff._id

  const objectId: Ref<Request> = generateId()
  let descriptionBox: AttachmentStyledBox

  const dispatch = createEventDispatcher()
  const client = getClient()
  const typesQuery = createQuery()
  const policiesQuery = createQuery()
  const balancesQuery = createQuery()

  let types: RequestType[] = []
  let type: RequestType | undefined = undefined
  let typeLabel = ''
  $: type && translate(type.label, {}, $themeStore.language).then((p) => (typeLabel = p))

  typesQuery.query(hr.class.RequestType, {}, (res) => {
    types = res
    if (type === undefined) {
      type = types[0]
    }
  })

  let value = new Date(date).getTime()
  $: dueDate = new Date(value).getTime()

  let halfDayStart = false
  let halfDayEnd = false
  let requestedDays = 0
  let supportsHalfDays = false

  $: supportsHalfDays = type !== undefined && type.value < 0

  $: if (!supportsHalfDays) {
    halfDayStart = false
    halfDayEnd = false
  }

  $: requestedDays = (() => {
    if (value == null || dueDate == null) return 0
    const options = {
      halfDayStart: supportsHalfDays && halfDayStart,
      halfDayEnd: supportsHalfDays && halfDayEnd
    }
    return computeRequestedDays(timeToTzDate(value), timeToTzDate(dueDate), options)
  })()

  // Determine staff of selected employee (could differ if manager creates on behalf)
  const staffQuery = createQuery()
  let selectedStaff: Staff | undefined = staff
  $: if (employee !== undefined && employee !== null) {
    if (employee === staff._id) selectedStaff = staff
    else {
      staffQuery.query(hr.mixin.Staff, { _id: employee as Ref<Staff> }, (res) => {
        selectedStaff = res[0]
      })
    }
  }

  // Active TimeOffPolicies mapped by RequestType
  let policiesByType = new Map<Ref<RequestType>, TimeOffPolicy>()
  policiesQuery.query(hr.class.TimeOffPolicy, { active: true }, (res) => {
    policiesByType = new Map(res.map((p) => [p.requestType as Ref<RequestType>, p]))
  })

  // TimeOffBalances for the selected staff
  let timeOffBalances: TimeOffBalance[] = []
  $: if (selectedStaff !== undefined) {
    balancesQuery.query(
      hr.class.TimeOffBalance,
      { staff: selectedStaff._id as Ref<Staff> },
      (res) => {
        timeOffBalances = res
      }
    )
  } else {
    timeOffBalances = []
  }

  // Only show time-off types that have an active policy
  const timeOffTypeIds = new Set([
    hr.ids.PTO,
    hr.ids.PTO2,
    hr.ids.Vacation,
    hr.ids.Sick,
    hr.ids.Remote,
    hr.ids.Overtime,
    hr.ids.Overtime2
  ])

  $: selectableTypes = types.filter((t) => {
    const isTimeOff = timeOffTypeIds.has(t._id as any)
    if (!isTimeOff) return true
    return policiesByType.has(t._id as Ref<RequestType>)
  })

  // Ensure selected type remains valid
  $: if (type && !selectableTypes.find((x) => x._id === type?._id)) {
    type = selectableTypes[0]
  }

// Calculate requested units (days) and check balances
$: requestedUnits = (() => {
  if (type === undefined) return 0
  if (type.value >= 0) return 0
  const unit = Math.abs(type.value)
  return requestedDays * unit
})()

  function getAvailableBalance (): number {
    if (selectedStaff === undefined || type === undefined) return Infinity
    // Find applicable policy by request type
    const policy = policiesByType.get(type._id as Ref<RequestType>)
    if (!policy) return Infinity
    // Find matching balance for this policy
    const bal = timeOffBalances.find((b) => b.policy === (policy._id as any))
    if (!bal) return Infinity
    // If negative balances are allowed, treat as unlimited from UI perspective
    if (policy.allowNegativeBalance === true) return Infinity
    // Available = balance - pending (match server check)
    return (bal.balance ?? 0) - (bal.pending ?? 0)
  }

  $: notEnoughBalance = (() => {
    if (type === undefined) return false
    if (type.value >= 0) return false
    const available = getAvailableBalance()
    return requestedUnits > available
  })()

  export function canClose (): boolean {
    return description.length === 0
  }

  async function saveRequest () {
    let date: number | undefined
    if (value != null) date = value
    if (date === undefined) return
    if (type === undefined) return
    if (employee === null) return
  // Calculate requested days (business days) for persistence
  const options = {
    halfDayStart: supportsHalfDays && halfDayStart,
    halfDayEnd: supportsHalfDays && halfDayEnd
  }
  const requestedDaysValue = computeRequestedDays(timeToTzDate(value), timeToTzDate(dueDate), options)
    const payload: any = {
      type: type._id,
      tzDate: timeToTzDate(date),
      tzDueDate: timeToTzDate(dueDate),
      description,
      department: staff.department,
      status: RequestStatus.Pending,
      submittedDate: Date.now(),
    requestedDays: requestedDaysValue,
    halfDayStart: supportsHalfDays && halfDayStart,
    halfDayEnd: supportsHalfDays && halfDayEnd
    }
    await client.addCollection(hr.class.Request, core.space.Workspace, employee, staff._class, 'requests', payload)
    await descriptionBox.createAttachments()
  }

  function typeSelected (_id: Ref<RequestType>): void {
    type = types.find((p) => p._id === _id)
  }

  function moreThanLimit (
    employeeRequests: Map<Ref<Staff>, Request[]>,
    staff: Staff,
    startDate: Date,
    endDate: Date,
    type: RequestType | undefined
  ): boolean {
    if (employeeRequests === undefined) return true
    if (type === undefined) return true
    const requests = getRequests(employeeRequests, startDate, endDate, staff._id)
    return requests.length > 0
  }
  $: notLimit = moreThanLimit(
    employeeRequests,
    staff,
    new Date(value),
    new Date(new Date(dueDate).setHours(23, 59, 59, 999)),
    type
  )
</script>

  <Card
  label={hr.string.CreateRequest}
  labelProps={{ type: typeLabel }}
  okAction={saveRequest}
  canSave={value !== undefined && !notLimit && !notEnoughBalance}
  gap={'gapV-4'}
  on:close={() => {
    dispatch('close')
  }}
  on:changeContent
>
  <svelte:fragment slot="header">
    <EmployeeBox
      label={hr.string.SelectEmployee}
      placeholder={hr.string.SelectEmployee}
      kind={'regular'}
      size={'large'}
      bind:value={employee}
      {readonly}
      showNavigate={false}
      {docQuery}
    />
  </svelte:fragment>
  <DropdownLabelsIntl
    items={selectableTypes.map((p) => {
      return { id: p._id, label: p.label }
    })}
    label={hr.string.RequestType}
    on:selected={(e) => {
      typeSelected(e.detail)
    }}
  />
  <AttachmentStyledBox
    bind:this={descriptionBox}
    {objectId}
    _class={hr.class.Request}
    space={core.space.Workspace}
    alwaysEdit
    showButtons={false}
    maxHeight={'card'}
    bind:content={description}
    placeholder={core.string.Description}
  />
  <svelte:fragment slot="pool">
    <DateRangePresenter bind:value editable labelNull={ui.string.SelectDate} kind={'regular'} size={'large'} />
    <DateRangePresenter
      bind:value={dueDate}
      labelNull={calendar.string.DueTo}
      editable
      kind={'regular'}
      size={'large'}
    />
    {#if supportsHalfDays}
      <div class="halfday-options">
        <label class="halfday-option">
          <Toggle bind:on={halfDayStart} />
          <span><Label label={hr.string.HalfDayStart} /></span>
        </label>
        <label class="halfday-option">
          <Toggle bind:on={halfDayEnd} />
          <span><Label label={hr.string.HalfDayEnd} /></span>
        </label>
      </div>
    {/if}
  </svelte:fragment>
  <svelte:fragment slot="footer">
    <Button
      icon={IconAttachment}
      size={'large'}
      on:click={() => {
        descriptionBox.handleAttach()
      }}
    />
  </svelte:fragment>
  <svelte:fragment slot="error">
    {#if notLimit}
      <Label label={hr.string.ExistingRequests} />
    {/if}
    {#if notEnoughBalance}
      <Label label={hr.string.InsufficientBalance} />
    {/if}
  </svelte:fragment>
</Card>

<style lang="scss">
  .halfday-options {
    display: flex;
    gap: 1rem;
    padding: 0.5rem 0;
  }

  .halfday-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--theme-caption-color);
  }
</style>
