<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { type Ref } from '@hcengineering/core'
  import {
    TimeOffAccrualFrequency,
    TimeOffAccrualMethod,
    type TimeOffPolicy
  } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { type IntlString } from '@hcengineering/platform'
import ui, { Button, Icon, Label, Scroller, showPopup } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import hr from '../plugin'
  import EditTimeOffPolicy from './EditTimeOffPolicy.svelte'

  const policiesQuery = createQuery()

  let policies: TimeOffPolicy[] = []

  const methodLabels: Record<TimeOffAccrualMethod, IntlString> = {
    [TimeOffAccrualMethod.None]: hr.string.AccrualMethodNone,
    [TimeOffAccrualMethod.LumpSum]: hr.string.AccrualMethodLumpSum,
    [TimeOffAccrualMethod.Periodic]: hr.string.AccrualMethodPeriodic,
    [TimeOffAccrualMethod.Hourly]: hr.string.AccrualMethodHourly
  }

  const frequencyLabels: Partial<Record<TimeOffAccrualFrequency, IntlString>> = {
    [TimeOffAccrualFrequency.Monthly]: hr.string.AccrualFrequencyMonthly,
    [TimeOffAccrualFrequency.Quarterly]: hr.string.AccrualFrequencyQuarterly,
    [TimeOffAccrualFrequency.Yearly]: hr.string.AccrualFrequencyYearly,
    [TimeOffAccrualFrequency.Anniversary]: hr.string.AccrualFrequencyAnniversary,
    [TimeOffAccrualFrequency.PerPayPeriod]: hr.string.AccrualFrequencyPerPayPeriod
  }

  policiesQuery.query(
    hr.class.TimeOffPolicy,
    {},
    (res) => {
      policies = res
    },
    { sort: { title: 1 } }
  )

  function createPolicy (): void {
    showPopup(EditTimeOffPolicy, {})
  }

  function editPolicy (policyId: Ref<TimeOffPolicy>): void {
    showPopup(EditTimeOffPolicy, { policyId })
  }

  function formatNumber (value?: number | null): string {
    if (value == null) return '—'
    return Number.isFinite(value) ? new Intl.NumberFormat(undefined, { maximumFractionDigits: 2 }).format(value) : '—'
  }

  function frequencyLabel (policy: TimeOffPolicy): IntlString | undefined {
    if (policy.accrualMethod === TimeOffAccrualMethod.None) return undefined
    if (policy.accrualFrequency == null) return undefined
    return frequencyLabels[policy.accrualFrequency]
  }
</script>

<Scroller>
  <div class="timeoff-policy-list">
    <div class="header flex-row-center flex-between">
      <div class="flex-row-center gap-2">
        <Icon icon={hr.icon.PTO} size={'small'} />
        <Label label={hr.string.TimeOffPolicies} />
        <span class="count">({policies.length})</span>
      </div>
      <Button
        label={hr.string.CreateTimeOffPolicy}
        icon={view.icon.Add}
        kind={'ghost'}
        size={'small'}
        on:click={createPolicy}
      />
    </div>

    <div class="items">
      {#each policies as policy (policy._id)}
        <div class="item" role="button" tabindex="0" on:click={() => editPolicy(policy._id)}>
          <div class="item__heading">
            <div class="title">{policy.title}</div>
            <div class="badges">
              <span class:inactive={!policy.active} class="badge">
                <Label label={policy.active ? hr.string.Active : hr.string.Inactive} />
              </span>
              <Label class="badge" label={methodLabels[policy.accrualMethod]} />
              {#if frequencyLabel(policy)}
                <Label class="badge" label={frequencyLabel(policy)} />
              {/if}
            </div>
          </div>

          <div class="grid">
            <div class="metric">
              <div class="metric__label"><Label label={hr.string.AccrualRate} /></div>
              <div class="metric__value">{formatNumber(policy.accrualRate)}</div>
            </div>
            <div class="metric">
              <div class="metric__label"><Label label={hr.string.CarryoverLimit} /></div>
              <div class="metric__value">{formatNumber(policy.carryoverLimit)}</div>
            </div>
            <div class="metric">
              <div class="metric__label"><Label label={hr.string.CarryoverExpiryDays} /></div>
              <div class="metric__value">{policy.carryoverExpiryDays ?? '—'}</div>
            </div>
            <div class="metric">
              <div class="metric__label"><Label label={hr.string.AllowNegativeBalance} /></div>
              <div class="metric__value">{policy.allowNegativeBalance === true ? 'Yes' : 'No'}</div>
            </div>
            <div class="metric">
              <div class="metric__label"><Label label={hr.string.AllowHalfDays} /></div>
              <div class="metric__value">{policy.allowHalfDays === true ? 'Yes' : 'No'}</div>
            </div>
            <div class="metric">
              <div class="metric__label"><Label label={hr.string.WaitingPeriodDays} /></div>
              <div class="metric__value">{policy.waitingPeriodDays ?? '—'}</div>
            </div>
          </div>
        </div>
      {/each}

      {#if policies.length === 0}
        <div class="empty">
          <div class="empty__title">No time-off policies yet</div>
          <div class="empty__description">Create your first policy to define accruals and carryover rules.</div>
          <Button
            label={hr.string.CreateTimeOffPolicy}
            icon={view.icon.Add}
            kind={'primary'}
            size={'medium'}
            on:click={createPolicy}
          />
        </div>
      {/if}
    </div>
  </div>
</Scroller>

<style lang="scss">
  .timeoff-policy-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .header {
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .count {
    font-size: 0.875rem;
    color: var(--theme-dark-color);
  }

  .items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .item {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    background: var(--theme-button-default);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover,
    &:focus-visible {
      outline: none;
      border-color: var(--theme-list-divider-color);
      background: var(--theme-button-hovered);
    }
  }

  .item__heading {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .title {
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    background: var(--theme-chip-background, rgba(0, 0, 0, 0.05));
    color: var(--theme-caption-color);

    &.inactive {
      background: rgba(255, 87, 87, 0.15);
      color: var(--theme-danger-color);
    }
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(10rem, 1fr));
    gap: 0.75rem;
  }

  .metric {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background: var(--theme-button-default-alt, rgba(0, 0, 0, 0.03));
  }

  .metric__label {
    font-size: 0.75rem;
    color: var(--theme-dark-color);
    opacity: 0.8;
  }

  .metric__value {
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 2rem;
    border: 1px dashed var(--theme-divider-color);
    border-radius: 0.75rem;
    background: var(--theme-surface-muted, transparent);
    text-align: center;
    color: var(--theme-dark-color);
  }

  .empty__title {
    font-weight: 600;
    font-size: 1rem;
  }

  .empty__description {
    max-width: 24rem;
    font-size: 0.875rem;
  }

  @media (max-width: 768px) {
    .grid {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (max-width: 540px) {
    .grid {
      grid-template-columns: 1fr;
    }
  }
</style>

