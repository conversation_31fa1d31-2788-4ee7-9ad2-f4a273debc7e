<!--
// Copyright © 2022, 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import contact from '@hcengineering/contact'
  import { Ref, WithLookup } from '@hcengineering/core'
  import type { Department } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Label, Spinner, SearchInput } from '@hcengineering/ui'
  import { tick } from 'svelte'

  import hr from '../plugin'
  import OrgChartNode from './OrgChartNode.svelte'

  export let root: Ref<Department> | undefined = undefined
  export let departmentId: Ref<Department> | undefined = undefined
  export let focus: Ref<Department> | undefined = undefined

  const query = createQuery()

  let loading = true
  let headDepartment: WithLookup<Department> | undefined
  let departmentsById = new Map<Ref<Department>, WithLookup<Department>>()
  let childrenByParent = new Map<Ref<Department>, WithLookup<Department>[]>()
  let rootDepartments: WithLookup<Department>[] = []
  let viewport: HTMLDivElement | undefined
  let content: HTMLDivElement | undefined
  let translateX = 0
  let translateY = 0
  let scale = 1
  let isPanning = false
  let pointerId: number | null = null
  let startX = 0
  let startY = 0
  let originX = 0
  let originY = 0
  let hasAutoCentered = false
  let lastNodesKey: string | undefined
  let centerTask: Promise<void> | null = null
  let search = ''
  let visibleIds: Set<Ref<Department>> | undefined = undefined
  let forceExpandIds: Set<Ref<Department>> | undefined = undefined
  $: isSearching = (search ?? '').trim().length > 0

  const sortByName = (a: WithLookup<Department>, b: WithLookup<Department>): number =>
    a.name.localeCompare(b.name)

  query.query(
    hr.class.Department,
    {},
    (res: WithLookup<Department>[]) => {
      const nextDepartments = new Map<Ref<Department>, WithLookup<Department>>()
      const nextChildren = new Map<Ref<Department>, WithLookup<Department>[]>()
      const nextRoots: WithLookup<Department>[] = []
      let nextHead: WithLookup<Department> | undefined

      for (const doc of res) {
        nextDepartments.set(doc._id, doc)
        if (doc._id === hr.ids.Head) {
          nextHead = doc
        }
      }

      for (const doc of res) {
        if (doc.parent !== undefined) {
          const scoped = nextChildren.get(doc.parent) ?? []
          scoped.push(doc)
          nextChildren.set(doc.parent, scoped)
        } else {
          nextRoots.push(doc)
        }
      }

      for (const [key, value] of nextChildren.entries()) {
        value.sort(sortByName)
        nextChildren.set(key, [...value])
      }
      nextRoots.sort(sortByName)

      departmentsById = nextDepartments
      childrenByParent = nextChildren
      rootDepartments = nextRoots
      headDepartment = nextHead
      loading = false
    },
    {
      lookup: {
        teamLead: contact.mixin.Employee,
        members: contact.mixin.Employee
      }
    }
  )

  function getChildren (id: Ref<Department> | undefined): WithLookup<Department>[] {
    if (id === undefined) return []
    return childrenByParent.get(id) ?? []
  }

  $: highlightId = focus ?? (root === undefined ? departmentId : undefined)

  $: computedRoots = (() => {
    if (root !== undefined) {
      const node = departmentsById.get(root)
      if (node !== undefined) return [node]
    }

    if (root === undefined && departmentId !== undefined) {
      const node = departmentsById.get(departmentId)
      if (node !== undefined) return [node]
    }

    if (headDepartment !== undefined) {
      return [headDepartment]
    }

    return rootDepartments
  })()

  $: displayNodes = computedRoots.length > 0 ? computedRoots : rootDepartments
  $: hasNodes = displayNodes.length > 0
  $: lowerSearch = (search ?? '').trim().toLowerCase()
  $: if (!isSearching) {
    visibleIds = undefined
    forceExpandIds = undefined
  } else {
    const v = new Set<Ref<Department>>()
    const fe = new Set<Ref<Department>>()

    const matches = (d: WithLookup<Department>): boolean => {
      const name = (d.name ?? '').toLowerCase()
      const lead = (d.$lookup?.teamLead as any)?.name?.toLowerCase?.() ?? ''
      const members = ((d.$lookup?.members ?? []) as any[]).map((m) => (m?.name ?? '').toLowerCase())
      return (
        name.includes(lowerSearch) ||
        (lead !== '' && lead.includes(lowerSearch)) ||
        members.some((mn) => mn.includes(lowerSearch))
      )
    }

    const visit = (d: WithLookup<Department>, ancestors: Ref<Department>[]): boolean => {
      const children = getChildren(d._id)
      let childMatched = false
      for (const c of children) {
        const m = visit(c, [...ancestors, d._id])
        childMatched = childMatched || m
      }
      const selfMatched = matches(d)
      const anyMatch = selfMatched || childMatched
      if (anyMatch) {
        v.add(d._id)
        for (const a of ancestors) v.add(a)
        for (const a of ancestors) fe.add(a)
        if (childMatched) fe.add(d._id)
      }
      return anyMatch
    }

    for (const root of displayNodes) {
      visit(root, [])
    }

    visibleIds = v
    forceExpandIds = fe
  }

  $: nodesKey = displayNodes.map((node) => `${node._id}`).join('|')
  $: if (nodesKey !== lastNodesKey) {
    lastNodesKey = nodesKey
    hasAutoCentered = false
  }

  $: if (!loading && hasNodes && !hasAutoCentered && viewport !== undefined && content !== undefined) {
    scheduleCentering()
  }

  function scheduleCentering (): void {
    if (centerTask !== null) return
    centerTask = (async () => {
      const centered = await centerView()
      if (centered) {
        hasAutoCentered = true
      }
      centerTask = null
    })()
  }

  async function centerView (): Promise<boolean> {
    if (viewport === undefined || content === undefined) return false
    scale = 1
    translateX = 0
    translateY = 0
    await tick()
    const viewWidth = viewport.clientWidth
    const viewHeight = viewport.clientHeight
    const contentWidth = content.scrollWidth
    const contentHeight = content.scrollHeight
    translateX = (viewWidth - contentWidth) / 2
    translateY = Math.min(32, (viewHeight - contentHeight) / 2)
    return true
  }

  function resetView (): void {
    hasAutoCentered = false
    scheduleCentering()
  }

  const clamp = (value: number, min: number, max: number): number => Math.min(max, Math.max(min, value))

  function handlePointerDown (event: PointerEvent): void {
    if (viewport === undefined || event.button !== 0) return
    pointerId = event.pointerId
    isPanning = true
    startX = event.clientX
    startY = event.clientY
    originX = translateX
    originY = translateY
    viewport.setPointerCapture(pointerId)
    event.preventDefault()
  }

  function handlePointerMove (event: PointerEvent): void {
    if (!isPanning || pointerId === null || event.pointerId !== pointerId) return
    translateX = originX + (event.clientX - startX)
    translateY = originY + (event.clientY - startY)
  }

  function stopPanning (event: PointerEvent): void {
    if (pointerId === null || event.pointerId !== pointerId) return
    if (viewport !== undefined && viewport.hasPointerCapture(pointerId)) {
      viewport.releasePointerCapture(pointerId)
    }
    pointerId = null
    isPanning = false
  }

  function handleWheel (event: WheelEvent): void {
    if (viewport === undefined || content === undefined) return
    if (event.ctrlKey || event.metaKey) {
      const factor = event.deltaY < 0 ? 1.1 : 0.9
      const next = clamp(scale * factor, 0.6, 2.2)
      if (next === scale) return
      const rect = content.getBoundingClientRect()
      const offsetX = (event.clientX - rect.left) / scale
      const offsetY = (event.clientY - rect.top) / scale
      translateX -= offsetX * (next - scale)
      translateY -= offsetY * (next - scale)
      scale = next
    } else {
      translateX -= event.deltaX
      translateY -= event.deltaY
    }
  }
</script>

{#if loading}
  <div class="org-state">
    <Spinner size={'small'} />
  </div>
{:else if !hasNodes}
  <div class="org-state">
    <span>No departments found</span>
  </div>
{:else}
  <div class="org-wrapper">
    <div class="org-controls">
      <SearchInput value={search} on:change={(e) => (search = e.detail ?? '')} width={'18rem'} />
    </div>
    <div
      class="org-viewport"
      role="region"
      aria-label="Organization chart"
      bind:this={viewport}
      class:dragging={isPanning}
      on:pointerdown={handlePointerDown}
      on:pointermove={handlePointerMove}
      on:pointerup={stopPanning}
      on:pointerleave={stopPanning}
      on:pointercancel={stopPanning}
      on:dblclick={resetView}
      on:wheel|preventDefault={handleWheel}
    >
      <div class="org-canvas">
        <div
          class="org-content"
          class:dragging={isPanning}
          bind:this={content}
          style={`transform: translate3d(${translateX}px, ${translateY}px, 0) scale(${scale})`}
        >
          <ul class="org-tree">
            {#each displayNodes as node (node._id)}
              <OrgChartNode
                node={node}
                getChildren={getChildren}
                focusId={highlightId}
                depth={0}
                query={search}
                visibleIds={visibleIds}
                forceExpandIds={forceExpandIds}
              />
            {/each}
          </ul>
          {#if isSearching && (visibleIds?.size ?? 0) === 0}
            <div class="org-empty">
              <span>No matches found</span>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}

<style lang="scss">
  .org-wrapper {
    position: relative;
    padding: 0;
    height: 100%;
    min-height: 24rem;
  }

  .org-viewport {
    position: relative;
    height: 100%;
    width: 100%;
    background: var(--theme-popup-color);
    border-radius: 1rem;
    border: 1px solid var(--theme-divider-color);
    box-shadow: inset 0 0 0 1px rgba(9, 30, 66, 0.02);
    overflow: hidden;
    cursor: grab;
    touch-action: none;
    user-select: none;
  }

  .org-controls {
    position: absolute;
    right: 1rem;
    top: 1rem;
    z-index: 10;
  }

  .org-viewport.dragging {
    cursor: grabbing;
  }

  .org-canvas {
    position: absolute;
    inset: 0;
  }

  .org-content {
    min-width: max-content;
    min-height: max-content;
    padding: 2rem 3rem 3rem;
    transform-origin: 50% 0;
    transition: transform 0.12s ease-out;
  }

  .org-content.dragging {
    transition: none;
  }

  .org-tree {
    display: flex;
    justify-content: center;
    position: relative;
    list-style: none;
    margin: 0;
    padding: 0;
    --org-line-color: var(--theme-divider-color);
    --org-line-width: 1px;
    --org-vertical-spacing: 2.5rem;
    --org-horizontal-spacing: 1.5rem;
    flex-direction: column;
    align-items: center;
  }

  .org-tree :global(ul) {
    padding-top: var(--org-vertical-spacing);
    gap: var(--org-horizontal-spacing);
  }

  .org-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 6rem;
    color: var(--theme-dark-color);
    font-size: 0.875rem;
  }

  .org-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-dark-color);
    padding-top: 1rem;
    font-size: 0.875rem;
  }
</style>
