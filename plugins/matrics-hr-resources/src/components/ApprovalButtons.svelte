<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Request, RequestStatus, Department, Staff } from '@hcengineering/matrics-hr'
  import { Employee } from '@hcengineering/contact'
  import { Button, showPopup } from '@hcengineering/ui'
  import { getClient, createQuery } from '@hcengineering/presentation'
  import { getCurrentAccount, Ref } from '@hcengineering/core'
  import hr from '../plugin'
  import { MarkupEditor } from '@hcengineering/text-editor-resources'
  import { EmptyMarkup } from '@hcengineering/text'

  export let object: Request

  const client = getClient()
  const currentAccount = getCurrentAccount()
  
  let currentEmployee: Staff | undefined
  let department: Department | undefined
  let canApprove = false

  const employeeQuery = createQuery()
  const departmentQuery = createQuery()

  // Get current employee
  $: employeeQuery.query(hr.mixin.Staff, { _id: currentAccount._id as Ref<Staff> }, (res) => {
    currentEmployee = res[0]
  })

  // Get department
  $: departmentQuery.query(hr.class.Department, { _id: object.department }, (res) => {
    department = res[0]
  })

  // Check if current employee can approve
  $: {
    if (department && currentEmployee) {
      const isManager = department.managers?.includes(currentEmployee._id as Ref<Employee>)
      const isTeamLead = department.teamLead === (currentEmployee._id as Ref<Employee>)
      canApprove = isManager || isTeamLead
    } else {
      canApprove = false
    }
  }

  $: isPending = object.status === RequestStatus.Pending
  $: isDraft = object.status === RequestStatus.Draft
  $: isApproved = object.status === RequestStatus.Approved
  $: isRejected = object.status === RequestStatus.Rejected
  $: isCancelled = object.status === RequestStatus.Cancelled
  $: isOwner = object.attachedTo === (currentEmployee?._id as Ref<Staff>)

  async function handleApprove () {
    await client.update(object, {
      status: RequestStatus.Approved,
      approvedBy: currentEmployee?._id as Ref<Employee>,
      approvalDate: Date.now()
    })
  }

  async function handleReject () {
    showPopup(MarkupEditor, {
      value: EmptyMarkup,
      placeholder: hr.string.ApprovalComment,
      onSave: async (comment: any) => {
        await client.update(object, {
          status: RequestStatus.Rejected,
          approvedBy: currentEmployee?._id as Ref<Employee>,
          approvalDate: Date.now(),
          approvalComment: comment
        })
      }
    })
  }

  async function handleSubmit () {
    await client.update(object, {
      status: RequestStatus.Pending,
      submittedDate: Date.now()
    })
  }

  async function handleCancel () {
    await client.update(object, {
      status: RequestStatus.Cancelled
    })
  }
</script>

<div class="flex-row-center gap-2">
  {#if isPending && canApprove}
    <Button
      label={hr.string.Approve}
      icon={hr.icon.Approved}
      kind={'positive'}
      on:click={handleApprove}
    />
    <Button
      label={hr.string.Reject}
      icon={hr.icon.Rejected}
      kind={'negative'}
      on:click={handleReject}
    />
  {/if}

  {#if isDraft && isOwner}
    <Button
      label={hr.string.Submit}
      kind={'primary'}
      on:click={handleSubmit}
    />
  {/if}

  {#if (isPending || isDraft) && isOwner}
    <Button
      label={hr.string.Cancel}
      kind={'ghost'}
      on:click={handleCancel}
    />
  {/if}
</div>

<style lang="scss">
  .gap-2 {
    gap: 0.5rem;
  }
</style>


