<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import type { Staff, AttendanceRecord } from '@hcengineering/matrics-hr'
  import { Label, Scroller, Component } from '@hcengineering/ui'
  import contact from '@hcengineering/contact'
  import hr from '../plugin'

  export let staffId: string
  export let staffById: Map<string, Staff>
  export let weekRecords: AttendanceRecord[]
  export let allHistoricalRecords: AttendanceRecord[]
  export let getStaffName: (ref: Ref<Staff> | string) => string
  export let getDepartmentName: (ref: any) => string
  export let getOfficeName: (ref: any) => string
  export let formatClockIn: (timestamp: number) => string
  export let formatTime: (timestamp: number) => string
  export let formatDate: (timestamp: number) => string
  export let calculateDuration: (clockIn: number, clockOut?: number) => string

  const dispatch = createEventDispatcher()

  $: staff = staffById.get(staffId)
  $: staffName = getStaffName(staffId)

  // Filter records for this employee
  $: employeeRecords = allHistoricalRecords
    .filter((r) => String(r.staff) === staffId)
    .sort((a, b) => b.clockIn - a.clockIn)

  $: weekEmployeeRecords = weekRecords.filter((r) => String(r.staff) === staffId)

  // Calculate comprehensive stats
  $: stats = (() => {
    const completedRecords = employeeRecords.filter((r) => r.clockOut)
    if (completedRecords.length === 0) {
      return {
        totalDays: 0,
        totalHours: 0,
        avgHoursPerDay: 0,
        avgStartTime: '—',
        avgEndTime: '—',
        earliestStart: '—',
        latestEnd: '—',
        shortestDay: '—',
        longestDay: '—',
        lateArrivals: 0,
        earlyDepartures: 0,
        consistency: 0
      }
    }

    const uniqueDays = new Set(employeeRecords.map((r) => new Date(r.clockIn).toDateString()))
    const totalHours = completedRecords.reduce((sum, r) => sum + (r.clockOut! - r.clockIn) / (1000 * 60 * 60), 0)

    // Start times
    const startTimes = completedRecords.map((r) => {
      const d = new Date(r.clockIn)
      return d.getHours() * 60 + d.getMinutes()
    })
    const avgStartMinutes = startTimes.reduce((sum, t) => sum + t, 0) / startTimes.length
    const avgStartHour = Math.floor(avgStartMinutes / 60)
    const avgStartMin = Math.floor(avgStartMinutes % 60)

    // End times
    const endTimes = completedRecords.map((r) => {
      const d = new Date(r.clockOut!)
      return d.getHours() * 60 + d.getMinutes()
    })
    const avgEndMinutes = endTimes.reduce((sum, t) => sum + t, 0) / endTimes.length
    const avgEndHour = Math.floor(avgEndMinutes / 60)
    const avgEndMin = Math.floor(avgEndMinutes % 60)

    // Earliest/Latest
    const earliestStartMinutes = Math.min(...startTimes)
    const latestEndMinutes = Math.max(...endTimes)

    // Day lengths
    const dayLengths = completedRecords.map((r) => (r.clockOut! - r.clockIn) / (1000 * 60 * 60))
    const shortestDay = Math.min(...dayLengths)
    const longestDay = Math.max(...dayLengths)

    // Late arrivals (after 9:15)
    const lateArrivals = employeeRecords.filter((r) => {
      const d = new Date(r.clockIn)
      const minutes = d.getHours() * 60 + d.getMinutes()
      return minutes > 9 * 60 + 15
    }).length

    // Early departures (before 17:00)
    const earlyDepartures = completedRecords.filter((r) => {
      const d = new Date(r.clockOut!)
      const minutes = d.getHours() * 60 + d.getMinutes()
      return minutes < 17 * 60
    }).length

    // Consistency (variance in start times)
    const variance =
      startTimes.reduce((sum, t) => sum + Math.pow(t - avgStartMinutes, 2), 0) / startTimes.length
    const stdDev = Math.sqrt(variance)
    const consistency = Math.max(0, 100 - stdDev) // 100 = perfect, lower = more variance

    return {
      totalDays: uniqueDays.size,
      totalHours,
      avgHoursPerDay: totalHours / uniqueDays.size,
      avgStartTime: `${avgStartHour.toString().padStart(2, '0')}:${avgStartMin.toString().padStart(2, '0')}`,
      avgEndTime: `${avgEndHour.toString().padStart(2, '0')}:${avgEndMin.toString().padStart(2, '0')}`,
      earliestStart: `${Math.floor(earliestStartMinutes / 60)
        .toString()
        .padStart(2, '0')}:${(earliestStartMinutes % 60).toString().padStart(2, '0')}`,
      latestEnd: `${Math.floor(latestEndMinutes / 60)
        .toString()
        .padStart(2, '0')}:${(latestEndMinutes % 60).toString().padStart(2, '0')}`,
      shortestDay: `${shortestDay.toFixed(1)}h`,
      longestDay: `${longestDay.toFixed(1)}h`,
      lateArrivals,
      earlyDepartures,
      consistency: Math.round(consistency)
    }
  })()

  // Day of week analysis
  $: dayOfWeekStats = (() => {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    const counts = new Array(7).fill(0)
    const hours = new Array(7).fill(0)

    for (const rec of employeeRecords) {
      if (!rec.clockOut) continue
      const day = new Date(rec.clockIn).getDay()
      counts[day]++
      hours[day] += (rec.clockOut - rec.clockIn) / (1000 * 60 * 60)
    }

    return days.map((name, i) => ({
      name,
      count: counts[i],
      avgHours: counts[i] > 0 ? (hours[i] / counts[i]).toFixed(1) : '0'
    }))
  })()

  // Work patterns
  $: workPattern = (() => {
    const morningRecords = employeeRecords.filter((r) => {
      const hour = new Date(r.clockIn).getHours()
      return hour < 10
    })
    const afternoonRecords = employeeRecords.filter((r) => {
      const hour = new Date(r.clockIn).getHours()
      return hour >= 10
    })

    if (employeeRecords.length === 0) return 'Not enough data'
    const morningPct = (morningRecords.length / employeeRecords.length) * 100

    if (morningPct > 80) return 'Early Bird 🌅'
    if (morningPct < 20) return 'Night Owl 🦉'
    return 'Flexible 🔄'
  })()
</script>

<div class="modal-backdrop" on:click={() => dispatch('close')}>
  <div class="modal-content" on:click|stopPropagation>
    <div class="modal-header">
      <div class="modal-header__left">
        {#if staff}
          <Component is={contact.component.Avatar} props={{ person: staff, size: 'medium', name: staffName }} />
        {/if}
        <div>
          <h2 class="modal-title">{staffName}</h2>
          <p class="modal-subtitle">Detailed Attendance Analytics</p>
        </div>
      </div>
      <button class="modal-close" on:click={() => dispatch('close')}>✕</button>
    </div>

    <Scroller padding={'var(--spacing-2)'} bottomPadding={'var(--spacing-2)'}>
      <div class="detail-root">
        <!-- Stats Grid -->
        <div class="stats-section">
          <h3 class="section-title">30-Day Summary</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-label">Total Days</div>
              <div class="stat-value">{stats.totalDays}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Total Hours</div>
              <div class="stat-value">{stats.totalHours.toFixed(1)}h</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Avg Hours/Day</div>
              <div class="stat-value">{stats.avgHoursPerDay.toFixed(1)}h</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Consistency</div>
              <div class="stat-value">{stats.consistency}%</div>
            </div>
          </div>

          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-label">Avg Start Time</div>
              <div class="stat-value">{stats.avgStartTime}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Avg End Time</div>
              <div class="stat-value">{stats.avgEndTime}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Earliest In</div>
              <div class="stat-value">{stats.earliestStart}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Latest Out</div>
              <div class="stat-value">{stats.latestEnd}</div>
            </div>
          </div>

          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-label">Shortest Day</div>
              <div class="stat-value">{stats.shortestDay}</div>
            </div>
            <div class="stat-card">
              <div class="stat-label">Longest Day</div>
              <div class="stat-value">{stats.longestDay}</div>
            </div>
            <div class="stat-card stat-card--warning">
              <div class="stat-label">Late Arrivals</div>
              <div class="stat-value">{stats.lateArrivals}</div>
            </div>
            <div class="stat-card stat-card--warning">
              <div class="stat-label">Early Departures</div>
              <div class="stat-value">{stats.earlyDepartures}</div>
            </div>
          </div>
        </div>

        <!-- Work Pattern -->
        <div class="pattern-section">
          <h3 class="section-title">Work Pattern</h3>
          <div class="pattern-badge">{workPattern}</div>
        </div>

        <!-- Day of Week Breakdown -->
        <div class="dow-section">
          <h3 class="section-title">By Day of Week</h3>
          <div class="dow-grid">
            {#each dayOfWeekStats as day}
              <div class="dow-card">
                <div class="dow-name">{day.name}</div>
                <div class="dow-count">{day.count} days</div>
                <div class="dow-hours">{day.avgHours}h avg</div>
              </div>
            {/each}
          </div>
        </div>

        <!-- Full History Table -->
        <div class="history-section">
          <h3 class="section-title">Complete Attendance History (Last 30 Days)</h3>
          {#if employeeRecords.length > 0}
            <div class="history-table-wrapper">
              <table class="history-table">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Clock In</th>
                    <th>Clock Out</th>
                    <th>Duration</th>
                    <th>Office</th>
                    <th>Department</th>
                  </tr>
                </thead>
                <tbody>
                  {#each employeeRecords as rec (rec._id)}
                    <tr>
                      <td>{formatDate(rec.clockIn)}</td>
                      <td>{formatTime(rec.clockIn)}</td>
                      <td>{rec.clockOut ? formatTime(rec.clockOut) : '—'}</td>
                      <td>{calculateDuration(rec.clockIn, rec.clockOut)}</td>
                      <td>{getOfficeName(rec.office)}</td>
                      <td>{getDepartmentName(rec.department)}</td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          {:else}
            <div class="empty-state">No attendance records found</div>
          {/if}
        </div>
      </div>
    </Scroller>
  </div>
</div>

<style lang="scss">
  .modal-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
  }

  .modal-content {
    background: var(--theme-panel-color);
    border-radius: var(--large-BorderRadius);
    width: 90%;
    max-width: 1200px;
    height: 90%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .modal-header__left {
    display: flex;
    align-items: center;
    gap: var(--spacing-1_5);
  }

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    margin: 0;
  }

  .modal-subtitle {
    font-size: 0.875rem;
    color: var(--theme-trans-color);
    margin: 0;
  }

  .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--theme-trans-color);
    cursor: pointer;
    padding: var(--spacing-0_5);
  }

  .modal-close:hover {
    color: var(--theme-caption-color);
  }

  .detail-root {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    margin-bottom: var(--spacing-1_5);
  }

  .stats-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1_5);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-1_5);
  }

  .stat-card {
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-divider-color);
    background: var(--theme-button-default);
  }

  .stat-card--warning {
    border-color: rgba(245, 158, 11, 0.3);
    background: rgba(245, 158, 11, 0.05);
  }

  .stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-trans-color);
    margin-bottom: var(--spacing-0_5);
  }

  .stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .pattern-section {
    padding: var(--spacing-2);
    border-radius: var(--medium-BorderRadius);
    background: var(--theme-bg-accent-color);
  }

  .pattern-badge {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .dow-section {
  }

  .dow-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: var(--spacing-1);
  }

  .dow-card {
    padding: var(--spacing-1);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-divider-color);
    background: var(--theme-button-default);
    text-align: center;
  }

  .dow-name {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    margin-bottom: var(--spacing-0_25);
  }

  .dow-count {
    font-size: 0.875rem;
    color: var(--theme-dark-color);
  }

  .dow-hours {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
  }

  .history-section {
  }

  .history-table-wrapper {
    width: 100%;
    overflow: auto;
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-divider-color);
  }

  .history-table {
    width: 100%;
    border-collapse: collapse;
  }

  .history-table th,
  .history-table td {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color);
    text-align: left;
    font-size: 0.875rem;
  }

  .history-table th {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-trans-color);
    background: var(--theme-bg-accent-color);
  }

  .history-table tbody tr:hover {
    background: var(--theme-bg-accent-color);
  }

  .empty-state {
    padding: var(--spacing-3);
    text-align: center;
    color: var(--theme-trans-color);
  }
</style>
