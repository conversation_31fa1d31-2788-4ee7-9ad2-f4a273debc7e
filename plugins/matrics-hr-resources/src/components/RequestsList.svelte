<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import { EmployeePresenter } from '@hcengineering/contact-resources'
  import { Department, Request, RequestStatus, Staff } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Button, Icon, Label, Scroller } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import hr from '../plugin'

  export let department: Ref<Department> | undefined = undefined

  const requestQuery = createQuery()
  const staffQuery = createQuery()

  let requests: Request[] = []
  let staffById = new Map<Ref<Staff>, Staff>()

  $: {
    const q: any = {}
    if (department) q.department = department
    requestQuery.query(hr.class.Request, q, (res) => {
      requests = res
    })
  }

  staffQuery.query(hr.mixin.Staff, {}, (res) => {
    staffById = new Map(res.map((s) => [s._id, s]))
  })

  function statusIcon (status: RequestStatus) {
    switch (status) {
      case RequestStatus.Approved: return view.icon.CheckCircle
      case RequestStatus.Pending: return view.icon.Statuses
      case RequestStatus.Rejected: return view.icon.Delete
      default: return view.icon.Statuses
    }
  }
</script>

<Scroller>
  <div class="requests-list">
    <div class="header flex-row-center flex-between">
      <div class="flex-row-center gap-2">
        <Icon icon={view.icon.List} size={'small'} />
        <Label label={hr.string.Requests} />
        <span class="count">({requests.length})</span>
      </div>
      <div class="flex-row-center gap-2">
        <Button label={hr.string.CreateDepartmentLabel} icon={view.icon.Add} kind={'ghost'} size={'small'} />
      </div>
    </div>

    <div class="items">
      {#each requests as r}
        <div class="item">
          <div class="row flex-row-center gap-2">
            <Icon icon={statusIcon(r.status)} size={'x-small'} />
            <EmployeePresenter value={staffById.get(r.attachedTo)} avatarSize={'x-small'} shouldShowAvatar shouldShowName />
          </div>
          <div class="meta">
            <span class="status">{r.status}</span>
          </div>
        </div>
      {/each}

      {#if requests.length === 0}
        <div class="empty">No time-off requests yet</div>
      {/if}
    </div>
  </div>
</Scroller>

<style lang="scss">
  .requests-list { display: flex; flex-direction: column; gap: 1rem; padding: 1.5rem; }
  .header { padding-bottom: 0.75rem; border-bottom: 1px solid var(--theme-divider-color); }
  .count { font-size: 0.875rem; color: var(--theme-dark-color); }
  .items { display: flex; flex-direction: column; gap: 0.5rem; }
  .item { padding: 0.75rem; border: 1px solid var(--theme-divider-color); border-radius: 0.5rem; background: var(--theme-button-default); cursor: pointer; transition: all 0.2s; }
  .item:hover { background: var(--theme-button-hovered); }
  .row { align-items: center; }
  .empty { padding: 2rem; text-align: center; color: var(--theme-dark-color); font-size: 0.875rem; }
</style>
