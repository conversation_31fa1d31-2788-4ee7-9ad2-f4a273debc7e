<!--
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { EmployeeArrayEditor, EditableAvatar } from '@hcengineering/contact-resources'
  import { getClient } from '@hcengineering/presentation'
  import { createFocusManager, EditBox, FocusHandler } from '@hcengineering/ui'
  import core from '@hcengineering/core'
  import { Office } from '@hcengineering/matrics-hr'
  import { createEventDispatcher, onMount } from 'svelte'
  import hr from '../plugin'

  export let object: Office
  export let readonly: boolean = false

  let avatarEditor: EditableAvatar

  const dispatch = createEventDispatcher()
  const client = getClient()

  async function onAvatarDone () {
    if (object === undefined) return

    if (object.avatar != null) {
      await avatarEditor.removeAvatar(object.avatar)
    }
    const avatar = await avatarEditor.createAvatar()
    await client.diffUpdate(object, avatar)
  }

  async function updateField (field: string, value: any): Promise<void> {
    if (object === undefined) return
    await client.update(object, {
      [field]: value
    })
  }

  const manager = createFocusManager()

  onMount(() => {
    dispatch('open', {
      ignoreKeys: ['comments', 'name', 'description', 'address', 'city', 'country', 'timezone']
    })
  })
</script>

<FocusHandler {manager} />

{#if object !== undefined}
  <div class="flex-row-stretch flex-grow step-tb-6">
    <div class="mr-8">
      {#key object}
        <EditableAvatar
          person={object}
          size={'x-large'}
          icon={hr.icon.Office}
          bind:this={avatarEditor}
          on:done={onAvatarDone}
        />
      {/key}
    </div>
    <div class="flex-grow flex-col">
      <div class="name">
        <EditBox
          placeholder={hr.string.Office}
          bind:value={object.name}
          on:change={() => updateField('name', object.name)}
          focusIndex={1}
          {readonly}
        />
      </div>
      <div class="separator mt-2" />
      <div class="mt-2">
        <EditBox
          placeholder={core.string.Description}
          bind:value={object.description}
          on:change={() => updateField('description', object.description)}
          focusIndex={2}
          {readonly}
        />
      </div>
    </div>
  </div>

  <div class="separator" />

  <div class="flex-col mt-4 gap-2">
    <EditBox
      label={hr.string.Address}
      placeholder={hr.string.Address}
      bind:value={object.address}
      on:change={() => updateField('address', object.address)}
      focusIndex={3}
      {readonly}
      kind={'regular'}
      size={'large'}
    />

    <div class="flex-row gap-2">
      <div class="flex-grow">
        <EditBox
          label={hr.string.City}
          placeholder={hr.string.City}
          bind:value={object.city}
          on:change={() => updateField('city', object.city)}
          focusIndex={4}
          {readonly}
          kind={'regular'}
          size={'large'}
        />
      </div>
      <div class="flex-grow">
        <EditBox
          label={hr.string.Country}
          placeholder={hr.string.Country}
          bind:value={object.country}
          on:change={() => updateField('country', object.country)}
          focusIndex={5}
          {readonly}
          kind={'regular'}
          size={'large'}
        />
      </div>
    </div>

    <EditBox
      label={hr.string.Timezone}
      placeholder={hr.string.Timezone}
      bind:value={object.timezone}
      on:change={() => updateField('timezone', object.timezone)}
      focusIndex={6}
      {readonly}
      kind={'regular'}
      size={'large'}
    />

    <div class="mt-4">
      <EmployeeArrayEditor
        label={hr.string.Managers}
        bind:value={object.managers}
        on:change={() => updateField('managers', object.managers)}
        focusIndex={7}
        {readonly}
        kind={'regular'}
        size={'large'}
      />
    </div>
  </div>
{/if}

<style lang="scss">
  .name {
    font-weight: 500;
    font-size: 1.25rem;
    color: var(--theme-caption-color);
  }

  .separator {
    height: 1px;
    background-color: var(--theme-divider-color);
    margin: 0.75rem 0;
  }
</style>


