<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import { Department, PublicHoliday, toTzDate } from '@hcengineering/matrics-hr'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import { Button, Label, Icon, showPopup, DatePresenter, Section } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import hr from '../plugin'
  import CreatePublicHoliday from './CreatePublicHoliday.svelte'
  import EditPublicHoliday from './EditPublicHoliday.svelte'

  const client = getClient()
  const holidaysQuery = createQuery()
  const departmentsQuery = createQuery()

  let holidays: PublicHoliday[] = []
  let departments: Map<Ref<Department>, Department> = new Map()

  $: holidaysQuery.query(hr.class.PublicHoliday, {}, (res) => {
    holidays = res.sort((a, b) => {
      const aDate = new Date(a.date.year, a.date.month, a.date.day)
      const bDate = new Date(b.date.year, b.date.month, b.date.day)
      return aDate.getTime() - bDate.getTime()
    })
  })

  $: departmentsQuery.query(hr.class.Department, {}, (res) => {
    departments = new Map(res.map((d) => [d._id, d]))
  })

  function getDepartmentName(deptId: Ref<Department> | undefined): string {
    if (!deptId) return 'All Departments'
    return departments.get(deptId)?.name || 'Unknown Department'
  }

  function createHoliday() {
    showPopup(CreatePublicHoliday, {})
  }

  function editHoliday(holiday: PublicHoliday) {
    showPopup(EditPublicHoliday, { holiday })
  }

  async function deleteHoliday(holiday: PublicHoliday) {
    await client.remove(holiday)
  }

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })

  function formatHolidayDate(holiday: PublicHoliday): string {
    const date = new Date(holiday.date.year, holiday.date.month, holiday.date.day)
    return dateFormatter.format(date)
  }
</script>

<div class="holiday-management">
  <div class="holiday-header">
    <div class="holiday-header-title">
      <Icon icon={hr.icon.PublicHoliday} size={'small'} />
      <Label label={hr.string.PublicHolidays} />
      <span class="holiday-count">{holidays.length}</span>
    </div>
    <Button
      label={hr.string.AddHoliday}
      icon={view.icon.Add}
      kind={'primary'}
      size={'small'}
      on:click={createHoliday}
    />
  </div>

  <div class="holiday-content">
    {#if holidays.length === 0}
      <div class="holiday-empty">
        <span class="holiday-empty-text"><Label label={hr.string.NoHolidays} /></span>
        <Button
          label={hr.string.AddHoliday}
          icon={view.icon.Add}
          kind={'primary'}
          size={'medium'}
          on:click={createHoliday}
        />
      </div>
    {:else}
      <div class="holiday-grid">
        {#each holidays as holiday (holiday._id)}
          <div class="holiday-card">
            <div class="holiday-main">
              <div class="holiday-icon">
                <Icon icon={hr.icon.PublicHoliday} size={'medium'} />
              </div>
              <div class="holiday-info">
                <div class="holiday-date">{formatHolidayDate(holiday)}</div>
                <div class="holiday-name">{holiday.title}</div>
                <div class="holiday-dept">{getDepartmentName(holiday.department)}</div>
              </div>
            </div>
            <div class="holiday-actions">
              <Button
                icon={view.icon.Edit}
                kind={'ghost'}
                size={'small'}
                on:click={() => editHoliday(holiday)}
              />
              <Button
                icon={view.icon.Delete}
                kind={'ghost'}
                size={'small'}
                on:click={() => deleteHoliday(holiday)}
              />
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</div>

<style lang="scss">
  .holiday-management {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-2);
    gap: var(--spacing-2);
  }

  .holiday-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-1_5);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .holiday-header-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .holiday-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0 var(--spacing-0_75);
    background-color: var(--theme-button-default);
    color: var(--theme-caption-color);
    border-radius: var(--round-BorderRadius);
    font-size: 0.75rem;
    font-weight: 500;
  }

  .holiday-content {
    padding: var(--spacing-1_5);
    overflow-y: auto;
  }

  .holiday-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
    gap: var(--spacing-1_5);
  }

  @media (max-width: 1024px) {
    .holiday-grid {
      grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
    }
  }

  @media (max-width: 640px) {
    .holiday-grid {
      grid-template-columns: 1fr;
    }
  }

  .holiday-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-divider-color);
    background-color: var(--theme-button-default);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .holiday-main {
    display: flex;
    gap: var(--spacing-1_5);
    align-items: flex-start;
  }

  .holiday-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    flex-shrink: 0;
    background-color: var(--theme-navpanel-selected);
    border-radius: var(--small-BorderRadius);
    color: var(--theme-caption-color);
  }

  .holiday-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_25);
    flex: 1;
    min-width: 0;
  }

  .holiday-date {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--theme-trans-color);
    letter-spacing: 0.025em;
  }

  .holiday-name {
    font-size: 0.9375rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .holiday-dept {
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .holiday-actions {
    display: flex;
    gap: var(--spacing-0_5);
    padding-top: var(--spacing-0_5);
    border-top: 1px solid var(--theme-divider-color);
    justify-content: flex-end;
  }

  .holiday-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    background-color: var(--theme-button-default);
    border-radius: var(--medium-BorderRadius);
  }

  .holiday-empty-text {
    color: var(--theme-dark-color);
    font-size: 0.9375rem;
  }

  @media (max-width: 768px) {
    .holiday-management {
      padding: var(--spacing-1_5);
    }

    .holiday-header {
      flex-direction: column;
      align-items: stretch;
      gap: var(--spacing-1_5);
    }

    .holiday-header-title {
      justify-content: space-between;
    }
  }
</style>
