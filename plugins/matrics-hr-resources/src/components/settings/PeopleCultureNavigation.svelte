<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { onDestroy, onMount } from 'svelte'
  import { Location, NavItem, getCurrentResolvedLocation, navigate, resolvedLocationStore } from '@hcengineering/ui'
  import hr from '../../plugin'

  export let categoryName: string

  let selected: string | undefined

  let unsubscribe: (() => void) | undefined
  onMount(() => {
    unsubscribe = resolvedLocationStore.subscribe((loc: Location) => {
      selected = loc.path?.[5] ?? 'offices'
    })
  })
  onDestroy(() => unsubscribe?.())

  function select (section: string): void {
    const loc = getCurrentResolvedLocation()
    // Keep top-level settings category (index 3) as-is ("setting")
    // Index 4 is the workspace setting category (e.g. 'peopleCulture')
    // Index 5 is the sub-section within People & Culture
    loc.path[4] = categoryName
    loc.path[5] = section
    loc.path.length = 6
    navigate(loc)
  }
</script>

<NavItem
  icon={hr.icon.HR}
  label={hr.string.Employees}
  selected={selected === 'employees'}
  on:click={() => { select('employees') }}
/>
<NavItem
  icon={hr.icon.Office}
  label={hr.string.Offices}
  selected={selected === 'offices'}
  on:click={() => { select('offices') }}
/>
<NavItem
  icon={hr.icon.PTO}
  label={hr.string.TimeOffPolicies}
  selected={selected === 'timeoff'}
  on:click={() => { select('timeoff') }}
/>
<NavItem
  icon={hr.icon.PublicHoliday}
  label={hr.string.PublicHolidays}
  selected={selected === 'holidays'}
  on:click={() => { select('holidays') }}
/>
<NavItem
  icon={hr.icon.Policy}
  label={hr.string.Policies}
  selected={selected === 'policies'}
  on:click={() => { select('policies') }}
/>
<NavItem
  icon={hr.icon.Workflow}
  label={hr.string.Workflows}
  selected={selected === 'workflows'}
  on:click={() => { select('workflows') }}
/>
