<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { onDestroy, onMount } from 'svelte'
  import { getCurrentAccount, hasAccountRole, AccountRole } from '@hcengineering/core'
  import { getCurrentEmployee } from '@hcengineering/contact'
  import { createQuery, isAdminUser } from '@hcengineering/presentation'
  import hr from '../../plugin'
  import OfficesList from '../OfficesList.svelte'
  import PolicyList from '../PolicyList.svelte'
  import WorkflowList from '../WorkflowList.svelte'
  import { Breadcrumb, Header, Label, Scroller, resolvedLocationStore, type Location } from '@hcengineering/ui'
  import TimeOffPolicyList from '../TimeOffPolicyList.svelte'
  import HolidayManagement from '../HolidayManagement.svelte'
  import EmployeeManagement from '../EmployeeManagement.svelte'

  // Settings shell may or may not pass `kind`; default to rendering content
  export let kind: 'content' | 'navigation' | undefined

  const admin = isAdminUser()
  const account = getCurrentAccount()
  const employee = getCurrentEmployee()

  const q = createQuery()

  let isManager = false
  const sections = new Set(['employees', 'offices', 'timeoff', 'holidays', 'policies', 'workflows'])
  let section: 'offices' | 'timeoff' | 'holidays' | 'policies' | 'workflows' | 'employees' = 'employees'

  // Detect selected section from URL
  let unsubscribe: (() => void) | undefined
  onMount(() => {
    unsubscribe = resolvedLocationStore.subscribe((loc: Location) => {
      const s = (loc.path?.[5]) ?? 'employees'
      section = sections.has(s) ? (s as typeof section) : 'employees'
    })
  })
  onDestroy(() => unsubscribe?.())

  // Determine if user manages any department OR is workspace maintainer/owner/admin
  $: q.query(
    hr.class.Department,
    { $or: [{ teamLead: employee }, { managers: employee }] },
    (res) => {
      isManager =
        admin ||
        hasAccountRole(account, AccountRole.Maintainer) ||
        hasAccountRole(account, AccountRole.Owner) ||
        res.length > 0
    }
  )
</script>

{#if kind === 'navigation'}
  <div class="hulyComponent" />
{:else}
  <div class="hulyComponent">
    <Header adaptive="disabled">
      <Breadcrumb icon={hr.icon.HR} label={hr.string.ConfigLabel} size="large" isCurrent />
    </Header>

    <div class="content">
      {#if !isManager}
        <div class="empty">
          <Label label={hr.string.Managers} />
          <div class="note">You don’t have permission to view People & Culture settings. Ask an HR manager or workspace maintainer.</div>
        </div>
      {:else}
        <Scroller>
          {#if section === 'employees'}
            <EmployeeManagement />
          {:else if section === 'offices'}
            <OfficesList />
          {:else if section === 'timeoff'}
            <TimeOffPolicyList />
          {:else if section === 'holidays'}
            <HolidayManagement />
          {:else if section === 'policies'}
            <PolicyList />
          {:else if section === 'workflows'}
            <WorkflowList />
          {/if}
        </Scroller>
      {/if}
    </div>
  </div>
{/if}

<style lang="scss">
  .content { padding: 1.5rem 2rem; }
  .empty { padding: 2rem; border: 1px dashed var(--theme-divider-color); border-radius: 0.5rem; margin: 2rem; }
  .note { margin-top: 0.5rem; color: var(--theme-dark-color); font-size: 0.9rem; }
</style>
