<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Workflow, WorkflowStatus, Department } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Button, Icon, Label, showPopup, DropdownLabelsIntl, DropdownIntlItem, Scroller } from '@hcengineering/ui'
  import { Ref } from '@hcengineering/core'
  import hr from '../plugin'
  import CreateWorkflow from './CreateWorkflow.svelte'
  import view from '@hcengineering/view'

  export let department: Ref<Department> | undefined = undefined
  export let filterStatus: WorkflowStatus | undefined = undefined

  let workflows: Workflow[] = []
  const workflowsQuery = createQuery()

  const statusOptions: DropdownIntlItem[] = [
    { id: 'all', label: hr.string.All },
    { id: 'draft', label: hr.string.StatusDraft },
    { id: 'active', label: hr.string.StatusApproved },
    { id: 'completed', label: hr.string.Completed },
    { id: 'archived', label: hr.string.StatusCancelled }
  ]

  $: {
    const query: any = {}
    if (department) query.department = department
    if (filterStatus) query.status = filterStatus

    workflowsQuery.query(hr.class.Workflow, query, (res) => {
      workflows = res
    })
  }

  function createWorkflow () {
    showPopup(CreateWorkflow, { department })
  }

  function onStatusSelect (value: string): void {
    filterStatus = value === 'all'
      ? undefined
      : value === 'draft' ? WorkflowStatus.Draft
      : value === 'active' ? WorkflowStatus.Active
      : value === 'completed' ? WorkflowStatus.Completed
      : WorkflowStatus.Archived
  }

  function getStatusIcon(status: WorkflowStatus) {
    switch (status) {
      case WorkflowStatus.Draft: return view.icon.Statuses
      case WorkflowStatus.Active: return view.icon.CheckCircle
      case WorkflowStatus.Completed: return view.icon.CheckCircle
      case WorkflowStatus.Archived: return view.icon.Archive
      default: return view.icon.Statuses
    }
  }

  function getStatusColor(status: WorkflowStatus) {
    switch (status) {
      case WorkflowStatus.Draft: return 'var(--theme-trans-color)'
      case WorkflowStatus.Active: return 'var(--theme-warning-color)'
      case WorkflowStatus.Completed: return 'var(--theme-won-color)'
      case WorkflowStatus.Archived: return 'var(--theme-darker-color)'
      default: return 'var(--theme-trans-color)'
    }
  }
</script>

<Scroller>
<div class="workflow-list">
  <div class="header flex-row-center flex-between">
    <div class="flex-row-center gap-2">
      <Icon icon={hr.icon.Workflow} size={'small'} />
      <Label label={hr.string.Workflows} />
      <span class="count">({workflows.length})</span>
    </div>
    <div class="flex-row-center gap-2">
      <DropdownLabelsIntl
        items={statusOptions}
        selected={filterStatus === undefined ? 'all' : filterStatus}
        on:selected={(e) => onStatusSelect(e.detail)}
      />
      <Button
        label={hr.string.CreateWorkflow}
        icon={view.icon.Add}
        kind={'ghost'}
        size={'small'}
        on:click={createWorkflow}
      />
    </div>
  </div>

  <div class="workflow-items">
    {#each workflows as workflow}
      <div class="workflow-item">
        <div class="flex-row-center gap-2 mb-2">
          <Icon 
            icon={getStatusIcon(workflow.status)} 
            size={'small'} 
            fill={getStatusColor(workflow.status)}
          />
          <span class="title">{workflow.title}</span>
        </div>
        <div class="meta flex-row gap-3">
          {#if workflow.assignee}
            <div class="flex-row-center gap-1">
              <Icon icon={view.icon.DetailsFilled} size={'x-small'} />
              <span class="meta-text">Assigned</span>
            </div>
          {/if}
          {#if workflow.dueDate}
            <div class="flex-row-center gap-1">
              <Icon icon={view.icon.Timeline} size={'x-small'} />
              <span class="meta-text">{new Date(workflow.dueDate).toLocaleDateString()}</span>
            </div>
          {/if}
          {#if workflow.steps && workflow.steps > 0}
            <div class="flex-row-center gap-1">
              <Icon icon={view.icon.List} size={'x-small'} />
              <span class="meta-text">{workflow.steps} steps</span>
            </div>
          {/if}
        </div>
      </div>
    {/each}

    {#if workflows.length === 0}
      <div class="empty-state">
        <span class="text">No workflows found</span>
        <Button
          label={hr.string.CreateWorkflow}
          icon={view.icon.Add}
          kind={'primary'}
          size={'medium'}
          on:click={createWorkflow}
        />
      </div>
    {/if}
  </div>
</div>
</Scroller>

<style lang="scss">
  .workflow-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
  }

  .header {
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .count {
    font-size: 0.875rem;
    color: var(--theme-dark-color);
  }

  .workflow-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .workflow-item {
    padding: 1rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    background-color: var(--theme-button-default);
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-divider-color);
    }

    .title {
      font-weight: 500;
      color: var(--theme-caption-color);
    }

    .meta {
      .meta-text {
        font-size: 0.75rem;
        color: var(--theme-dark-color);
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    
    .text {
      color: var(--theme-dark-color);
      font-size: 0.875rem;
    }
  }

  .gap-1 {
    gap: 0.25rem;
  }

  .gap-2 {
    gap: 0.5rem;
  }

  .gap-3 {
    gap: 0.75rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .flex-between {
    justify-content: space-between;
  }
</style>


