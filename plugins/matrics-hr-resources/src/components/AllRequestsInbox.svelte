<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import contact, { getName, getCurrentEmployee, type Employee } from '@hcengineering/contact'
  import { Department, Request, RequestStatus, RequestType } from '@hcengineering/matrics-hr'
  import { createQuery, getClient, MessageBox } from '@hcengineering/presentation'
  import { Button, Label, Icon, IconCheck, IconClose, showPopup, Scroller, Component } from '@hcengineering/ui'
  import hr from '../plugin'
  import RequestStatusPresenter from './RequestStatusPresenter.svelte'
  import RequestPresenter from './RequestPresenter.svelte'
  import view from '@hcengineering/view'

  const currentUser = getCurrentEmployee()

  export let department: Ref<Department>
  export let descendants: Map<Ref<Department>, Department[]>
  export let departmentById: Map<Ref<Department>, Department>

  const client = getClient()
  const requestsQuery = createQuery()
  const typesQuery = createQuery()

  let requests: Request[] = []
  let requestTypes: Map<Ref<RequestType>, RequestType> = new Map()
  let groupedRequests: { type: RequestType | undefined, requests: Request[] }[] = []

  // Get all descendant departments
  function getAllDepartments(dept: Ref<Department>, map: Map<Ref<Department>, Department[]>): Ref<Department>[] {
    const result: Ref<Department>[] = [dept]
    const children = map.get(dept) ?? []
    for (const child of children) {
      result.push(...getAllDepartments(child._id, map))
    }
    return result
  }

  $: allDepartments = getAllDepartments(department, descendants)

  // Query all pending requests in managed departments
  $: requestsQuery.query(
    hr.class.Request,
    {
      department: { $in: allDepartments },
      status: { $in: [RequestStatus.Pending, RequestStatus.Draft] }
    },
    (res) => {
      requests = res.sort((a, b) => (b.submittedDate ?? b.modifiedOn) - (a.submittedDate ?? a.modifiedOn))
    }
  )

  // Query request types for display
  $: typesQuery.query(
    hr.class.RequestType,
    {},
    (res) => {
      requestTypes = new Map(res.map(t => [t._id, t]))
    }
  )

  // Group requests by type for better organization
  $: {
    const groups = new Map<Ref<RequestType> | 'unknown', Request[]>()
    for (const req of requests) {
      const key = req.type ?? 'unknown'
      const arr = groups.get(key) ?? []
      arr.push(req)
      groups.set(key, arr)
    }
    
    groupedRequests = Array.from(groups.entries()).map(([typeId, reqs]) => ({
      type: typeId === 'unknown' ? undefined : requestTypes.get(typeId as Ref<RequestType>),
      requests: reqs
    }))
  }

  async function approveRequest(request: Request) {
    showPopup(
      MessageBox,
      {
        label: hr.string.Approve,
        message: hr.string.Request,
        action: async () => {
          await client.update(request, {
            status: RequestStatus.Approved,
            approvedBy: currentUser as any,
            approvalDate: Date.now()
          })
        }
      },
      'top'
    )
  }

  async function rejectRequest(request: Request) {
    showPopup(
      MessageBox,
      {
        label: hr.string.Reject,
        message: hr.string.Request,
        action: async () => {
          await client.update(request, {
            status: RequestStatus.Rejected,
            approvedBy: currentUser as any,
            approvalDate: Date.now()
          })
        }
      },
      'top'
    )
  }


  function getRequestTypeIcon(typeId: Ref<RequestType>): any {
    // Time-off requests
    if ([hr.ids.PTO, hr.ids.PTO2, hr.ids.Vacation, hr.ids.Sick, hr.ids.Remote, hr.ids.Overtime, hr.ids.Overtime2].includes(typeId)) {
      return hr.icon.PTO
    }
    // Profile/Emergency updates
    if ([hr.ids.ProfileUpdate, hr.ids.EmergencyInfoUpdate].includes(typeId)) {
      return contact.icon.Person
    }
    // Asset requests - use a generic icon
    if ([hr.ids.AssetAssignment, hr.ids.AssetReturn].includes(typeId)) {
      return contact.icon.Person
    }
    return view.icon.Edit
  }

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
</script>

<div class="requests-inbox">
  <div class="requests-header">
    <div class="requests-header-title">
      <h2 class="requests-title"><Label label={hr.string.PendingRequests} /></h2>
      <span class="requests-subtitle">Review and approve team requests</span>
    </div>
  </div>

  <Scroller>
    <div class="requests-content">
      {#if requests.length === 0}
        <div class="requests-empty">
          <div class="empty-icon">📋</div>
          <div class="empty-title">All caught up!</div>
          <div class="empty-subtitle">No pending requests at the moment</div>
        </div>
      {:else}
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-label">Total Pending</div>
            <div class="stat-value">{requests.length}</div>
          </div>
          <div class="stat-card">
            <div class="stat-label">Needs Action</div>
            <div class="stat-value">{requests.filter(r => r.status === RequestStatus.Pending).length}</div>
          </div>
        </div>
        {#each groupedRequests as group}
          <div class="group">
            <div class="group-header">
              <div class="group-header-left">
                {#if group.type}
                  <Icon icon={getRequestTypeIcon(group.type._id)} size="small" />
                  <Label label={group.type.label} />
                {:else}
                  <span>Unknown Type</span>
                {/if}
              </div>
              <span class="group-count">{group.requests.length}</span>
            </div>
            
            <div class="requests-grid">
              {#each group.requests as request}
                {@const requester = request.attachedTo}
                {@const dept = departmentById.get(request.department)}
                
                <div class="request-card" class:pending={request.status === RequestStatus.Pending}>
                  {#await client.findOne(contact.mixin.Employee, { _id: requester }) then employee}
                    <div class="card-header">
                      <div class="card-header-left">
                        <div class="request-avatar">
                          <Component is={contact.component.Avatar} props={{ person: employee, size: 'large', name: employee ? getName(client.getHierarchy(), employee) : 'Unknown' }} />
                        </div>
                        <div class="request-info">
                          <div class="requester-name">
                            {#if employee}
                              {getName(client.getHierarchy(), employee)}
                            {:else}
                              Unknown Employee
                            {/if}
                          </div>
                          <div class="request-meta">
                            <span class="department">{dept?.name ?? 'Unknown Dept'}</span>
                            <span class="separator">•</span>
                            <span class="date">
                              {request.submittedDate 
                                ? dateFormatter.format(new Date(request.submittedDate)) 
                                : dateFormatter.format(new Date(request.modifiedOn))}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="request-status-badge">
                        <RequestStatusPresenter value={request} />
                      </div>
                    </div>

                    <div class="card-body">
                      <div class="request-details">
                        <RequestPresenter value={request} />
                      </div>
                      {#if request.description}
                        <div class="request-description">{request.description}</div>
                      {/if}
                    </div>

                    {#if request.status === RequestStatus.Pending}
                      <div class="card-actions">
                        <Button
                          label={hr.string.Approve}
                          icon={IconCheck}
                          kind="positive"
                          size="large"
                          on:click={() => approveRequest(request)}
                        />
                        <Button
                          label={hr.string.Reject}
                          icon={IconClose}
                          kind="negative"
                          size="large"
                          on:click={() => rejectRequest(request)}
                        />
                      </div>
                    {/if}
                  {/await}
                </div>
              {/each}
            </div>
          </div>
        {/each}
      {/if}
    </div>
  </Scroller>
</div>

<style lang="scss">
  .requests-inbox {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .requests-header {
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-2);
    background: linear-gradient(180deg, var(--theme-bg-color) 0%, transparent 100%);
  }

  .requests-header-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .requests-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--theme-caption-color);
    margin: 0;
  }

  .requests-subtitle {
    font-size: 0.875rem;
    color: var(--theme-dark-color);
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-1_5);
    margin-bottom: var(--spacing-2);
  }

  .stat-card {
    padding: var(--spacing-2);
    background: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--medium-BorderRadius);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-trans-color);
    margin-bottom: var(--spacing-0_5);
  }

  .stat-value {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .requests-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding: 0 var(--spacing-3) var(--spacing-3);
  }

  .group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-1) 0;
    margin-bottom: var(--spacing-1_5);
    border-bottom: 2px solid var(--theme-divider-color);
  }

  .group-header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-weight: 600;
    font-size: 0.9375rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-content-color);
  }

  .group-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0 var(--spacing-0_75);
    background-color: var(--theme-bg-accent-color);
    color: var(--theme-caption-color);
    border-radius: var(--round-BorderRadius);
    font-size: 0.75rem;
    font-weight: 600;
  }

  .requests-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-6) var(--spacing-4);
    background: linear-gradient(135deg, var(--theme-button-default) 0%, var(--theme-bg-color) 100%);
    border: 1px dashed var(--theme-divider-color);
    border-radius: var(--large-BorderRadius);
    margin-top: var(--spacing-4);
  }

  .empty-icon {
    font-size: 4rem;
    opacity: 0.5;
  }

  .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .empty-subtitle {
    color: var(--theme-dark-color);
    font-size: 0.9375rem;
  }

  .requests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-2);
  }

  @media (max-width: 900px) {
    .requests-grid {
      grid-template-columns: 1fr;
    }
  }

  .request-card {
    display: flex;
    flex-direction: column;
    background: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--large-BorderRadius);
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border-color: var(--theme-list-divider-color);
    }

    &.pending {
      border-left: 3px solid var(--theme-warning-color);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    background: linear-gradient(180deg, var(--theme-bg-accent-color) 0%, transparent 100%);
  }

  .card-header-left {
    display: flex;
    gap: var(--spacing-1_5);
    align-items: flex-start;
    flex: 1;
    min-width: 0;
  }

  .request-avatar {
    flex-shrink: 0;
  }

  .card-body {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1_5);
    padding: 0 var(--spacing-2) var(--spacing-2);
  }

  .request-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    flex: 1;
    min-width: 0;
  }

  .requester-name {
    font-weight: 600;
    font-size: 1rem;
    color: var(--theme-caption-color);
    line-height: 1.4;
  }

  .request-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-0_75);
    font-size: 0.8125rem;
    color: var(--theme-dark-color);

    .separator {
      opacity: 0.5;
    }
  }

  .request-status-badge {
    flex-shrink: 0;
  }

  .request-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-1_5);
    background-color: var(--theme-bg-color);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--medium-BorderRadius);
  }

  .request-description {
    font-size: 0.875rem;
    color: var(--theme-content-color);
    line-height: 1.5;
    margin-top: var(--spacing-0_5);
  }

  .card-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-1_5);
    padding: var(--spacing-2);
    background: var(--theme-bg-accent-color);
    border-top: 1px solid var(--theme-divider-color);
  }

  .card-actions :global(button) {
    flex: 1;
  }

  @media (max-width: 768px) {
    .requests-header {
      padding: var(--spacing-2);
    }

    .requests-content {
      padding: 0 var(--spacing-2) var(--spacing-2);
    }

    .stats-cards {
      grid-template-columns: 1fr;
    }

    .card-header-left {
      flex-direction: column;
      align-items: flex-start;
    }

    .card-actions {
      grid-template-columns: 1fr;
    }
  }
</style>
