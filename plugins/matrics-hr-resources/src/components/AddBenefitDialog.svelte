<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import { Staff, BenefitType, BenefitStatus } from '@hcengineering/matrics-hr'
  import { getClient } from '@hcengineering/presentation'
  import { ModernDialog, EditBox, DropdownLabelsIntl } from '@hcengineering/ui'
  import { DateEditor } from '@hcengineering/view-resources'
  import hr from '../plugin'

  export let employee: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let benefitName = ''
  let benefitType: BenefitType = BenefitType.HealthInsurance
  let status: BenefitStatus = BenefitStatus.Eligible
  let provider = ''
  let enrollmentDate: number | null = null
  let effectiveDate: number | null = Date.now()
  let employeeContribution: number | null = null
  let employerContribution: number | null = null
  let currency = 'USD'
  let notes = ''

  const benefitTypes = [
    { id: BenefitType.HealthInsurance, label: hr.string.BenefitTypeHealthInsurance },
    { id: BenefitType.DentalInsurance, label: hr.string.BenefitTypeDentalInsurance },
    { id: BenefitType.VisionInsurance, label: hr.string.BenefitTypeVisionInsurance },
    { id: BenefitType.LifeInsurance, label: hr.string.BenefitTypeLifeInsurance },
    { id: BenefitType.Retirement401k, label: hr.string.BenefitTypeRetirement401k },
    { id: BenefitType.StockOptions, label: hr.string.BenefitTypeStockOptions },
    { id: BenefitType.GymMembership, label: hr.string.BenefitTypeGymMembership },
    { id: BenefitType.Other, label: hr.string.BenefitTypeOther }
  ]

  const benefitStatuses = [
    { id: BenefitStatus.Eligible, label: hr.string.BenefitStatusEligible },
    { id: BenefitStatus.Enrolled, label: hr.string.BenefitStatusEnrolled },
    { id: BenefitStatus.Declined, label: hr.string.BenefitStatusDeclined },
    { id: BenefitStatus.Terminated, label: hr.string.BenefitStatusTerminated }
  ]

  async function save (): Promise<void> {
    await client.addCollection(
      hr.class.EmployeeBenefit,
      employee.space,
      employee._id as Ref<Staff>,
      hr.mixin.Staff,
      'benefits',
      {
        benefitName,
        benefitType,
        status,
        provider: provider || undefined,
        enrollmentDate: enrollmentDate ?? undefined,
        effectiveDate: effectiveDate ?? undefined,
        employeeContribution: employeeContribution ?? undefined,
        employerContribution: employerContribution ?? undefined,
        currency,
        notes
      }
    )
    dispatch('close', true)
  }

  $: canSave = benefitName.trim().length > 0
</script>

<ModernDialog
  label={hr.string.AddBenefit}
  canSubmit={canSave}
  on:submit={save}
  on:close={() => dispatch('close')}
>
  <div class="form">
    <div class="form-row">
      <span class="label">Benefit Name *</span>
      <EditBox bind:value={benefitName} placeholder={'e.g., Premium Health Plan'} />
    </div>

    <div class="form-row">
      <span class="label">Benefit Type</span>
      <DropdownLabelsIntl
        items={benefitTypes}
        selected={benefitType}
        on:selected={(e) => benefitType = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Status</span>
      <DropdownLabelsIntl
        items={benefitStatuses}
        selected={status}
        on:selected={(e) => status = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Provider</span>
      <EditBox bind:value={provider} placeholder={'e.g., Blue Cross Blue Shield'} />
    </div>

    <div class="form-row">
      <span class="label">Enrollment Date</span>
      <DateEditor value={enrollmentDate} type={undefined} onChange={(v) => (enrollmentDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Effective Date</span>
      <DateEditor value={effectiveDate} type={undefined} onChange={(v) => (effectiveDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Employee Contribution (per month)</span>
      <EditBox bind:value={employeeContribution} placeholder={'0'} format={'number'} />
    </div>

    <div class="form-row">
      <span class="label">Employer Contribution (per month)</span>
      <EditBox bind:value={employerContribution} placeholder={'0'} format={'number'} />
    </div>

    <div class="form-row">
      <span class="label">Currency</span>
      <EditBox bind:value={currency} placeholder={'USD'} />
    </div>

    <div class="form-row">
      <span class="label">Notes</span>
      <EditBox bind:value={notes} placeholder={'Additional notes...'} />
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
    max-height: 70vh;
    overflow-y: auto;
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--theme-caption-color);
    }
  }
</style>
