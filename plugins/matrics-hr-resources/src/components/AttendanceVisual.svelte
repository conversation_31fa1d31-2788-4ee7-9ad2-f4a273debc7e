<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import type { Staff, AttendanceRecord, Office, Department } from '@hcengineering/matrics-hr'
  import * as THREE from 'three'
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

  export let attendance: AttendanceRecord[]
  export let staffById: Map<string, Staff>
  export let offices: Office[]
  export let departments: Department[]
  export let getStaffName: (ref: Ref<Staff> | string) => string
  export let getOfficeName: (ref: any) => string
  export let getDepartmentName: (ref: any) => string

  let container: HTMLDivElement
  let scene: THREE.Scene
  let camera: THREE.PerspectiveCamera
  let renderer: THREE.WebGLRenderer
  let controls: OrbitControls
  let animationId: number
  let employeeObjects: Map<string, THREE.Group> = new Map()

  const COLORS = {
    background: 0x0a0a0a,
    floor: 0x1a1a1a,
    wall: 0x2a2a2a,
    person: 0x3b82f6,
    personActive: 0x10b981,
    light: 0xffffff
  }

  onMount(() => {
    initScene()
    createOfficeLayout()
    updateEmployees()
    animate()

    return () => {
      if (animationId) cancelAnimationFrame(animationId)
      if (renderer) renderer.dispose()
      if (controls) controls.dispose()
    }
  })

  onDestroy(() => {
    if (animationId) cancelAnimationFrame(animationId)
    if (renderer) renderer.dispose()
    if (controls) controls.dispose()
  })

  $: if (scene && attendance) {
    updateEmployees()
  }

  function initScene() {
    // Scene
    scene = new THREE.Scene()
    scene.background = new THREE.Color(COLORS.background)
    scene.fog = new THREE.Fog(COLORS.background, 10, 50)

    // Camera
    const aspect = container.clientWidth / container.clientHeight
    camera = new THREE.PerspectiveCamera(60, aspect, 0.1, 1000)
    camera.position.set(15, 15, 15)
    camera.lookAt(0, 0, 0)

    // Renderer
    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    renderer.setSize(container.clientWidth, container.clientHeight)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    container.appendChild(renderer.domElement)

    // Controls
    controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05
    controls.maxPolarAngle = Math.PI / 2 - 0.1
    controls.minDistance = 5
    controls.maxDistance = 40

    // Lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4)
    scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(10, 20, 10)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 2048
    directionalLight.shadow.mapSize.height = 2048
    directionalLight.shadow.camera.left = -20
    directionalLight.shadow.camera.right = 20
    directionalLight.shadow.camera.top = 20
    directionalLight.shadow.camera.bottom = -20
    scene.add(directionalLight)

    const pointLight = new THREE.PointLight(COLORS.light, 0.5, 50)
    pointLight.position.set(0, 10, 0)
    scene.add(pointLight)

    // Handle resize
    window.addEventListener('resize', handleResize)
  }

  function handleResize() {
    if (!camera || !renderer || !container) return
    camera.aspect = container.clientWidth / container.clientHeight
    camera.updateProjectionMatrix()
    renderer.setSize(container.clientWidth, container.clientHeight)
  }

  function createOfficeLayout() {
    // Floor
    const floorGeometry = new THREE.PlaneGeometry(40, 40)
    const floorMaterial = new THREE.MeshStandardMaterial({
      color: COLORS.floor,
      roughness: 0.8,
      metalness: 0.2
    })
    const floor = new THREE.Mesh(floorGeometry, floorMaterial)
    floor.rotation.x = -Math.PI / 2
    floor.receiveShadow = true
    scene.add(floor)

    // Grid
    const gridHelper = new THREE.GridHelper(40, 40, 0x444444, 0x222222)
    scene.add(gridHelper)

    // Office areas (circular zones for different offices)
    const officeCount = Math.max(offices.length, 1)
    const radius = 8
    const angleStep = (Math.PI * 2) / officeCount

    offices.forEach((office, index) => {
      const angle = angleStep * index
      const x = Math.cos(angle) * 10
      const z = Math.sin(angle) * 10

      // Office zone circle
      const zoneGeometry = new THREE.CircleGeometry(radius, 32)
      const zoneMaterial = new THREE.MeshBasicMaterial({
        color: 0x1e40af,
        transparent: true,
        opacity: 0.1,
        side: THREE.DoubleSide
      })
      const zone = new THREE.Mesh(zoneGeometry, zoneMaterial)
      zone.rotation.x = -Math.PI / 2
      zone.position.set(x, 0.01, z)
      scene.add(zone)

      // Office label
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')!
      canvas.width = 512
      canvas.height = 128
      context.fillStyle = '#ffffff'
      context.font = 'bold 48px Arial'
      context.textAlign = 'center'
      context.fillText(office.name, 256, 64)

      const texture = new THREE.CanvasTexture(canvas)
      const spriteMaterial = new THREE.SpriteMaterial({ map: texture })
      const sprite = new THREE.Sprite(spriteMaterial)
      sprite.position.set(x, 0.5, z)
      sprite.scale.set(4, 1, 1)
      scene.add(sprite)
    })
  }

  function updateEmployees() {
    // Remove old employee objects
    employeeObjects.forEach((obj) => {
      scene.remove(obj)
    })
    employeeObjects.clear()

    // Group employees by office
    const byOffice = new Map<string, AttendanceRecord[]>()
    for (const rec of attendance) {
      const officeKey = rec.office ? String(rec.office) : 'none'
      if (!byOffice.has(officeKey)) byOffice.set(officeKey, [])
      byOffice.get(officeKey)!.push(rec)
    }

    // Create employee representations
    const officeCount = Math.max(offices.length, 1)
    const angleStep = (Math.PI * 2) / officeCount

    byOffice.forEach((records, officeKey) => {
      const officeIndex = offices.findIndex((o) => String(o._id) === officeKey)
      const angle = officeIndex >= 0 ? angleStep * officeIndex : 0
      const centerX = Math.cos(angle) * 10
      const centerZ = Math.sin(angle) * 10

      records.forEach((rec, index) => {
        const staffId = String(rec.staff)
        const employee = createEmployee(staffId, index, records.length, centerX, centerZ)
        employeeObjects.set(staffId, employee)
        scene.add(employee)
      })
    })
  }

  function createEmployee(
    staffId: string,
    index: number,
    total: number,
    centerX: number,
    centerZ: number
  ): THREE.Group {
    const group = new THREE.Group()

    // Arrange in a circle around office center
    const radius = 3 + Math.floor(index / 8) * 2
    const angleOffset = (Math.PI * 2 / Math.min(total, 8)) * (index % 8)
    const x = centerX + Math.cos(angleOffset) * radius
    const z = centerZ + Math.sin(angleOffset) * radius

    // Person body (cylinder)
    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.3, 1.5, 8)
    const bodyMaterial = new THREE.MeshStandardMaterial({
      color: COLORS.personActive,
      roughness: 0.5,
      metalness: 0.3
    })
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial)
    body.position.y = 0.75
    body.castShadow = true
    group.add(body)

    // Person head (sphere)
    const headGeometry = new THREE.SphereGeometry(0.25, 16, 16)
    const headMaterial = new THREE.MeshStandardMaterial({
      color: 0xfbbf24,
      roughness: 0.4,
      metalness: 0.1
    })
    const head = new THREE.Mesh(headGeometry, headMaterial)
    head.position.y = 1.75
    head.castShadow = true
    group.add(head)

    // Glow effect
    const glowGeometry = new THREE.SphereGeometry(0.5, 16, 16)
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: COLORS.personActive,
      transparent: true,
      opacity: 0.2
    })
    const glow = new THREE.Mesh(glowGeometry, glowMaterial)
    glow.position.y = 1
    group.add(glow)

    // Name label
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')!
    canvas.width = 256
    canvas.height = 64
    context.fillStyle = '#ffffff'
    context.font = '24px Arial'
    context.textAlign = 'center'
    const staffName = getStaffName(staffId)
    context.fillText(staffName.substring(0, 15), 128, 40)

    const texture = new THREE.CanvasTexture(canvas)
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture })
    const sprite = new THREE.Sprite(spriteMaterial)
    sprite.position.y = 2.5
    sprite.scale.set(2, 0.5, 1)
    group.add(sprite)

    // Position group
    group.position.set(x, 0, z)

    // Animate entrance
    group.scale.set(0, 0, 0)
    animateScale(group, 1, 500)

    // Add floating animation
    const userData = { initialY: 0, time: Math.random() * Math.PI * 2 }
    group.userData = userData

    return group
  }

  function animateScale(object: THREE.Group, targetScale: number, duration: number) {
    const startScale = object.scale.x
    const startTime = Date.now()

    function update() {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      const eased = 1 - Math.pow(1 - progress, 3) // ease out cubic
      const scale = startScale + (targetScale - startScale) * eased
      object.scale.set(scale, scale, scale)

      if (progress < 1) {
        requestAnimationFrame(update)
      }
    }

    update()
  }

  function animate() {
    animationId = requestAnimationFrame(animate)

    // Update floating animation for employees
    const time = Date.now() * 0.001
    employeeObjects.forEach((obj) => {
      if (obj.userData) {
        obj.position.y = Math.sin(time + obj.userData.time) * 0.1
      }
    })

    if (controls) controls.update()
    if (renderer && scene && camera) renderer.render(scene, camera)
  }
</script>

<div class="visual-container">
  <div class="visual-info">
    <div class="visual-info__stat">
      <span class="stat-label">In Office</span>
      <span class="stat-value">{attendance.length}</span>
    </div>
    <div class="visual-info__controls">
      <span class="control-hint">🖱️ Drag to rotate • Scroll to zoom</span>
    </div>
  </div>
  <div class="visual-canvas" bind:this={container}></div>
</div>

<style lang="scss">
  .visual-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .visual-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2);
    background: var(--theme-bg-accent-color);
    border-radius: var(--medium-BorderRadius);
    margin-bottom: var(--spacing-1);
  }

  .visual-info__stat {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_25);
  }

  .stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-trans-color);
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .visual-info__controls {
    font-size: 0.875rem;
    color: var(--theme-trans-color);
  }

  .control-hint {
    font-size: 0.75rem;
  }

  .visual-canvas {
    flex: 1;
    min-height: 500px;
    border-radius: var(--medium-BorderRadius);
    overflow: hidden;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  }
</style>
