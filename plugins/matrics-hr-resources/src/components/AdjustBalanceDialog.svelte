<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { getName } from '@hcengineering/contact'
  import { Staff, TimeOffBalance, TimeOffPolicy } from '@hcengineering/matrics-hr'
  import { getClient, createQuery } from '@hcengineering/presentation'
  import {
    Button,
    ModernDialog,
    Label,
    EditBox
  } from '@hcengineering/ui'
  import hr from '../plugin'

  export let employee: Staff
  export let balance: TimeOffBalance

  const policyQuery = createQuery()
  let policy: TimeOffPolicy | undefined

  policyQuery.query(hr.class.TimeOffPolicy, { _id: balance.policy }, (res) => {
    policy = res[0]
  })

  const client = getClient()
  const dispatch = createEventDispatcher()

  let adjustmentAmount: number = 0
  let adjustmentReason: string = ''
  let isAdding: boolean = true

  async function save (): Promise<void> {
    const finalAmount = isAdding ? adjustmentAmount : -adjustmentAmount
    const newBalance = (balance.balance ?? 0) + finalAmount

    await client.update(balance, {
      balance: newBalance
    })

    dispatch('close', true)
  }

  function cancel (): void {
    dispatch('close', false)
  }

  $: canSave = adjustmentAmount > 0 && adjustmentReason.trim().length > 0
</script>

<ModernDialog
  label={hr.string.BalanceAdjustment}
  canSubmit={canSave}
  on:submit={save}
  on:close={cancel}
>
  <div class="form">
    <div class="info">
      <div class="info-row">
        <span class="info-label">Policy:</span>
        <strong>{policy?.title ?? 'Time Off'}</strong>
      </div>
      <div class="info-row">
        <Label label={hr.string.CurrentBalance} />: <strong>{balance.balance ?? 0}</strong> days
      </div>
    </div>

    <div class="form-row">
      <div class="toggle-group">
        <Button
          label={hr.string.AddDays}
          kind={isAdding ? 'primary' : 'ghost'}
          size={'small'}
          on:click={() => isAdding = true}
        />
        <Button
          label={hr.string.SubtractDays}
          kind={!isAdding ? 'primary' : 'ghost'}
          size={'small'}
          on:click={() => isAdding = false}
        />
      </div>
    </div>

    <div class="form-row">
      <Label label={hr.string.Amount} />
      <EditBox
        bind:value={adjustmentAmount}
        placeholder={'0'}
        format={'number'}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.AdjustmentReason} />
      <EditBox
        bind:value={adjustmentReason}
        placeholder={'Explain why you are adjusting the balance'}
      />
    </div>

    <div class="preview">
      {#if adjustmentAmount > 0}
        <span class="preview-label"><Label label={hr.string.NewValue} />:</span>
        <span class="preview-value">
          {(balance.balance ?? 0) + (isAdding ? adjustmentAmount : -adjustmentAmount)} days
        </span>
      {/if}
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }

  .info {
    padding: 0.75rem;
    background: var(--theme-comp-header-color);
    border-radius: 0.375rem;
    font-size: 0.9375rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .info-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .info-label {
    color: var(--theme-dark-color);
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .toggle-group {
    display: flex;
    gap: 0.5rem;
  }

  .preview {
    padding: 0.75rem;
    background: var(--theme-bg-accent-color);
    border-radius: 0.375rem;
    font-size: 0.9375rem;
    display: flex;
    gap: 0.5rem;
    align-items: center;

    .preview-label {
      color: var(--theme-dark-color);
    }

    .preview-value {
      font-weight: 600;
      color: var(--theme-caption-color);
    }
  }
</style>
