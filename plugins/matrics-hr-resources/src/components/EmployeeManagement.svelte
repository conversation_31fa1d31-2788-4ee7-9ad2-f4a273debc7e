<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Ref } from '@hcengineering/core'
  import type { Employee } from '@hcengineering/contact'
  import contact, { getName } from '@hcengineering/contact'
  import { Department, Staff, TimeOffBalance, TimeOffPolicy, TimeOffTransaction, EmploymentType } from '@hcengineering/matrics-hr'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import ui, {
    Button,
    Label,
    EditBox,
    Section,
    showPopup,
    Component,
    DropdownLabelsIntl,
    DatePresenter,
    ListView,
    Tabs,
    Scroller,
    SectionEmpty
  } from '@hcengineering/ui'
  import hr from '../plugin'
  import EditEmployeeDialog from './EditEmployeeDialog.svelte'
  import AdjustBalanceDialog from './AdjustBalanceDialog.svelte'
  import AddDocumentDialog from './AddDocumentDialog.svelte'
  import AddCompensationDialog from './AddCompensationDialog.svelte'
  import AddPerformanceReviewDialog from './AddPerformanceReviewDialog.svelte'
  import AddBenefitDialog from './AddBenefitDialog.svelte'
  import AddLifecycleTaskDialog from './AddLifecycleTaskDialog.svelte'

  const client = getClient()
  const dispatch = createEventDispatcher()

  type ExtendedStaff = Staff & {
    emergencyContact?: string | null
    emergencyPhone?: string | null
    emergencyEmail?: string | null
    emergencyRelationship?: string | null
  }

  const staffQuery = createQuery()
  const departmentsQuery = createQuery()
  const balancesQuery = createQuery()
  const transactionsQuery = createQuery()
  const policiesQuery = createQuery()
  const documentsQuery = createQuery()
  const compensationQuery = createQuery()
  const reviewsQuery = createQuery()
  const benefitsQuery = createQuery()
  const lifecycleQuery = createQuery()

  let allStaff: ExtendedStaff[] = []
  let departments: Department[] = []
  let selectedDepartment: Ref<Department> | undefined = undefined
  let searchTerm = ''
  let selectedEmployee: ExtendedStaff | undefined = undefined
  let timeOffBalances: TimeOffBalance[] = []
  let timeOffTransactions: TimeOffTransaction[] = []
  let timeOffPolicies: TimeOffPolicy[] = []
  let employeeDocuments: any[] = []
  let employeeCompensations: any[] = []
  let performanceReviews: any[] = []
  let employeeBenefits: any[] = []
  let lifecycleTasks: any[] = []

  let selectedTab = 0
  let listSelection = 0
  let lifecycleCategory: 'onboarding' | 'offboarding' = 'onboarding'

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })

  function formatDate (value?: number | null): string | undefined {
    if (value == null) return undefined
    return dateFormatter.format(new Date(value))
  }

  staffQuery.query(hr.mixin.Staff, {}, (res) => {
    allStaff = res as ExtendedStaff[]
    if (allStaff.length > 0 && !selectedEmployee) {
      selectedEmployee = allStaff[0]
    }
  })

  departmentsQuery.query(hr.class.Department, {}, (res) => {
    departments = res
  })

  policiesQuery.query(hr.class.TimeOffPolicy, { active: true }, (res) => {
    timeOffPolicies = res
  })

  $: if (selectedEmployee) {
    balancesQuery.query(hr.class.TimeOffBalance, { staff: selectedEmployee._id as any }, (res) => {
      timeOffBalances = res
    })
    transactionsQuery.query(
      hr.class.TimeOffTransaction,
      { staff: selectedEmployee._id as any },
      (res) => {
        timeOffTransactions = res
      }
    )
    documentsQuery.query(
      hr.class.EmployeeDocument,
      { attachedTo: selectedEmployee._id as any },
      (res) => {
        employeeDocuments = res
      }
    )
    compensationQuery.query(
      hr.class.CompensationRecord,
      { attachedTo: selectedEmployee._id as any },
      (res) => {
        employeeCompensations = res
      }
    )
    reviewsQuery.query(
      hr.class.PerformanceReview,
      { attachedTo: selectedEmployee._id as any },
      (res) => {
        performanceReviews = res
      }
    )
    benefitsQuery.query(
      hr.class.EmployeeBenefit,
      { attachedTo: selectedEmployee._id as any },
      (res) => {
        employeeBenefits = res
      }
    )
    lifecycleQuery.query(
      hr.class.EmployeeLifecycleTask,
      { attachedTo: selectedEmployee._id as any },
      (res) => {
        lifecycleTasks = res
      }
    )
  }

  $: filteredStaff = allStaff.filter((staff) => {
    const matchesDept = !selectedDepartment || staff.department === selectedDepartment
    const matchesSearch = !searchTerm || getName(client.getHierarchy(), staff).toLowerCase().includes(searchTerm.toLowerCase())
    return matchesDept && matchesSearch
  })

  $: departmentOptions = [
    { id: undefined, label: hr.string.All },
    ...departments.map((d) => ({ id: d._id, label: d.name as any }))
  ]

  function editEmployee (employee: ExtendedStaff): void {
    showPopup(EditEmployeeDialog, { employee }, 'top', (result) => {
      if (result) {
        staffQuery.query(hr.mixin.Staff, {}, (res) => {
          allStaff = res
        })
      }
    })
  }

  function adjustBalance (employee: ExtendedStaff, balance: TimeOffBalance): void {
    showPopup(AdjustBalanceDialog, { employee, balance }, 'top')
  }

  function selectEmployee (employee: ExtendedStaff): void {
    selectedEmployee = employee
  }

  const employmentTypeLabels = {
    full_time: hr.string.EmploymentTypeFullTime,
    part_time: hr.string.EmploymentTypePartTime,
    contractor: hr.string.EmploymentTypeContractor,
    intern: hr.string.EmploymentTypeIntern,
    temporary: hr.string.EmploymentTypeTemporary
  }

  function addDocument (employee: ExtendedStaff): void {
    showPopup(AddDocumentDialog, { employee }, 'top')
  }

  function addCompensation (employee: ExtendedStaff): void {
    showPopup(AddCompensationDialog, { employee }, 'top')
  }

  function addPerformanceReview (employee: ExtendedStaff): void {
    showPopup(AddPerformanceReviewDialog, { employee }, 'top')
  }

  function addBenefit (employee: ExtendedStaff): void {
    showPopup(AddBenefitDialog, { employee }, 'top')
  }

  function addLifecycleTask (employee: ExtendedStaff): void {
    showPopup(AddLifecycleTaskDialog, { employee }, 'top')
  }

  $: tabs = [
    { id: 'overview', label: hr.string.Overview, component: null, props: {} },
    { id: 'timeoff', label: hr.string.TimeOff, component: null, props: {} },
    { id: 'documents', label: hr.string.Documents, component: null, props: {} },
    { id: 'compensation', label: hr.string.Compensation, component: null, props: {} },
    { id: 'reviews', label: hr.string.Performance, component: null, props: {} },
    { id: 'benefits', label: hr.string.Benefits, component: null, props: {} },
    { id: 'onboarding', label: hr.string.Onboarding, component: null, props: {} }
  ]
</script>

<div class="employee-management">
  <div class="employee-management-header">
    <div class="filters">
      <EditBox
        bind:value={searchTerm}
        kind={'ghost'}
        placeholder={ui.string.SearchDots}
      />
      <DropdownLabelsIntl
        items={departmentOptions}
        label={hr.string.FilterByDepartment}
        kind={'ghost'}
        on:selected={(e) => {
          selectedDepartment = e.detail
        }}
      />
    </div>
  </div>

  <div class="master-detail">
    <div class="master-pane">
      <ListView
        items={filteredStaff}
        count={filteredStaff.length}
        bind:selection={listSelection}
        on:click={(e) => { selectEmployee(filteredStaff[e.detail]) }}
      >
        <svelte:fragment slot="item" let:item={index}>
          {@const employee = filteredStaff[index]}
          <div class="employee-list-item">
            <Component
              is={contact.component.Avatar}
              props={{ person: employee, size: 'small', name: getName(client.getHierarchy(), employee) }}
            />
            <div class="employee-info">
              <div class="name">{getName(client.getHierarchy(), employee)}</div>
              <div class="role">{employee.jobTitle || '—'}</div>
            </div>
            <div class="dept-badge">
              {departments.find((d) => d._id === employee.department)?.name || '—'}
            </div>
          </div>
        </svelte:fragment>
      </ListView>
    </div>

    <div class="detail-pane">
      {#if selectedEmployee}
        <div class="detail-header">
          <div class="profile-header">
            <Component
              is={contact.component.Avatar}
              props={{ person: selectedEmployee, size: 'large', name: getName(client.getHierarchy(), selectedEmployee) }}
            />
            <div class="profile-info">
              <h1>{getName(client.getHierarchy(), selectedEmployee)}</h1>
              <div class="subtitle">
                {selectedEmployee.jobTitle || '—'} • {selectedEmployee.location || '—'}
              </div>
            </div>
            <div class="actions">
              <Button
                label={hr.string.EditEmployee}
                kind={'secondary'}
                on:click={() => { if (selectedEmployee) editEmployee(selectedEmployee) }}
              />
            </div>
          </div>
          <Tabs model={tabs} bind:selected={selectedTab} />
        </div>

        <div class="detail-content">
          <Scroller>
            {#if selectedTab === 0}
              <!-- Overview Tab -->
              <div class="tab-content">
                <Section label={hr.string.EmploymentInformation}>
                  <svelte:fragment slot="content">
                    <div class="detail-grid">
                    <div class="detail-item">
                    <span class="detail-label"><Label label={hr.string.Manager} /></span>
                    <span class="detail-value">
                      {#if selectedEmployee && selectedEmployee.manager}
                        {getName(client.getHierarchy(), allStaff.find((s) => s._id === selectedEmployee.manager)) || '—'}
                      {:else}
                        —
                      {/if}
                    </span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label"><Label label={hr.string.Department} /></span>
                    <span class="detail-value">
                      {(selectedEmployee && departments.find((d) => d._id === selectedEmployee.department)?.name) || '—'}
                    </span>
                  </div>
                  <div class="detail-item">
                      <span class="detail-label"><Label label={hr.string.EmploymentType} /></span>
                      <span class="detail-value">
                        {#if selectedEmployee && selectedEmployee.employmentType && employmentTypeLabels[selectedEmployee.employmentType]}
                          <Label label={employmentTypeLabels[selectedEmployee.employmentType]} />
                        {:else}
                          —
                        {/if}
                      </span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label"><Label label={hr.string.WorkHoursPerWeek} /></span>
                      <span class="detail-value">{selectedEmployee.workHoursPerWeek ? `${selectedEmployee.workHoursPerWeek} hrs` : '—'}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label"><Label label={hr.string.FTE} /></span>
                      <span class="detail-value">
                        {#if selectedEmployee.ftePercent != null}
                          {selectedEmployee.ftePercent}%
                        {:else}
                          —
                        {/if}
                      </span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label"><Label label={hr.string.CostCenter} /></span>
                      <span class="detail-value">{selectedEmployee.costCenter ?? '—'}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label"><Label label={hr.string.HireDate} /></span>
                      <span class="detail-value">
                        {#if selectedEmployee.hireDate}
                          <DatePresenter value={selectedEmployee.hireDate} />
                        {:else}
                          —
                        {/if}
                      </span>
                    </div>
                  </div>
                  </svelte:fragment>
                </Section>

                <Section label={hr.string.EmergencyInfo}>
                  <svelte:fragment slot="content">
                    <div class="detail-grid">
                      <div class="detail-item">
                        <span class="detail-label"><Label label={hr.string.EmergencyContact} /></span>
                        <span class="detail-value">{selectedEmployee.emergencyContact ?? '—'}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label"><Label label={hr.string.EmergencyPhone} /></span>
                        <span class="detail-value">{selectedEmployee.emergencyPhone ?? '—'}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label"><Label label={hr.string.EmergencyEmail} /></span>
                        <span class="detail-value">{selectedEmployee.emergencyEmail ?? '—'}</span>
                      </div>
                      <div class="detail-item">
                        <span class="detail-label"><Label label={hr.string.EmergencyRelationship} /></span>
                        <span class="detail-value">{selectedEmployee.emergencyRelationship ?? '—'}</span>
                      </div>
                    </div>
                  </svelte:fragment>
                </Section>
              </div>
            {:else if selectedTab === 1}
              <!-- Time Off Tab -->
              <div class="tab-content">
                <Section label={hr.string.TimeOffBalance}>
                  <svelte:fragment slot="content">
                    {#if timeOffBalances.length > 0}
                      <div class="balance-grid">
                        {#each timeOffBalances as balance (balance._id)}
                          <div class="balance-card">
                            <div class="balance-header">
                              <span class="balance-name">{timeOffPolicies.find((p) => p._id === balance.policy)?.title ?? 'Time Off'}</span>
                              <Button
                                label={hr.string.AdjustBalance}
                                kind={'link'}
                                size={'x-small'}
                                on:click={() => { if (selectedEmployee) adjustBalance(selectedEmployee, balance) }}
                              />
                            </div>
                            <div class="balance-stats">
                              <div class="balance-stat">
                                <span class="stat-label"><Label label={hr.string.CurrentBalance} /></span>
                                <span class="stat-value">{balance.balance ?? 0}</span>
                              </div>
                              <div class="balance-stat">
                                <span class="stat-label"><Label label={hr.string.PendingBalance} /></span>
                                <span class="stat-value">{balance.pending ?? 0}</span>
                              </div>
                              <div class="balance-stat">
                                <span class="stat-label"><Label label={hr.string.Carryover} /></span>
                                <span class="stat-value">{balance.carryover ?? 0}</span>
                              </div>
                            </div>
                          </div>
                        {/each}
                      </div>
                    {:else}
                      <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.Document} />
                    {/if}
                  </svelte:fragment>
                </Section>

                <Section label={hr.string.BalanceHistory}>
                  <svelte:fragment slot="content">
                    {#if timeOffTransactions.length > 0}
                      <div class="bamboo-list">
                        {#each timeOffTransactions as tx (tx._id)}
                          <div class="bamboo-list-item">
                            <div class="bamboo-list-main">
                              <div class="bamboo-list-title">{timeOffPolicies.find((p) => p._id === tx.policy)?.title ?? 'Time Off'}</div>
                              <div class="bamboo-list-sub">{tx.kind}</div>
                            </div>
                            <div class="bamboo-list-meta">
                              {tx.amount}
                              {#if tx.effectiveDate}
                                · {formatDate(tx.effectiveDate) ?? ''}
                              {/if}
                            </div>
                          </div>
                        {/each}
                      </div>
                    {:else}
                      <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.TimeOff} />
                    {/if}
                  </svelte:fragment>
                </Section>
              </div>
            {:else if selectedTab === 2}
              <!-- Documents Tab -->
              <div class="tab-content">
                <div class="section-actions">
                  <Button
                    label={hr.string.UploadDocument}
                    kind={'secondary'}
                    size={'small'}
                    on:click={() => { if (selectedEmployee) addDocument(selectedEmployee) }}
                  />
                </div>
                {#if employeeDocuments.length > 0}
                  <div class="bamboo-list">
                    {#each employeeDocuments as doc (doc._id)}
                      <div class="bamboo-list-item">
                        <div class="bamboo-list-main">
                          <div class="bamboo-list-title">{doc.title}</div>
                          {#if doc.documentType}
                            <div class="bamboo-list-sub">{doc.documentType}</div>
                          {/if}
                        </div>
                        {#if doc.status}
                          <div class="bamboo-list-meta">{doc.status}</div>
                        {/if}
                      </div>
                    {/each}
                  </div>
                {:else}
                  <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.TimeOff} />
                {/if}
              </div>
            {:else if selectedTab === 3}
              <!-- Compensation Tab -->
              <div class="tab-content">
                <div class="section-actions">
                  <Button
                    label={hr.string.AddCompensation}
                    kind={'secondary'}
                    size={'small'}
                    on:click={() => { if (selectedEmployee) addCompensation(selectedEmployee) }}
                  />
                </div>
                {#if employeeCompensations.length > 0}
                  <div class="bamboo-list">
                    {#each employeeCompensations as comp (comp._id)}
                      <div class="bamboo-list-item">
                        <div class="bamboo-list-main">
                          <div class="bamboo-list-title">{comp.amount} {comp.currency}</div>
                          {#if comp.compensationType}
                            <div class="bamboo-list-sub">{comp.compensationType}</div>
                          {/if}
                        </div>
                        <div class="bamboo-list-meta">
                          {#if comp.effectiveDate}
                            {formatDate(comp.effectiveDate) ?? ''}
                          {/if}
                          {#if comp.endDate}
                            · {formatDate(comp.endDate) ?? ''}
                          {/if}
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.Compensation} />
                {/if}
              </div>
            {:else if selectedTab === 4}
              <!-- Performance Tab -->
              <div class="tab-content">
                <div class="section-actions">
                  <Button
                    label={hr.string.AddReview}
                    kind={'secondary'}
                    size={'small'}
                    on:click={() => { if (selectedEmployee) addPerformanceReview(selectedEmployee) }}
                  />
                </div>
                {#if performanceReviews.length > 0}
                  <div class="bamboo-list">
                    {#each performanceReviews as review (review._id)}
                      <div class="bamboo-list-item">
                        <div class="bamboo-list-main">
                          <div class="bamboo-list-title">{review.title}</div>
                          <div class="bamboo-list-sub">
                            {#if review.reviewType}
                              {review.reviewType}
                            {/if}
                            {#if review.reviewPeriodStart && review.reviewPeriodEnd}
                              {#if review.reviewType}
                                ·
                              {/if}
                              {formatDate(review.reviewPeriodStart) ?? ''} – {formatDate(review.reviewPeriodEnd) ?? ''}
                            {/if}
                          </div>
                        </div>
                        <div class="bamboo-list-meta">
                          {#if review.status}{review.status}{/if}
                          {#if review.overallRating != null}
                            · {review.overallRating}/5
                          {/if}
                          {#if review.dueDate}
                            · {formatDate(review.dueDate) ?? ''}
                          {/if}
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.Performance} />
                {/if}
              </div>
            {:else if selectedTab === 5}
              <!-- Benefits Tab -->
              <div class="tab-content">
                <div class="section-actions">
                  <Button
                    label={hr.string.AddBenefit}
                    kind={'secondary'}
                    size={'small'}
                    on:click={() => { if (selectedEmployee) addBenefit(selectedEmployee) }}
                  />
                </div>
                {#if employeeBenefits.length > 0}
                  <div class="bamboo-list">
                    {#each employeeBenefits as benefit (benefit._id)}
                      <div class="bamboo-list-item">
                        <div class="bamboo-list-main">
                          <div class="bamboo-list-title">{benefit.benefitName}</div>
                          {#if benefit.provider}
                            <div class="bamboo-list-sub">{benefit.provider}</div>
                          {/if}
                        </div>
                        <div class="bamboo-list-meta">
                          {#if benefit.status}{benefit.status}{/if}
                          {#if benefit.effectiveDate}
                            · {formatDate(benefit.effectiveDate) ?? ''}
                          {/if}
                          {#if benefit.terminationDate}
                            · {formatDate(benefit.terminationDate) ?? ''}
                          {/if}
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.Benefit} />
                {/if}
              </div>
            {:else if selectedTab === 6}
              <!-- Onboarding Tab -->
              <div class="tab-content-board">
                <div class="board-header">
                  <div class="board-tabs">
                    <button
                      class="board-tab"
                      class:active={lifecycleCategory === 'onboarding'}
                      on:click={() => { lifecycleCategory = 'onboarding' }}
                    >
                      Onboarding
                    </button>
                    <button
                      class="board-tab"
                      class:active={lifecycleCategory === 'offboarding'}
                      on:click={() => { lifecycleCategory = 'offboarding' }}
                    >
                      Offboarding
                    </button>
                  </div>
                  <Button
                    label={hr.string.AddTask}
                    kind={'secondary'}
                    size={'small'}
                    on:click={() => { if (selectedEmployee) addLifecycleTask(selectedEmployee) }}
                  />
                </div>
                {#if lifecycleTasks.length > 0}
                  {@const filteredTasks = lifecycleTasks.filter((t) => t.category === lifecycleCategory)}
                  {@const notStartedTasks = filteredTasks.filter((t) => t.status === 'not_started')}
                  {@const inProgressTasks = filteredTasks.filter((t) => t.status === 'in_progress')}
                  {@const completedTasks = filteredTasks.filter((t) => t.status === 'completed')}
                  {@const skippedTasks = filteredTasks.filter((t) => t.status === 'skipped')}
                  <div class="kanban-board kanban-board-4col">
                    <!-- Not Started Column -->
                    <div class="kanban-column">
                      <div class="kanban-column-header kanban-header-notstarted">
                        <span class="kanban-column-title">Not Started</span>
                        <span class="kanban-column-count">{notStartedTasks.length}</span>
                      </div>
                      <div class="kanban-column-content">
                        {#each notStartedTasks as task (task._id)}
                          <div class="kanban-card">
                            <div class="kanban-card-header">
                              <span class="kanban-card-title">{task.title}</span>
                            </div>
                            {#if task.dueDate}
                              <div class="kanban-card-meta">
                                <span class="meta-label">Due:</span>
                                {formatDate(task.dueDate) ?? ''}
                              </div>
                            {/if}
                          </div>
                        {/each}
                      </div>
                    </div>
                    <!-- In Progress Column -->
                    <div class="kanban-column">
                      <div class="kanban-column-header kanban-header-inprogress">
                        <span class="kanban-column-title">In Progress</span>
                        <span class="kanban-column-count">{inProgressTasks.length}</span>
                      </div>
                      <div class="kanban-column-content">
                        {#each inProgressTasks as task (task._id)}
                          <div class="kanban-card">
                            <div class="kanban-card-header">
                              <span class="kanban-card-title">{task.title}</span>
                            </div>
                            {#if task.dueDate}
                              <div class="kanban-card-meta">
                                <span class="meta-label">Due:</span>
                                {formatDate(task.dueDate) ?? ''}
                              </div>
                            {/if}
                          </div>
                        {/each}
                      </div>
                    </div>
                    <!-- Completed Column -->
                    <div class="kanban-column">
                      <div class="kanban-column-header kanban-header-completed">
                        <span class="kanban-column-title">Completed</span>
                        <span class="kanban-column-count">{completedTasks.length}</span>
                      </div>
                      <div class="kanban-column-content">
                        {#each completedTasks as task (task._id)}
                          <div class="kanban-card kanban-card-completed">
                            <div class="kanban-card-header">
                              <span class="kanban-card-title">{task.title}</span>
                              <span class="status-badge status-completed">✓</span>
                            </div>
                            {#if task.completedDate}
                              <div class="kanban-card-meta">
                                <span class="meta-label">Completed:</span>
                                {formatDate(task.completedDate) ?? ''}
                              </div>
                            {/if}
                          </div>
                        {/each}
                      </div>
                    </div>
                    <!-- Skipped Column -->
                    <div class="kanban-column">
                      <div class="kanban-column-header kanban-header-skipped">
                        <span class="kanban-column-title">Skipped</span>
                        <span class="kanban-column-count">{skippedTasks.length}</span>
                      </div>
                      <div class="kanban-column-content">
                        {#each skippedTasks as task (task._id)}
                          <div class="kanban-card kanban-card-skipped">
                            <div class="kanban-card-header">
                              <span class="kanban-card-title">{task.title}</span>
                            </div>
                          </div>
                        {/each}
                      </div>
                    </div>
                  </div>
                {:else}
                  <SectionEmpty label={hr.string.NoProfileData} icon={hr.icon.Task} />
                {/if}
              </div>
            {/if}
          </Scroller>
        </div>
      {:else}
        <div class="empty-selection">
          <SectionEmpty label={hr.string.NoEmployeesFound} icon={hr.icon.Employee} />
        </div>
      {/if}
    </div>
  </div>
</div>

  <style lang="scss">
  .employee-management {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .employee-management-header {
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
    background: var(--theme-bg-color);
  }
  .filters {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .master-detail {
    display: flex;
    flex: 1;
    min-height: 0;
    overflow: hidden;
  }

  .master-pane {
    width: 300px;
    border-right: 1px solid var(--theme-divider-color);
    display: flex;
    flex-direction: column;
    background: var(--theme-bg-color);
  }

  .detail-pane {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    background: var(--theme-bg-color);
  }

  .employee-list-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    width: 100%;
  }

  .employee-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;

    .name {
      font-weight: 500;
      font-size: 0.875rem;
      color: var(--theme-caption-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .role {
      font-size: 0.75rem;
      color: var(--theme-dark-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .dept-badge {
    font-size: 0.6875rem;
    padding: 0.125rem 0.375rem;
    background: var(--theme-comp-header-color);
    border-radius: 0.25rem;
    color: var(--theme-dark-color);
    white-space: nowrap;
  }

  .detail-header {
    padding: 1.5rem 1.5rem 0;
    border-bottom: 1px solid var(--theme-divider-color);
    background: var(--theme-bg-color);
  }

  .profile-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;

    .profile-info {
      flex: 1;

      h1 {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--theme-caption-color);
        margin: 0 0 0.25rem;
      }

      .subtitle {
        font-size: 0.875rem;
        color: var(--theme-dark-color);
      }
    }
  }

  .detail-content {
    flex: 1;
    position: relative;
  }

  .tab-content {
    padding: 1.5rem;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;

    .detail-label {
      font-size: 0.75rem;
      color: var(--theme-dark-color);
      text-transform: uppercase;
      letter-spacing: 0.02em;
    }

    .detail-value {
      font-size: 0.9375rem;
      color: var(--theme-caption-color);
      font-weight: 500;
    }
  }

  .balance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .balance-card {
    padding: 1.25rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    background: var(--theme-comp-header-color);
  }

  .balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    .balance-name {
      font-weight: 600;
      font-size: 1rem;
      color: var(--theme-caption-color);
    }
  }

  .balance-stats {
    display: flex;
    gap: 2rem;
  }

  .balance-stat {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    .stat-label {
      font-size: 0.75rem;
      color: var(--theme-dark-color);
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--theme-caption-color);
    }
  }

  .section-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
  }

  .bamboo-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .bamboo-list-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem 1.25rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.375rem;
    background: var(--theme-button-default);
    transition: all 0.15s ease;
    cursor: pointer;

    &:hover {
      border-color: var(--theme-divider-hover-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transform: translateY(-1px);
    }
  }

  .bamboo-list-main {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    flex: 1;

    .bamboo-list-title {
      font-weight: 500;
      font-size: 0.9375rem;
      color: var(--theme-caption-color);
      line-height: 1.4;
    }

    .bamboo-list-sub {
      font-size: 0.8125rem;
      color: var(--theme-dark-color);
    }
  }

  .bamboo-list-meta {
    font-size: 0.8125rem;
    color: var(--theme-trans-color);
    white-space: nowrap;
    margin-left: 1rem;
  }

  .empty-selection {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--theme-dark-color);
  }

  /* Kanban Board Styles */
  .tab-content-board {
    padding: 1.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .board-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .board-tabs {
    display: flex;
    gap: 0.25rem;
    background: var(--theme-button-default);
    padding: 0.25rem;
    border-radius: 0.5rem;
    border: 1px solid var(--theme-divider-color);
  }

  .board-tab {
    padding: 0.5rem 1.25rem;
    border: none;
    background: transparent;
    color: var(--theme-dark-color);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover {
      background: var(--theme-bg-accent-color);
      color: var(--theme-caption-color);
    }

    &.active {
      background: var(--theme-bg-color);
      color: var(--theme-caption-color);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  .kanban-board {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    flex: 1;
    min-height: 0;
  }

  .kanban-board-4col {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  .kanban-column {
    display: flex;
    flex-direction: column;
    background: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .kanban-column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    background: var(--theme-comp-header-color);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .kanban-header-notstarted {
    border-left: 3px solid #94a3b8;
  }

  .kanban-header-inprogress {
    border-left: 3px solid #f59e0b;
  }

  .kanban-header-completed {
    border-left: 3px solid #16a34a;
  }

  .kanban-header-skipped {
    border-left: 3px solid #6b7280;
  }

  .kanban-column-title {
    font-weight: 600;
    font-size: 1rem;
    color: var(--theme-caption-color);
  }

  .kanban-column-count {
    font-size: 0.8125rem;
    color: var(--theme-trans-color);
    background: var(--theme-button-default);
    padding: 0.25rem 0.625rem;
    border-radius: 999px;
  }

  .kanban-column-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .kanban-card {
    background: var(--theme-bg-color);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.375rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover {
      border-color: var(--theme-divider-hover-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

  }

  .kanban-card-completed {
    opacity: 0.8;
    background: var(--theme-comp-header-color);
  }

  .kanban-card-skipped {
    opacity: 0.6;
    background: var(--theme-comp-header-color);
  }

  .kanban-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .kanban-card-title {
    font-weight: 500;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
    line-height: 1.4;
    flex: 1;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
  }

  .status-completed {
    background: rgba(22, 163, 74, 0.15);
    color: #16a34a;
  }

  .status-in-progress {
    background: rgba(245, 158, 11, 0.15);
    color: #f59e0b;
  }

  .kanban-card-meta {
    font-size: 0.8125rem;
    color: var(--theme-trans-color);
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }

  .meta-label {
    font-weight: 500;
  }
</style>
