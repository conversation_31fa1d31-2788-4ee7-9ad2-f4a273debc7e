<!--
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Employee } from '@hcengineering/contact'
  import { EmployeeArrayEditor } from '@hcengineering/contact-resources'
  import core, { Arr, Ref } from '@hcengineering/core'
  import { Card, getClient } from '@hcengineering/presentation'
  import { Button, EditBox, FocusHandler, createFocusManager } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import hr from '../plugin'

  const dispatch = createEventDispatcher()

  let name: string = ''
  let description: string = ''
  let address: string = ''
  let city: string = ''
  let country: string = ''
  let timezone: string = Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC'
  let managers: Arr<Ref<Employee>> = []

  export function canClose (): boolean {
    return name === ''
  }

  const client = getClient()

  async function createOffice () {
    const id = await client.createDoc(hr.class.Office, core.space.Workspace, {
      name,
      description,
      address,
      city,
      country,
      timezone,
      managers
    })
    dispatch('close', id)
  }

  const manager = createFocusManager()
</script>

<FocusHandler {manager} />
<Card
  label={hr.string.CreateOffice}
  okAction={createOffice}
  canSave={!!name && !!address && !!city && !!country}
  on:close={() => {
    dispatch('close')
  }}
  on:changeContent
>
  <div class="flex-row-center clear-mins">
    <div class="mr-3">
      <Button focusIndex={1} icon={hr.icon.Office} size={'medium'} kind={'link-bordered'} noFocus />
    </div>
    <div class="clear-mins flex-grow">
      <EditBox
        focusIndex={2}
        bind:value={name}
        placeholder={hr.string.Office}
        kind={'large-style'}
        autoFocus
      />
    </div>
  </div>

  <div class="mt-4">
    <EditBox
      focusIndex={3}
      bind:value={description}
      placeholder={core.string.Description}
      kind={'small-style'}
    />
  </div>

  <svelte:fragment slot="pool">
    <div class="mt-2">
      <EditBox
        focusIndex={4}
        bind:value={address}
        placeholder={hr.string.Address}
        kind={'regular'}
        size={'large'}
      />
    </div>

    <div class="flex-row mt-2 gap-2">
      <div class="flex-grow">
        <EditBox
          focusIndex={5}
          bind:value={city}
          placeholder={hr.string.City}
          kind={'regular'}
          size={'large'}
        />
      </div>
      <div class="flex-grow">
        <EditBox
          focusIndex={6}
          bind:value={country}
          placeholder={hr.string.Country}
          kind={'regular'}
          size={'large'}
        />
      </div>
    </div>

    <div class="mt-2">
      <EditBox
        focusIndex={7}
        bind:value={timezone}
        placeholder={hr.string.Timezone}
        kind={'regular'}
        size={'large'}
      />
    </div>

    <div class="mt-4">
      <EmployeeArrayEditor
        focusIndex={8}
        label={hr.string.Managers}
        bind:value={managers}
        kind={'regular'}
        size={'large'}
      />
    </div>
  </svelte:fragment>
</Card>


