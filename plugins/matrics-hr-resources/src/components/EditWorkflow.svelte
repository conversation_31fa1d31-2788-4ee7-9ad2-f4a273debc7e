<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { EmployeeBox } from '@hcengineering/contact-resources'
  import { getClient, createQuery } from '@hcengineering/presentation'
  import { createFocusManager, EditBox, FocusHandler, DropdownLabels, Label } from '@hcengineering/ui'
  import { Workflow, WorkflowStatus, WorkflowStep } from '@hcengineering/matrics-hr'
  import { StyledTextArea } from '@hcengineering/text-editor-resources'
  import { Ref } from '@hcengineering/core'
  import core from '@hcengineering/core'
  import { createEventDispatcher, onMount } from 'svelte'
  import hr from '../plugin'
  import DepartmentEditor from './DepartmentEditor.svelte'
  import WorkflowStepPresenter from './WorkflowStepPresenter.svelte'

  export let object: Workflow
  export let readonly: boolean = false

  const dispatch = createEventDispatcher()
  const client = getClient()

  let steps: WorkflowStep[] = []
  const stepsQuery = createQuery()

  $: stepsQuery.query(
    hr.class.WorkflowStep, 
    { attachedTo: object._id }, 
    (res) => {
      steps = res.sort((a, b) => a.order - b.order)
    }
  )

  const statusOptions = [
    { id: WorkflowStatus.Draft, label: hr.string.StatusDraft },
    { id: WorkflowStatus.Active, label: hr.string.StatusApproved },
    { id: WorkflowStatus.Completed, label: hr.string.Completed },
    { id: WorkflowStatus.Archived, label: hr.string.StatusCancelled }
  ]

  async function updateField (field: string, value: any): Promise<void> {
    if (object === undefined) return
    await client.update(object, {
      [field]: value
    })
  }

  async function onChangeDescription (): Promise<void> {
    if (object === undefined) return
    await client.update(object, {
      description: object.description
    })
  }

  const manager = createFocusManager()

  onMount(() => {
    dispatch('open', {
      ignoreKeys: ['comments', 'title', 'description', 'attachments'],
      collectionArrays: ['steps']
    })
  })
</script>

<FocusHandler {manager} />

{#if object !== undefined}
  <div class="flex-col flex-grow gap-3">
    <div class="name">
      <EditBox
        placeholder={hr.string.Title}
        bind:value={object.title}
        on:change={() => updateField('title', object.title)}
        focusIndex={1}
        {readonly}
      />
    </div>

    <div class="separator" />

    <div class="flex-row gap-2">
      <div class="flex-grow">
        <DepartmentEditor 
          label={hr.string.Department} 
          bind:value={object.department} 
          onChange={(val) => updateField('department', val)}
          kind={'regular'} 
          size={'large'}
          allowDeselect={true}
        />
      </div>
      <div class="flex-grow">
        <EmployeeBox
          focusIndex={2}
          label={hr.string.Assignee}
          placeholder={hr.string.Assignee}
          kind={'regular'}
          size={'large'}
          bind:value={object.assignee}
          on:change={() => updateField('assignee', object.assignee)}
          allowDeselect
          showNavigate={false}
          {readonly}
        />
      </div>
    </div>

    <div class="status-row">
      <Label label={hr.string.Status} />
      <DropdownLabels
        items={statusOptions}
        selected={object.status}
        on:selected={(e) => updateField('status', e.detail)}
        disabled={readonly}
      />
    </div>

    <div class="separator mt-2" />

    <div class="description-section">
      <StyledTextArea
        bind:content={object.description}
        placeholder={core.string.Description}
        showButtons={false}
        on:value={onChangeDescription}
      />
    </div>

    <div class="separator mt-4" />

    <div class="steps-section">
      <div class="steps-header">
        <Label label={hr.string.Steps} />
        <span class="count">({steps.length})</span>
      </div>
      {#each steps as step}
        <WorkflowStepPresenter value={step} />
      {/each}
      {#if steps.length === 0}
        <span class="empty-text">No steps added yet</span>
      {/if}
    </div>
  </div>
{/if}

<style lang="scss">
  .name {
    font-weight: 500;
    font-size: 1.25rem;
    color: var(--theme-caption-color);
  }

  .separator {
    height: 1px;
    background-color: var(--theme-divider-color);
    margin: 0.5rem 0;
  }

  .description-section {
    margin-top: 0.5rem;
  }

  .status-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }

  .steps-section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .steps-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;

      .count {
        font-size: 0.875rem;
        color: var(--theme-dark-color);
      }
    }

    .empty-text {
      font-size: 0.875rem;
      color: var(--theme-dark-color);
      font-style: italic;
    }
  }

  .gap-2 {
    gap: 0.5rem;
  }

  .gap-3 {
    gap: 0.75rem;
  }

  .mt-2 {
    margin-top: 0.5rem;
  }

  .mt-4 {
    margin-top: 1rem;
  }
</style>


