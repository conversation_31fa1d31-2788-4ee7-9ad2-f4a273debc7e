<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { WorkflowStep } from '@hcengineering/matrics-hr'
  import { Icon, CheckBox } from '@hcengineering/ui'
  import { getClient } from '@hcengineering/presentation'
  import view from '@hcengineering/view'

  export let value: WorkflowStep

  const client = getClient()

  async function toggleComplete () {
    await client.update(value, {
      completed: !value.completed,
      completedDate: value.completed ? undefined : Date.now()
    })
  }
</script>

<div class="workflow-step" class:completed={value.completed}>
  <CheckBox checked={value.completed} on:value={toggleComplete} />
  <div class="step-content">
    <span class="title">{value.title}</span>
    {#if value.description}
      <span class="description">{value.description}</span>
    {/if}
  </div>
  {#if value.assignee}
    <Icon icon={view.icon.Person} size={'small'} />
  {/if}
</div>

<style lang="scss">
  .workflow-step {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.375rem;
    background-color: var(--theme-button-default);

    &.completed {
      opacity: 0.6;

      .title {
        text-decoration: line-through;
      }
    }

    .step-content {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      flex-grow: 1;

      .title {
        font-weight: 500;
        color: var(--theme-caption-color);
      }

      .description {
        font-size: 0.875rem;
        color: var(--theme-dark-color);
      }
    }
  }
</style>


