<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'
  import { createFocusManager, EditBox, FocusHandler, Toggle, Label } from '@hcengineering/ui'
  import { Policy } from '@hcengineering/matrics-hr'
  import { StyledTextArea } from '@hcengineering/text-editor-resources'
  import core from '@hcengineering/core'
  import { createEventDispatcher, onMount } from 'svelte'
  import hr from '../plugin'
  import DepartmentEditor from './DepartmentEditor.svelte'

  export let object: Policy
  export let readonly: boolean = false

  const dispatch = createEventDispatcher()
  const client = getClient()

  async function updateField (field: string, value: any): Promise<void> {
    if (object === undefined) return
    await client.update(object, {
      [field]: value
    })
  }

  async function onChangeDescription (): Promise<void> {
    if (object === undefined) return
    await client.update(object, {
      description: object.description
    })
  }

  const manager = createFocusManager()

  onMount(() => {
    dispatch('open', {
      ignoreKeys: ['comments', 'title', 'description', 'triggerType', 'triggerConfig']
    })
  })
</script>

<FocusHandler {manager} />

{#if object !== undefined}
  <div class="modal-content">
    <div class="header-section">
      <div class="title-row">
        <div class="flex-grow">
          <EditBox
            placeholder={hr.string.Title}
            bind:value={object.title}
            on:change={() => updateField('title', object.title)}
            focusIndex={1}
            {readonly}
            kind={'large-style'}
          />
        </div>
        <div class="active-toggle">
          <Toggle 
            bind:on={object.active} 
            on:change={() => updateField('active', object.active)}
            disabled={readonly}
          />
          <span class="active-label">{object.active ? hr.string.Active : hr.string.Inactive}</span>
        </div>
      </div>

      <div class="meta-row">
        <DepartmentEditor 
          label={hr.string.Department} 
          bind:value={object.department} 
          onChange={(val) => updateField('department', val)}
          kind={'regular'} 
          size={'large'}
          allowDeselect={true}
        />
      </div>

      <div class="description-box">
        <StyledTextArea
          bind:content={object.description}
          placeholder={core.string.Description}
          showButtons={false}
          on:value={onChangeDescription}
        />
      </div>
    </div>

    <div class="section-card">
      <div class="section-header">
        <div class="section-title">Automation (Coming Soon)</div>
        <div class="section-subtitle">Configure automated triggers for this policy</div>
      </div>
      
      <div class="section-body disabled">
        <EditBox
          label={hr.string.TriggerType}
          placeholder={'e.g., contract_ending, anniversary, etc.'}
          bind:value={object.triggerType}
          on:change={() => updateField('triggerType', object.triggerType)}
          focusIndex={4}
          readonly={true}
          kind={'regular'}
          size={'large'}
        />
        <EditBox
          label={hr.string.TriggerConfig}
          placeholder={'JSON configuration'}
          bind:value={object.triggerConfig}
          on:change={() => updateField('triggerConfig', object.triggerConfig)}
          focusIndex={5}
          readonly={true}
          kind={'regular'}
          size={'large'}
        />
      </div>
    </div>
  </div>
{/if}

<style lang="scss">
  .modal-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding-bottom: var(--spacing-2);
  }

  .header-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .title-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .active-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-0_5) var(--spacing-1_5);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--small-BorderRadius);
    border: 1px solid var(--theme-divider-color);
  }

  .active-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--theme-content-color);
  }

  .meta-row {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .description-box {
    margin-top: var(--spacing-1);
  }

  .section-card {
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--medium-BorderRadius);
    overflow: hidden;
  }

  .section-header {
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
    background-color: var(--theme-bg-tertiary);
  }

  .section-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
  }

  .section-subtitle {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
    margin-top: 2px;
  }

  .section-body {
    padding: var(--spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);

    &.disabled {
      opacity: 0.6;
      pointer-events: none;
    }
  }
</style>


