<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Policy, Department } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Button, Icon, Label, showPopup, Scroller } from '@hcengineering/ui'
  import { Ref } from '@hcengineering/core'
  import hr from '../plugin'
  import CreatePolicy from './CreatePolicy.svelte'
  import view from '@hcengineering/view'

  export let department: Ref<Department> | undefined = undefined

  let policies: Policy[] = []
  const policiesQuery = createQuery()

  $: {
    if (department) {
      policiesQuery.query(hr.class.Policy, { department }, (res) => {
        policies = res
      })
    } else {
      policiesQuery.query(hr.class.Policy, {}, (res) => {
        policies = res
      })
    }
  }

  function createPolicy () {
    showPopup(CreatePolicy, { department })
  }
</script>

<div class="policy-list">
  <div class="policy-header">
    <div class="policy-header-title">
      <Icon icon={hr.icon.Policy} size={'small'} />
      <Label label={hr.string.Policies} />
      <span class="policy-count">{policies.length}</span>
    </div>
    <Button
      label={hr.string.CreatePolicy}
      icon={view.icon.Add}
      kind={'primary'}
      size={'small'}
      on:click={createPolicy}
    />
  </div>

  <Scroller>
    <div class="policy-content">
      {#if policies.length === 0}
        <div class="policy-empty">
          <span class="policy-empty-text">No policies created yet</span>
          <Button
            label={hr.string.CreatePolicy}
            icon={view.icon.Add}
            kind={'primary'}
            size={'medium'}
            on:click={createPolicy}
          />
        </div>
      {:else}
        <div class="policy-grid">
          {#each policies as policy (policy._id)}
            <div class="policy-card" class:inactive={!policy.active}>
              <div class="policy-status">
                <Icon 
                  icon={policy.active ? view.icon.CheckCircle : view.icon.Statuses} 
                  size={'small'} 
                  fill={policy.active ? 'var(--theme-won-color)' : 'var(--theme-trans-color)'}
                />
              </div>
              <div class="policy-info">
                <div class="policy-title">{policy.title}</div>
                <div class="policy-scope">
                  {#if policy.department}
                    Department-specific
                  {:else}
                    Company-wide
                  {/if}
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </Scroller>
</div>

<style lang="scss">
  .policy-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-2);
    gap: var(--spacing-2);
  }

  .policy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-1_5);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .policy-header-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .policy-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0 var(--spacing-0_75);
    background-color: var(--theme-button-default);
    color: var(--theme-caption-color);
    border-radius: var(--round-BorderRadius);
    font-size: 0.75rem;
    font-weight: 500;
  }

  .policy-content {
    padding: var(--spacing-1_5);
  }

  .policy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
    gap: var(--spacing-1_5);
  }

  @media (max-width: 1024px) {
    .policy-grid {
      grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
    }
  }

  @media (max-width: 640px) {
    .policy-grid {
      grid-template-columns: 1fr;
    }
  }

  .policy-card {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-1_5);
    padding: var(--spacing-1_5);
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--medium-BorderRadius);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }

    &.inactive {
      opacity: 0.6;
    }
  }

  .policy-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    flex-shrink: 0;
    background-color: var(--theme-navpanel-selected);
    border-radius: var(--small-BorderRadius);
  }

  .policy-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    flex: 1;
    min-width: 0;
  }

  .policy-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .policy-scope {
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .policy-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    background-color: var(--theme-button-default);
    border-radius: var(--medium-BorderRadius);
  }

  .policy-empty-text {
    color: var(--theme-dark-color);
    font-size: 0.9375rem;
  }

  @media (max-width: 768px) {
    .policy-list {
      padding: var(--spacing-1_5);
    }

    .policy-header {
      flex-direction: column;
      align-items: stretch;
      gap: var(--spacing-1_5);
    }

    .policy-header-title {
      justify-content: space-between;
    }
  }
</style>


