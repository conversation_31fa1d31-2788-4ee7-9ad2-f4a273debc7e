<!--
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import contact from '@hcengineering/contact'
  import { DocumentQuery, Ref, WithLookup } from '@hcengineering/core'
  import type { Department, Staff } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Button, IconAdd, Label, Scroller, SearchEdit, eventToHTMLElement, showPopup, Switcher } from '@hcengineering/ui'
  import hr from '../plugin'
  import CreateDepartment from './CreateDepartment.svelte'
  import DepartmentCard from './DepartmentCard.svelte'
  import OrgChart from './OrgChart.svelte'

  let search = ''
  let resultQuery: DocumentQuery<Department> = {}

  function updateResultQuery (search: string): void {
    resultQuery = search === '' ? {} : { $search: search }
  }

  function showCreateDialog (ev: MouseEvent): void {
    showPopup(CreateDepartment, {}, eventToHTMLElement(ev))
  }

  const query = createQuery()
  const spaceMembers = createQuery()

  let descendants: Map<Ref<Department>, Department[]> = new Map<Ref<Department>, Department[]>()
  let allEmployees: WithLookup<Staff>[] = []
  let head: Department | undefined
  let roots: Department[] = []
  let display: 'tree' | 'org' = 'tree'

  function handleDisplaySelect (event: CustomEvent<{ id?: 'tree' | 'org' }>): void {
    const next = event.detail?.id
    if (next !== undefined) display = next
  }

  query.query(
    hr.class.Department,
    {
      ...resultQuery
    },
    (res) => {
      head = res.find((p) => p._id === hr.ids.Head)
      descendants.clear()
      roots = []
      for (const doc of res) {
        if (doc.parent !== undefined) {
          const current = descendants.get(doc.parent) ?? []
          current.push(doc)
          descendants.set(doc.parent, current)
        } else {
          roots.push(doc)
        }
      }
      roots = roots.sort((a, b) => a.name.localeCompare(b.name))
      descendants = descendants
    },
    {
      lookup: {
        teamLead: contact.mixin.Employee
      }
    }
  )

  spaceMembers.query(hr.mixin.Staff, {}, (res) => {
    allEmployees = res
  })
</script>

<div class="ac-header full divide caption-height">
  <div class="ac-header__wrap-title mr-3">
    <span class="ac-header__title"><Label label={hr.string.Structure} /></span>
  </div>

  <div class="mb-1 clear-mins">
    <Button icon={IconAdd} label={hr.string.CreateDepartmentLabel} kind={'primary'} on:click={showCreateDialog} />
  </div>
</div>
<div class="ac-header full divide search-start">
  <div class="ac-header-full small-gap">
    <SearchEdit
      bind:value={search}
      on:change={() => {
        updateResultQuery(search)
      }}
    />
    <!-- <ActionIcon icon={IconMoreH} size={'small'} /> -->
  </div>
</div>

<div class="ac-header full divide">
  <div class="ac-header-full small-gap">
    <Switcher
      name={'structure-view'}
      kind={'subtle'}
      items={[
        { id: 'tree', icon: hr.icon.Structure, labelIntl: hr.string.Structure },
        { id: 'org', icon: hr.icon.OrgChart, labelIntl: hr.string.OrgChart }
      ]}
      selected={display}
      on:select={handleDisplaySelect}
    />
  </div>
  
</div>

<Scroller padding={'1rem 2.5rem'}>
  {#if display === 'tree'}
    {#if head}
      <DepartmentCard value={head} {descendants} {allEmployees} dragOver={undefined} dragPerson={undefined} />
    {:else if roots.length > 0}
      {#each roots as r}
        <DepartmentCard value={r} {descendants} {allEmployees} dragOver={undefined} dragPerson={undefined} />
      {/each}
    {:else}
      <div class="text-sm content-dark-color">No departments</div>
    {/if}
  {:else}
    <OrgChart root={head?._id} />
  {/if}
</Scroller>
