<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import { Staff, CompensationType, PayFrequency } from '@hcengineering/matrics-hr'
  import { getClient } from '@hcengineering/presentation'
  import { ModernDialog, EditBox, DropdownLabelsIntl } from '@hcengineering/ui'
  import { DateEditor } from '@hcengineering/view-resources'
  import hr from '../plugin'

  export let employee: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let compensationType: CompensationType = CompensationType.Salary
  let amount: number = 0
  let currency = 'USD'
  let payFrequency: PayFrequency = PayFrequency.Monthly
  let effectiveDate: number = Date.now()
  let endDate: number | null = null
  let reason = ''

  const compensationTypes = [
    { id: CompensationType.Salary, label: hr.string.CompensationTypeSalary },
    { id: CompensationType.Bonus, label: hr.string.CompensationTypeBonus },
    { id: CompensationType.Commission, label: hr.string.CompensationTypeCommission },
    { id: CompensationType.Equity, label: hr.string.CompensationTypeEquity },
    { id: CompensationType.Other, label: hr.string.CompensationTypeOther }
  ]

  const payFrequencies = [
    { id: PayFrequency.Monthly, label: hr.string.PayFrequencyMonthly },
    { id: PayFrequency.BiWeekly, label: hr.string.PayFrequencyBiWeekly },
    { id: PayFrequency.Weekly, label: hr.string.PayFrequencyWeekly },
    { id: PayFrequency.Annual, label: hr.string.PayFrequencyAnnual }
  ]

  async function save (): Promise<void> {
    await client.addCollection(
      hr.class.CompensationRecord,
      employee.space,
      employee._id as Ref<Staff>,
      hr.mixin.Staff,
      'compensation',
      {
        compensationType,
        amount,
        currency,
        payFrequency,
        effectiveDate,
        endDate: endDate ?? undefined,
        reason,
        approvedBy: client.getHierarchy().getAccount()._id as any
      }
    )
    dispatch('close', true)
  }

  $: canSave = amount > 0
</script>

<ModernDialog
  label={hr.string.AddCompensation}
  canSubmit={canSave}
  on:submit={save}
  on:close={() => dispatch('close')}
>
  <div class="form">
    <div class="form-row">
      <span class="label">Compensation Type</span>
      <DropdownLabelsIntl
        items={compensationTypes}
        selected={compensationType}
        on:selected={(e) => compensationType = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Amount *</span>
      <EditBox bind:value={amount} placeholder={'0'} format={'number'} />
    </div>

    <div class="form-row">
      <span class="label">Currency</span>
      <EditBox bind:value={currency} placeholder={'USD'} />
    </div>

    <div class="form-row">
      <span class="label">Pay Frequency</span>
      <DropdownLabelsIntl
        items={payFrequencies}
        selected={payFrequency}
        on:selected={(e) => payFrequency = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Effective Date</span>
      <DateEditor value={effectiveDate} type={undefined} onChange={(v) => (effectiveDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">End Date (Optional)</span>
      <DateEditor value={endDate} type={undefined} onChange={(v) => (endDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Reason</span>
      <EditBox bind:value={reason} placeholder={'e.g., Annual raise, Promotion, Bonus for Q4 performance...'} />
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--theme-caption-color);
    }
  }
</style>
