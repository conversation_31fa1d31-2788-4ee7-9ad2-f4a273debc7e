<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { AttachmentStyledBox } from '@hcengineering/attachment-resources'
  import core, { generateId, Markup, Ref } from '@hcengineering/core'
  import { Department } from '@hcengineering/matrics-hr'
  import { Card, getClient } from '@hcengineering/presentation'
  import { Button, EditBox, FocusHandler, createFocusManager, Toggle, Label } from '@hcengineering/ui'
  import { EmptyMarkup } from '@hcengineering/text'
  import { createEventDispatcher } from 'svelte'
  import hr from '../plugin'
  import DepartmentEditor from './DepartmentEditor.svelte'

  export let department: Ref<Department> | undefined = undefined

  const dispatch = createEventDispatcher()
  const client = getClient()

  let title: string = ''
  let description: Markup = EmptyMarkup
  let active: boolean = true
  let descriptionBox: AttachmentStyledBox
  
  const objectId = generateId()

  export function canClose (): boolean {
    return title === ''
  }

  async function createPolicy () {
    await client.createDoc(hr.class.Policy, core.space.Workspace, {
      title,
      description,
      department,
      active,
      triggerType: undefined,
      triggerConfig: undefined
    })
    await descriptionBox.createAttachments()
    dispatch('close', objectId)
  }

  const manager = createFocusManager()
</script>

<FocusHandler {manager} />
<Card
  label={hr.string.CreatePolicy}
  okAction={createPolicy}
  canSave={!!title}
  on:close={() => {
    dispatch('close')
  }}
  on:changeContent
>
  <div class="modal-content">
    <div class="header-section">
      <div class="title-row">
        <div class="icon-wrapper">
          <Button focusIndex={1} icon={hr.icon.Policy} size={'medium'} kind={'link-bordered'} noFocus />
        </div>
        <div class="flex-grow">
          <EditBox
            focusIndex={2}
            bind:value={title}
            placeholder={hr.string.Title}
            kind={'large-style'}
            autoFocus
          />
        </div>
        <div class="active-toggle">
          <Toggle bind:on={active} />
          <span class="active-label">{active ? hr.string.Active : hr.string.Inactive}</span>
        </div>
      </div>

      <div class="meta-row">
        <DepartmentEditor 
          label={hr.string.Department} 
          bind:value={department} 
          kind={'regular'} 
          size={'large'}
          allowDeselect={true}
        />
      </div>

      <div class="description-box">
        <AttachmentStyledBox
          bind:this={descriptionBox}
          {objectId}
          _class={hr.class.Policy}
          space={core.space.Workspace}
          alwaysEdit
          showButtons={false}
          kind={'indented'}
          enableBackReferences={false}
          bind:content={description}
          placeholder={core.string.Description}
          focusIndex={3}
        />
      </div>
    </div>
  </div>
</Card>

<style lang="scss">
  .modal-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding-bottom: var(--spacing-2);
  }

  .header-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .title-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .active-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-0_5) var(--spacing-1_5);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--small-BorderRadius);
    border: 1px solid var(--theme-divider-color);
  }

  .active-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--theme-content-color);
  }

  .meta-row {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .description-box {
    margin-top: var(--spacing-1);
  }
</style>


