<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Policy } from '@hcengineering/matrics-hr'
  import { Icon } from '@hcengineering/ui'
  import view from '@hcengineering/view'

  export let value: Policy

  $: statusIcon = value.active ? view.icon.Checkmark : view.icon.Draft
  $: statusColor = value.active ? 'var(--theme-won-color)' : 'var(--theme-trans-color)'
</script>

<div class="flex-row-center gap-2">
  <Icon icon={statusIcon} size={'small'} fill={statusColor} />
  <span>{value.title}</span>
</div>

<style lang="scss">
  .gap-2 {
    gap: 0.5rem;
  }
</style>


