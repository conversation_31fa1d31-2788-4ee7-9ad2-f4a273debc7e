<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import { Office, Department } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Button, Icon, Label, showPopup, Scroller } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import hr from '../plugin'
  import CreateOffice from './CreateOffice.svelte'

  export let department: Ref<Department> | undefined = undefined

  let offices: Office[] = []
  const officesQuery = createQuery()

  $: {
    // Offices are global but we keep the optional department prop for future filtering
    officesQuery.query(hr.class.Office, {}, (res) => {
      offices = res
    })
  }

  function createOffice () {
    showPopup(CreateOffice, {})
  }
</script>

<div class="offices-list">
  <div class="offices-header">
    <div class="offices-header-title">
      <Icon icon={hr.icon.Office} size={'small'} />
      <Label label={hr.string.Offices} />
      <span class="offices-count">{offices.length}</span>
    </div>
    <Button 
      label={hr.string.CreateOffice} 
      icon={view.icon.Add} 
      kind={'primary'} 
      size={'small'} 
      on:click={createOffice} 
    />
  </div>

  <Scroller>
    <div class="offices-content">
      {#if offices.length === 0}
        <div class="offices-empty">
          <span class="offices-empty-text">No offices configured yet</span>
          <Button 
            label={hr.string.CreateOffice} 
            icon={view.icon.Add} 
            kind={'primary'} 
            size={'medium'} 
            on:click={createOffice} 
          />
        </div>
      {:else}
        <div class="offices-grid">
          {#each offices as office (office._id)}
            <div class="office-card">
              <div class="office-icon">
                <Icon icon={hr.icon.Office} size={'medium'} />
              </div>
              <div class="office-info">
                <div class="office-name">{office.name}</div>
                <div class="office-location">
                  <span class="office-city">{office.city}</span>
                  <span class="office-separator">•</span>
                  <span class="office-country">{office.country}</span>
                </div>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </Scroller>
</div>

<style lang="scss">
  .offices-list {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: var(--spacing-2);
    gap: var(--spacing-2);
  }

  .offices-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-1_5);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .offices-header-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .offices-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0 var(--spacing-0_75);
    background-color: var(--theme-button-default);
    color: var(--theme-caption-color);
    border-radius: var(--round-BorderRadius);
    font-size: 0.75rem;
    font-weight: 500;
  }

  .offices-content {
    padding: var(--spacing-1_5);
  }

  .offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(16rem, 1fr));
    gap: var(--spacing-1_5);
  }

  @media (max-width: 1024px) {
    .offices-grid {
      grid-template-columns: repeat(auto-fill, minmax(14rem, 1fr));
    }
  }

  @media (max-width: 640px) {
    .offices-grid {
      grid-template-columns: 1fr;
    }
  }

  .office-card {
    display: flex;
    gap: var(--spacing-1_5);
    padding: var(--spacing-1_5);
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--medium-BorderRadius);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .office-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    flex-shrink: 0;
    background-color: var(--theme-navpanel-selected);
    border-radius: var(--small-BorderRadius);
    color: var(--theme-caption-color);
  }

  .office-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    flex: 1;
    min-width: 0;
  }

  .office-name {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .office-location {
    display: flex;
    align-items: center;
    gap: var(--spacing-0_5);
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .office-separator {
    opacity: 0.5;
  }

  .offices-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    background-color: var(--theme-button-default);
    border-radius: var(--medium-BorderRadius);
  }

  .offices-empty-text {
    color: var(--theme-dark-color);
    font-size: 0.9375rem;
  }

  @media (max-width: 768px) {
    .offices-list {
      padding: var(--spacing-1_5);
    }

    .offices-header {
      flex-direction: column;
      align-items: stretch;
      gap: var(--spacing-1_5);
    }

    .offices-header-title {
      justify-content: space-between;
    }
  }
</style>
