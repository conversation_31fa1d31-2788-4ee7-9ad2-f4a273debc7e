<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Request, RequestStatus } from '@hcengineering/matrics-hr'
  import { Icon, tooltip } from '@hcengineering/ui'
  import { IntlString } from '@hcengineering/platform'
  import hr from '../plugin'
  import view from '@hcengineering/view'

  export let value: Request | undefined

  $: status = value?.status ?? RequestStatus.Draft

  interface StatusConfig {
    icon: any
    label: IntlString
    color: string
  }

  const statusConfig: Record<RequestStatus, StatusConfig> = {
    [RequestStatus.Draft]: {
      icon: view.icon.Draft,
      label: hr.string.StatusDraft,
      color: 'var(--theme-trans-color)'
    },
    [RequestStatus.Pending]: {
      icon: hr.icon.Pending,
      label: hr.string.StatusPending,
      color: 'var(--theme-warning-color)'
    },
    [RequestStatus.Approved]: {
      icon: hr.icon.Approved,
      label: hr.string.StatusApproved,
      color: 'var(--theme-won-color)'
    },
    [RequestStatus.Rejected]: {
      icon: hr.icon.Rejected,
      label: hr.string.StatusRejected,
      color: 'var(--theme-error-color)'
    },
    [RequestStatus.Cancelled]: {
      icon: view.icon.Delete,
      label: hr.string.StatusCancelled,
      color: 'var(--theme-darker-color)'
    }
  }

  $: config = statusConfig[status]
</script>

{#if value !== undefined && config}
  <div class="status-container" use:tooltip={{ label: config.label }}>
    <Icon icon={config.icon} size={'small'} fill={config.color} />
  </div>
{/if}

<style lang="scss">
  .status-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
</style>


