<!--
// Copyright © 2023 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Ref } from '@hcengineering/core'
  import { getCurrentEmployee } from '@hcengineering/contact'
  import { Department } from '@hcengineering/matrics-hr'
  import { Scroller, Separator, NavItem, NavGroup, deviceOptionsStore as deviceInfo } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import { NavFooter, NavHeader } from '@hcengineering/workbench-resources'

  import hr from '../../plugin'

  import DepartmentsHierarchy from './DepartmentsHierarchy.svelte'

  export let department: Ref<Department>
  export let descendants: Map<Ref<Department>, Department[]>
  export let departmentById: Map<Ref<Department>, Department>
  export let page: string

  const dispatch = createEventDispatcher()
  const currentEmployee = getCurrentEmployee()
  const departments = [hr.ids.Head]
  $: isManager = Array.from(departmentById?.values?.() ?? []).some(
    (d) => d.teamLead === currentEmployee || (d.managers as any)?.includes?.(currentEmployee)
  )
</script>

<div
  class="antiPanel-navigator {$deviceInfo.navigator.direction === 'horizontal' ? 'portrait' : 'landscape'} border-left"
  class:fly={$deviceInfo.navigator.float}
>
  <div class="antiPanel-wrap__content hulyNavPanel-container">
    <NavHeader label={hr.string.HRApplication} />

    <Scroller shrink>
      <NavItem
        _id={'nav-dashboard'}
        icon={view.icon.Views}
        label={hr.string.Dashboard}
        selected={page === 'dashboard'}
        on:mousedown={(e) => e.stopPropagation()}
        on:click={(e) => {
          e.stopPropagation()
          dispatch('navigate', 'dashboard')
        }}
      />

      <NavItem
        _id={'nav-attendance'}
        icon={hr.icon.PTO}
        label={hr.string.Attendance}
        selected={page === 'attendance'}
        on:mousedown={(e) => e.stopPropagation()}
        on:click={(e) => {
          e.stopPropagation()
          dispatch('navigate', 'attendance')
        }}
      />

      <DepartmentsHierarchy {departments} {descendants} {departmentById} selected={department} on:selected />
      {#if isManager}
        <NavItem
          _id={'nav-inbox'}
          icon={hr.icon.Pending}
          label={hr.string.PendingRequests}
          selected={page === 'inbox'}
          on:mousedown={(e) => e.stopPropagation()}
          on:click={(e) => {
            e.stopPropagation()
            dispatch('navigate', 'inbox')
          }}
        />
        <NavItem
          _id={'nav-employees'}
          icon={hr.icon.Members}
          label={hr.string.EmployeeManagement}
          selected={page === 'employees'}
          on:mousedown={(e) => e.stopPropagation()}
          on:click={(e) => {
            e.stopPropagation()
            dispatch('navigate', 'employees')
          }}
        />
      {/if}
    </Scroller>

    <NavFooter />
  </div>
  {#if !($deviceInfo.isMobile && $deviceInfo.isPortrait && $deviceInfo.minWidth)}
    <Separator
      name={'schedule'}
      float={$deviceInfo.navigator.float ? 'navigator' : true}
      index={0}
      color={'var(--theme-navpanel-border)'}
    />
  {/if}
</div>
