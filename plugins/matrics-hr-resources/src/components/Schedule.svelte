<!--
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { onDestroy } from 'svelte'
  import { CalendarMode } from '@hcengineering/calendar-resources'
  import calendar from '@hcengineering/calendar-resources/src/plugin'
  import { DocumentQuery, Ref } from '@hcengineering/core'
  import { Department, Staff } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { getEmbeddedLabel } from '@hcengineering/platform'
  import type { TabItem, DropdownIntlItem } from '@hcengineering/ui'
  import {
    ModernButton,
    ButtonIcon,
    IconBack,
    IconForward,
    SearchInput,
    Separator,
    Header,
    Breadcrumb,
    Switcher,
    defineSeparators,
    twoPanelsSeparators,
    deviceOptionsStore as deviceInfo,
    tableToCSV,
    showPopup,
    Scroller
  } from '@hcengineering/ui'
  import view, { Viewlet, ViewletPreference } from '@hcengineering/view'
  import { ViewletSelector, ViewletSettingButton } from '@hcengineering/view-resources'
  import { getCurrentEmployee } from '@hcengineering/contact'

  import hr from '../plugin'

  import ScheduleView from './ScheduleView.svelte'
  import OrgChart from './OrgChart.svelte'
  import Structure from './Structure.svelte'
  import RequestsList from './RequestsList.svelte'
  import AllRequestsInbox from './AllRequestsInbox.svelte'
  import OfficesList from './OfficesList.svelte'
  import PolicyList from './PolicyList.svelte'
  import WorkflowList from './WorkflowList.svelte'
  import Sidebar from './sidebar/Sidebar.svelte'
  import ExportPopup from './schedule/ExportPopup.svelte'
  import MyProfile from './my/MyProfile.svelte'
  import MyTimeOff from './my/MyTimeOff.svelte'
  import Dashboard from './dashboard/Dashboard.svelte'
  import PerformanceList from './PerformanceList.svelte'
  import EmployeeManagement from './EmployeeManagement.svelte'
  import AttendanceOverview from './AttendanceOverview.svelte'

  const me = getCurrentEmployee()
  let accountStaff: Staff | undefined

  const accountStaffQ = createQuery()

  let department = accountStaff !== undefined ? accountStaff.department : hr.ids.Head
  $: if (me !== undefined) {
    accountStaffQ.query(hr.mixin.Staff, { _id: me as Ref<Staff> }, (res) => {
      accountStaff = res[0]
      department = accountStaff !== undefined ? accountStaff.department : hr.ids.Head
    })
  }

  let currentDate: Date = new Date()

  let search = ''
  let resultQuery: DocumentQuery<Staff> = {}

  function updateResultQuery (search: string): void {
    resultQuery = search === '' ? {} : { name: { $like: '%' + search + '%' } }
  }

  const query = createQuery()

  const hrPages = [
    'dashboard',
    'schedule',
    'structure',
    'requests',
    'inbox',
    'attendance',
    'employees',
    'assets',
    'documents',
    'performance',
    'training',
    'benefits',
    'my-profile',
    'my-timeoff'
  ] as const

  type HrPage = (typeof hrPages)[number]

  function isHrPage (value: string): value is HrPage {
    return (hrPages as readonly string[]).includes(value)
  }

  function navigateTo (value: string): void {
    if (isHrPage(value)) {
      page = value
    }
  }

  let descendants: Map<Ref<Department>, Department[]> = new Map<Ref<Department>, Department[]>()
  let departments: Map<Ref<Department>, Department> = new Map<Ref<Department>, Department>()

  let mode: CalendarMode = CalendarMode.Month
  let display: 'chart' | 'stats' | 'org' = 'chart'
  let page: HrPage = 'dashboard'

  $: breadcrumbLabel = (() => {
    switch (page) {
      case 'dashboard':
        return hr.string.Dashboard
      case 'my-profile':
        return getEmbeddedLabel('My Profile')
      case 'my-timeoff':
        return getEmbeddedLabel('My Time Off')
      case 'requests':
        return getEmbeddedLabel('Requests')
      case 'inbox':
        return hr.string.PendingRequests
      case 'attendance':
        return hr.string.Attendance
      case 'employees':
        return hr.string.EmployeeManagement
      case 'structure':
        return hr.string.Structure
      case 'assets':
        return getEmbeddedLabel('Assets')
      case 'documents':
        return getEmbeddedLabel('Documents')
      case 'performance':
        return getEmbeddedLabel('Performance')
      case 'training':
        return getEmbeddedLabel('Training')
      case 'benefits':
        return getEmbeddedLabel('Benefits')
      default:
        return display === 'org' ? hr.string.OrgChart : hr.string.Schedule
    }
  })()

  query.query(hr.class.Department, {}, (res) => {
    departments.clear()
    descendants.clear()
    for (const doc of res) {
      if (doc.parent !== undefined && doc._id !== hr.ids.Head) {
        const current = descendants.get(doc.parent) ?? []
        current.push(doc)
        descendants.set(doc.parent, current)
      }
      departments.set(doc._id, doc)
    }
    departments = departments
    descendants = descendants
  })

  function inc (val: number): void {
    switch (mode) {
      case CalendarMode.Month: {
        currentDate.setDate(1)
        currentDate.setMonth(currentDate.getMonth() + val)
        break
      }
      case CalendarMode.Year: {
        currentDate.setFullYear(currentDate.getFullYear() + val)
        break
      }
    }
    currentDate = currentDate
  }

  function getMonthName (date: Date): string {
    return new Intl.DateTimeFormat('default', {
      month: 'long'
    }).format(date)
  }

  const handleSelect = (event: CustomEvent<{ id?: 'ModeMonth' | 'ModeYear' }>): void => {
    const modeId = event.detail?.id
    if (modeId === 'ModeMonth') mode = CalendarMode.Month
    else if (modeId === 'ModeYear') mode = CalendarMode.Year
  }

  function departmentSelected (selected: Ref<Department>): void {
    department = selected
    page = 'schedule'
  }

  const viewslist: TabItem[] = [
    { id: 'chart', icon: view.icon.Views, labelIntl: hr.string.Schedule },
    { id: 'stats', icon: view.icon.Table, labelIntl: hr.string.Summary },
    { id: 'org', icon: hr.icon.OrgChart, labelIntl: hr.string.OrgChart }
  ]

  let viewlet: Viewlet | undefined
  let preference: ViewletPreference | undefined
  let loading = false

  function exportTable (evt: Event): void {
    interface ExportPopupItem extends DropdownIntlItem {
      separator: ',' | ';'
    }
    const items: ExportPopupItem[] = [
      {
        id: '0',
        label: getEmbeddedLabel(', (csv)'),
        separator: ','
      },
      {
        id: '1',
        label: getEmbeddedLabel('; (MS Excel)'),
        separator: ';'
      }
    ]
    showPopup(
      ExportPopup,
      {
        items
      },
      evt.target as HTMLElement,
      (res) => {
        if (res != null) {
          const filename = 'exportStaff' + new Date().toLocaleDateString() + '.csv'
          const link = document.createElement('a')
          link.style.display = 'none'
          link.setAttribute('target', '_blank')
          link.setAttribute(
            'href',
            'data:text/csv;charset=utf-8,%EF%BB%BF' +
              encodeURIComponent(tableToCSV('exportableData', items[res].separator))
          )
          link.setAttribute('download', filename)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      }
    )
  }

  let replacedPanel: HTMLElement
  $: $deviceInfo.replacedPanel = replacedPanel
  onDestroy(() => ($deviceInfo.replacedPanel = undefined))

  defineSeparators('schedule', twoPanelsSeparators)
</script>

<div class="hulyPanels-container">
  {#if $deviceInfo.navigator.visible}
    <Sidebar
      {department}
      {descendants}
      departmentById={departments}
      {page}
      on:selected={(e) => {
        departmentSelected(e.detail)
      }}
      on:navigate={(e) => {
        navigateTo(e.detail)
      }}
    />
    <Separator
      name={'schedule'}
      float={$deviceInfo.navigator.float}
      index={0}
      color={'transparent'}
      separatorSize={0}
      short
    />
  {/if}

  <div class="hulyComponent" bind:this={replacedPanel}>
    <Header
      adaptive={'disabled'}
      hideBefore={page !== 'schedule' || mode === CalendarMode.Year}
      hideActions={page !== 'schedule' ? true : !(mode === CalendarMode.Month && display === 'stats')}
    >
      <svelte:fragment slot="beforeTitle">
        {#if page !== 'structure' && mode === CalendarMode.Month}
          <Switcher
            name={'schedule-mode-view'}
            items={viewslist}
            kind={'subtle'}
            selected={display}
            on:select={(event) => {
              const next = event.detail?.id
              if (next !== undefined) display = next
            }}
          />
          {#if display === 'stats'}
            <ViewletSelector
              hidden
              bind:viewlet
              bind:preference
              bind:loading
              viewletQuery={{ _id: hr.viewlet.StaffStats }}
            />
            <ViewletSettingButton bind:viewlet />
          {/if}
        {/if}
      </svelte:fragment>

      <Breadcrumb
        icon={hr.icon.HR}
        label={breadcrumbLabel}
        size={'large'}
        isCurrent
      />

      <svelte:fragment slot="search">
        {#if page === 'schedule'}
        <SearchInput
          bind:value={search}
          collapsed
          on:change={() => {
            updateResultQuery(search)
          }}
        />
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="actions">
        <Switcher
          name={'hr-page-view'}
          kind={'subtle'}
          items={[{ id: 'schedule', labelIntl: hr.string.Schedule }, { id: 'structure', labelIntl: hr.string.Structure }]}
          selected={page}
          on:select={(result) => {
            if (result.detail !== undefined) navigateTo(result.detail.id)
          }}
        />
        {#if page === 'schedule' && mode === CalendarMode.Month && display === 'stats'}
          <ModernButton
            label={hr.string.Export}
            size={'small'}
            on:click={(evt) => {
              exportTable(evt)
            }}
          />
        {/if}
      </svelte:fragment>
    </Header>
    {#if page === 'schedule' && display !== 'org'}
    <div class="hulyHeader-container clearPadding justify-between flex-gap-4">
      <div class="flex-row-center flex-gap-2">
        <ButtonIcon
          icon={IconBack}
          kind={'tertiary'}
          size={'small'}
          on:click={() => {
            inc(-1)
          }}
        />
        <ModernButton
          label={calendar.string.Today}
          kind={'tertiary'}
          size={'small'}
          on:click={() => (currentDate = new Date())}
        />
        <ButtonIcon
          icon={IconForward}
          kind={'tertiary'}
          size={'small'}
          on:click={() => {
            inc(1)
          }}
        />
        <div class="hulyHeader-divider short" />
        <div class="fs-title flex-row-center flex-grow firstLetter">
          {#if mode === CalendarMode.Month}
            <span class="mr-2 overflow-label">{getMonthName(currentDate)}</span>
          {/if}
          {currentDate.getFullYear()}
        </div>
      </div>
      <Switcher
        name={'calendar-mode-view'}
        selected={mode === CalendarMode.Month ? 'ModeMonth' : 'ModeYear'}
        kind={'subtle'}
        items={[
          { id: 'ModeMonth', labelIntl: calendar.string.ModeMonth },
          { id: 'ModeYear', labelIntl: calendar.string.ModeYear }
        ]}
        on:select={handleSelect}
      />
    </div>
    {/if}

    {#if page === 'dashboard'}
      <Dashboard
        {department}
        {descendants}
        departmentById={departments}
        on:navigate={(e) => {
          navigateTo(e.detail)
        }}
      />
    {:else if page === 'structure'}
      <Structure />
    {:else if page === 'my-profile'}
      <MyProfile />
    {:else if page === 'my-timeoff'}
      <MyTimeOff />
    {:else if page === 'requests'}
      <RequestsList department={department} />
    {:else if page === 'inbox'}
      <AllRequestsInbox {department} {descendants} departmentById={departments} />
    {:else if page === 'attendance'}
      <AttendanceOverview />
    {:else if page === 'employees'}
      <Scroller>
        <EmployeeManagement />
      </Scroller>
    {:else if page === 'assets'}
      <div class="padding">Assets</div>
    {:else if page === 'documents'}
      <div class="padding">Documents</div>
    {:else if page === 'performance'}
      <PerformanceList />
    {:else if page === 'training'}
      <div class="padding">Training</div>
    {:else if page === 'benefits'}
      <div class="padding">Benefits</div>
    {:else}
      {#if display === 'org'}
        <OrgChart departmentId={department} />
      {:else}
        <ScheduleView
          {department}
          {descendants}
          departmentById={departments}
          staffQuery={resultQuery}
          {currentDate}
          {mode}
          {display}
          {preference}
          {viewlet}
          {loading}
        />
      {/if}
    {/if}
  </div>
</div>
