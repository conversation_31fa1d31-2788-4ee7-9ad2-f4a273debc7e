<script lang="ts">
  import type { Ref } from '@hcengineering/core'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import { EmployeePresenter } from '@hcengineering/contact-resources'
  import contact, { getName } from '@hcengineering/contact'
  import { Staff, PerformanceReview } from '@hcengineering/matrics-hr'
  import { Scroller, Icon, Label } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import hr from '../plugin'

  const client = getClient()

  const reviewsQuery = createQuery()
  const staffQuery = createQuery()

  let reviews: PerformanceReview[] = []
  let staffById = new Map<Ref<Staff>, Staff>()

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })

  function formatDate (value?: number | null): string | undefined {
    if (value == null) return undefined
    return dateFormatter.format(new Date(value))
  }

  reviewsQuery.query(hr.class.PerformanceReview, {}, (res) => {
    reviews = res
  })

  staffQuery.query(hr.mixin.Staff, {}, (res) => {
    staffById = new Map(res.map((s) => [s._id, s]))
  })
</script>

<Scroller>
  <div class="performance-list">
    <div class="header flex-row-center flex-between">
      <div class="flex-row-center gap-2">
        <Icon icon={view.icon.Views} size={'small'} />
        <Label label={hr.string.PerformanceReviews} />
        <span class="count">({reviews.length})</span>
      </div>
    </div>

    <div class="items">
      {#each reviews as review}
        {@const employee = staffById.get(review.attachedTo)}
        <div class="item">
          <div class="row flex-row-center gap-2">
            {#if employee}
              <EmployeePresenter value={employee} avatarSize={'x-small'} shouldShowAvatar shouldShowName />
            {/if}
            <div class="title">{review.title}</div>
          </div>
          <div class="meta">
            {#if review.reviewType}
              <span class="pill">{review.reviewType}</span>
            {/if}
            {#if review.reviewPeriodStart && review.reviewPeriodEnd}
              <span class="pill">{formatDate(review.reviewPeriodStart) ?? ''} – {formatDate(review.reviewPeriodEnd) ?? ''}</span>
            {/if}
            {#if review.status}
              <span class="pill">{review.status}</span>
            {/if}
            {#if review.overallRating != null}
              <span class="pill">{review.overallRating}/5</span>
            {/if}
            {#if review.dueDate}
              <span class="pill">{formatDate(review.dueDate) ?? ''}</span>
            {/if}
          </div>
        </div>
      {/each}

      {#if reviews.length === 0}
        <div class="empty">No performance reviews yet</div>
      {/if}
    </div>
  </div>
</Scroller>

<style lang="scss">
  .performance-list { display: flex; flex-direction: column; gap: 1rem; padding: 1.5rem; }
  .header { padding-bottom: 0.75rem; border-bottom: 1px solid var(--theme-divider-color); }
  .count { font-size: 0.875rem; color: var(--theme-dark-color); }
  .items { display: flex; flex-direction: column; gap: 0.5rem; margin-top: 0.75rem; }
  .item { padding: 0.75rem; border: 1px solid var(--theme-divider-color); border-radius: 0.5rem; background: var(--theme-button-default); }
  .row { align-items: center; gap: 0.75rem; }
  .title { font-weight: 500; }
  .meta { margin-top: 0.25rem; display: flex; flex-wrap: wrap; gap: 0.4rem; font-size: 0.8125rem; color: var(--theme-dark-color); }
  .pill { padding: 0.1rem 0.4rem; border-radius: 999px; background: var(--theme-comp-header-color); }
  .empty { padding: 2rem; text-align: center; color: var(--theme-dark-color); font-size: 0.875rem; }
</style>
