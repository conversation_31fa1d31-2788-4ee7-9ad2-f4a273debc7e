<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { AttachmentStyledBox } from '@hcengineering/attachment-resources'
  import core, { generateId, type Markup, type Ref } from '@hcengineering/core'
  import { Employee } from '@hcengineering/contact'
  import { EmployeeBox } from '@hcengineering/contact-resources'
  import {
    TimeOffAccrualFrequency,
    TimeOffAccrualMethod,
    type TimeOffPolicy,
    type Department,
    type RequestType
  } from '@hcengineering/matrics-hr'
  import { Card, createQuery, getClient } from '@hcengineering/presentation'
  import {
    DropdownLabelsIntl,
    EditBox,
    FocusHandler,
    Label,
    NumberInput,
    Toggle,
    createFocusManager
  } from '@hcengineering/ui'
  import { EmptyMarkup } from '@hcengineering/text'
  import { createEventDispatcher } from 'svelte'
  import hr from '../plugin'
  import DepartmentEditor from './DepartmentEditor.svelte'

  export let policyId: Ref<TimeOffPolicy> | undefined
  export let department: Ref<Department> | undefined

  const dispatch = createEventDispatcher()
  const client = getClient()
  const requestTypesQuery = createQuery()
  const policyQuery = createQuery()

  let requestTypes: RequestType[] = []
  let currentPolicy: TimeOffPolicy | undefined

  let title = ''
  let description: Markup = EmptyMarkup
  let selectedRequestType: Ref<RequestType> | undefined
  let selectedDepartment: Ref<Department> | undefined = department
  let active = true

  let accrualMethod: TimeOffAccrualMethod = TimeOffAccrualMethod.None
  let accrualFrequency: TimeOffAccrualFrequency | undefined = undefined
  let accrualRate: number | undefined = undefined
  let accrualCap: number | undefined = undefined
  let accrualStartMonth: number | undefined = undefined
  let accrualStartDay: number | undefined = undefined
  let carryoverLimit: number | undefined = undefined
  let carryoverExpiryDays: number | undefined = undefined
  let allowNegativeBalance = false
  let allowHalfDays = true
  let waitingPeriodDays: number | undefined = undefined
  let defaultApprover: Ref<Employee> | undefined = undefined
  let autoApprove = false
  let autoApproveMaxDays: number | undefined = undefined

  let descriptionBox: AttachmentStyledBox
  const objectId: Ref<TimeOffPolicy> = policyId ?? generateId()
  const manager = createFocusManager()

  const accrualMethodItems = [
    { id: TimeOffAccrualMethod.None, label: hr.string.AccrualMethodNone },
    { id: TimeOffAccrualMethod.LumpSum, label: hr.string.AccrualMethodLumpSum },
    { id: TimeOffAccrualMethod.Periodic, label: hr.string.AccrualMethodPeriodic },
    { id: TimeOffAccrualMethod.Hourly, label: hr.string.AccrualMethodHourly }
  ]

  const accrualFrequencyItems = [
    { id: TimeOffAccrualFrequency.Monthly, label: hr.string.AccrualFrequencyMonthly },
    { id: TimeOffAccrualFrequency.Quarterly, label: hr.string.AccrualFrequencyQuarterly },
    { id: TimeOffAccrualFrequency.Yearly, label: hr.string.AccrualFrequencyYearly },
    { id: TimeOffAccrualFrequency.Anniversary, label: hr.string.AccrualFrequencyAnniversary },
    { id: TimeOffAccrualFrequency.PerPayPeriod, label: hr.string.AccrualFrequencyPerPayPeriod }
  ]

  // Filter to only show time-off related request types (exclude ProfileUpdate, AssetAssignment, etc.)
  const timeOffTypeIds = [
    hr.ids.Vacation,
    hr.ids.Leave,
    hr.ids.Sick,
    hr.ids.PTO,
    hr.ids.PTO2,
    hr.ids.Remote,
    hr.ids.Overtime,
    hr.ids.Overtime2
  ]

  requestTypesQuery.query(hr.class.RequestType, {}, (res) => {
    requestTypes = res.filter((rt) => timeOffTypeIds.includes(rt._id))
    if (selectedRequestType === undefined && requestTypes.length > 0) {
      selectedRequestType = requestTypes[0]._id
    }
  })

  if (policyId !== undefined) {
    policyQuery.query(hr.class.TimeOffPolicy, { _id: policyId }, (res) => {
      const value = res[0]
      if (value !== undefined) {
        currentPolicy = value
        applyPolicy(value)
      }
    })
  }

  function applyPolicy (policy: TimeOffPolicy): void {
    title = policy.title
    description = policy.description ?? EmptyMarkup
    selectedRequestType = policy.requestType
    selectedDepartment = policy.department ?? undefined
    active = policy.active
    accrualMethod = policy.accrualMethod
    accrualFrequency = policy.accrualFrequency ?? undefined
    accrualRate = policy.accrualRate ?? undefined
    accrualCap = policy.accrualCap ?? undefined
    accrualStartMonth = policy.accrualStartMonth ?? undefined
    accrualStartDay = policy.accrualStartDay ?? undefined
    carryoverLimit = policy.carryoverLimit ?? undefined
    carryoverExpiryDays = policy.carryoverExpiryDays ?? undefined
    allowNegativeBalance = policy.allowNegativeBalance
    allowHalfDays = policy.allowHalfDays
    waitingPeriodDays = policy.waitingPeriodDays ?? undefined
    defaultApprover = policy.defaultApprover ?? undefined
    autoApprove = policy.autoApprove
    autoApproveMaxDays = policy.autoApproveMaxDays ?? undefined
  }

  function typeSelected (id: Ref<RequestType>): void {
    selectedRequestType = id
  }

  function accrualMethodSelected (id: TimeOffAccrualMethod | unknown): void {
    accrualMethod = id as TimeOffAccrualMethod
  }

  function accrualFrequencySelected (id: TimeOffAccrualFrequency | unknown): void {
    accrualFrequency = id as TimeOffAccrualFrequency
  }

  function sanitizeNumber (value: number | null | undefined): number | undefined {
    if (value == null || Number.isNaN(value)) return undefined
    return value
  }

  $: if (accrualMethod === TimeOffAccrualMethod.None) {
    accrualFrequency = undefined
    accrualRate = undefined
    accrualCap = undefined
    accrualStartMonth = undefined
    accrualStartDay = undefined
  }

  $: if (!autoApprove) {
    autoApproveMaxDays = undefined
  }

  async function savePolicy (): Promise<void> {
    if (!title || selectedRequestType === undefined) return

    const payload = {
      title,
      description,
      requestType: selectedRequestType,
      department: selectedDepartment,
      active,
      accrualMethod,
      accrualFrequency: accrualMethod === TimeOffAccrualMethod.None ? undefined : accrualFrequency,
      accrualRate: sanitizeNumber(accrualRate),
      accrualCap: sanitizeNumber(accrualCap),
      accrualStartMonth: sanitizeNumber(accrualStartMonth),
      accrualStartDay: sanitizeNumber(accrualStartDay),
      carryoverLimit: sanitizeNumber(carryoverLimit),
      carryoverExpiryDays: sanitizeNumber(carryoverExpiryDays),
      allowNegativeBalance,
      allowHalfDays,
      waitingPeriodDays: sanitizeNumber(waitingPeriodDays),
      defaultApprover,
      autoApprove,
      autoApproveMaxDays: autoApprove ? sanitizeNumber(autoApproveMaxDays) : undefined
    }

    if (currentPolicy) {
      await client.updateDoc(hr.class.TimeOffPolicy, currentPolicy._id, payload)
    } else {
      await client.createDoc(hr.class.TimeOffPolicy, core.space.Workspace, payload)
    }

    await descriptionBox.createAttachments()
    dispatch('close', objectId)
  }
</script>

<FocusHandler {manager} />
<Card
  label={currentPolicy ? hr.string.EditTimeOffPolicy : hr.string.CreateTimeOffPolicy}
  okAction={savePolicy}
  canSave={!!title && selectedRequestType !== undefined}
  on:close={() => {
    dispatch('close')
  }}
  on:changeContent
>
  <div class="modal-content">
    <!-- Header Section -->
    <div class="header-section">
      <div class="title-row">
        <div class="flex-grow">
          <EditBox
            focusIndex={1}
            bind:value={title}
            placeholder={hr.string.Title}
            kind={'large-style'}
            autoFocus
          />
        </div>
        <div class="active-toggle">
          <Toggle bind:on={active} />
          <span class="active-label">{active ? hr.string.Active : hr.string.Inactive}</span>
        </div>
      </div>
      
      <div class="meta-grid">
        <DropdownLabelsIntl
          items={requestTypes.map((item) => ({ id: item._id, label: item.label }))}
          label={hr.string.RequestType}
          selected={selectedRequestType}
          on:selected={(event) => { typeSelected(event.detail) }}
        />
        <DepartmentEditor
          label={hr.string.Department}
          bind:value={selectedDepartment}
          kind={'regular'}
          size={'large'}
          allowDeselect={true}
        />
      </div>

      <div class="description-box">
        <AttachmentStyledBox
          bind:this={descriptionBox}
          {objectId}
          _class={hr.class.TimeOffPolicy}
          space={core.space.Workspace}
          alwaysEdit
          showButtons={false}
          maxHeight="limited"
          bind:content={description}
          placeholder={hr.string.Description}
        />
      </div>
    </div>

    <!-- Settings Grid -->
    <div class="settings-grid">
      <!-- Accrual Column -->
      <div class="settings-column">
        <div class="section-card">
          <div class="section-header">
            <div class="section-title">{hr.string.AccrualSettings}</div>
            <div class="section-subtitle">How employees earn time off</div>
          </div>
          
          <div class="section-body">
            <div class="field-row">
              <DropdownLabelsIntl
                items={accrualMethodItems}
                label={hr.string.AccrualMethod}
                selected={accrualMethod}
                on:selected={(event) => {
                  accrualMethodSelected(event.detail)
                }}
              />
            </div>

            {#if accrualMethod !== TimeOffAccrualMethod.None}
              <div class="field-row">
                <DropdownLabelsIntl
                  items={accrualFrequencyItems}
                  label={hr.string.AccrualFrequency}
                  selected={accrualFrequency}
                  on:selected={(event) => {
                    accrualFrequencySelected(event.detail)
                  }}
                />
              </div>

              <div class="field-grid">
                <div class="field-item">
                  <div class="field-label">Rate</div>
                  <NumberInput bind:value={accrualRate} minValue={0} step={0.5} />
                </div>
                <div class="field-item">
                  <div class="field-label">Cap</div>
                  <NumberInput bind:value={accrualCap} minValue={0} step={0.5} />
                </div>
              </div>

              <div class="field-grid">
                <div class="field-item">
                  <div class="field-label">Start Month</div>
                  <NumberInput bind:value={accrualStartMonth} minValue={1} maxValue={12} step={1} />
                </div>
                <div class="field-item">
                  <div class="field-label">Start Day</div>
                  <NumberInput bind:value={accrualStartDay} minValue={1} maxValue={31} step={1} />
                </div>
              </div>
            {/if}
          </div>
        </div>

        <div class="section-card">
          <div class="section-header">
            <div class="section-title">{hr.string.CarryoverRules}</div>
            <div class="section-subtitle">Year-end balance handling</div>
          </div>
          
          <div class="section-body">
            <div class="field-grid">
              <div class="field-item">
                <div class="field-label">Max Carryover</div>
                <NumberInput bind:value={carryoverLimit} minValue={0} step={0.5} />
              </div>
              <div class="field-item">
                <div class="field-label">Expiry Days</div>
                <NumberInput bind:value={carryoverExpiryDays} minValue={0} step={1} />
              </div>
            </div>
            
            <div class="toggle-row">
              <Toggle bind:on={allowNegativeBalance} />
              <div class="toggle-text">
                <div class="toggle-label">{hr.string.AllowNegativeBalance}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Approval Column -->
      <div class="settings-column">
        <div class="section-card">
          <div class="section-header">
            <div class="section-title">{hr.string.ApprovalRules}</div>
            <div class="section-subtitle">Request validation & workflow</div>
          </div>
          
          <div class="section-body">
            <div class="field-row">
              <EmployeeBox
                label={hr.string.DefaultApprover}
                placeholder={hr.string.DefaultApprover}
                kind={'regular'}
                size={'large'}
                bind:value={defaultApprover}
                allowDeselect
                showNavigate={false}
              />
            </div>

            <div class="field-item">
              <div class="field-label">Waiting Period (Days)</div>
              <NumberInput bind:value={waitingPeriodDays} minValue={0} step={1} />
            </div>

            <div class="separator"></div>

            <div class="toggle-row">
              <Toggle bind:on={allowHalfDays} />
              <div class="toggle-text">
                <div class="toggle-label">{hr.string.AllowHalfDays}</div>
              </div>
            </div>

            <div class="toggle-row">
              <Toggle bind:on={autoApprove} />
              <div class="toggle-text">
                <div class="toggle-label">{hr.string.AutoApprove}</div>
                <div class="toggle-hint">Auto-approve small requests</div>
              </div>
            </div>

            {#if autoApprove}
              <div class="field-item indented">
                <div class="field-label">Max Auto-Approve Days</div>
                <NumberInput bind:value={autoApproveMaxDays} minValue={0} step={0.5} />
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
</Card>

<style lang="scss">
  .modal-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding-bottom: var(--spacing-2);
  }

  .header-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .title-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .active-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-0_5) var(--spacing-1_5);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--small-BorderRadius);
    border: 1px solid var(--theme-divider-color);
  }

  .active-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--theme-content-color);
  }

  .meta-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2);
  }

  .description-box {
    margin-top: var(--spacing-1);
  }

  .settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3);
    align-items: start;
  }

  .settings-column {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .section-card {
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--medium-BorderRadius);
    overflow: hidden;
  }

  .section-header {
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
    background-color: var(--theme-bg-tertiary);
  }

  .section-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
  }

  .section-subtitle {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
    margin-top: 2px;
  }

  .section-body {
    padding: var(--spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .field-row {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .field-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2);
  }

  .field-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    
    &.indented {
      margin-left: var(--spacing-4);
      border-left: 2px solid var(--theme-divider-color);
      padding-left: var(--spacing-2);
    }
  }

  .field-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--theme-trans-color);
  }

  .toggle-row {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-1_5);
    padding: var(--spacing-0_5) 0;
  }

  .toggle-text {
    display: flex;
    flex-direction: column;
  }

  .toggle-label {
    font-size: 0.875rem;
    color: var(--theme-content-color);
  }

  .toggle-hint {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
  }

  .separator {
    height: 1px;
    background-color: var(--theme-divider-color);
    margin: var(--spacing-1) 0;
  }

  @media (max-width: 768px) {
    .meta-grid, .settings-grid, .field-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
