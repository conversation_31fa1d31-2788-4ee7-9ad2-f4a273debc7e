<script lang="ts">
  import core, { Ref } from '@hcengineering/core'
  import { Staff, RequestStatus, timeToTzDate } from '@hcengineering/matrics-hr'
  import { Card, getClient } from '@hcengineering/presentation'
  import { EditBox, Label } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import hr from '../../plugin'
  const s = (hr.string as any)

  type ExtendedStaff = Staff & {
    emergencyContact?: string | null
    emergencyPhone?: string | null
    emergencyEmail?: string | null
    emergencyRelationship?: string | null
  }
  export let staff: ExtendedStaff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let emergencyContact = staff.emergencyContact ?? ''
  let emergencyPhone = staff.emergencyPhone ?? ''
  let emergencyEmail = staff.emergencyEmail ?? ''
  let emergencyRelationship = staff.emergencyRelationship ?? ''

  $: changed = (
    emergencyContact !== (staff.emergencyContact ?? '') ||
    emergencyPhone !== (staff.emergencyPhone ?? '') ||
    emergencyEmail !== (staff.emergencyEmail ?? '') ||
    emergencyRelationship !== (staff.emergencyRelationship ?? '')
  )

  function buildDescription(): string {
    const lines: string[] = []
    if (emergencyContact !== (staff.emergencyContact ?? '')) lines.push(`EmergencyContact: ${staff.emergencyContact ?? '—'} -> ${emergencyContact}`)
    if (emergencyPhone !== (staff.emergencyPhone ?? '')) lines.push(`EmergencyPhone: ${staff.emergencyPhone ?? '—'} -> ${emergencyPhone}`)
    if (emergencyEmail !== (staff.emergencyEmail ?? '')) lines.push(`EmergencyEmail: ${staff.emergencyEmail ?? '—'} -> ${emergencyEmail}`)
    if (emergencyRelationship !== (staff.emergencyRelationship ?? '')) lines.push(`EmergencyRelationship: ${staff.emergencyRelationship ?? '—'} -> ${emergencyRelationship}`)
    return lines.join('\n')
  }

  function buildPayload(): any {
    const payload: any = {}
    if (emergencyContact !== (staff.emergencyContact ?? '')) payload.emergencyContact = emergencyContact
    if (emergencyPhone !== (staff.emergencyPhone ?? '')) payload.emergencyPhone = emergencyPhone
    if (emergencyEmail !== (staff.emergencyEmail ?? '')) payload.emergencyEmail = emergencyEmail
    if (emergencyRelationship !== (staff.emergencyRelationship ?? '')) payload.emergencyRelationship = emergencyRelationship
    return payload
  }

  async function save () {
    if (!changed) return
    const now = Date.now()
    const payload: any = {
      type: (hr.ids as any).EmergencyInfoUpdate as Ref<any>,
      tzDate: timeToTzDate(now),
      tzDueDate: timeToTzDate(now),
      description: buildDescription(),
      payload: JSON.stringify(buildPayload()),
      department: staff.department,
      status: RequestStatus.Pending,
      submittedDate: now
    }
    await client.addCollection(hr.class.Request, core.space.Workspace, staff._id as Ref<Staff>, staff._class, 'requests', payload)
    dispatch('close')
  }
</script>

<Card
  label={s.RequestEmergencyInfoChange}
  okAction={save}
  canSave={changed}
  on:close={() => dispatch('close')}
  on:changeContent
>
  <div class="flex-col gap-2">
    <EditBox label={hr.string.EmergencyContact} bind:value={emergencyContact} />
    <EditBox label={hr.string.EmergencyPhone} bind:value={emergencyPhone} />
    <EditBox label={hr.string.EmergencyEmail} bind:value={emergencyEmail} />
    <EditBox label={hr.string.EmergencyRelationship} bind:value={emergencyRelationship} />
  </div>
  <svelte:fragment slot="error">
    {#if !changed}
      <Label label={hr.string.UnchangeableType} />
    {/if}
  </svelte:fragment>
</Card>
