<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import core, { Ref } from '@hcengineering/core'
  import contact, { getCurrentEmployee, getName, type Employee } from '@hcengineering/contact'
  import { Department, Staff, Request, RequestStatus, PublicHoliday, fromTzDate, TimeOffBalance, TimeOffPolicy } from '@hcengineering/matrics-hr'
  import type { AttendanceRecord } from '@hcengineering/matrics-hr'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import type { IntlString } from '@hcengineering/platform'
  import {
    Button,
    Label,
    MILLISECONDS_IN_DAY,
    Section,
    TabList,
    showPopup,
    Component,
    Scroller,
    type TabItem
  } from '@hcengineering/ui'

  import hr from '../../plugin'
  import RequestProfileUpdate from './RequestProfileUpdate.svelte'
  import RequestEmergencyInfoUpdate from './RequestEmergencyInfoUpdate.svelte'
  import RequestAssetAssignment from './RequestAssetAssignment.svelte'
  import RequestAssetReturn from './RequestAssetReturn.svelte'
  import DashboardExtensions from './DashboardExtensions.svelte'
  import TimeOffSummary from './widgets/TimeOffSummary.svelte'
  import UpcomingHolidays from './widgets/UpcomingHolidays.svelte'
  import AttendanceCard from './widgets/AttendanceCard.svelte'
  import TeamStatus from './widgets/TeamStatus.svelte'
  import ProfileSummary from './widgets/ProfileSummary.svelte'
  import JobDetails from './widgets/JobDetails.svelte'
  import AssetsList from './widgets/AssetsList.svelte'

  export let department: Ref<Department>
  export let descendants: Map<Ref<Department>, Department[]>
  export let departmentById: Map<Ref<Department>, Department>

  const dispatch = createEventDispatcher<{ navigate: string }>()

  const currentEmployee = getCurrentEmployee()
  const client = getClient()
  const staffQuery = createQuery()
  const requestsQuery = createQuery()
  const holidaysQuery = createQuery()
  const managerQuery = createQuery()
  const assetsQuery = createQuery()
  const balancesQuery = createQuery()
  const policiesQuery = createQuery()
  const attendanceQuery = createQuery()

  const s = hr.string as any
  const hc = hr.class as any

  type ExtendedStaff = Staff & {
    jobTitle?: string | null
    employmentType?: EmploymentTypeValue | null
    manager?: Ref<Employee> | null
    location?: string | null
    workHoursPerWeek?: number | null
    ftePercent?: number | null
    costCenter?: string | null
    hireDate?: number | null
    terminationDate?: number | null
    probationEndDate?: number | null
    vacationAllowance?: number | null
    vacationCarryover?: number | null
    sickAllowance?: number | null
    sickCarryover?: number | null
    personalAllowance?: number | null
    personalCarryover?: number | null
    ptoAllowance?: number | null
    ptoCarryover?: number | null
    emergencyContact?: string | null
    emergencyPhone?: string | null
    emergencyEmail?: string | null
    emergencyRelationship?: string | null
  }

  type EmploymentTypeValue = 'full_time' | 'part_time' | 'contractor' | 'intern' | 'temporary'

  interface UpcomingHoliday {
    id: string
    title: string
    date: Date
    departmentName?: string
  }

  interface AssignedAsset {
    _id: string
    assetName: string
    assetTag?: string
    serialNumber?: string
    assignedDate?: number
    returnDate?: number
    condition?: string
  }

  let staffRecord: ExtendedStaff | undefined
  let manager: Employee | undefined
  let myRequests: Request[] = []
  let rawHolidays: PublicHoliday[] = []
  let scopedDepartments: Ref<Department>[] = []
  let upcomingHolidays: UpcomingHoliday[] = []
  let employeeName: string | undefined
  let managerName: string | undefined
  let departmentName: string | undefined
  let employmentTypeIntl: IntlString | undefined
  let hireDateLabel: string | undefined
  let terminationDateLabel: string | undefined
  let probationEndLabel: string | undefined
  let workHoursLabel: string | undefined
  let approvedDays = 0
  let pendingDays = 0
  let availableDays = 0
  let remainingDays = 0
  let hasDeficit = false
  let assignedAssets: AssignedAsset[] = []
  let timeOffBalances: TimeOffBalance[] = []
  let timeOffPolicies: TimeOffPolicy[] = []
  let openAttendance: AttendanceRecord | undefined

  const timestampFormatter = new Intl.DateTimeFormat(undefined, {
    hour: 'numeric',
    minute: 'numeric',
    year: 'numeric'
  })
  const daysFormatter = new Intl.NumberFormat(undefined, {
    maximumFractionDigits: 1,
    minimumFractionDigits: 0
  })
  const hoursFormatter = new Intl.NumberFormat(undefined, {
    maximumFractionDigits: 1,
    minimumFractionDigits: 0
  })

  const tabs: TabItem[] = [
    { id: 'overview', labelIntl: s.Overview },
    { id: 'profile', labelIntl: hr.string.CurrentEmployee },
    { id: 'job', labelIntl: hr.string.Job },
    { id: 'balances', labelIntl: hr.string.TimeOffBalance },
    { id: 'documents', labelIntl: hr.string.Documents },
    { id: 'compensation', labelIntl: hr.string.Compensation },
    { id: 'performance', labelIntl: hr.string.Performance },
    { id: 'benefits', labelIntl: hr.string.Benefits },
    { id: 'lifecycle', labelIntl: hr.string.Onboarding },
    { id: 'assets', labelIntl: s.Assets }
  ]

  let selectedTab: string | number = 'overview'

  const employmentTypeLabels: Record<EmploymentTypeValue, IntlString> = {
    full_time: hr.string.EmploymentTypeFullTime,
    part_time: hr.string.EmploymentTypePartTime,
    contractor: hr.string.EmploymentTypeContractor,
    intern: hr.string.EmploymentTypeIntern,
    temporary: hr.string.EmploymentTypeTemporary
  }

  function collectDepartments (
    root: Ref<Department> | undefined,
    map: Map<Ref<Department>, Department[]>,
    visited: Set<string>
  ): Ref<Department>[] {
    if (root === undefined) return []
    const key = String(root)
    if (visited.has(key)) return []
    visited.add(key)
    const result: Ref<Department>[] = [root]
    const children = map.get(root) ?? []
    for (const child of children) {
      result.push(...collectDepartments(child._id, map, visited))
    }
    return result
  }

  function getEmploymentTypeIntl (type: EmploymentTypeValue | null | undefined): IntlString | undefined {
    if (type == null) return undefined
    return employmentTypeLabels[type]
  }

  function formatTimestamp (value?: number | null): string | undefined {
    if (value == null) return undefined
    return timestampFormatter.format(new Date(value))
  }

  function getRequestDuration (request: Request): number {
    const start = new Date(fromTzDate(request.tzDate))
    const end = new Date(fromTzDate(request.tzDueDate))
    start.setHours(0, 0, 0, 0)
    end.setHours(0, 0, 0, 0)
    const diff = Math.round((end.getTime() - start.getTime()) / MILLISECONDS_IN_DAY) + 1
    return diff > 0 ? diff : 1
  }

  function sumDaysByStatus (requests: Request[], status: RequestStatus): number {
    return requests
      .filter((request) => request.status === status)
      .reduce((total, request) => total + getRequestDuration(request), 0)
  }

  function computeUpcomingHolidays (
    holidays: PublicHoliday[],
    map: Map<Ref<Department>, Department>
  ): UpcomingHoliday[] {
    const startOfToday = new Date()
    startOfToday.setHours(0, 0, 0, 0)
    return holidays
      .map((holiday) => {
        const date = new Date(fromTzDate(holiday.date))
        date.setHours(0, 0, 0, 0)
        return {
          id: String(holiday._id),
          title: holiday.title,
          date,
          department: holiday.department
        }
      })
      .filter((entry) => entry.date.getTime() >= startOfToday.getTime())
      .sort((a, b) => a.date.getTime() - b.date.getTime())
      .slice(0, 3)
      .map((entry) => ({
        id: entry.id,
        title: entry.title,
        date: entry.date,
        departmentName: map.get(entry.department)?.name
      }))
  }

  // Use only the current employee's department for holidays (not the sidebar-selected one)
  $: employeeDepartment = staffRecord?.department
  $: scopedDepartments = employeeDepartment ? [employeeDepartment] : []

  $: if (currentEmployee !== undefined) {
    staffQuery.query(
      hr.mixin.Staff,
      { _id: currentEmployee as Ref<Staff> },
      (res) => {
        staffRecord = res[0] as ExtendedStaff
      }
    )
  } else {
    staffRecord = undefined
  }

  // Query open attendance record for current employee
  $: if (currentEmployee !== undefined) {
    attendanceQuery.query(
      hr.class.AttendanceRecord,
      { staff: currentEmployee as Ref<Staff>, clockOut: { $exists: false } },
      (res) => {
        openAttendance = (res[0] as AttendanceRecord | undefined)
      }
    )
  } else {
    openAttendance = undefined
  }

  $: if (currentEmployee !== undefined) {
    requestsQuery.query(
      hr.class.Request,
      {
        attachedTo: currentEmployee as Ref<Staff>,
        status: { $in: [RequestStatus.Pending, RequestStatus.Approved, RequestStatus.Rejected] }
      },
      (res) => {
        myRequests = res
      }
    )
  } else {
    myRequests = []
  }

  $: if (scopedDepartments.length > 0) {
    holidaysQuery.query(
      hr.class.PublicHoliday,
      { department: { $in: scopedDepartments } },
      (res) => {
        rawHolidays = res
      }
    )
  } else {
    rawHolidays = []
  }

  $: if (currentEmployee !== undefined) {
    assetsQuery.query(
      hc.AssignedAsset,
      { attachedTo: currentEmployee as Ref<Staff> },
      (res) => {
        assignedAssets = res as unknown as AssignedAsset[]
      }
    )
  } else {
    assignedAssets = []
  }

  // Query TimeOffBalances for current employee
  $: if (currentEmployee !== undefined) {
    balancesQuery.query(
      hr.class.TimeOffBalance,
      { staff: currentEmployee as Ref<Staff> },
      (res) => {
        timeOffBalances = res
      }
    )
  } else {
    timeOffBalances = []
  }

  // Query all active TimeOffPolicies
  policiesQuery.query(
    hr.class.TimeOffPolicy,
    { active: true },
    (res) => {
      timeOffPolicies = res
    }
  )

  $: if (staffRecord?.manager != null) {
    managerQuery.query(
      contact.mixin.Employee,
      { _id: staffRecord.manager as Ref<Employee> },
      (res) => {
        manager = res[0]
      }
    )
  } else {
    manager = undefined
  }

  $: upcomingHolidays = computeUpcomingHolidays(rawHolidays, departmentById)
  $: employeeName = staffRecord ? getName(client.getHierarchy(), staffRecord) : undefined
  $: managerName = manager ? getName(client.getHierarchy(), manager) : undefined
  $: departmentName = staffRecord ? departmentById.get(staffRecord.department)?.name : undefined
  $: employmentTypeIntl = getEmploymentTypeIntl(staffRecord?.employmentType)
  $: hireDateLabel = formatTimestamp(staffRecord?.hireDate)
  $: terminationDateLabel = formatTimestamp(staffRecord?.terminationDate)
  $: probationEndLabel = formatTimestamp(staffRecord?.probationEndDate)
  $: workHoursLabel =
    staffRecord?.workHoursPerWeek != null ? `${hoursFormatter.format(staffRecord.workHoursPerWeek)} h` : undefined

  // Compute totals from TimeOffBalance (new approach)
  $: totalBalance = timeOffBalances.reduce((sum, bal) => sum + (bal.balance ?? 0), 0)
  $: totalPending = timeOffBalances.reduce((sum, bal) => sum + (bal.pending ?? 0), 0)
  $: totalCarryover = timeOffBalances.reduce((sum, bal) => sum + (bal.carryover ?? 0), 0)
  
  // Legacy calculation for approved/pending days from requests (for backwards compatibility)
  $: approvedDays = sumDaysByStatus(myRequests, RequestStatus.Approved)
  $: pendingDays = sumDaysByStatus(myRequests, RequestStatus.Pending)
  
  // Use TimeOffBalance data only
  $: availableDays = totalBalance + totalCarryover
  $: usedFromBalance = 0
  $: pendingFromBalance = totalPending
  $: remainingDays = Math.max(availableDays - pendingFromBalance, 0)
  $: hasDeficit = availableDays > 0 && pendingFromBalance > availableDays

  $: isManager = Array.from(departmentById?.values?.() ?? []).some(
    (d) => d.teamLead === currentEmployee || (d.managers as any)?.includes?.(currentEmployee)
  )

  async function clockIn (): Promise<void> {
    if (currentEmployee === undefined || staffRecord == null || openAttendance != null) return

    const departmentRef = staffRecord.department
    const officeRef = departmentById.get(departmentRef)?.office

    await client.createDoc(
      hr.class.AttendanceRecord,
      core.space.Workspace,
      {
        staff: currentEmployee as Ref<Staff>,
        department: departmentRef,
        office: officeRef,
        clockIn: Date.now(),
        source: 'manual'
      }
    )
  }

  async function clockOut (): Promise<void> {
    if (!openAttendance) return
    const attendanceId = openAttendance._id as any
    await client.updateDoc(
      hr.class.AttendanceRecord,
      core.space.Workspace,
      attendanceId,
      {
        clockOut: Date.now()
      }
    )
    // Optimistically clear local state so UI switches back to "Clock in" immediately
    openAttendance = undefined

    // Refresh open attendance for current employee from backend
    if (currentEmployee !== undefined) {
      attendanceQuery.query(
        hr.class.AttendanceRecord,
        { staff: currentEmployee as Ref<Staff>, clockOut: { $exists: false } },
        (res) => {
          openAttendance = (res[0] as AttendanceRecord | undefined)
        }
      )
    }
  }

  function goTo (target: string): void {
    dispatch('navigate', target)
  }
</script>

<div class="dashboard">
  <div class="dashboard__hero">
    <div class="dashboard__greeting">
      {#if staffRecord}
        <div class="dashboard__avatar">
          <Component is={contact.component.Avatar} props={{ person: staffRecord, size: 'large', name: employeeName }} />
        </div>
      {/if}
      <div class="dashboard__greeting-content">
        {#if employeeName}
          <div class="dashboard__hello"><Label label={s.Hello} />, {employeeName}!</div>
        {/if}
        <div class="dashboard__subtitle"><Label label={s.DashboardSubtitle} /></div>
      </div>
    </div>
    <div class="dashboard__actions">
      <Button label={hr.string.TimeOff} kind={'ghost'} size={'small'} on:click={() => goTo('my-timeoff')} />
    </div>
  </div>

  <div class="dashboard__tabs">
    <TabList items={tabs} bind:selected={selectedTab} />
  </div>

  <Scroller padding={'var(--spacing-2) 0'} bottomPadding={'var(--spacing-2)'}>
  {#if selectedTab === 'overview'}
    <div class="dashboard__tab-content">
      <Section label={hr.string.TimeOffBalance}>
        <svelte:fragment slot="header">
          <Button label={hr.string.TimeOff} kind={'link'} size={'small'} on:click={() => { goTo('my-timeoff') }} />
        </svelte:fragment>
        <svelte:fragment slot="content">
          <TimeOffSummary
            availableDays={availableDays}
            usedDays={usedFromBalance}
            pendingDays={pendingFromBalance}
            remainingDays={remainingDays}
            hasDeficit={hasDeficit}
          />
        </svelte:fragment>
      </Section>

      <Section label={hr.string.UpcomingHolidays}>
        <svelte:fragment slot="content">
          <UpcomingHolidays holidays={upcomingHolidays} />
        </svelte:fragment>
      </Section>

      <Section label={hr.string.Attendance}>
        <svelte:fragment slot="content">
          <AttendanceCard
            openAttendance={openAttendance}
            on:clockIn={clockIn}
            on:clockOut={clockOut}
          />
        </svelte:fragment>
      </Section>
    </div>
  {:else if selectedTab === 'profile'}
    <div class="dashboard__tab-content">
      <div class="dashboard__header-actions" style="justify-content: flex-end; margin-bottom: var(--spacing-2);">
        {#if staffRecord}
          <Button label={s.RequestProfileChange} kind={'link-bordered'} size={'small'} on:click={() => showPopup(RequestProfileUpdate, { staff: staffRecord })} />
          <Button label={s.RequestEmergencyInfoChange} kind={'link-bordered'} size={'small'} on:click={() => showPopup(RequestEmergencyInfoUpdate, { staff: staffRecord })} />
        {/if}
      </div>
      <ProfileSummary
        staffRecord={staffRecord}
        employeeName={employeeName}
        departmentName={departmentName}
        managerName={managerName}
        employmentTypeIntl={employmentTypeIntl}
        workHoursLabel={workHoursLabel}
        hireDateLabel={hireDateLabel}
        probationEndLabel={probationEndLabel}
        terminationDateLabel={terminationDateLabel}
      />
    </div>
  {:else if selectedTab === 'balances'}
    <div class="dashboard__tab-content">
      <Section label={hr.string.TimeOffBalance}>
        <svelte:fragment slot="header">
          <Button label={hr.string.TimeOff} kind={'link'} size={'small'} on:click={() => { goTo('my-timeoff') }} />
        </svelte:fragment>
        <svelte:fragment slot="content">
          {#if timeOffBalances.length > 0}
            <div class="dashboard__balance-grid">
              {#each timeOffBalances as balance (balance._id)}
                {@const policy = timeOffPolicies.find((p) => p._id === balance.policy)}
                <div class="dashboard__balance-card">
                  <div class="dashboard__balance-header">
                    <span class="dashboard__balance-name">{policy?.title ?? 'Time Off'}</span>
                  </div>
                  <div class="dashboard__balance-stats">
                    <div class="dashboard__balance-stat">
                      <span class="dashboard__balance-stat-label">Current</span>
                      <span class="dashboard__balance-stat-value">{balance.balance ?? 0}</span>
                    </div>
                    <div class="dashboard__balance-stat">
                      <span class="dashboard__balance-stat-label">Pending</span>
                      <span class="dashboard__balance-stat-value">{balance.pending ?? 0}</span>
                    </div>
                    <div class="dashboard__balance-stat">
                      <span class="dashboard__balance-stat-label">Carryover</span>
                      <span class="dashboard__balance-stat-value">{balance.carryover ?? 0}</span>
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          {:else}
            <div class="dashboard__empty-state">
              <div class="dashboard__empty-icon">📅</div>
              <div class="dashboard__empty-title">No Time Off Policies</div>
              <div class="dashboard__empty-description">
                You don't have any time-off policies assigned yet.
              </div>
            </div>
          {/if}
        </svelte:fragment>
      </Section>
    </div>
  {:else if selectedTab === 'job'}
    <div class="dashboard__tab-content">
      <div class="dashboard__header-actions" style="justify-content: flex-end; margin-bottom: var(--spacing-2);">
        <Button
          label={hr.string.RequestChange}
          kind={'link'}
          size={'small'}
          on:click={() => showPopup(RequestProfileUpdate, { currentEmployee, staff: staffRecord }, 'top')}
        />
      </div>
      <JobDetails
        staffRecord={staffRecord}
        departmentName={departmentName}
        managerName={managerName}
        employmentTypeLabels={employmentTypeLabels}
      />

      <!-- Compensation (Placeholder for future) -->
      <Section label={hr.string.Compensation}>
        <svelte:fragment slot="content">
          <div class="dashboard__section-content">
            <div class="dashboard__empty-state">
              <div class="dashboard__empty-icon">💰</div>
              <div class="dashboard__empty-title">Compensation Information</div>
              <div class="dashboard__empty-description">
                Salary, bonuses, and compensation details will appear here.
              </div>
            </div>
          </div>
        </svelte:fragment>
      </Section>

      <!-- Contracts & Documents -->
      <Section label={hr.string.ContractsDocuments}>
        <svelte:fragment slot="header">
          <Button
            label={hr.string.UploadDocument}
            kind={'link'}
            size={'small'}
            disabled
          />
        </svelte:fragment>
        <svelte:fragment slot="content">
          <div class="dashboard__section-content">
            <div class="dashboard__empty-state">
              <div class="dashboard__empty-icon">📄</div>
              <div class="dashboard__empty-title">No Documents Yet</div>
              <div class="dashboard__empty-description">
                Employment contracts, NDAs, offer letters, and other documents will appear here.
              </div>
            </div>
          </div>
        </svelte:fragment>
      </Section>

      <!-- Job History (Placeholder for future) -->
      <Section label={hr.string.JobHistory}>
        <svelte:fragment slot="content">
          <div class="dashboard__section-content">
            <div class="dashboard__empty-state">
              <div class="dashboard__empty-icon">📋</div>
              <div class="dashboard__empty-title">Job History</div>
              <div class="dashboard__empty-description">
                Position changes, promotions, and transfers will be tracked here.
              </div>
            </div>
          </div>
        </svelte:fragment>
      </Section>
    </div>
  {:else if selectedTab === 'assets'}
    <div class="dashboard__tab-content">
      <Section label={hr.string.Assets}>
        <svelte:fragment slot="header">
          {#if staffRecord}
            <div class="dashboard__header-actions">
              <Button label={s.RequestAssetAssignment} kind={'link-bordered'} size={'small'} on:click={() => showPopup(RequestAssetAssignment, { staff: staffRecord })} />
              <Button label={s.RequestAssetReturn} kind={'link-bordered'} size={'small'} on:click={() => showPopup(RequestAssetReturn, { staff: staffRecord })} />
            </div>
          {/if}
        </svelte:fragment>
        <svelte:fragment slot="content">
          <AssetsList assignedAssets={assignedAssets} />
        </svelte:fragment>
      </Section>
    </div>
  {:else}
    <DashboardExtensions
      selectedTab={selectedTab}
      staffRecord={staffRecord}
      isManager={isManager}
    />
  {/if}
  </Scroller>
</div>

<style lang="scss">
  .dashboard {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    flex: 1;
    min-height: 0;
  }

  .dashboard__hero {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--medium-BorderRadius);
    background: linear-gradient(135deg, var(--theme-tablist-color), var(--theme-button-default));
  }

  .dashboard__greeting {
    display: flex;
    align-items: center;
    gap: var(--spacing-1_5);
    flex: 1;
    min-width: 0;
    color: var(--theme-caption-color);
  }

  .dashboard__avatar {
    flex-shrink: 0;
  }

  .dashboard__greeting-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .dashboard__hello {
    font-size: 1.75rem;
    font-weight: 600;
    line-height: 1.2;
  }

  .dashboard__subtitle {
    font-size: 0.9375rem;
    color: var(--theme-dark-color);
  }

  .dashboard__actions {
    display: inline-flex;
    gap: var(--spacing-1);
    align-items: center;
    flex-shrink: 0;
  }

  .dashboard__header-actions {
    display: inline-flex;
    gap: var(--spacing-1);
    align-items: center;
    flex-shrink: 0;
  }

  .dashboard__tabs {
    border-bottom: 1px solid var(--theme-tablist-plain-divider);
    padding-bottom: var(--spacing-0_5);
  }

  .dashboard__tab-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .dashboard__section-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .dashboard__stats-grid-overview {
    display: grid;
    gap: var(--spacing-2);
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 1200px) {
    .dashboard__stats-grid-overview {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .dashboard__stats-grid-overview {
      grid-template-columns: 1fr;
    }
  }

  .dashboard__stat-card-large {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-navpanel-divider);
    background-color: var(--theme-button-default);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .dashboard__stat-value-large {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--theme-caption-color);
    line-height: 1;
  }

  .dashboard__stats-grid {
    display: grid;
    gap: var(--spacing-1_5);
    grid-template-columns: repeat(4, 1fr);
  }

  .dashboard__stats-grid.is-detailed {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 1024px) {
    .dashboard__stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .dashboard__stats-grid {
      grid-template-columns: 1fr;
    }
  }

  .dashboard__stat-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_25);
    padding: var(--spacing-1);
    border-radius: var(--small-BorderRadius);
    border: 1px solid var(--theme-navpanel-divider);
    background-color: var(--theme-button-default);
  }

  .dashboard__stat-label {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
  }

  .dashboard__stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .dashboard__warning {
    margin-top: var(--spacing-1);
    padding: var(--spacing-0_75);
    border-radius: var(--small-BorderRadius);
    background-color: rgba(229, 160, 45, 0.15);
    color: var(--theme-warning-color);
  }

  .dashboard__profile-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .dashboard__profile-avatar {
    flex-shrink: 0;
  }

  .dashboard__profile-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .dashboard__profile-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-1_5);
  }

  .dashboard__profile-grid-emergency {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 1200px) {
    .dashboard__profile-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 1024px) {
    .dashboard__profile-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .dashboard__profile-grid {
      grid-template-columns: 1fr;
    }
  }

  .dashboard__profile-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_75);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .dashboard__profile-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--theme-dark-color);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .dashboard__profile-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--theme-caption-color);
  }

  .dashboard__requests {
    margin-top: var(--spacing-1_5);
    display: grid;
    gap: var(--spacing-0_75);
  }

  .dashboard__request-item {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-0_75) var(--spacing-1);
    border-radius: var(--small-BorderRadius);
    background-color: var(--theme-button-default);
  }

  .dashboard__request-label {
    font-size: 0.875rem;
    color: var(--theme-dark-color);
  }

  .dashboard__request-value {
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .dashboard__holidays-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-1_5);
  }

  @media (max-width: 1024px) {
    .dashboard__holidays-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .dashboard__holidays-grid {
      grid-template-columns: 1fr;
    }
  }

  .dashboard__holiday-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .dashboard__holiday-date {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--theme-trans-color);
    letter-spacing: 0.025em;
  }

  .dashboard__holiday-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
  }

  .dashboard__holiday-dept {
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .dashboard__job-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-2);
  }

  .dashboard__job-grid-dates {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 1024px) {
    .dashboard__job-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    .dashboard__job-grid-dates {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .dashboard__job-grid {
      grid-template-columns: 1fr;
    }
    .dashboard__job-grid-dates {
      grid-template-columns: 1fr;
    }
  }

  .dashboard__job-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    padding: var(--spacing-1_5);
    background: var(--theme-button-default);
    border-radius: var(--small-BorderRadius);
    border: 1px solid var(--theme-divider-color);
  }

  .dashboard__job-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--theme-dark-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .dashboard__job-value {
    font-size: 0.9375rem;
    font-weight: 500;
    color: var(--theme-caption-color);
  }

  .dashboard__empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-1);
    padding: var(--spacing-4);
    text-align: center;
    color: var(--theme-dark-color);
  }

  .dashboard__empty-icon {
    font-size: 3rem;
    opacity: 0.5;
  }

  .dashboard__empty-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .dashboard__empty-description {
    font-size: 0.875rem;
    max-width: 400px;
  }

  .dashboard__assets-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-1_5);
  }

  @media (max-width: 1200px) {
    .dashboard__assets-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .dashboard__assets-grid {
      grid-template-columns: 1fr;
    }
  }

  .dashboard__asset-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-button-hovered);
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
    }
  }

  .dashboard__asset-name {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
  }

  .dashboard__asset-details {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-0_5);
  }

  .dashboard__asset-dates {
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .dashboard__badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-0_25);
    padding: var(--spacing-0_25) var(--spacing-0_75);
    font-size: 0.75rem;
    border-radius: var(--small-BorderRadius);
    background-color: var(--theme-navpanel-selected);
    color: var(--theme-caption-color);
    font-weight: 500;
  }

  .dashboard__empty {
    padding: var(--spacing-1_5);
    text-align: center;
    color: var(--theme-dark-color);
    background-color: var(--theme-button-default);
    border-radius: var(--small-BorderRadius);
  }

  .dashboard__balance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-2);
  }

  .dashboard__balance-card {
    padding: var(--spacing-2);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-divider-color);
    background: var(--theme-button-default);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
      transform: translateY(-2px);
    }
  }

  .dashboard__balance-header {
    margin-bottom: var(--spacing-1_5);
    padding-bottom: var(--spacing-1);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .dashboard__balance-name {
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--theme-caption-color);
  }

  .dashboard__balance-stats {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-2);
  }

  .dashboard__balance-stat {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    flex: 1;
  }

  .dashboard__balance-stat-label {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-weight: 500;
  }

  .dashboard__balance-stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--theme-caption-color);
    line-height: 1;
  }

  @media (max-width: 768px) {
    .dashboard {
      padding: var(--spacing-1_5);
    }

    .dashboard__hero {
      flex-direction: column;
      align-items: stretch;
      padding: var(--spacing-2);
    }

    .dashboard__greeting {
      flex-direction: column;
      align-items: flex-start;
      text-align: left;
    }

    .dashboard__actions {
      width: 100%;
      justify-content: flex-start;
    }

    .dashboard__header-actions {
      flex-direction: column;
      width: 100%;
    }

    .dashboard__balance-stats {
      flex-direction: column;
      gap: var(--spacing-1_5);
    }
  }
</style>
