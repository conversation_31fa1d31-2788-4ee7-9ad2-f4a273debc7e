<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import core from '@hcengineering/core'
  import { Staff, RequestStatus, timeToTzDate } from '@hcengineering/matrics-hr'
  import { Card, getClient } from '@hcengineering/presentation'
  import { EditBox, DropdownLabelsIntl, Label } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import hr from '../../plugin'
  const s = (hr.string as any)

  export let staff: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let jobTitle = staff.jobTitle ?? ''
  let employmentType = staff.employmentType ?? ''
  let location = staff.location ?? ''
  let workHoursPerWeek = staff.workHoursPerWeek?.toString() ?? ''
  let hireDate = staff.hireDate ? new Date(staff.hireDate).toISOString().slice(0, 10) : ''
  let terminationDate = staff.terminationDate ? new Date(staff.terminationDate).toISOString().slice(0, 10) : ''
  let probationEndDate = staff.probationEndDate ? new Date(staff.probationEndDate).toISOString().slice(0, 10) : ''

  const employmentTypeOptions = [
    { id: 'full_time', label: hr.string.EmploymentTypeFullTime },
    { id: 'part_time', label: hr.string.EmploymentTypePartTime },
    { id: 'contractor', label: hr.string.EmploymentTypeContractor },
    { id: 'intern', label: hr.string.EmploymentTypeIntern },
    { id: 'temporary', label: hr.string.EmploymentTypeTemporary }
  ]

  $: changed = (
    jobTitle !== (staff.jobTitle ?? '') ||
    employmentType !== (staff.employmentType ?? '') ||
    location !== (staff.location ?? '') ||
    workHoursPerWeek !== (staff.workHoursPerWeek?.toString() ?? '') ||
    hireDate !== (staff.hireDate ? new Date(staff.hireDate).toISOString().slice(0, 10) : '') ||
    terminationDate !== (staff.terminationDate ? new Date(staff.terminationDate).toISOString().slice(0, 10) : '') ||
    probationEndDate !== (staff.probationEndDate ? new Date(staff.probationEndDate).toISOString().slice(0, 10) : '')
  )

  function fmtTimestamp(ms?: number | null) {
    if (ms == null) return '—'
    return new Intl.DateTimeFormat(undefined, { year: 'numeric', month: '2-digit', day: '2-digit' }).format(new Date(ms))
  }

  function buildDescription (): string {
    const lines: string[] = []
    if (jobTitle !== (staff.jobTitle ?? '')) lines.push(`JobTitle: ${staff.jobTitle ?? '—'} -> ${jobTitle}`)
    if (employmentType !== (staff.employmentType ?? '')) lines.push(`EmploymentType: ${staff.employmentType ?? '—'} -> ${employmentType}`)
    if (location !== (staff.location ?? '')) lines.push(`Location: ${staff.location ?? '—'} -> ${location}`)
    if (workHoursPerWeek !== (staff.workHoursPerWeek?.toString() ?? '')) lines.push(`WorkHoursPerWeek: ${staff.workHoursPerWeek ?? '—'} -> ${workHoursPerWeek}`)
    if (hireDate !== (staff.hireDate ? new Date(staff.hireDate).toISOString().slice(0, 10) : '')) lines.push(`HireDate: ${fmtTimestamp(staff.hireDate)} -> ${hireDate || '—'}`)
    if (terminationDate !== (staff.terminationDate ? new Date(staff.terminationDate).toISOString().slice(0, 10) : '')) lines.push(`TerminationDate: ${fmtTimestamp(staff.terminationDate)} -> ${terminationDate || '—'}`)
    if (probationEndDate !== (staff.probationEndDate ? new Date(staff.probationEndDate).toISOString().slice(0, 10) : '')) lines.push(`ProbationEndDate: ${fmtTimestamp(staff.probationEndDate)} -> ${probationEndDate || '—'}`)
    return lines.join('\n')
  }

  function buildPayload (): any {
    const payload: any = {}
    if (jobTitle !== (staff.jobTitle ?? '')) payload.jobTitle = jobTitle
    if (employmentType !== (staff.employmentType ?? '')) payload.employmentType = employmentType
    if (location !== (staff.location ?? '')) payload.location = location
    if (workHoursPerWeek !== (staff.workHoursPerWeek?.toString() ?? '')) payload.workHoursPerWeek = workHoursPerWeek
    if (hireDate !== (staff.hireDate ? new Date(staff.hireDate).toISOString().slice(0, 10) : '')) payload.hireDate = hireDate
    if (terminationDate !== (staff.terminationDate ? new Date(staff.terminationDate).toISOString().slice(0, 10) : '')) payload.terminationDate = terminationDate
    if (probationEndDate !== (staff.probationEndDate ? new Date(staff.probationEndDate).toISOString().slice(0, 10) : '')) payload.probationEndDate = probationEndDate
    return payload
  }

  async function save () {
    if (!changed) return
    const now = Date.now()
    const payload: any = {
      type: (hr.ids as any).ProfileUpdate as Ref<any>,
      tzDate: timeToTzDate(now),
      tzDueDate: timeToTzDate(now),
      description: buildDescription(),
      payload: JSON.stringify(buildPayload()),
      department: staff.department,
      status: RequestStatus.Pending,
      submittedDate: now
    }
    await client.addCollection(hr.class.Request, core.space.Workspace, staff._id as Ref<Staff>, staff._class, 'requests', payload)
    dispatch('close')
  }
</script>

<Card
  label={s.RequestProfileChange}
  okAction={save}
  canSave={changed}
  on:close={() => dispatch('close')}
  on:changeContent
>
  <div class="flex-col gap-2">
    <EditBox label={hr.string.JobTitle} bind:value={jobTitle} />
    <DropdownLabelsIntl label={hr.string.EmploymentType} items={employmentTypeOptions} bind:selected={employmentType} />
    <EditBox label={hr.string.Location} bind:value={location} />
    <EditBox label={hr.string.WorkHoursPerWeek} bind:value={workHoursPerWeek} />
    <EditBox label={hr.string.HireDate} bind:value={hireDate} />
    <EditBox label={hr.string.TerminationDate} bind:value={terminationDate} />
    <EditBox label={hr.string.ProbationEndDate} bind:value={probationEndDate} />
  </div>
  <svelte:fragment slot="error">
    {#if !changed}
      <Label label={hr.string.UnchangeableType} />
    {/if}
  </svelte:fragment>
</Card>
