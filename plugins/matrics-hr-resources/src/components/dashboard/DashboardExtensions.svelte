<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import type { Staff } from '@hcengineering/matrics-hr'
  import { createQuery } from '@hcengineering/presentation'
  import { Button, Label, Section, showPopup } from '@hcengineering/ui'
  import hr from '../../plugin'
  import AddDocumentDialog from '../AddDocumentDialog.svelte'
  import AddCompensationDialog from '../AddCompensationDialog.svelte'
  import AddPerformanceReviewDialog from '../AddPerformanceReviewDialog.svelte'
  import AddBenefitDialog from '../AddBenefitDialog.svelte'
  import AddLifecycleTaskDialog from '../AddLifecycleTaskDialog.svelte'

  export let selectedTab: string | number
  export let staffRecord: Staff | undefined
  export let isManager: boolean = false

  const documentsQuery = createQuery()
  const compensationQuery = createQuery()
  const reviewsQuery = createQuery()
  const benefitsQuery = createQuery()
  const lifecycleQuery = createQuery()

  let documents: any[] = []
  let compensations: any[] = []
  let reviews: any[] = []
  let benefits: any[] = []
  let lifecycleTasks: any[] = []

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })

  function formatDate (value?: number | null): string | undefined {
    if (value == null) return undefined
    return dateFormatter.format(new Date(value))
  }

  $: if (staffRecord) {
    documentsQuery.query(
      hr.class.EmployeeDocument,
      { attachedTo: staffRecord._id as any },
      (res) => {
        documents = res
      }
    )

    compensationQuery.query(
      hr.class.CompensationRecord,
      { attachedTo: staffRecord._id as any },
      (res) => {
        compensations = res
      }
    )

    reviewsQuery.query(
      hr.class.PerformanceReview,
      { attachedTo: staffRecord._id as any },
      (res) => {
        reviews = res
      }
    )

    benefitsQuery.query(
      hr.class.EmployeeBenefit,
      { attachedTo: staffRecord._id as any },
      (res) => {
        benefits = res
      }
    )

    lifecycleQuery.query(
      hr.class.EmployeeLifecycleTask,
      { attachedTo: staffRecord._id as any },
      (res) => {
        lifecycleTasks = res
      }
    )
  } else {
    documents = []
    compensations = []
    reviews = []
    benefits = []
    lifecycleTasks = []
  }

  function openAddDocument (): void {
    if (!staffRecord) return
    showPopup(AddDocumentDialog, { employee: staffRecord }, 'top')
  }

  function openAddCompensation (): void {
    if (!staffRecord) return
    showPopup(AddCompensationDialog, { employee: staffRecord }, 'top')
  }

  function openAddReview (): void {
    if (!staffRecord) return
    showPopup(AddPerformanceReviewDialog, { employee: staffRecord }, 'top')
  }

  function openAddBenefit (): void {
    if (!staffRecord) return
    showPopup(AddBenefitDialog, { employee: staffRecord }, 'top')
  }

  function openAddLifecycleTask (): void {
    if (!staffRecord) return
    showPopup(AddLifecycleTaskDialog, { employee: staffRecord }, 'top')
  }
</script>

{#if selectedTab === 'documents'}
  <div class="dashboard__tab-content">
    <Section label={hr.string.EmployeeDocuments}>
      <svelte:fragment slot="header">
        {#if isManager}
          <Button
            label={hr.string.UploadDocument}
            kind={'primary'}
            size={'small'}
            on:click={openAddDocument}
            disabled={!staffRecord}
          />
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="content">
        {#if staffRecord && documents.length > 0}
          <div class="dashboard__list">
            {#each documents as doc (doc._id)}
              <div class="dashboard__list-item">
                <div class="dashboard__list-main">
                  <div class="dashboard__list-title">{doc.title}</div>
                  {#if doc.documentType}
                    <div class="dashboard__list-sub">{doc.documentType}</div>
                  {/if}
                </div>
                {#if doc.status}
                  <div class="dashboard__list-meta">{doc.status}</div>
                {/if}
              </div>
            {/each}
          </div>
        {:else}
          <div class="dashboard__empty-state">
            <div class="dashboard__empty-icon">📄</div>
            <div class="dashboard__empty-title"><Label label={hr.string.EmployeeDocuments} /></div>
            <div class="dashboard__empty-description">
              Employment contracts, offer letters, NDAs, and other documents will appear here.
            </div>
          </div>
        {/if}
      </svelte:fragment>
    </Section>
  </div>
{:else if selectedTab === 'compensation'}
  <div class="dashboard__tab-content">
    <Section label={hr.string.CompensationHistory}>
      <svelte:fragment slot="header">
        {#if isManager}
          <Button
            label={hr.string.AddCompensation}
            kind={'primary'}
            size={'small'}
            on:click={openAddCompensation}
            disabled={!staffRecord}
          />
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="content">
        {#if staffRecord && compensations.length > 0}
          <div class="dashboard__list">
            {#each compensations as comp (comp._id)}
              <div class="dashboard__list-item">
                <div class="dashboard__list-main">
                  <div class="dashboard__list-title">{comp.amount} {comp.currency}</div>
                  {#if comp.compensationType}
                    <div class="dashboard__list-sub">{comp.compensationType}</div>
                  {/if}
                </div>
                {#if comp.effectiveDate}
                  <div class="dashboard__list-meta">{formatDate(comp.effectiveDate) ?? ''}</div>
                {/if}
              </div>
            {/each}
          </div>
        {:else}
          <div class="dashboard__empty-state">
            <div class="dashboard__empty-icon">💰</div>
            <div class="dashboard__empty-title"><Label label={hr.string.CompensationHistory} /></div>
            <div class="dashboard__empty-description">
              Salary history, bonuses, and compensation changes will be tracked here.
            </div>
          </div>
        {/if}
      </svelte:fragment>
    </Section>
  </div>
{:else if selectedTab === 'performance'}
  <div class="dashboard__tab-content">
    <Section label={hr.string.PerformanceReviews}>
      <svelte:fragment slot="header">
        {#if isManager}
          <Button
            label={hr.string.AddReview}
            kind={'primary'}
            size={'small'}
            on:click={openAddReview}
            disabled={!staffRecord}
          />
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="content">
        {#if staffRecord && reviews.length > 0}
          <div class="dashboard__list">
            {#each reviews as review (review._id)}
              <div class="dashboard__list-item">
                <div class="dashboard__list-main">
                  <div class="dashboard__list-title">{review.title}</div>
                  <div class="dashboard__list-sub">
                    {#if review.reviewType}
                      {review.reviewType}
                    {/if}
                    {#if review.reviewPeriodStart && review.reviewPeriodEnd}
                      {#if review.reviewType}
                        
                        ·
                      {/if}
                      {formatDate(review.reviewPeriodStart) ?? ''} – {formatDate(review.reviewPeriodEnd) ?? ''}
                    {/if}
                  </div>
                </div>
                <div class="dashboard__list-meta">
                  {#if review.status}{review.status}{/if}
                  {#if review.overallRating != null}
                    · {review.overallRating}/5
                  {/if}
                  {#if review.dueDate}
                    · {formatDate(review.dueDate) ?? ''}
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <div class="dashboard__empty-state">
            <div class="dashboard__empty-icon">📊</div>
            <div class="dashboard__empty-title"><Label label={hr.string.PerformanceReviews} /></div>
            <div class="dashboard__empty-description">
              Annual reviews, mid-year check-ins, and performance feedback will be stored here.
            </div>
          </div>
        {/if}
      </svelte:fragment>
    </Section>
  </div>
{:else if selectedTab === 'benefits'}
  <div class="dashboard__tab-content">
    <Section label={hr.string.EmployeeBenefits}>
      <svelte:fragment slot="header">
        {#if isManager}
          <Button
            label={hr.string.AddBenefit}
            kind={'primary'}
            size={'small'}
            on:click={openAddBenefit}
            disabled={!staffRecord}
          />
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="content">
        {#if staffRecord && benefits.length > 0}
          <div class="dashboard__list">
            {#each benefits as benefit (benefit._id)}
              <div class="dashboard__list-item">
                <div class="dashboard__list-main">
                  <div class="dashboard__list-title">{benefit.benefitName}</div>
                  {#if benefit.provider}
                    <div class="dashboard__list-sub">{benefit.provider}</div>
                  {/if}
                </div>
                <div class="dashboard__list-meta">
                  {#if benefit.status}{benefit.status}{/if}
                  {#if benefit.effectiveDate}
                    · {formatDate(benefit.effectiveDate) ?? ''}
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <div class="dashboard__empty-state">
            <div class="dashboard__empty-icon">🎁</div>
            <div class="dashboard__empty-title"><Label label={hr.string.EmployeeBenefits} /></div>
            <div class="dashboard__empty-description">
              Health insurance, retirement plans, and other benefits enrollment will appear here.
            </div>
          </div>
        {/if}
      </svelte:fragment>
    </Section>
  </div>
{:else if selectedTab === 'lifecycle'}
  <div class="dashboard__tab-content">
    <Section label={hr.string.OnboardingChecklist}>
      <svelte:fragment slot="header">
        {#if isManager}
          <Button
            label={hr.string.AddTask}
            kind={'primary'}
            size={'small'}
            on:click={openAddLifecycleTask}
            disabled={!staffRecord}
          />
        {/if}
      </svelte:fragment>
      <svelte:fragment slot="content">
        {#if staffRecord && lifecycleTasks.length > 0}
          <div class="dashboard__list">
            {#each lifecycleTasks as task (task._id)}
              <div class="dashboard__list-item">
                <div class="dashboard__list-main">
                  <div class="dashboard__list-title">{task.title}</div>
                  {#if task.category}
                    <div class="dashboard__list-sub">{task.category}</div>
                  {/if}
                </div>
                <div class="dashboard__list-meta">
                  {#if task.status}{task.status}{/if}
                  {#if task.dueDate}
                    · {formatDate(task.dueDate) ?? ''}
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <div class="dashboard__empty-state">
            <div class="dashboard__empty-icon">🚀</div>
            <div class="dashboard__empty-title"><Label label={hr.string.OnboardingChecklist} /></div>
            <div class="dashboard__empty-description">
              Onboarding and offboarding tasks will be managed here.
            </div>
          </div>
        {/if}
      </svelte:fragment>
    </Section>
  </div>
{/if}

<style lang="scss">
  .dashboard__tab-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .dashboard__empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-1);
    padding: var(--spacing-4);
    text-align: center;
    color: var(--theme-dark-color);
  }

  .dashboard__empty-icon {
    font-size: 3rem;
    opacity: 0.5;
  }

  .dashboard__empty-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .dashboard__empty-description {
    font-size: 0.875rem;
    max-width: 400px;
  }

  .dashboard__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .dashboard__list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-1) var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-navpanel-divider);
  }

  .dashboard__list-main {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
  }

  .dashboard__list-title {
    font-size: 0.9375rem;
    font-weight: 500;
  }

  .dashboard__list-sub {
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
  }

  .dashboard__list-meta {
    font-size: 0.8125rem;
    color: var(--theme-dark-color);
    white-space: nowrap;
    margin-left: var(--spacing-1);
  }
</style>
