<script lang="ts">
  import core, { Ref } from '@hcengineering/core'
  import { Staff, RequestStatus, timeToTzDate } from '@hcengineering/matrics-hr'
  import { Card, createQuery, getClient } from '@hcengineering/presentation'
  import { DropdownLabels, Label } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import hr from '../../plugin'
  const s = (hr.string as any)

  export let staff: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  type AssignedAsset = {
    _id: string
    assetName: string
    assetTag?: string
    serialNumber?: string
  }

  let assigned: AssignedAsset[] = []
  let selected: string | '' = ''

  const q = createQuery()
  $: q.query((hr.class as any).AssignedAsset, { attachedTo: staff._id as Ref<Staff> }, (res) => {
    assigned = (res as unknown as AssignedAsset[])
  })

  $: options = assigned.map(a => ({ id: a._id, label: a.assetName }))
  $: canSave = selected !== ''

  function buildDescription(): string {
    const a = assigned.find(x => x._id === selected)
    if (!a) return ''
    const extras = [a.assetTag ? `#${a.assetTag}` : '', a.serialNumber ?? ''].filter(Boolean).join(' ')
    return `Return asset: ${a.assetName}${extras ? ' (' + extras + ')' : ''}`
  }

  function buildPayload(): any {
    return { assignedAssetId: selected }
  }

  async function save () {
    if (!canSave) return
    const now = Date.now()
    const payload: any = {
      type: (hr.ids as any).AssetReturn as Ref<any>,
      tzDate: timeToTzDate(now),
      tzDueDate: timeToTzDate(now),
      description: buildDescription(),
      payload: JSON.stringify(buildPayload()),
      department: staff.department,
      status: RequestStatus.Pending,
      submittedDate: now
    }
    await client.addCollection(hr.class.Request, core.space.Workspace, staff._id as Ref<Staff>, staff._class, 'requests', payload)
    dispatch('close')
  }
</script>

<Card
  label={s.RequestAssetReturn}
  okAction={save}
  {canSave}
  on:close={() => dispatch('close')}
  on:changeContent
>
  <div class="flex-col gap-2">
    <DropdownLabels label={hr.string.AssetName} items={options} bind:selected={selected} />
  </div>
  <svelte:fragment slot="error">
    {#if !canSave}
      <Label label={hr.string.UnchangeableType} />
    {/if}
  </svelte:fragment>
</Card>
