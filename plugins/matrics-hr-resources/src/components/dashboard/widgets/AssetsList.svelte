<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Label } from '@hcengineering/ui'
  import hr from '../../../plugin'

  export interface AssignedAsset {
    _id: string
    assetName: string
    assetTag?: string
    serialNumber?: string
    assignedDate?: number
    returnDate?: number
    condition?: string
  }

  export let assignedAssets: AssignedAsset[] = []

  const s = hr.string as any
  const timestampFormatter = new Intl.DateTimeFormat(undefined, {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
</script>

{#if assignedAssets.length > 0}
  <div class="assets-grid">
    {#each assignedAssets as asset (asset._id)}
      <div class="asset-card">
        <div class="asset-header">
          <div class="asset-icon">💻</div>
          <div class="asset-name">{asset.assetName}</div>
        </div>
        
        <div class="asset-badges">
          {#if asset.assetTag}<span class="badge">#{asset.assetTag}</span>{/if}
          {#if asset.serialNumber}<span class="badge">{asset.serialNumber}</span>{/if}
          {#if asset.condition}<span class="badge">{asset.condition}</span>{/if}
        </div>

        <div class="asset-dates">
          {#if asset.assignedDate}
            <div class="date-row">
              <span class="label"><Label label={s.AssignedShort} />:</span>
              <span class="value">{timestampFormatter.format(new Date(asset.assignedDate))}</span>
            </div>
          {/if}
          {#if asset.returnDate}
            <div class="date-row">
              <span class="label"><Label label={s.ReturnShort} />:</span>
              <span class="value">{timestampFormatter.format(new Date(asset.returnDate))}</span>
            </div>
          {/if}
        </div>
      </div>
    {/each}
  </div>
{:else}
  <div class="empty-state">
    <Label label={s.NoAssignedAssets} />
  </div>
{/if}

<style lang="scss">
  .assets-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-2);
  }

  @media (max-width: 1200px) {
    .assets-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .assets-grid {
      grid-template-columns: 1fr;
    }
  }

  .asset-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1_5);
    padding: var(--spacing-2);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-navpanel-divider);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
      transform: translateY(-1px);
    }
  }

  .asset-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
  }

  .asset-icon {
    font-size: 1.25rem;
  }

  .asset-name {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
  }

  .asset-badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-0_5);
  }

  .badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    font-size: 0.75rem;
    border-radius: var(--small-BorderRadius);
    background-color: var(--theme-bg-tertiary);
    color: var(--theme-caption-color);
    font-weight: 500;
    border: 1px solid var(--theme-divider-color);
  }

  .asset-dates {
    margin-top: auto;
    padding-top: var(--spacing-1);
    border-top: 1px solid var(--theme-divider-color);
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .date-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
  }

  .label {
    color: var(--theme-trans-color);
  }

  .value {
    color: var(--theme-caption-color);
    font-weight: 500;
  }

  .empty-state {
    padding: var(--spacing-3);
    text-align: center;
    color: var(--theme-trans-color);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--medium-BorderRadius);
  }
</style>
