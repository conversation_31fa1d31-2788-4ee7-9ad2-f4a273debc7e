<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Label } from '@hcengineering/ui'
  import hr from '../../../plugin'

  export interface UpcomingHoliday {
    id: string
    title: string
    date: Date
    departmentName?: string
  }

  export let holidays: UpcomingHoliday[] = []

  const holidayDateFormatter = new Intl.DateTimeFormat(undefined, {
    month: 'short',
    day: 'numeric',
    weekday: 'short'
  })
</script>

{#if holidays.length > 0}
  <div class="holidays-grid">
    {#each holidays as holiday (holiday.id)}
      <div class="holiday-card">
        <div class="holiday-date-badge">
          <span class="day">{holiday.date.getDate()}</span>
          <span class="month">{holiday.date.toLocaleString('default', { month: 'short' })}</span>
        </div>
        <div class="holiday-info">
          <div class="holiday-title">{holiday.title}</div>
          <div class="holiday-meta">
            <span class="weekday">{holiday.date.toLocaleString('default', { weekday: 'long' })}</span>
            {#if holiday.departmentName}
              <span class="separator">•</span>
              <span class="dept">{holiday.departmentName}</span>
            {/if}
          </div>
        </div>
      </div>
    {/each}
  </div>
{:else}
  <div class="empty-state">
    <Label label={hr.string.NoUpcomingHolidays} />
  </div>
{/if}

<style lang="scss">
  .holidays-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-2);
  }

  @media (max-width: 1024px) {
    .holidays-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .holidays-grid {
      grid-template-columns: 1fr;
    }
  }

  .holiday-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-navpanel-divider);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
      transform: translateY(-1px);
    }
  }

  .holiday-date-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: var(--small-BorderRadius);
    background-color: var(--theme-bg-tertiary);
    border: 1px solid var(--theme-divider-color);
    flex-shrink: 0;

    .day {
      font-size: 1.125rem;
      font-weight: 700;
      color: var(--theme-caption-color);
      line-height: 1;
    }

    .month {
      font-size: 0.6875rem;
      text-transform: uppercase;
      color: var(--theme-trans-color);
      font-weight: 600;
      margin-top: 2px;
    }
  }

  .holiday-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
  }

  .holiday-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .holiday-meta {
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    color: var(--theme-trans-color);
    
    .separator {
      margin: 0 4px;
      opacity: 0.5;
    }
  }

  .empty-state {
    padding: var(--spacing-3);
    text-align: center;
    color: var(--theme-trans-color);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--medium-BorderRadius);
    border: 1px dashed var(--theme-divider-color);
  }
</style>
