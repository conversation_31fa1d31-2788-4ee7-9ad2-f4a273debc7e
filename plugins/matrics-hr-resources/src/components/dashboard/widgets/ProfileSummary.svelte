<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Label, Component } from '@hcengineering/ui'
  import contact from '@hcengineering/contact'
  import { Staff } from '@hcengineering/matrics-hr'
  import hr from '../../../plugin'
  import type { IntlString } from '@hcengineering/platform'

  export let staffRecord: Staff | undefined
  export let employeeName: string | undefined
  export let departmentName: string | undefined
  export let managerName: string | undefined
  export let employmentTypeIntl: IntlString | undefined
  export let workHoursLabel: string | undefined
  export let hireDateLabel: string | undefined
  export let probationEndLabel: string | undefined
  export let terminationDateLabel: string | undefined

  $: profileFields = [
    { label: hr.string.JobTitle, value: staffRecord?.jobTitle },
    { label: hr.string.Department, value: departmentName },
    { label: hr.string.Manager, value: managerName },
    { label: hr.string.EmploymentType, valueIntl: employmentTypeIntl },
    { label: hr.string.Location, value: staffRecord?.location },
    { label: hr.string.WorkHoursPerWeek, value: workHoursLabel },
    {
      label: hr.string.FTE,
      value: staffRecord?.ftePercent != null ? `${staffRecord.ftePercent}%` : undefined
    },
    {
      label: hr.string.CostCenter,
      value: staffRecord?.costCenter
    },
    { label: hr.string.HireDate, value: hireDateLabel },
    { label: hr.string.ProbationEndDate, value: probationEndLabel },
    { label: hr.string.TerminationDate, value: terminationDateLabel }
  ].filter((field) => (field.value !== undefined && field.value !== '') || field.valueIntl !== undefined)

  const s = hr.string as any
  
  $: emergencyFields = [
    { label: s.EmergencyContact, value: staffRecord?.emergencyContact },
    { label: s.EmergencyPhone, value: staffRecord?.emergencyPhone },
    { label: s.EmergencyEmail, value: staffRecord?.emergencyEmail },
    { label: s.EmergencyRelationship, value: staffRecord?.emergencyRelationship }
  ].filter((field) => field.value !== undefined && field.value !== '')
</script>

{#if staffRecord && employeeName}
  <div class="profile-container">
    <div class="profile-header">
      <div class="avatar-large">
        <Component is={contact.component.Avatar} props={{ person: staffRecord, size: 'medium', name: employeeName }} />
      </div>
      <div class="header-info">
        <div class="employee-name">{employeeName}</div>
        {#if staffRecord.jobTitle}
          <div class="job-title">{staffRecord.jobTitle}</div>
        {/if}
      </div>
    </div>

    <div class="fields-grid">
      {#each profileFields as field}
        <div class="field-item">
          <div class="field-label"><Label label={field.label} /></div>
          <div class="field-value">
            {#if field.valueIntl}
              <Label label={field.valueIntl} />
            {:else}
              {field.value}
            {/if}
          </div>
        </div>
      {/each}
    </div>

    {#if emergencyFields.length > 0}
      <div class="emergency-section">
        <div class="section-title"><Label label={s.EmergencyInfo} /></div>
        <div class="fields-grid emergency">
          {#each emergencyFields as field}
            <div class="field-item">
              <div class="field-label"><Label label={field.label} /></div>
              <div class="field-value">{field.value}</div>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  </div>
{:else}
  <div class="empty-state">
    <Label label={s.NoProfileData} />
  </div>
{/if}

<style lang="scss">
  .profile-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .profile-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .header-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .employee-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .job-title {
    font-size: 1rem;
    color: var(--theme-trans-color);
  }

  .fields-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-1_5);

    &.emergency {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 1200px) {
    .fields-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 1024px) {
    .fields-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .fields-grid {
      grid-template-columns: 1fr;
    }
  }

  .field-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-navpanel-divider);
  }

  .field-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--theme-trans-color);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .field-value {
    font-size: 0.9375rem;
    font-weight: 500;
    color: var(--theme-caption-color);
  }

  .emergency-section {
    margin-top: var(--spacing-1);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .empty-state {
    padding: var(--spacing-3);
    text-align: center;
    color: var(--theme-trans-color);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--medium-BorderRadius);
  }
</style>
