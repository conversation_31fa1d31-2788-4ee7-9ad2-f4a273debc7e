<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Label } from '@hcengineering/ui'
  import { Staff } from '@hcengineering/matrics-hr'
  import hr from '../../../plugin'
  import type { IntlString } from '@hcengineering/platform'

  export let staffRecord: Staff | undefined
  export let departmentName: string | undefined
  export let managerName: string | undefined
  export let employmentTypeLabels: Record<string, IntlString>

  function formatDateCompact(timestamp: number): string {
    return new Intl.DateTimeFormat(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(timestamp))
  }

  $: jobFields = [
    { label: hr.string.JobTitle, value: staffRecord?.jobTitle ?? '—' },
    { label: hr.string.Department, value: departmentName ?? '—' },
    { label: hr.string.Manager, value: managerName ?? '—' },
    { label: hr.string.Location, value: staffRecord?.location ?? '—' },
    {
      label: hr.string.EmploymentType,
      valueIntl: staffRecord?.employmentType ? employmentTypeLabels[staffRecord.employmentType as string] : undefined,
      value: staffRecord?.employmentType ? undefined : '—'
    },
    {
      label: hr.string.WorkHoursPerWeek,
      value: staffRecord?.workHoursPerWeek ? `${staffRecord.workHoursPerWeek} hours/week` : '—'
    },
    {
      label: hr.string.FTE,
      value: staffRecord?.ftePercent != null ? `${staffRecord.ftePercent}%` : '—'
    },
    {
      label: hr.string.CostCenter,
      value: staffRecord?.costCenter ?? '—'
    }
  ]

  $: dateFields = [
    {
      label: hr.string.HireDate,
      value: staffRecord?.hireDate ? formatDateCompact(staffRecord.hireDate) : '—'
    },
    {
      label: hr.string.ProbationEndDate,
      value: staffRecord?.probationEndDate ? formatDateCompact(staffRecord.probationEndDate) : '—'
    },
    {
      label: hr.string.TerminationDate,
      value: staffRecord?.terminationDate ? formatDateCompact(staffRecord.terminationDate) : '—'
    }
  ]
</script>

<div class="job-container">
  <div class="section">
    <div class="section-title"><Label label={hr.string.EmploymentInformation} /></div>
    <div class="grid">
      {#each jobFields as field}
        <div class="field-card">
          <div class="label"><Label label={field.label} /></div>
          <div class="value">
            {#if field.valueIntl}
              <Label label={field.valueIntl} />
            {:else}
              {field.value}
            {/if}
          </div>
        </div>
      {/each}
    </div>
  </div>

  <div class="section">
    <div class="section-title"><Label label={hr.string.EmploymentDates} /></div>
    <div class="grid dates">
      {#each dateFields as field}
        <div class="field-card">
          <div class="label"><Label label={field.label} /></div>
          <div class="value">{field.value}</div>
        </div>
      {/each}
    </div>
  </div>
</div>

<style lang="scss">
  .job-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-2);

    &.dates {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 1024px) {
    .grid {
      grid-template-columns: repeat(2, 1fr);
      
      &.dates {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  @media (max-width: 640px) {
    .grid {
      grid-template-columns: 1fr;
      
      &.dates {
        grid-template-columns: 1fr;
      }
    }
  }

  .field-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
    padding: var(--spacing-1_5);
    background: var(--theme-bg-secondary);
    border-radius: var(--small-BorderRadius);
    border: 1px solid var(--theme-divider-color);
  }

  .label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--theme-trans-color);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .value {
    font-size: 0.9375rem;
    font-weight: 500;
    color: var(--theme-caption-color);
  }
</style>
