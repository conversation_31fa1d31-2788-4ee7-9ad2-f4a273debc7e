<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Label } from '@hcengineering/ui'
  import hr from '../../../plugin'

  export let availableDays: number = 0
  export let usedDays: number = 0
  export let pendingDays: number = 0
  export let remainingDays: number = 0
  export let hasDeficit: boolean = false

  const daysFormatter = new Intl.NumberFormat(undefined, {
    maximumFractionDigits: 1,
    minimumFractionDigits: 0
  })

  $: cards = [
    { id: 'available', label: hr.string.AvailableDays, value: availableDays },
    { id: 'used', label: hr.string.TimeOffUsed, value: usedDays },
    { id: 'pending', label: hr.string.TimeOffPending, value: pendingDays },
    { id: 'remaining', label: hr.string.TimeOffRemaining, value: remainingDays }
  ]
</script>

<div class="stats-grid">
  {#each cards as card (card.id)}
    <div class="stat-card">
      <div class="stat-label">
        <Label label={card.label} />
      </div>
      <div class="stat-value">{daysFormatter.format(card.value)}</div>
    </div>
  {/each}
</div>

{#if hasDeficit}
  <div class="warning">
    <Label label={hr.string.InsufficientBalance} />
  </div>
{/if}

<style lang="scss">
  .stats-grid {
    display: grid;
    gap: var(--spacing-2);
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: 1200px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 640px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }

  .stat-card {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-navpanel-divider);
    background-color: var(--theme-bg-secondary);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--theme-list-divider-color);
      box-shadow: var(--button-shadow);
      transform: translateY(-1px);
    }
  }

  .stat-label {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--theme-caption-color);
    line-height: 1;
  }

  .warning {
    margin-top: var(--spacing-2);
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    background-color: rgba(229, 160, 45, 0.1);
    border: 1px solid rgba(229, 160, 45, 0.2);
    color: var(--theme-warning-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
