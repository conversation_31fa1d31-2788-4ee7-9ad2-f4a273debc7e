<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Label, Component } from '@hcengineering/ui'
  import { AttendanceRecord, Staff } from '@hcengineering/matrics-hr'
  import contact, { getName } from '@hcengineering/contact'
  import { getClient } from '@hcengineering/presentation'
  import hr from '../../../plugin'

  export let activeAttendance: AttendanceRecord[] = []
  export let activeStaffById: Map<string, Staff> = new Map()

  const client = getClient()
  const timestampFormatter = new Intl.DateTimeFormat(undefined, {
    hour: 'numeric',
    minute: 'numeric'
  })

  function getStaffName(staffRef: any): string {
    const staff = activeStaffById.get(String(staffRef))
    if (staff == null) return 'Unknown'
    return getName(client.getHierarchy(), staff as any)
  }

  function getStaffRecord(staffRef: any): Staff | undefined {
    return activeStaffById.get(String(staffRef))
  }
</script>

{#if activeAttendance.length > 0}
  <div class="team-list">
    {#each activeAttendance as rec (rec._id)}
      <div class="team-member-row">
        <div class="member-info">
          <div class="avatar-wrapper">
            <Component 
              is={contact.component.Avatar} 
              props={{ 
                person: getStaffRecord(rec.staff), 
                size: 'small', 
                name: getStaffName(rec.staff) 
              }} 
            />
            <div class="online-dot"></div>
          </div>
          <div class="member-name">{getStaffName(rec.staff)}</div>
        </div>
        <div class="clock-time">
          {#if rec.clockIn}
            {timestampFormatter.format(new Date(rec.clockIn))}
          {/if}
        </div>
      </div>
    {/each}
  </div>
{:else}
  <div class="empty-state">
    <Label label={hr.string.NotInOffice} />
  </div>
{/if}

<style lang="scss">
  .team-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .team-member-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-1_5);
    border-radius: var(--small-BorderRadius);
    background-color: var(--theme-bg-secondary);
    border: 1px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--theme-bg-tertiary);
      border-color: var(--theme-divider-color);
    }
  }

  .member-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-1_5);
  }

  .avatar-wrapper {
    position: relative;
  }

  .online-dot {
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--theme-success-color);
    border: 1.5px solid var(--theme-bg-secondary);
  }

  .member-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--theme-caption-color);
  }

  .clock-time {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
    font-variant-numeric: tabular-nums;
  }

  .empty-state {
    padding: var(--spacing-2);
    text-align: center;
    color: var(--theme-trans-color);
    background-color: var(--theme-bg-secondary);
    border-radius: var(--medium-BorderRadius);
    font-size: 0.875rem;
  }
</style>
