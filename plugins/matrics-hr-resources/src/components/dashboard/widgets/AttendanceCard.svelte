<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Button, Label } from '@hcengineering/ui'
  import { AttendanceRecord } from '@hcengineering/matrics-hr'
  import hr from '../../../plugin'
  import { createEventDispatcher } from 'svelte'

  export let openAttendance: AttendanceRecord | undefined

  const dispatch = createEventDispatcher()
  const timestampFormatter = new Intl.DateTimeFormat(undefined, {
    hour: 'numeric',
    minute: 'numeric'
  })
</script>

<div class="attendance-card">
  <div class="status-section">
    <div class="status-indicator" class:active={!!openAttendance}></div>
    <div class="status-text">
      {#if openAttendance}
        <div class="status-title"><Label label={hr.string.CurrentlyInOffice} /></div>
        {#if openAttendance.clockIn}
          <div class="status-subtitle">
            Clocked in at {timestampFormatter.format(new Date(openAttendance.clockIn))}
          </div>
        {/if}
      {:else}
        <div class="status-title"><Label label={hr.string.NotInOffice} /></div>
        <div class="status-subtitle">Ready to start your day?</div>
      {/if}
    </div>
  </div>

  <div class="action-section">
    {#if openAttendance}
      <Button
        label={hr.string.ClockOut}
        kind={'secondary'}
        size={'medium'}
        on:click={() => dispatch('clockOut')}
      />
    {:else}
      <Button
        label={hr.string.ClockIn}
        kind={'primary'}
        size={'medium'}
        on:click={() => dispatch('clockIn')}
      />
    {/if}
  </div>
</div>

<style lang="scss">
  .attendance-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2);
    border-radius: var(--medium-BorderRadius);
    background-color: var(--theme-bg-secondary);
    border: 1px solid var(--theme-navpanel-divider);
  }

  .status-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--theme-trans-color);
    
    &.active {
      background-color: var(--theme-success-color);
      box-shadow: 0 0 0 4px rgba(var(--theme-success-color-rgb), 0.15);
    }
  }

  .status-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .status-title {
    font-weight: 600;
    font-size: 0.9375rem;
    color: var(--theme-caption-color);
  }

  .status-subtitle {
    font-size: 0.8125rem;
    color: var(--theme-trans-color);
  }

  .action-section {
    flex-shrink: 0;
  }
</style>
