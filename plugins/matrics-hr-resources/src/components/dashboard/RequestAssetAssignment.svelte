<script lang="ts">
  import core, { Ref } from '@hcengineering/core'
  import { Staff, RequestStatus, timeToTzDate } from '@hcengineering/matrics-hr'
  import { Card, createQuery, getClient } from '@hcengineering/presentation'
  import { EditBox, Label } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import hr from '../../plugin'
  const s = (hr.string as any)

  export let staff: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let assetName = ''
  let assetTag = ''
  let serialNumber = ''
  let condition = ''

  $: canSave = assetName.trim().length > 0

  function buildDescription(): string {
    const lines: string[] = []
    if (assetName.trim()) lines.push(`AssetName: ${assetName}`)
    if (assetTag.trim()) lines.push(`AssetTag: ${assetTag}`)
    if (serialNumber.trim()) lines.push(`SerialNumber: ${serialNumber}`)
    if (condition.trim()) lines.push(`Condition: ${condition}`)
    return lines.join('\n')
  }

  function buildPayload(): any {
    return {
      assetName,
      assetTag,
      serialNumber,
      condition
    }
  }

  async function save () {
    if (!canSave) return
    const now = Date.now()
    const payload: any = {
      type: (hr.ids as any).AssetAssignment as Ref<any>,
      tzDate: timeToTzDate(now),
      tzDueDate: timeToTzDate(now),
      description: buildDescription(),
      payload: JSON.stringify(buildPayload()),
      department: staff.department,
      status: RequestStatus.Pending,
      submittedDate: now
    }
    await client.addCollection(hr.class.Request, core.space.Workspace, staff._id as Ref<Staff>, staff._class, 'requests', payload)
    dispatch('close')
  }
</script>

<Card
  label={s.RequestAssetAssignment}
  okAction={save}
  {canSave}
  on:close={() => dispatch('close')}
  on:changeContent
>
  <div class="flex-col gap-2">
    <EditBox label={hr.string.AssetName} bind:value={assetName} />
    <EditBox label={hr.string.AssetTag} bind:value={assetTag} />
    <EditBox label={hr.string.SerialNumber} bind:value={serialNumber} />
    <EditBox label={hr.string.Condition} bind:value={condition} />
  </div>
  <svelte:fragment slot="error">
    {#if !canSave}
      <Label label={hr.string.UnchangeableType} />
    {/if}
  </svelte:fragment>
</Card>
