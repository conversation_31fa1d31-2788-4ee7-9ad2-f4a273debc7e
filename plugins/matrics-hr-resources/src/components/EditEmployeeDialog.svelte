<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import type { Employee } from '@hcengineering/contact'
  import { Department, Staff, EmploymentType } from '@hcengineering/matrics-hr'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import {
    Button,
    ModernDialog,
    Label,
    EditBox,
    DropdownLabelsIntl
  } from '@hcengineering/ui'
  import { DateEditor } from '@hcengineering/view-resources'
  import hr from '../plugin'
  import contact, { getName } from '@hcengineering/contact'

  export let employee: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()
  const deptQuery = createQuery()
  const staffQuery = createQuery()

  let departments: Department[] = []
  let allStaff: Staff[] = []
  let jobTitle = employee.jobTitle ?? ''
  let employmentType: string | undefined = employee.employmentType as any
  let location = employee.location ?? ''
  let workHoursPerWeek: number | undefined = employee.workHoursPerWeek ?? undefined
  let ftePercent: number | undefined = employee.ftePercent ?? undefined
  let costCenter = employee.costCenter ?? ''
  let hireDate = employee.hireDate ?? null
  let terminationDate = employee.terminationDate ?? null
  let probationEndDate = employee.probationEndDate ?? null
  let selectedDepartment = employee.department
  let manager: Ref<Employee> | null = (employee.manager as Ref<Employee> | null) ?? null

  deptQuery.query(hr.class.Department, {}, (res) => {
    departments = res
  })

  staffQuery.query(hr.mixin.Staff, {}, (res) => {
    allStaff = res
  })

  $: managerOptions = allStaff
    .filter((s) => s._id !== employee._id)
    .map((s) => ({
      id: s._id,
      label: getName(client.getHierarchy(), s as any) as any
    }))

  const employmentTypes = [
    { id: EmploymentType.FullTime, label: hr.string.EmploymentTypeFullTime },
    { id: EmploymentType.PartTime, label: hr.string.EmploymentTypePartTime },
    { id: EmploymentType.Contractor, label: hr.string.EmploymentTypeContractor },
    { id: EmploymentType.Intern, label: hr.string.EmploymentTypeIntern },
    { id: EmploymentType.Temporary, label: hr.string.EmploymentTypeTemporary }
  ]

  async function save (): Promise<void> {
    await client.updateMixin(
      employee._id,
      employee._class,
      employee.space,
      hr.mixin.Staff,
      {
        jobTitle: jobTitle || null,
        employmentType: employmentType as any,
        location: location || null,
        workHoursPerWeek: workHoursPerWeek,
        ftePercent: ftePercent,
        costCenter: costCenter || null,
        hireDate: hireDate,
        terminationDate: terminationDate,
        probationEndDate: probationEndDate,
        department: selectedDepartment,
        manager: manager
      }
    )
    dispatch('close', true)
  }

  function cancel (): void {
    dispatch('close', false)
  }
</script>

<ModernDialog
  label={hr.string.EditEmployee}
  canSubmit={true}
  on:submit={save}
  on:close={() => dispatch('close')}
>
  <div class="form">
    <div class="form-row">
      <Label label={hr.string.JobTitle} />
      <EditBox bind:value={jobTitle} placeholder={hr.string.JobTitle} />
    </div>

    <div class="form-row">
      <Label label={hr.string.Department} />
      <DropdownLabelsIntl
        items={departments.map((d) => ({ id: d._id, label: d.name }))}
        selected={selectedDepartment}
        on:selected={(e) => {
          selectedDepartment = e.detail
        }}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.Manager} />
      <DropdownLabelsIntl
        items={managerOptions}
        selected={manager}
        on:selected={(e) => {
          manager = e.detail
        }}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.EmploymentType} />
      <DropdownLabelsIntl
        items={employmentTypes}
        selected={employmentType}
        on:selected={(e) => {
          employmentType = e.detail
        }}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.Location} />
      <EditBox bind:value={location} placeholder={hr.string.Location} />
    </div>

    <div class="form-row">
      <Label label={hr.string.WorkHoursPerWeek} />
      <EditBox
        bind:value={workHoursPerWeek}
        placeholder={'40'}
        format={'number'}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.FTE} />
      <EditBox
        bind:value={ftePercent}
        placeholder={'100'}
        format={'number'}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.CostCenter} />
      <EditBox bind:value={costCenter} placeholder={hr.string.CostCenter} />
    </div>

    <div class="form-row">
      <Label label={hr.string.HireDate} />
      <DateEditor
        value={hireDate}
        type={undefined}
        onChange={(v) => (hireDate = v)}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.ProbationEndDate} />
      <DateEditor
        value={probationEndDate}
        type={undefined}
        onChange={(v) => (probationEndDate = v)}
      />
    </div>

    <div class="form-row">
      <Label label={hr.string.TerminationDate} />
      <DateEditor
        value={terminationDate}
        type={undefined}
        onChange={(v) => (terminationDate = v)}
      />
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }

  .form-row {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 1rem;
    align-items: center;
  }
</style>
