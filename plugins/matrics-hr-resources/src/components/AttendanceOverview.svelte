<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import contact, { getCurrentEmployee, getName } from '@hcengineering/contact'
  import { Department, Office, Staff, type AttendanceRecord } from '@hcengineering/matrics-hr'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import { Section, Label, DropdownLabelsIntl, Scroller, Component, TabList, showPopup, type DropdownIntlItem } from '@hcengineering/ui'

  import hr from '../plugin'
  import EmployeeAttendanceDetail from './EmployeeAttendanceDetail.svelte'
  import AttendanceVisual from './AttendanceVisual.svelte'

  const client = getClient()
  const attendanceQuery = createQuery()
  const staffQuery = createQuery()
  const officesQuery = createQuery()
  const departmentsQuery = createQuery()

  const me = getCurrentEmployee()

  let attendance: AttendanceRecord[] = []
  let myOpenAttendance: AttendanceRecord | undefined
  let staffById: Map<string, Staff> = new Map()
  let offices: Office[] = []
  let departments: Department[] = []
  let isManager = false

  let currentTab: 'live' | 'visual' | 'today' | 'week' | 'history' | 'insights' = 'live'
  let todayRecords: AttendanceRecord[] = []
  let weekRecords: AttendanceRecord[] = []
  let allHistoricalRecords: AttendanceRecord[] = []
  let selectedEmployeeId: string | undefined = undefined

  const todayQuery = createQuery()
  const weekQuery = createQuery()
  const historyQuery = createQuery()

  const ALL_OFFICES_ID = 'all-offices'
  const ALL_DEPARTMENTS_ID = 'all-departments'

  let officeOptions: DropdownIntlItem[] = []
  let departmentOptions: DropdownIntlItem[] = []
  let selectedOfficeId: string | number = ALL_OFFICES_ID
  let selectedDepartmentId: string | number = ALL_DEPARTMENTS_ID

  officesQuery.query(hr.class.Office, {}, (res) => {
    offices = res as Office[]
  })

  departmentsQuery.query(hr.class.Department, {}, (res) => {
    departments = res
  })

  // Query today's records (all with clockIn today)
  $: {
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)
    todayQuery.query(
      hr.class.AttendanceRecord,
      { clockIn: { $gte: todayStart.getTime() } },
      (res) => {
        todayRecords = res as AttendanceRecord[]
      }
    )
  }

  // Query this week's records
  $: {
    const now = new Date()
    const weekStart = new Date(now)
    weekStart.setDate(now.getDate() - now.getDay())
    weekStart.setHours(0, 0, 0, 0)
    weekQuery.query(
      hr.class.AttendanceRecord,
      { clockIn: { $gte: weekStart.getTime() } },
      (res) => {
        weekRecords = res as AttendanceRecord[]
      }
    )
  }

  // Query all historical records (last 30 days for now)
  $: {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    thirtyDaysAgo.setHours(0, 0, 0, 0)
    historyQuery.query(
      hr.class.AttendanceRecord,
      { clockIn: { $gte: thirtyDaysAgo.getTime() } },
      (res) => {
        allHistoricalRecords = res as AttendanceRecord[]
      }
    )
  }

  function refreshAttendance (): void {
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0)
    const filter: any = {
      clockOut: { $exists: false },
      clockIn: { $gte: todayStart.getTime() }
    }

    if (selectedOfficeId !== ALL_OFFICES_ID) {
      const office = offices.find((o) => String(o._id) === String(selectedOfficeId))
      if (office) filter.office = office._id
    }

    if (selectedDepartmentId !== ALL_DEPARTMENTS_ID) {
      const dept = departments.find((d) => String(d._id) === String(selectedDepartmentId))
      if (dept) filter.department = dept._id
    }
    attendanceQuery.query(
      hr.class.AttendanceRecord,
      filter,
      (res) => {
        attendance = res as AttendanceRecord[]
      }
    )
  }

  $: refreshAttendance()

  // Load all staff who have any attendance records (live, today, week, history)
  $: {
    const allStaffRefs = new Set<string>()
    
    // Collect all unique staff references
    for (const rec of attendance) allStaffRefs.add(String(rec.staff))
    for (const rec of todayRecords) allStaffRefs.add(String(rec.staff))
    for (const rec of weekRecords) allStaffRefs.add(String(rec.staff))
    for (const rec of allHistoricalRecords) allStaffRefs.add(String(rec.staff))

    if (allStaffRefs.size > 0) {
      const refs = Array.from(allStaffRefs)
      staffQuery.query(
        hr.mixin.Staff,
        { _id: { $in: refs } } as any,
        (res) => {
          staffById = new Map((res as Staff[]).map((s) => [String(s._id), s]))
        }
      )
    } else {
      staffById = new Map()
    }
  }

  // Determine if current user is a manager of any department
  $: if (me !== undefined && departments.length > 0) {
    isManager = departments.some(
      (d) => d.teamLead === me || (d.managers as any)?.includes?.(me)
    )
  } else {
    isManager = false
  }

  // Derive current user's open attendance from overall list
  $: myOpenAttendance = me !== undefined
    ? attendance.find((rec) => String(rec.staff) === String(me))
    : undefined

  $: officeOptions = [
    { id: ALL_OFFICES_ID, label: hr.string.All },
    ...offices.map((o) => ({ id: String(o._id), label: o.name as any }))
  ]

  $: departmentOptions = [
    { id: ALL_DEPARTMENTS_ID, label: hr.string.All },
    ...departments.map((d) => ({ id: String(d._id), label: d.name as any }))
  ]

  function getStaffName (ref: Ref<Staff> | string): string {
    const staff = staffById.get(String(ref))
    if (!staff) return 'Unknown'
    return getName(client.getHierarchy(), staff as any)
  }

  function getOfficeName (officeRef?: Ref<Office>): string {
    if (!officeRef) return '—'
    const office = offices.find((o) => o._id === officeRef)
    return office?.name ?? '—'
  }

  function getDepartmentName (deptRef?: Ref<Department>): string {
    if (!deptRef) return '—'
    const dept = departments.find((d) => d._id === deptRef)
    return dept?.name ?? '—'
  }

  const timeFormatter = new Intl.DateTimeFormat(undefined, {
    hour: '2-digit',
    minute: '2-digit'
  })

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })

  const HOUR_MS = 1000 * 60 * 60
  const DAILY_BREAK_HOURS = 1

  function formatClockIn (timestamp: number): string {
    const date = new Date(timestamp)
    return `${dateFormatter.format(date)} ${timeFormatter.format(date)}`
  }

  function formatTime (timestamp: number): string {
    return timeFormatter.format(new Date(timestamp))
  }

  function formatDate (timestamp: number): string {
    return dateFormatter.format(new Date(timestamp))
  }

  function calculateDuration (clockIn: number, clockOut?: number): string {
    if (!clockOut) return 'In progress'
    const duration = clockOut - clockIn
    const hours = Math.floor(duration / HOUR_MS)
    const minutes = Math.floor((duration % HOUR_MS) / (1000 * 60))
    return `${hours}h ${minutes}m`
  }

  // Weekly summary per person
  type WeeklySummary = {
    staffId: string
    staffName: string
    daysPresent: number
    totalHours: number
    records: AttendanceRecord[]
  }

  $: weeklySummaries = (() => {
    const summaryMap = new Map<string, WeeklySummary>()
    for (const rec of weekRecords) {
      const staffId = String(rec.staff)
      if (!summaryMap.has(staffId)) {
        summaryMap.set(staffId, {
          staffId,
          staffName: getStaffName(rec.staff),
          daysPresent: 0,
          totalHours: 0,
          records: []
        })
      }
      const summary = summaryMap.get(staffId)!
      summary.records.push(rec)
      if (rec.clockOut) {
        const duration = rec.clockOut - rec.clockIn
        summary.totalHours += duration / HOUR_MS
      }
    }

    // Count unique days
    for (const summary of summaryMap.values()) {
      const uniqueDays = new Set(
        summary.records.map((r) => new Date(r.clockIn).toDateString())
      )
      summary.daysPresent = uniqueDays.size

      if (summary.daysPresent > 0 && summary.totalHours > 0) {
        summary.totalHours = Math.max(
          0,
          summary.totalHours - summary.daysPresent * DAILY_BREAK_HOURS
        )
      }
    }

    return Array.from(summaryMap.values()).sort((a, b) => b.totalHours - a.totalHours)
  })()

  const tabs = [
    { id: 'live', label: 'Live' },
    { id: 'visual', label: 'Visual' },
    { id: 'today', label: 'Today' },
    { id: 'week', label: 'This Week' },
    { id: 'history', label: 'History' },
    { id: 'insights', label: 'Insights' }
  ]

  // Manager insights: late arrivals, early departures, patterns
  const STANDARD_START_HOUR = 9 // 9 AM
  const STANDARD_END_HOUR = 17 // 5 PM
  const LATE_THRESHOLD_MINUTES = 15

  type EmployeeInsight = {
    staffId: string
    staffName: string
    daysWorked: number
    avgStartTime: string
    avgEndTime: string
    avgHours: number
    lateArrivals: number
    earlyDepartures: number
    workingNow: boolean
    lastSeen: number
  }

  type DistributionBucket = {
    label: string
    count: number
    percent: number
  }

  type PunctualityBucket = DistributionBucket
  type OvertimeBucket = DistributionBucket
  type ArrivalSlot = DistributionBucket

  type WeeklyHoursByDay = {
    label: string
    dayIndex: number
    totalHours: number
    barPercent: number
  }

  $: employeeInsights = (() => {
    if (!isManager) return []
    const insightMap = new Map<string, EmployeeInsight>()

    for (const rec of weekRecords) {
      const staffId = String(rec.staff)
      if (!insightMap.has(staffId)) {
        insightMap.set(staffId, {
          staffId,
          staffName: getStaffName(rec.staff),
          daysWorked: 0,
          avgStartTime: '',
          avgEndTime: '',
          avgHours: 0,
          lateArrivals: 0,
          earlyDepartures: 0,
          workingNow: false,
          lastSeen: 0
        })
      }
      const insight = insightMap.get(staffId)!

      // Check if late
      const clockInDate = new Date(rec.clockIn)
      const clockInHour = clockInDate.getHours()
      const clockInMinute = clockInDate.getMinutes()
      const minutesSinceStandardStart = (clockInHour - STANDARD_START_HOUR) * 60 + clockInMinute
      if (minutesSinceStandardStart > LATE_THRESHOLD_MINUTES) {
        insight.lateArrivals++
      }

      // Check if early departure
      if (rec.clockOut) {
        const clockOutDate = new Date(rec.clockOut)
        const clockOutHour = clockOutDate.getHours()
        if (clockOutHour < STANDARD_END_HOUR) {
          insight.earlyDepartures++
        }
      }

      // Track last seen
      if (rec.clockIn > insight.lastSeen) {
        insight.lastSeen = rec.clockIn
      }
      if (rec.clockOut && rec.clockOut > insight.lastSeen) {
        insight.lastSeen = rec.clockOut
      }
    }

    // Calculate averages
    for (const insight of insightMap.values()) {
      const records = weekRecords.filter((r) => String(r.staff) === insight.staffId)
      const uniqueDays = new Set(records.map((r) => new Date(r.clockIn).toDateString()))
      insight.daysWorked = uniqueDays.size

      // Average start time
      const startTimes = records.map((r) => new Date(r.clockIn))
      const avgStartMinutes =
        startTimes.reduce((sum, d) => sum + d.getHours() * 60 + d.getMinutes(), 0) / startTimes.length
      const avgStartHour = Math.floor(avgStartMinutes / 60)
      const avgStartMin = Math.floor(avgStartMinutes % 60)
      insight.avgStartTime = `${avgStartHour.toString().padStart(2, '0')}:${avgStartMin.toString().padStart(2, '0')}`

      // Average end time
      const completedRecords = records.filter((r) => r.clockOut)
      if (completedRecords.length > 0) {
        const endTimes = completedRecords.map((r) => new Date(r.clockOut!))
        const avgEndMinutes =
          endTimes.reduce((sum, d) => sum + d.getHours() * 60 + d.getMinutes(), 0) / endTimes.length
        const avgEndHour = Math.floor(avgEndMinutes / 60)
        const avgEndMin = Math.floor(avgEndMinutes % 60)
        insight.avgEndTime = `${avgEndHour.toString().padStart(2, '0')}:${avgEndMin.toString().padStart(2, '0')}`

        // Average hours
        const totalHoursRaw = completedRecords.reduce(
          (sum, r) => sum + (r.clockOut! - r.clockIn) / HOUR_MS,
          0
        )
        const daysForAvg = Math.max(1, insight.daysWorked || completedRecords.length)
        const totalHours = Math.max(
          0,
          totalHoursRaw - daysForAvg * DAILY_BREAK_HOURS
        )
        insight.avgHours = totalHours / daysForAvg
      } else {
        insight.avgEndTime = '—'
        insight.avgHours = 0
      }

      // Check if working now
      insight.workingNow = attendance.some((r) => String(r.staff) === insight.staffId)
    }

    return Array.from(insightMap.values()).sort((a, b) => b.lateArrivals - a.lateArrivals)
  })()

  $: punctualityBuckets = (() => {
    if (!isManager || weekRecords.length === 0) return []

    const counts: {
      veryEarly: number
      early: number
      onTime: number
      late: number
      veryLate: number
    } = {
      veryEarly: 0,
      early: 0,
      onTime: 0,
      late: 0,
      veryLate: 0
    }

    for (const rec of weekRecords) {
      const d = new Date(rec.clockIn)
      const minutes = d.getHours() * 60 + d.getMinutes()
      const stdMinutes = STANDARD_START_HOUR * 60
      const diff = minutes - stdMinutes

      if (diff <= -30) counts.veryEarly++
      else if (diff <= 0) counts.early++
      else if (diff <= LATE_THRESHOLD_MINUTES) counts.onTime++
      else if (diff <= 60) counts.late++
      else counts.veryLate++
    }

    const total = weekRecords.length || 1
    const defs: { key: keyof typeof counts; label: string }[] = [
      { key: 'veryEarly', label: 'Very early (< -30m)' },
      { key: 'early', label: 'Early (-30 to 0m)' },
      { key: 'onTime', label: 'On time (0-15m)' },
      { key: 'late', label: 'Late (15-60m)' },
      { key: 'veryLate', label: 'Very late (>60m)' }
    ]

    return defs
      .map((def) => ({
        label: def.label,
        count: counts[def.key],
        percent: (counts[def.key] / total) * 100
      }))
      .filter((bucket) => bucket.count > 0)
  })()

  $: arrivalSlots = (() => {
    if (!isManager || weekRecords.length === 0) return []

    const slotDefs: { label: string; min: number; max: number }[] = [
      { label: 'Before 8:00', min: 0, max: 8 * 60 },
      { label: '08:00–09:00', min: 8 * 60, max: 9 * 60 },
      { label: '09:00–10:00', min: 9 * 60, max: 10 * 60 },
      { label: '10:00–12:00', min: 10 * 60, max: 12 * 60 },
      { label: 'After 12:00', min: 12 * 60, max: 24 * 60 }
    ]

    const counts = new Array(slotDefs.length).fill(0)

    for (const rec of weekRecords) {
      const d = new Date(rec.clockIn)
      const minutes = d.getHours() * 60 + d.getMinutes()
      const idx = slotDefs.findIndex((s) => minutes >= s.min && minutes < s.max)
      if (idx >= 0) counts[idx]++
    }

    const total = counts.reduce((sum, v) => sum + v, 0) || 1

    return slotDefs
      .map((slot, i) => ({
        label: slot.label,
        count: counts[i],
        percent: (counts[i] / total) * 100
      }))
      .filter((slot) => slot.count > 0)
  })()

  $: overtimeBuckets = (() => {
    if (!isManager || weekRecords.length === 0) return []

    const totalsByDay = new Map<string, number>()
    for (const rec of weekRecords) {
      if (!rec.clockOut) continue
      const dayKey = new Date(rec.clockIn).toDateString()
      const hours = (rec.clockOut - rec.clockIn) / HOUR_MS
      totalsByDay.set(dayKey, (totalsByDay.get(dayKey) ?? 0) + hours)
    }

    if (totalsByDay.size === 0) return []

    const counts: {
      short: number
      normal: number
      long: number
      veryLong: number
    } = {
      short: 0,
      normal: 0,
      long: 0,
      veryLong: 0
    }

    for (const hours of totalsByDay.values()) {
      if (hours < 6) counts.short++
      else if (hours < 8) counts.normal++
      else if (hours < 10) counts.long++
      else counts.veryLong++
    }

    const totalDays = totalsByDay.size || 1
    const defs: { key: keyof typeof counts; label: string }[] = [
      { key: 'short', label: '< 6h' },
      { key: 'normal', label: '6–8h' },
      { key: 'long', label: '8–10h' },
      { key: 'veryLong', label: '> 10h' }
    ]

    return defs
      .map((def) => ({
        label: def.label,
        count: counts[def.key],
        percent: (counts[def.key] / totalDays) * 100
      }))
      .filter((bucket) => bucket.count > 0)
  })()

  $: weeklyHoursByDay = (() => {
    if (!isManager) return []
    const labels = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    const totals = new Array(7).fill(0)
    const staffPerDay: Array<Set<string>> = new Array(7).fill(null).map(() => new Set<string>())

    for (const rec of weekRecords) {
      if (!rec.clockOut) continue
      const day = new Date(rec.clockIn).getDay()
      totals[day] += (rec.clockOut - rec.clockIn) / HOUR_MS
      staffPerDay[day].add(String(rec.staff))
    }

    for (let i = 0; i < 7; i++) {
      const staffCount = staffPerDay[i].size
      if (staffCount > 0 && totals[i] > 0) {
        totals[i] = Math.max(0, totals[i] - staffCount * DAILY_BREAK_HOURS)
      }
    }

    const maxTotal = Math.max(...totals, 0.1)

    return labels.map((label, i) => ({
      label,
      dayIndex: i,
      totalHours: totals[i],
      barPercent: totals[i] > 0 ? (totals[i] / maxTotal) * 100 : 0
    }))
  })()

  // Group today's records by employee
  type TodayEmployeeSummary = {
    staffId: string
    staffName: string
    records: AttendanceRecord[]
    totalHours: number
    clockedInNow: boolean
  }

  $: todayByEmployee = (() => {
    const map = new Map<string, TodayEmployeeSummary>()
    for (const rec of todayRecords) {
      const staffId = String(rec.staff)
      if (!map.has(staffId)) {
        map.set(staffId, {
          staffId,
          staffName: getStaffName(rec.staff),
          records: [],
          totalHours: 0,
          clockedInNow: false
        })
      }
      const summary = map.get(staffId)!
      summary.records.push(rec)
      if (rec.clockOut) {
        summary.totalHours += (rec.clockOut - rec.clockIn) / HOUR_MS
      } else {
        summary.clockedInNow = true
      }
    }

    for (const summary of map.values()) {
      if (summary.totalHours > 0) {
        summary.totalHours = Math.max(0, summary.totalHours - DAILY_BREAK_HOURS)
      }
    }
    return Array.from(map.values()).sort((a, b) => b.totalHours - a.totalHours)
  })()

  function openEmployeeDetails (staffId: string) {
    if (!isManager) return
    selectedEmployeeId = staffId
  }
</script>

<div class="attendance-container">
  <div class="attendance-header">
    <TabList items={tabs} bind:selected={currentTab} />
    {#if isManager && currentTab === 'live'}
      <div class="attendance-filters">
        <DropdownLabelsIntl
          items={officeOptions}
          label={hr.string.Office}
          kind={'ghost'}
          size={'small'}
          bind:selected={selectedOfficeId}
        />
        <DropdownLabelsIntl
          items={departmentOptions}
          label={hr.string.Department}
          kind={'ghost'}
          size={'small'}
          bind:selected={selectedDepartmentId}
        />
      </div>
    {/if}
  </div>

  <Scroller padding={'var(--spacing-2)'} bottomPadding={'var(--spacing-2)'}>
    <div class="attendance-root">
      {#if currentTab === 'live'}
        <div class="attendance-list-header">
          <div class="attendance-list-header__left">
            <Label label={hr.string.CurrentlyInOffice} />
            {#if myOpenAttendance && myOpenAttendance.clockIn}
              <span class="self-status">
                You are in since {formatClockIn(myOpenAttendance.clockIn)}
              </span>
            {:else}
              <span class="self-status self-status--muted">You are not currently in office</span>
            {/if}
          </div>
          {#if attendance.length > 0}
            <span class="attendance-count">{attendance.length}</span>
          {/if}
        </div>

        {#if attendance.length > 0}
          <div class="attendance-table-wrapper">
            <table class="attendance-table">
              <thead>
                <tr>
                  <th class="col-person"><Label label={hr.string.Staff} /></th>
                  <th class="col-department"><Label label={hr.string.Department} /></th>
                  <th class="col-office"><Label label={hr.string.Office} /></th>
                  <th class="col-clockin"><Label label={hr.string.ClockIn} /></th>
                </tr>
              </thead>
              <tbody>
                {#each attendance as rec (rec._id)}
                  {@const staffName = getStaffName(rec.staff)}
                  {@const isSelf = me && String(rec.staff) === String(me)}
                  <tr class:is-self={isSelf}>
                    <td>
                      <div class="attendance-table__person">
                        {#if staffById.get(String(rec.staff))}
                          {@const staff = staffById.get(String(rec.staff))}
                          <Component is={contact.component.Avatar} props={{ person: staff, size: 'small', name: staffName }} />
                        {/if}
                        <span class="attendance-table__name">{staffName}</span>
                      </div>
                    </td>
                    <td>{getDepartmentName(rec.department)}</td>
                    <td>{getOfficeName(rec.office)}</td>
                    <td>{formatClockIn(rec.clockIn)}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {:else}
          <div class="attendance-empty"><Label label={hr.string.NotInOffice} /></div>
        {/if}
      {:else if currentTab === 'visual'}
        <AttendanceVisual 
          {attendance}
          {staffById}
          {offices}
          {departments}
          {getStaffName}
          {getOfficeName}
          {getDepartmentName}
        />
      {:else if currentTab === 'today'}
        <div class="attendance-list-header">
          <div class="attendance-list-header__left">
            <span class="section-title">Today's Attendance by Employee</span>
            <span class="attendance-count">{todayByEmployee.length} employees, {todayRecords.length} records</span>
          </div>
        </div>
        {#if todayByEmployee.length > 0}
          <div class="attendance-table-wrapper">
            <table class="attendance-table">
              <thead>
                <tr>
                  <th><Label label={hr.string.Staff} /></th>
                  <th>Sessions</th>
                  <th>Total Hours</th>
                  <th>Status</th>
                  <th>First In</th>
                  <th>Last Out</th>
                  {#if isManager}<th>Actions</th>{/if}
                </tr>
              </thead>
              <tbody>
                {#each todayByEmployee as emp}
                  {@const isSelf = me && emp.staffId === String(me)}
                  {@const firstRecord = emp.records[0]}
                  {@const lastRecord = emp.records[emp.records.length - 1]}
                  <tr class:is-self={isSelf} class:clickable={isManager} on:click={() => isManager && openEmployeeDetails(emp.staffId)}>
                    <td>
                      <div class="attendance-table__person">
                        {#if staffById.get(emp.staffId)}
                          {@const staff = staffById.get(emp.staffId)}
                          <Component is={contact.component.Avatar} props={{ person: staff, size: 'small', name: emp.staffName }} />
                        {/if}
                        <span class="attendance-table__name">{emp.staffName}</span>
                      </div>
                    </td>
                    <td>{emp.records.length}</td>
                    <td>{emp.totalHours > 0 ? `${emp.totalHours.toFixed(1)}h` : '—'}</td>
                    <td>
                      {#if emp.clockedInNow}
                        <span class="badge badge--success">In Office</span>
                      {:else}
                        <span class="badge">Left</span>
                      {/if}
                    </td>
                    <td>{formatTime(firstRecord.clockIn)}</td>
                    <td>{lastRecord.clockOut ? formatTime(lastRecord.clockOut) : '—'}</td>
                    {#if isManager}
                      <td>
                        <span class="link">View Details →</span>
                      </td>
                    {/if}
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {:else}
          <div class="attendance-empty">No attendance records today</div>
        {/if}
      {:else if currentTab === 'week'}
        <div class="attendance-list-header">
          <div class="attendance-list-header__left">
            <span class="section-title">This Week's Summary by Employee</span>
            <span class="attendance-count">{weeklySummaries.length} employees</span>
          </div>
        </div>
        {#if weeklySummaries.length > 0}
          <div class="attendance-table-wrapper">
            <table class="attendance-table">
              <thead>
                <tr>
                  <th><Label label={hr.string.Staff} /></th>
                  <th>Days Present</th>
                  <th>Total Hours</th>
                  <th>Avg Hours/Day</th>
                  {#if isManager}<th>Actions</th>{/if}
                </tr>
              </thead>
              <tbody>
                {#each weeklySummaries as summary}
                  {@const isSelf = me && summary.staffId === String(me)}
                  {@const avgHours = summary.daysPresent > 0 ? (summary.totalHours / summary.daysPresent).toFixed(1) : '0'}
                  <tr class:is-self={isSelf} class:clickable={isManager} on:click={() => isManager && openEmployeeDetails(summary.staffId)}>
                    <td>
                      <div class="attendance-table__person">
                        {#if staffById.get(summary.staffId)}
                          {@const staff = staffById.get(summary.staffId)}
                          <Component is={contact.component.Avatar} props={{ person: staff, size: 'small', name: summary.staffName }} />
                        {/if}
                        <span class="attendance-table__name">{summary.staffName}</span>
                      </div>
                    </td>
                    <td>{summary.daysPresent}</td>
                    <td>{summary.totalHours.toFixed(1)}h</td>
                    <td>{avgHours}h</td>
                    {#if isManager}
                      <td>
                        <span class="link">View Details →</span>
                      </td>
                    {/if}
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {:else}
          <div class="attendance-empty">No attendance records this week</div>
        {/if}
      {:else if currentTab === 'history'}
        <div class="attendance-list-header">
          <div class="attendance-list-header__left">
            <span class="section-title">Last 30 Days</span>
            <span class="attendance-count">{allHistoricalRecords.length} records</span>
          </div>
        </div>
        {#if allHistoricalRecords.length > 0}
          <div class="attendance-table-wrapper">
            <table class="attendance-table">
              <thead>
                <tr>
                  <th><Label label={hr.string.Staff} /></th>
                  <th>Date</th>
                  <th><Label label={hr.string.ClockIn} /></th>
                  <th><Label label={hr.string.ClockOut} /></th>
                  <th>Duration</th>
                </tr>
              </thead>
              <tbody>
                {#each allHistoricalRecords as rec (rec._id)}
                  {@const staffName = getStaffName(rec.staff)}
                  {@const isSelf = me && String(rec.staff) === String(me)}
                  <tr class:is-self={isSelf}>
                    <td>
                      <div class="attendance-table__person">
                        {#if staffById.get(String(rec.staff))}
                          {@const staff = staffById.get(String(rec.staff))}
                          <Component is={contact.component.Avatar} props={{ person: staff, size: 'small', name: staffName }} />
                        {/if}
                        <span class="attendance-table__name">{staffName}</span>
                      </div>
                    </td>
                    <td>{formatDate(rec.clockIn)}</td>
                    <td>{formatTime(rec.clockIn)}</td>
                    <td>{rec.clockOut ? formatTime(rec.clockOut) : '—'}</td>
                    <td>{calculateDuration(rec.clockIn, rec.clockOut)}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {:else}
          <div class="attendance-empty">No attendance records in last 30 days</div>
        {/if}
      {:else if currentTab === 'insights'}
        {#if isManager}
          <div class="attendance-list-header">
            <div class="attendance-list-header__left">
              <span class="section-title">Manager Insights (This Week)</span>
              <span class="attendance-count">{employeeInsights.length} employees</span>
            </div>
          </div>
          {#if employeeInsights.length > 0}
            <div class="insights-grid">
              <div class="insight-card">
                <div class="insight-card__label">Late Arrivals (>15 min)</div>
                <div class="insight-card__value">
                  {employeeInsights.reduce((sum, e) => sum + e.lateArrivals, 0)}
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-card__label">Early Departures</div>
                <div class="insight-card__value">
                  {employeeInsights.reduce((sum, e) => sum + e.earlyDepartures, 0)}
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-card__label">Avg Hours/Day</div>
                <div class="insight-card__value">
                  {(employeeInsights.reduce((sum, e) => sum + e.avgHours, 0) / employeeInsights.length).toFixed(1)}h
                </div>
              </div>
              <div class="insight-card">
                <div class="insight-card__label">Currently Working</div>
                <div class="insight-card__value">
                  {employeeInsights.filter((e) => e.workingNow).length}
                </div>
              </div>
            </div>

            {#if arrivalSlots.length > 0 || punctualityBuckets.length > 0 || overtimeBuckets.length > 0}
              <div class="insights-advanced">
                {#if arrivalSlots.length > 0}
                  <div class="insights-subsection">
                    <div class="insights-subtitle">Arrival time distribution</div>
                    <div class="insights-bar-list">
                      {#each arrivalSlots as slot}
                        <div class="insights-bar-row">
                          <span class="insights-bar-label">{slot.label}</span>
                          <div class="insights-bar-track">
                            <div class="insights-bar-fill" style={`width: ${slot.percent}%`}></div>
                          </div>
                          <span class="insights-bar-value">{slot.count}</span>
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}

                {#if punctualityBuckets.length > 0}
                  <div class="insights-subsection">
                    <div class="insights-subtitle">Punctuality buckets</div>
                    <div class="insights-bar-list">
                      {#each punctualityBuckets as bucket}
                        <div class="insights-bar-row">
                          <span class="insights-bar-label">{bucket.label}</span>
                          <div class="insights-bar-track">
                            <div class="insights-bar-fill" style={`width: ${bucket.percent}%`}></div>
                          </div>
                          <span class="insights-bar-value">{bucket.count}</span>
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}

                {#if overtimeBuckets.length > 0}
                  <div class="insights-subsection">
                    <div class="insights-subtitle">Daily hours distribution</div>
                    <div class="insights-bar-list">
                      {#each overtimeBuckets as bucket}
                        <div class="insights-bar-row">
                          <span class="insights-bar-label">{bucket.label}</span>
                          <div class="insights-bar-track">
                            <div class="insights-bar-fill" style={`width: ${bucket.percent}%`}></div>
                          </div>
                          <span class="insights-bar-value">{bucket.count}</span>
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}
              </div>
            {/if}

            {#if weeklyHoursByDay.length > 0}
              <div class="insights-chart">
                {#each weeklyHoursByDay as day}
                  <div class="insights-chart__column">
                    <div class="insights-chart__bar-wrapper">
                      <div class="insights-chart__bar" style={`height: ${day.barPercent}%`}></div>
                    </div>
                    <div class="insights-chart__label">{day.label}</div>
                    <div class="insights-chart__value">{day.totalHours.toFixed(1)}h</div>
                  </div>
                {/each}
              </div>
            {/if}

            <div class="attendance-table-wrapper">
              <table class="attendance-table">
                <thead>
                  <tr>
                    <th><Label label={hr.string.Staff} /></th>
                    <th>Days Worked</th>
                    <th>Avg Start</th>
                    <th>Avg End</th>
                    <th>Avg Hours</th>
                    <th>Late Arrivals</th>
                    <th>Early Leaves</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {#each employeeInsights as insight}
                    {@const isSelf = me && insight.staffId === String(me)}
                    <tr class:is-self={isSelf} class:is-late={insight.lateArrivals > 2} class:clickable={isManager} on:click={() => openEmployeeDetails(insight.staffId)}>
                      <td>
                        <div class="attendance-table__person">
                          {#if staffById.get(insight.staffId)}
                            {@const staff = staffById.get(insight.staffId)}
                            <Component
                              is={contact.component.Avatar}
                              props={{ person: staff, size: 'small', name: insight.staffName }}
                            />
                          {/if}
                          <span class="attendance-table__name">{insight.staffName}</span>
                        </div>
                      </td>
                      <td>{insight.daysWorked}</td>
                      <td>{insight.avgStartTime}</td>
                      <td>{insight.avgEndTime}</td>
                      <td>{insight.avgHours > 0 ? `${insight.avgHours.toFixed(1)}h` : '—'}</td>
                      <td>
                        {#if insight.lateArrivals > 0}
                          <span class="badge badge--warning">{insight.lateArrivals}</span>
                        {:else}
                          —
                        {/if}
                      </td>
                      <td>
                        {#if insight.earlyDepartures > 0}
                          <span class="badge badge--warning">{insight.earlyDepartures}</span>
                        {:else}
                          —
                        {/if}
                      </td>
                      <td>
                        {#if insight.workingNow}
                          <span class="badge badge--success">In Office</span>
                        {:else}
                          <span class="badge">Out</span>
                        {/if}
                      </td>
                      <td>
                        <span class="link">View Details →</span>
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          {:else}
            <div class="attendance-empty">No attendance data this week</div>
          {/if}
        {:else}
          <div class="attendance-empty">Manager-only view</div>
        {/if}
      {/if}
    </div>
  </Scroller>
</div>

<style lang="scss">
  .attendance-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .attendance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .section-title {
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .attendance-filters {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
  }

  .attendance-root {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1_5);
  }

  .attendance-list-header {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    gap: var(--spacing-1);
  }

  .attendance-list-header__left {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-1);
  }

  .self-status {
    font-size: 0.75rem;
    color: var(--theme-caption-color);
  }

  .self-status--muted {
    color: var(--theme-trans-color);
  }

  .attendance-count {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
  }

  .attendance-table-wrapper {
    width: 100%;
    overflow: auto;
  }

  .attendance-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 480px;
  }

  .attendance-table th,
  .attendance-table td {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color);
    text-align: left;
    font-size: 0.875rem;
  }

  .attendance-table th {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-trans-color);
  }

  .attendance-table__person {
    display: flex;
    align-items: center;
    gap: var(--spacing-0_5);
  }

  .attendance-table__name {
    color: var(--theme-caption-color);
    font-weight: 500;
  }

  .attendance-table tr.is-self {
    background-color: var(--theme-bg-accent-color);
  }

  .attendance-empty {
    padding: var(--spacing-2);
    text-align: center;
    color: var(--theme-dark-color);
  }

  .insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-1_5);
    margin-bottom: var(--spacing-2);
  }

  .insight-card {
    padding: var(--spacing-1_5);
    border-radius: var(--medium-BorderRadius);
    border: 1px solid var(--theme-divider-color);
    background: var(--theme-button-default);
  }

  .insight-card__label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.04em;
    color: var(--theme-trans-color);
    margin-bottom: var(--spacing-0_5);
  }

  .insight-card__value {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .insights-advanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: var(--spacing-1_5);
    margin: var(--spacing-2) 0;
  }

  .insights-subsection {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_75);
  }

  .insights-subtitle {
    font-size: 0.8125rem;
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .insights-bar-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .insights-bar-row {
    display: grid;
    grid-template-columns: minmax(80px, 140px) 1fr auto;
    align-items: center;
    gap: var(--spacing-0_5);
    font-size: 0.8125rem;
  }

  .insights-bar-label {
    color: var(--theme-caption-color);
  }

  .insights-bar-track {
    position: relative;
    height: 6px;
    border-radius: 999px;
    background: var(--theme-bg-secondary);
    overflow: hidden;
  }

  .insights-bar-fill {
    position: absolute;
    inset: 0;
    width: 0;
    border-radius: 999px;
    background: linear-gradient(90deg, #3b82f6 0%, #22c55e 100%);
    transition: width 0.2s ease;
  }

  .insights-bar-value {
    font-variant-numeric: tabular-nums;
    color: var(--theme-trans-color);
  }

  .insights-chart {
    display: flex;
    gap: var(--spacing-1);
    margin: var(--spacing-2) 0;
    align-items: flex-end;
  }

  .insights-chart__column {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-0_25);
  }

  .insights-chart__bar-wrapper {
    width: 100%;
    height: 80px;
    border-radius: var(--small-BorderRadius);
    background: var(--theme-bg-secondary);
    overflow: hidden;
    display: flex;
    align-items: flex-end;
  }

  .insights-chart__bar {
    width: 100%;
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: var(--small-BorderRadius);
    transition: height 0.2s ease;
  }

  .insights-chart__label {
    font-size: 0.75rem;
    color: var(--theme-trans-color);
  }

  .insights-chart__value {
    font-size: 0.75rem;
    color: var(--theme-caption-color);
    font-variant-numeric: tabular-nums;
  }

  .badge {
    padding: 0.125rem 0.5rem;
    border-radius: 999px;
    background-color: var(--theme-tablist-color);
    font-size: 0.75rem;
    color: var(--theme-caption-color);
  }

  .badge--warning {
    background-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
  }

  .badge--success {
    background-color: rgba(22, 163, 74, 0.2);
    color: #16a34a;
  }

  .attendance-table tr.is-late {
    background-color: rgba(245, 158, 11, 0.05);
  }

  .attendance-table tr.clickable {
    cursor: pointer;
  }

  .attendance-table tr.clickable:hover {
    background-color: var(--theme-bg-accent-color);
  }

  .link {
    color: var(--theme-link-color);
    font-size: 0.75rem;
    text-decoration: none;
  }

  .link:hover {
    text-decoration: underline;
  }
</style>

{#if selectedEmployeeId}
  <EmployeeAttendanceDetail
    staffId={selectedEmployeeId}
    {staffById}
    {weekRecords}
    {allHistoricalRecords}
    {getStaffName}
    {getDepartmentName}
    {getOfficeName}
    {formatClockIn}
    {formatTime}
    {formatDate}
    {calculateDuration}
    on:close={() => { selectedEmployeeId = undefined }}
  />
{/if}
