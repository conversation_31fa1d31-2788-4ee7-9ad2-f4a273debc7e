<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import { generateId } from '@hcengineering/core'
  import { Staff, DocumentType, DocumentStatus } from '@hcengineering/matrics-hr'
  import { getClient } from '@hcengineering/presentation'
  import { ModernDialog, getEventPositionElement, showPopup, EditBox, DropdownLabelsIntl } from '@hcengineering/ui'
  import { DateEditor } from '@hcengineering/view-resources'
  import { AttachmentStyledBox } from '@hcengineering/attachment-resources'
  import hr from '../plugin'

  export let employee: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let title = ''
  let description = ''
  let documentType: DocumentType = DocumentType.Contract
  let status: DocumentStatus = DocumentStatus.Active
  let effectiveDate: number | null = Date.now()
  let expiryDate: number | null = null
  let signedDate: number | null = null
  let documentId: Ref<any> = generateId()

  const documentTypes = [
    { id: DocumentType.Contract, label: hr.string.DocumentTypeContract },
    { id: DocumentType.OfferLetter, label: hr.string.DocumentTypeOfferLetter },
    { id: DocumentType.NDA, label: hr.string.DocumentTypeNDA },
    { id: DocumentType.Agreement, label: hr.string.DocumentTypeAgreement },
    { id: DocumentType.Policy, label: hr.string.DocumentTypePolicy },
    { id: DocumentType.Certificate, label: hr.string.DocumentTypeCertificate },
    { id: DocumentType.Other, label: hr.string.DocumentTypeOther }
  ]

  const documentStatuses = [
    { id: DocumentStatus.Draft, label: hr.string.DocumentStatusDraft },
    { id: DocumentStatus.Active, label: hr.string.DocumentStatusActive },
    { id: DocumentStatus.Expired, label: hr.string.DocumentStatusExpired },
    { id: DocumentStatus.Revoked, label: hr.string.DocumentStatusRevoked }
  ]

  async function save (): Promise<void> {
    await client.addCollection(
      hr.class.EmployeeDocument,
      employee.space,
      employee._id as Ref<Staff>,
      hr.mixin.Staff,
      'documents',
      {
        title,
        description,
        documentType,
        status,
        effectiveDate: effectiveDate ?? undefined,
        expiryDate: expiryDate ?? undefined,
        signedDate: signedDate ?? undefined,
        uploadedBy: client.getHierarchy().getAccount()._id as any
      },
      documentId as any
    )
    dispatch('close', true)
  }

  $: canSave = title.trim().length > 0
</script>

<ModernDialog
  label={hr.string.AddDocument}
  canSubmit={canSave}
  on:submit={save}
  on:close={() => dispatch('close')}
>
  <div class="form">
    <div class="form-row">
      <span class="label">Title *</span>
      <EditBox bind:value={title} placeholder={'e.g., Employment Contract'} />
    </div>

    <div class="form-row">
      <span class="label">Document Type</span>
      <DropdownLabelsIntl
        items={documentTypes}
        selected={documentType}
        on:selected={(e) => documentType = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Status</span>
      <DropdownLabelsIntl
        items={documentStatuses}
        selected={status}
        on:selected={(e) => status = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Effective Date</span>
      <DateEditor value={effectiveDate} type={undefined} onChange={(v) => (effectiveDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Expiry Date</span>
      <DateEditor value={expiryDate} type={undefined} onChange={(v) => (expiryDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Signed Date</span>
      <DateEditor value={signedDate} type={undefined} onChange={(v) => (signedDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Description</span>
      <EditBox bind:value={description} placeholder={'Document notes...'} />
    </div>

    <div class="form-row">
      <span class="label">File</span>
      {#key documentId}
        <AttachmentStyledBox
          objectId={documentId}
          _class={hr.class.EmployeeDocument}
          space={employee.space}
          alwaysEdit
          showButtons
          maxHeight={'card'}
        />
      {/key}
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--theme-caption-color);
    }
  }
</style>
