<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import core, { Ref } from '@hcengineering/core'
  import { Department, PublicHoliday, toTzDate } from '@hcengineering/matrics-hr'
  import { Card, createQuery, getClient } from '@hcengineering/presentation'
  import { Button, EditBox, Label, DatePresenter } from '@hcengineering/ui'
  import hr from '../plugin'
  import { createEventDispatcher } from 'svelte'

  export let holiday: PublicHoliday

  const dispatch = createEventDispatcher()
  const client = getClient()
  const departmentsQuery = createQuery()

  let title: string = holiday.title
  let description: string = holiday.description
  let selectedDate: number = new Date(holiday.date.year, holiday.date.month, holiday.date.day).getTime()
  let department: Ref<Department> | undefined = holiday.department
  let departments: Department[] = []

  $: departmentsQuery.query(hr.class.Department, {}, (res) => {
    departments = res
  })

  async function updateHoliday() {
    if (!title.trim() || !description.trim()) {
      return
    }

    const tzDate = toTzDate(new Date(selectedDate))

    await client.updateDoc(
      hr.class.PublicHoliday,
      core.space.Workspace,
      holiday._id,
      {
        title: title.trim(),
        description: description.trim(),
        date: tzDate,
        department: department ?? hr.ids.Head
      }
    )

    dispatch('close')
  }
</script>

<Card
  label={hr.string.EditHoliday}
  okAction={updateHoliday}
  canSave={title.trim().length > 0 && description.trim().length > 0}
  on:close={() => { dispatch('close') }}
  on:changeContent
>
  <div class="form-content">
    <div class="form-field">
      <Label label={hr.string.HolidayName} />
      <EditBox
        bind:value={title}
        placeholder={hr.string.HolidayNamePlaceholder}
        kind={'large-style'}
        autoFocus
      />
    </div>

    <div class="form-field">
      <Label label={hr.string.HolidayDescription} />
      <EditBox
        bind:value={description}
        placeholder={hr.string.HolidayDescriptionPlaceholder}
        kind={'large-style'}
      />
    </div>

    <div class="form-field">
      <Label label={hr.string.Date} />
      <DatePresenter bind:value={selectedDate} editable />
    </div>

    <div class="form-field">
      <Label label={hr.string.DepartmentOptional} />
      <select bind:value={department} class="department-select">
        <option value={undefined}>All Departments</option>
        {#each departments as dept}
          <option value={dept._id}>{dept.name}</option>
        {/each}
      </select>
    </div>
  </div>
</Card>

<style lang="scss">
  .form-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
  }

  .form-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .department-select {
    padding: var(--spacing-1);
    border: 1px solid var(--theme-divider-color);
    border-radius: var(--small-BorderRadius);
    background-color: var(--theme-button-default);
    color: var(--theme-caption-color);
    font-size: 0.9375rem;
    cursor: pointer;

    &:hover {
      border-color: var(--theme-button-border);
    }

    &:focus {
      outline: none;
      border-color: var(--primary-button-default);
    }
  }
</style>
