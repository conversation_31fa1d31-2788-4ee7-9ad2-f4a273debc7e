<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Ref } from '@hcengineering/core'
  import { createQuery } from '@hcengineering/presentation'
  import { getEmbeddedLabel } from '@hcengineering/platform'
  import { Button, Icon, Label, Scroller, showPopup } from '@hcengineering/ui'
  import { getCurrentEmployee } from '@hcengineering/contact'
  import { Request, Staff, TimeOffBalance, TimeOffPolicy, TimeOffTransaction } from '@hcengineering/matrics-hr'
  import view from '@hcengineering/view'
  import hr from '../../plugin'
  import CreateRequest from '../CreateRequest.svelte'

  const me = getCurrentEmployee() as Ref<Staff>
  const q = createQuery()

  let requests: Request[] = []
  let staff: Staff | undefined
  let balances: TimeOffBalance[] = []
  let policies: TimeOffPolicy[] = []
  let transactions: TimeOffTransaction[] = []

  const dateFormatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })

  function formatDate (value?: number | null): string | undefined {
    if (value == null) return undefined
    return dateFormatter.format(new Date(value))
  }

  if (me !== undefined) {
    q.query(hr.mixin.Staff, { _id: me }, (res) => {
      staff = res[0]
    })

    q.query(hr.class.Request, { attachedTo: me }, (res) => {
      requests = res
    })

    q.query(hr.class.TimeOffBalance, { staff: me as any }, (res) => {
      balances = res
    })

    q.query(hr.class.TimeOffTransaction, { staff: me as any }, (res) => {
      transactions = res
    })

    q.query(hr.class.TimeOffPolicy, { active: true }, (res) => {
      policies = res
    })
  }

  function createNew (evt: Event): void {
    if (staff === undefined) return
    showPopup(
      CreateRequest,
      {
        staff,
        date: new Date(),
        readonly: true,
        employeeRequests: new Map(),
        docQuery: { _id: me as any }
      },
      evt.target as HTMLElement
    )
  }
</script>

<Scroller>
  <div class="my-timeoff">
    <div class="header flex-row-center flex-between">
      <div class="flex-row-center gap-2">
        <Icon icon={view.icon.List} size={'small'} />
        <Label label={getEmbeddedLabel('My Time Off')} />
        <span class="count">({requests.length})</span>
      </div>
      <div class="flex-row-center gap-2">
        <Button label={hr.string.CreateRequest} icon={view.icon.Add} kind={'ghost'} size={'small'} on:click={createNew} />
      </div>
    </div>

    <div class="sections">
      <div class="section">
        <div class="section-header">
          <Label label={hr.string.TimeOffBalance} />
        </div>
        {#if balances.length > 0}
          <div class="balance-grid">
            {#each balances as balance (balance._id)}
              {@const policy = policies.find((p) => p._id === balance.policy)}
              <div class="balance-card">
                <div class="balance-header">
                  <span class="balance-name">{policy?.title ?? 'Time Off'}</span>
                </div>
                <div class="balance-stats">
                  <div class="balance-stat">
                    <span class="stat-label"><Label label={hr.string.CurrentBalance} /></span>
                    <span class="stat-value">{balance.balance ?? 0}</span>
                  </div>
                  <div class="balance-stat">
                    <span class="stat-label"><Label label={hr.string.PendingBalance} /></span>
                    <span class="stat-value">{balance.pending ?? 0}</span>
                  </div>
                  <div class="balance-stat">
                    <span class="stat-label"><Label label={hr.string.Carryover} /></span>
                    <span class="stat-value">{balance.carryover ?? 0}</span>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <div class="empty">No time-off balances yet</div>
        {/if}
      </div>

      <div class="section">
        <div class="section-header">
          <Label label={hr.string.BalanceHistory} />
        </div>
        {#if transactions.length > 0}
          <div class="items">
            {#each transactions as tx (tx._id)}
              {@const policy = policies.find((p) => p._id === tx.policy)}
              <div class="item">
                <div class="row flex-row-center gap-2">
                  <Icon icon={view.icon.Statuses} size={'x-small'} />
                  <div class="title">{policy?.title ?? 'Time Off'}</div>
                </div>
                <div class="meta">
                  <span class="kind">{tx.kind}</span>
                  <span class="amount">{tx.amount}</span>
                  {#if tx.effectiveDate}
                    <span class="date">{formatDate(tx.effectiveDate) ?? ''}</span>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <div class="empty">No balance history yet</div>
        {/if}
      </div>

      <div class="section">
        <div class="section-header">
          <Label label={getEmbeddedLabel('Requests')} />
        </div>
        <div class="items">
          {#each requests as r}
            <div class="item">
              <div class="row flex-row-center gap-2">
                <Icon icon={view.icon.Statuses} size={'x-small'} />
                <div class="title">{r.status}</div>
              </div>
            </div>
          {/each}
          {#if requests.length === 0}
            <div class="empty">No time-off requests yet</div>
          {/if}
        </div>
      </div>
    </div>
  </div>
</Scroller>

<style lang="scss">
  .my-timeoff { display: flex; flex-direction: column; gap: 1rem; padding: 1.5rem; }
  .header { padding-bottom: 0.75rem; border-bottom: 1px solid var(--theme-divider-color); }
  .count { font-size: 0.875rem; color: var(--theme-dark-color); }

  .sections { display: flex; flex-direction: column; gap: 1.5rem; margin-top: 1rem; }
  .section { display: flex; flex-direction: column; gap: 0.75rem; }
  .section-header { font-size: 0.9rem; font-weight: 600; color: var(--theme-caption-color); }

  .balance-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)); gap: 1rem; }
  .balance-card { padding: 1rem; border: 1px solid var(--theme-divider-color); border-radius: 0.5rem; background: var(--theme-comp-header-color); }
  .balance-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem; }
  .balance-name { font-weight: 600; font-size: 0.9375rem; }
  .balance-stats { display: flex; gap: 1.5rem; }
  .balance-stat { display: flex; flex-direction: column; gap: 0.25rem; }
  .stat-label { font-size: 0.75rem; color: var(--theme-dark-color); }
  .stat-value { font-size: 1.1rem; font-weight: 600; color: var(--theme-caption-color); }

  .items { display: flex; flex-direction: column; gap: 0.5rem; }
  .item { padding: 0.75rem; border: 1px solid var(--theme-divider-color); border-radius: 0.5rem; background: var(--theme-button-default); }
  .row { align-items: center; }
  .meta { margin-top: 0.25rem; display: flex; gap: 0.5rem; font-size: 0.8125rem; color: var(--theme-dark-color); }
  .empty { padding: 1.5rem; text-align: center; color: var(--theme-dark-color); font-size: 0.875rem; }
</style>
