<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { Card, createQuery } from '@hcengineering/presentation'
  import { getCurrentEmployee } from '@hcengineering/contact'
  import { EmployeePresenter } from '@hcengineering/contact-resources'
  import { Ref } from '@hcengineering/core'
  import { Staff, type TimeOffBalance } from '@hcengineering/matrics-hr'
  import { Label, Scroller } from '@hcengineering/ui'
  import hr from '../../plugin'

  const me = getCurrentEmployee() as Ref<Staff>
  const q = createQuery()
  const balancesQuery = createQuery()

  let staff: Staff | undefined
  if (me) {
    q.query(hr.mixin.Staff, { _id: me }, (res) => {
      staff = res[0]
    })
  }

  // TimeOffBalances for current employee
  let timeOffBalances: TimeOffBalance[] = []
  if (me) {
    balancesQuery.query(hr.class.TimeOffBalance, { staff: me }, (res) => {
      timeOffBalances = res
    })
  }

  // Aggregates
  $: totalBalance = timeOffBalances.reduce((sum, b) => sum + (b.balance ?? 0), 0)
  $: totalPending = timeOffBalances.reduce((sum, b) => sum + (b.pending ?? 0), 0)
  $: totalCarryover = timeOffBalances.reduce((sum, b) => sum + (b.carryover ?? 0), 0)
  $: availableDays = totalBalance + totalCarryover
  $: remainingDays = Math.max(availableDays - totalPending, 0)
</script>

<Scroller>
  <div class="my-profile">
    <Card label={hr.string.CurrentEmployee} gap={'gapV-4'} okAction={() => {}} on:close>
      {#if staff}
        <div class="row">
          <EmployeePresenter value={staff} avatarSize={'large'} shouldShowAvatar shouldShowName />
        </div>
        <div class="grid">
          <div class="field">
            <div class="label"><Label label={hr.string.JobTitle} /></div>
            <div class="value">{staff.jobTitle ?? '-'}</div>
          </div>
          <div class="field">
            <div class="label"><Label label={hr.string.AvailableDays} /></div>
            <div class="value">{availableDays}</div>
          </div>
          <div class="field">
            <div class="label"><Label label={hr.string.TimeOffPending} /></div>
            <div class="value">{totalPending}</div>
          </div>
          <div class="field">
            <div class="label"><Label label={hr.string.TimeOffRemaining} /></div>
            <div class="value">{remainingDays}</div>
          </div>
        </div>
      {:else}
        <div class="empty">No profile data</div>
      {/if}
    </Card>
  </div>
</Scroller>

<style lang="scss">
  .my-profile { padding: 1rem; }
  .row { display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem; }
  .grid { display: grid; gap: .75rem; grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .field { display: flex; flex-direction: column; gap: .25rem; padding: .75rem; border: 1px solid var(--theme-divider-color); border-radius: .5rem; background: var(--theme-button-default); }
  .label { font-size: .75rem; opacity: .7; }
  .value { font-weight: 600; }
  .empty { padding: 2rem; text-align: center; color: var(--theme-dark-color); }
</style>
