<script lang="ts">
  import { Avatar } from '@hcengineering/contact-resources'
  import type { Employee } from '@hcengineering/contact'
  import { Ref, WithLookup } from '@hcengineering/core'
  import type { Department } from '@hcengineering/matrics-hr'
  import { Label, IconChevronDown, IconChevronRight, navigate } from '@hcengineering/ui'
  import { getClient } from '@hcengineering/presentation'
  import view from '@hcengineering/view'
  import { getObjectLinkFragment } from '@hcengineering/view-resources'
  import type { Class, Doc } from '@hcengineering/core'

  import hr from '../plugin'
  import OrgChartNode from './OrgChartNode.svelte'

  export let node: WithLookup<Department>
  export let getChildren: (id: Ref<Department> | undefined) => WithLookup<Department>[]
  export let focusId: Ref<Department> | undefined
  export let depth: number = 0
  export let query: string = ''
  export let visibleIds: Set<Ref<Department>> | undefined = undefined
  export let forceExpandIds: Set<Ref<Department>> | undefined = undefined

  const MEMBER_PREVIEW = 5
  let isExpanded = true

  $: children = getChildren(node._id)
  $: hasChildren = children.length > 0
  $: isFocused = focusId !== undefined && node._id === focusId
  $: teamLead = (node.$lookup?.teamLead ?? undefined) as WithLookup<Employee> | undefined
  $: teamLeadRole = teamLead ? ((teamLead as any).jobTitle || (teamLead as any).position || 'Team Lead') : 'Team Lead'
  $: memberLookups = (node.$lookup?.members ?? []) as unknown as WithLookup<Employee>[]
  $: memberCount = node.members?.length ?? 0
  $: previewMembers = memberLookups.slice(0, MEMBER_PREVIEW)
  $: overflowMembers = Math.max(0, memberCount - MEMBER_PREVIEW)
  $: q = (query ?? '').trim().toLowerCase()
  $: isSearching = q.length > 0
  $: displayChildren = visibleIds !== undefined ? children.filter((c) => visibleIds.has(c._id)) : children
  $: renderedChildren = isSearching ? displayChildren : children
  $: hasRenderedChildren = renderedChildren.length > 0
  $: isVisible = visibleIds === undefined ? true : visibleIds.has(node._id)

  function toggleExpand(): void {
    if (hasChildren) {
      isExpanded = !isExpanded
    }
  }

  const client = getClient()
  const hierarchy = client.getHierarchy()

  async function viewProfile (person: WithLookup<Employee> | undefined): Promise<void> {
    if (person === undefined) return
    const panelComponent = hierarchy.classHierarchyMixin(person._class as Ref<Class<Doc>>, view.mixin.ObjectPanel)
    const comp = panelComponent?.component ?? view.component.EditDoc
    const loc = await getObjectLinkFragment(hierarchy, person, {}, comp)
    navigate(loc)
  }

  $: if (isSearching && forceExpandIds?.has(node._id)) {
    isExpanded = true
  }
</script>

{#if isVisible}
<li class="org-node" class:root={depth === 0} class:has-children={hasRenderedChildren && isExpanded}>
  <div class="org-node__wrapper">
    <div class="org-card" class:focused={isFocused}>
      <div class="org-card__header">
        <div class="org-card__title">
          {#if hasChildren}
            <button class="org-card__toggle" on:click={toggleExpand} aria-label={isExpanded ? 'Collapse' : 'Expand'}>
              <svelte:component this={isExpanded ? IconChevronDown : IconChevronRight} size={'small'} />
            </button>
          {:else}
            <div class="org-card__spacer" />
          {/if}
          <span class="org-card__name">{node.name}</span>
        </div>
        <span class="org-card__count">
    <Label label={hr.string.MemberCount} params={{ count: memberCount }} />
        </span>
      </div>
      {#if teamLead}
        <div class="org-card__lead">
          <Avatar size={'small'} person={teamLead} name={teamLead.name} clickable on:click={() => viewProfile(teamLead)} />
          <div class="org-card__lead-info">
            <span class="org-card__lead-name">{teamLead.name}</span>
            <span class="org-card__lead-role">{teamLeadRole}</span>
          </div>
        </div>
      {/if}

      {#if previewMembers.length > 0}
        <div class="org-card__members" title={`Members (${memberCount})`}>
          <div class="org-card__avatars">
            {#each previewMembers as member (member._id)}
              <div class="org-card__avatar" title={member.name}>
                <Avatar size={'x-small'} person={member} name={member.name} clickable on:click={() => viewProfile(member)} />
              </div>
            {/each}
            {#if overflowMembers > 0}
              <span class="org-card__overflow">+{overflowMembers}</span>
            {/if}
          </div>
        </div>
      {/if}
    </div>
    {#if hasRenderedChildren && isExpanded}
      <ul class="org-children">
        {#each renderedChildren as child (child._id)}
          <OrgChartNode node={child} {getChildren} {focusId} depth={depth + 1} {query} {visibleIds} {forceExpandIds} />
        {/each}
      </ul>
    {/if}
  </div>
</li>
{/if}

<style lang="scss">
  .org-node {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    list-style: none;
  }

  .org-node__wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* Vertical line from parent to children */
  .org-node.has-children > .org-node__wrapper::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    width: 1px;
    height: calc(var(--org-vertical-spacing, 3rem) / 2);
    background: var(--theme-divider-color);
    transform: translateX(-50%);
  }

  .org-card {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--theme-button-default);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
    box-shadow: var(--theme-button-shadow);
    min-width: 12rem;
    max-width: 14rem;
    transition: all 0.15s ease;
    position: relative;
    z-index: 1;

    &:hover {
      background: var(--theme-button-hovered);
      border-color: var(--theme-divider-color);
      box-shadow: var(--theme-popup-shadow);
    }

    &.focused {
      border-color: var(--primary-button-default);
      background: var(--theme-button-pressed);
    }
  }

  .org-card__toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.125rem;
    border: none;
    background: transparent;
    color: var(--theme-dark-color);
    cursor: pointer;
    border-radius: 0.25rem;
    transition: all 0.1s ease;

    &:hover {
      background: var(--theme-button-hovered);
      color: var(--theme-content-color);
    }
  }

  .org-card__spacer {
    width: 1.25rem;
  }

  .org-card__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
  }

  .org-card__title {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    min-width: 0;
    flex: 1;
  }

  .org-card__name {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--theme-caption-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  .org-card__count {
    font-size: 0.6875rem;
    color: var(--theme-halfcontent-color);
    white-space: nowrap;
    flex-shrink: 0;
  }

  .org-card__lead {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0 0.25rem;
    border-top: 1px solid var(--theme-divider-color);
  }

  .org-card__lead-info {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
    min-width: 0;
    flex: 1;
  }

  .org-card__lead-name {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--theme-caption-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .org-card__lead-role {
    font-size: 0.6875rem;
    color: var(--theme-halfcontent-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .org-card__members {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .org-card__avatars {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -0.25rem;
  }

  .org-card__avatar {
    margin-left: -0.25rem;
    position: relative;
    border: 1.5px solid var(--theme-button-default);
    border-radius: 50%;
    transition: transform 0.1s ease;

    &:hover {
      transform: translateY(-2px) scale(1.1);
      z-index: 10;
      border-color: var(--theme-popup-color);
    }
  }

  .org-card__overflow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    margin-left: -0.25rem;
    padding: 0 0.375rem;
    border-radius: 50%;
    background: var(--theme-button-hovered);
    border: 1.5px solid var(--theme-button-default);
    font-size: 0.625rem;
    font-weight: 600;
    color: var(--theme-content-color);
  }


  .org-children {
    display: flex;
    justify-content: center;
    gap: var(--org-horizontal-spacing, 2rem);
    margin: 0;
    padding: 0;
    padding-top: calc(var(--org-vertical-spacing, 2.5rem) / 2);
    position: relative;
    animation: slideDown 0.15s ease-out;
  }

  /* Horizontal line connecting all children */
  .org-children::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--theme-divider-color);
  }

  /* Vertical line from horizontal connector to each child */
  .org-children > :global(.org-node)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 1px;
    height: calc(var(--org-vertical-spacing, 2.5rem) / 2);
    background: var(--theme-divider-color);
    transform: translateX(-50%);
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-0.5rem);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
