<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { AttachmentStyledBox } from '@hcengineering/attachment-resources'
  import { Employee } from '@hcengineering/contact'
  import { EmployeeBox } from '@hcengineering/contact-resources'
  import core, { generateId, Markup, Ref } from '@hcengineering/core'
  import { Department, WorkflowStatus } from '@hcengineering/matrics-hr'
  import { Card, getClient } from '@hcengineering/presentation'
  import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FocusHandler, createFocusManager, DatePresenter } from '@hcengineering/ui'
  import { EmptyMarkup } from '@hcengineering/text'
  import { createEventDispatcher } from 'svelte'
  import hr from '../plugin'
  import DepartmentEditor from './DepartmentEditor.svelte'

  export let department: Ref<Department> | undefined = undefined

  const dispatch = createEventDispatcher()
  const client = getClient()

  let title: string = ''
  let description: Markup = EmptyMarkup
  let status: WorkflowStatus = WorkflowStatus.Draft
  let assignee: Ref<Employee> | undefined = undefined
  let dueDate: number | undefined = undefined
  let descriptionBox: AttachmentStyledBox
  
  const objectId = generateId()

  export function canClose (): boolean {
    return title === ''
  }

  async function createWorkflow () {
    await client.createDoc(hr.class.Workflow, core.space.Workspace, {
      title,
      description,
      status,
      department,
      assignee,
      dueDate
    })
    await descriptionBox.createAttachments()
    dispatch('close', objectId)
  }

  const manager = createFocusManager()
</script>

<FocusHandler {manager} />
<Card
  label={hr.string.CreateWorkflow}
  okAction={createWorkflow}
  canSave={!!title}
  on:close={() => {
    dispatch('close')
  }}
  on:changeContent
>
  <div class="flex-row-center clear-mins">
    <div class="mr-3">
      <Button focusIndex={1} icon={hr.icon.Workflow} size={'medium'} kind={'link-bordered'} noFocus />
    </div>
    <div class="clear-mins flex-grow">
      <EditBox
        focusIndex={2}
        bind:value={title}
        placeholder={hr.string.Title}
        kind={'large-style'}
        autoFocus
      />
    </div>
  </div>

  <svelte:fragment slot="header">
    <DepartmentEditor 
      label={hr.string.Department} 
      bind:value={department} 
      kind={'regular'} 
      size={'large'}
      allowDeselect={true}
    />
    <EmployeeBox
      focusIndex={3}
      label={hr.string.Assignee}
      placeholder={hr.string.Assignee}
      kind={'regular'}
      size={'large'}
      bind:value={assignee}
      allowDeselect
      showNavigate={false}
    />
  </svelte:fragment>

  <svelte:fragment slot="pool">
    <AttachmentStyledBox
      bind:this={descriptionBox}
      {objectId}
      _class={hr.class.Workflow}
      space={core.space.Workspace}
      alwaysEdit
      showButtons={false}
      kind={'indented'}
      enableBackReferences={false}
      bind:content={description}
      placeholder={core.string.Description}
      focusIndex={4}
    />
  </svelte:fragment>
</Card>


