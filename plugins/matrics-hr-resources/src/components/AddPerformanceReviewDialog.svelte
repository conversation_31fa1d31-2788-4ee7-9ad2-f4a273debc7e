<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import { Staff, ReviewType, ReviewStatus } from '@hcengineering/matrics-hr'
  import { getClient, MessageBox } from '@hcengineering/presentation'
  import { ModernDialog, EditBox, DropdownLabelsIntl } from '@hcengineering/ui'
  import { DateEditor } from '@hcengineering/view-resources'
  import { Employee } from '@hcengineering/contact'
  import hr from '../plugin'

  export let employee: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let title = ''
  let reviewType: ReviewType = ReviewType.Annual
  let status: ReviewStatus = ReviewStatus.NotStarted
  let reviewPeriodStart: number = Date.now()
  let reviewPeriodEnd: number = Date.now()
  let dueDate: number | null = null
  let overallRating: number | null = null
  let strengths = ''
  let areasForImprovement = ''
  let goals = ''
  let feedback = ''

  const reviewTypes = [
    { id: ReviewType.Annual, label: hr.string.ReviewTypeAnnual },
    { id: ReviewType.MidYear, label: hr.string.ReviewTypeMidYear },
    { id: ReviewType.Probation, label: hr.string.ReviewTypeProbation },
    { id: ReviewType.ProjectBased, label: hr.string.ReviewTypeProjectBased },
    { id: ReviewType.Custom, label: hr.string.ReviewTypeCustom }
  ]

  const reviewStatuses = [
    { id: ReviewStatus.NotStarted, label: hr.string.ReviewStatusNotStarted },
    { id: ReviewStatus.InProgress, label: hr.string.ReviewStatusInProgress },
    { id: ReviewStatus.Completed, label: hr.string.ReviewStatusCompleted },
    { id: ReviewStatus.Cancelled, label: hr.string.ReviewStatusCancelled }
  ]

  async function save (): Promise<void> {
    await client.addCollection(
      hr.class.PerformanceReview,
      employee.space,
      employee._id as Ref<Staff>,
      hr.mixin.Staff,
      'reviews',
      {
        title,
        reviewType,
        status,
        reviewPeriodStart,
        reviewPeriodEnd,
        dueDate: dueDate ?? undefined,
        reviewer: client.getHierarchy().getAccount()._id as Ref<Employee>,
        overallRating: overallRating ?? undefined,
        strengths,
        areasForImprovement,
        goals,
        feedback
      }
    )
    dispatch('close', true)
  }

  $: canSave = title.trim().length > 0
</script>

<ModernDialog
  label={hr.string.AddReview}
  canSubmit={canSave}
  on:submit={save}
  on:close={() => dispatch('close')}
>
  <div class="form">
    <div class="form-row">
      <span class="label">Title *</span>
      <EditBox bind:value={title} placeholder={'e.g., Annual Review 2025'} />
    </div>

    <div class="form-row">
      <span class="label">Review Type</span>
      <DropdownLabelsIntl
        items={reviewTypes}
        selected={reviewType}
        on:selected={(e) => reviewType = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Status</span>
      <DropdownLabelsIntl
        items={reviewStatuses}
        selected={status}
        on:selected={(e) => status = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Review Period Start</span>
      <DateEditor value={reviewPeriodStart} type={undefined} onChange={(v) => (reviewPeriodStart = v)} />
    </div>

    <div class="form-row">
      <span class="label">Review Period End</span>
      <DateEditor value={reviewPeriodEnd} type={undefined} onChange={(v) => (reviewPeriodEnd = v)} />
    </div>

    <div class="form-row">
      <span class="label">Due Date (Optional)</span>
      <DateEditor value={dueDate} type={undefined} onChange={(v) => (dueDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Overall Rating (1-5)</span>
      <EditBox bind:value={overallRating} placeholder={'1-5'} format={'number'} />
    </div>

    <div class="form-row">
      <span class="label">Strengths</span>
      <EditBox bind:value={strengths} placeholder={'What does this employee do well?'} />
    </div>

    <div class="form-row">
      <span class="label">Areas for Improvement</span>
      <EditBox bind:value={areasForImprovement} placeholder={'What could be improved?'} />
    </div>

    <div class="form-row">
      <span class="label">Goals</span>
      <EditBox bind:value={goals} placeholder={'Goals for next period...'} />
    </div>

    <div class="form-row">
      <span class="label">Feedback</span>
      <EditBox bind:value={feedback} placeholder={'Additional feedback...'} />
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
    max-height: 70vh;
    overflow-y: auto;
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--theme-caption-color);
    }
  }
</style>
