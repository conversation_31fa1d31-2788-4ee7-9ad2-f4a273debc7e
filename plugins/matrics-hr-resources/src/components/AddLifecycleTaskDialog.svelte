<!--
// Copyright © 2025 Hardcore Engineering Inc.
// Licensed under the Eclipse Public License, Version 2.0
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import type { Ref } from '@hcengineering/core'
  import { Staff, TaskCategory, TaskStatus } from '@hcengineering/matrics-hr'
  import { getClient } from '@hcengineering/presentation'
  import { ModernDialog, EditBox, DropdownLabelsIntl } from '@hcengineering/ui'
  import { DateEditor } from '@hcengineering/view-resources'
  import hr from '../plugin'

  export let employee: Staff

  const client = getClient()
  const dispatch = createEventDispatcher()

  let category: TaskCategory = TaskCategory.Onboarding
  let title = ''
  let description = ''
  let status: TaskStatus = TaskStatus.NotStarted
  let dueDate: number | null = null
  let order = 1

  const categories = [
    { id: TaskCategory.Onboarding, label: hr.string.TaskCategoryOnboarding },
    { id: TaskCategory.Offboarding, label: hr.string.TaskCategoryOffboarding }
  ]

  const statuses = [
    { id: TaskStatus.NotStarted, label: hr.string.TaskStatusNotStarted },
    { id: TaskStatus.InProgress, label: hr.string.TaskStatusInProgress },
    { id: TaskStatus.Completed, label: hr.string.TaskStatusCompleted },
    { id: TaskStatus.Skipped, label: hr.string.TaskStatusSkipped }
  ]

  async function save (): Promise<void> {
    await client.addCollection(
      hr.class.EmployeeLifecycleTask,
      employee.space,
      employee._id as Ref<Staff>,
      hr.mixin.Staff,
      'lifecycleTasks',
      {
        category,
        title,
        description,
        status,
        dueDate: dueDate ?? undefined,
        order
      }
    )
    dispatch('close', true)
  }

  $: canSave = title.trim().length > 0
</script>

<ModernDialog
  label={hr.string.AddTask}
  canSubmit={canSave}
  on:submit={save}
  on:close={() => dispatch('close')}
>
  <div class="form">
    <div class="form-row">
      <span class="label">Category</span>
      <DropdownLabelsIntl
        items={categories}
        selected={category}
        on:selected={(e) => category = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Title *</span>
      <EditBox bind:value={title} placeholder={'e.g., Complete I-9 form, Set up workspace'} />
    </div>

    <div class="form-row">
      <span class="label">Description</span>
      <EditBox bind:value={description} placeholder={'Task details...'} />
    </div>

    <div class="form-row">
      <span class="label">Status</span>
      <DropdownLabelsIntl
        items={statuses}
        selected={status}
        on:selected={(e) => status = e.detail}
      />
    </div>

    <div class="form-row">
      <span class="label">Due Date</span>
      <DateEditor value={dueDate} type={undefined} onChange={(v) => (dueDate = v)} />
    </div>

    <div class="form-row">
      <span class="label">Order</span>
      <EditBox bind:value={order} placeholder={'1'} format={'number'} />
    </div>
  </div>
</ModernDialog>

<style lang="scss">
  .form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }

  .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .label {
      font-size: 0.875rem;
      font-weight: 500;
      color: var(--theme-caption-color);
    }
  }
</style>
