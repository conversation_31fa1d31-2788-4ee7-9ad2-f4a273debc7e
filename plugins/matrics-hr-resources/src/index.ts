//
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { type Resources } from '@hcengineering/platform'
import CreateDepartment from './components/CreateDepartment.svelte'
import DepartmentEditor from './components/DepartmentEditor.svelte'
import DepartmentStaff from './components/DepartmentStaff.svelte'
import EditDepartment from './components/EditDepartment.svelte'
import EditRequest from './components/EditRequest.svelte'
import Schedule from './components/Schedule.svelte'
import Structure from './components/Structure.svelte'
import TzDatePresenter from './components/TzDatePresenter.svelte'
import TzDateEditor from './components/TzDateEditor.svelte'
import DepartmentPresenter from './components/DepartmentPresenter.svelte'
import RequestPresenter from './components/RequestPresenter.svelte'
import { showPopup } from '@hcengineering/ui'
import { type Request } from '@hcengineering/matrics-hr'
import EditRequestType from './components/EditRequestType.svelte'
import DepartmentRefPresenter from './components/DepartmentRefPresenter.svelte'
// Office components
import CreateOffice from './components/CreateOffice.svelte'
import EditOffice from './components/EditOffice.svelte'
import OfficePresenter from './components/OfficePresenter.svelte'
import OfficeEditor from './components/OfficeEditor.svelte'
// Approval components
import RequestStatusPresenter from './components/RequestStatusPresenter.svelte'
import ApprovalButtons from './components/ApprovalButtons.svelte'
// Policy components
import CreatePolicy from './components/CreatePolicy.svelte'
import EditPolicy from './components/EditPolicy.svelte'
import PolicyPresenter from './components/PolicyPresenter.svelte'
import PolicyList from './components/PolicyList.svelte'
// Workflow components
import CreateWorkflow from './components/CreateWorkflow.svelte'
import EditWorkflow from './components/EditWorkflow.svelte'
import WorkflowPresenter from './components/WorkflowPresenter.svelte'
import WorkflowList from './components/WorkflowList.svelte'
import WorkflowStepPresenter from './components/WorkflowStepPresenter.svelte'
// Settings components
import OfficesList from './components/OfficesList.svelte'
import PeopleCultureSettings from './components/settings/PeopleCultureSettings.svelte'
import PeopleCultureSettingsNavigation from './components/settings/PeopleCultureNavigation.svelte'
import TimeOffPolicyList from './components/TimeOffPolicyList.svelte'
import EditTimeOffPolicy from './components/EditTimeOffPolicy.svelte'

async function editRequestType (object: Request): Promise<void> {
  showPopup(EditRequestType, { object })
}

export default async (): Promise<Resources> => ({
  component: {
    Structure,
    CreateDepartment,
    EditDepartment,
    DepartmentStaff,
    DepartmentEditor,
    Schedule,
    EditRequest,
    TzDatePresenter,
    TzDateEditor,
    RequestPresenter,
    EditRequestType,
    DepartmentPresenter,
    DepartmentRefPresenter,
    // Office components
    CreateOffice,
    EditOffice,
    OfficePresenter,
    OfficeEditor,
    // Approval components
    RequestStatusPresenter,
    ApprovalButtons,
    // Policy components
    CreatePolicy,
    EditPolicy,
    PolicyPresenter,
    PolicyList,
    // Workflow components
    CreateWorkflow,
    EditWorkflow,
    WorkflowPresenter,
    WorkflowList,
    WorkflowStepPresenter,
    // Settings
    OfficesList,
    PeopleCultureSettings,
    PeopleCultureSettingsNavigation,
    TimeOffPolicyList,
    EditTimeOffPolicy
  },
  actionImpl: {
    EditRequestType: editRequestType
  }
})
