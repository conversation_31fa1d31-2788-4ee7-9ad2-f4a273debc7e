<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { get } from 'svelte/store'
  import activityPlugin from '@hcengineering/activity'
  import { AnnotationActivity, Label as LabelType, LabelWithColors } from '@hcengineering/annotationtool'
  import { getCurrentAccount } from '@hcengineering/core'
  import { getClient } from '@hcengineering/presentation'
  import { Project } from '@hcengineering/tasker'
  import { Component, Label, Scroller, Spinner, getPlatformColorDef, themeStore } from '@hcengineering/ui'
  import { fetchEpisodeDetails, fetchEpisodeHistory, fetchStripHistory } from '../../api'
  import { createEpisodeLabellingStore } from '../../episodeLabellingStore'
  import annotationTool from '../../plugin'
  import { createSubmitLabelsStore } from '../../submitLabelsStore'
  import { Episode, LabellingHistory, Strip, StripLabellingConfig } from '../../types'
  import EpisodeCard from '../annotate/components/EpisodeCard.svelte'
  import EpisodeStrip from '../annotate/components/EpisodeStrip.svelte'
  import LabellingHistoryEntry from './components/LabellingHistoryEntry.svelte'
  import {
    snapToClosestEndTimestamp,
    snapToClosestStartTimestamp,
    snapTimestampToExistingSegments
  } from '../../utils/segmentSnapping'
  import { determineDefaultLabelingMode } from '../../utils/labelModeUtils'
  import { ValidationResult, ExistingSegment } from '../../utils/segmentValidation'

  export let episodeId: string | null = null
  export let stripId: string | null = null
  export let catalog: string
  export let labels: LabelType[] = []
  export let project: Project
  export let isPreview: boolean = false
  export let annotatorAccessLevel: string

  export let isEpisodeActivity = stripId == null || stripId === ''

  const dispatch = createEventDispatcher()

  const blurExternalActivity = !annotatorAccessLevel || annotatorAccessLevel === 'level1'

  let history: LabellingHistory[] = []
  let activity: AnnotationActivity | undefined = undefined
  let activityBoundary: HTMLElement | undefined = undefined
  let loading: boolean = false
  let activityLoading: boolean = false
  let episode: Episode | undefined = undefined
  let strip: Strip | undefined = undefined
  let nextStrip: Strip | undefined = undefined
  let episodeStore: any | undefined = undefined
  let episodeLoading: boolean = false
  let annotationsView: 'my' | 'all' = 'my'
  let episodeStripRefs: EpisodeStrip[] = []
  let episodeLabellingStore: any
  let episodeLabellingStoreActions: any
  let segmentValidations: ValidationResult[] = []

  // Multi-strip segmenting variables
  let selectedLabel: LabelWithColors | null = null
  let lastAppliedLabel: LabelWithColors | null = null
  let startSegmentStrip: string | null = null
  let firstClickTimestamp: number | null = null
  let secondClickTimestamp: number | null = null


  const client = getClient()

  async function createActivity(episodeId: string | null, stripId: string | null, project: Project) {
    activityLoading = true

    try {
      activity = await client.findOne(annotationTool.class.AnnotationActivity, {
        episodeId,
        ...(stripId != null && stripId !== '' ? { stripId } : { stripId: '' }),
        projectId: project._id,
        datasetId: catalog
      })
      if (!activity) {
        const id = await client.createDoc(annotationTool.class.AnnotationActivity, project.space, {
          episodeId,
          stripId: stripId == null ? '' : stripId,
          projectId: project._id,
          datasetId: catalog,
          // @ts-ignore
          space: project._id
        })

        activity = await client.findOne(annotationTool.class.AnnotationActivity, { _id: id as any })
      }
    } catch (error) {
      console.error(error)
    }
    activityLoading = false
  }

  async function getEpisodeHistory(episodeId: string, catalog: string) {
    loading = true
    try {
      const response = await fetchEpisodeHistory(episodeId, catalog)
      history = response
    } catch (error) {
      console.error(error)
    }
    loading = false
  }

  async function getStripHistory(stripId: string, catalog: string) {
    loading = true
    try {
      const response = await fetchStripHistory(stripId, catalog)
      history = response
    } catch (error) {
      console.error(error)
    }
    loading = false
  }

  async function getEpisodeAndPrepareForPreview(episodeId: string, stripId: string, catalog: string) {
    episodeLoading = true

    try {
      episode = await fetchEpisodeDetails(catalog, episodeId)

      if (!episode) return

      const { addToSubmitQueue } = createSubmitLabelsStore()

      episodeStore = createEpisodeLabellingStore(
        episode,
        getCurrentAccount().uuid,
        project.datasetID ?? '',
        addToSubmitQueue
      )

      if (stripId) {
        strip = episode?.strips.find((strip: Strip) => strip.id === stripId) ?? undefined
        nextStrip = episode?.strips.find((strip: Strip) => strip.id === stripId + 1) ?? undefined
      }
    } catch (error) {
      console.error(error)
    }

    episodeLoading = false
  }

  const onStripLabelSelect = (label: LabelType, stripId: string) => {
    if (selectedLabel?.label !== label.label) {
      const dynamicPlatformColor = getPlatformColorDef(label?.color, $themeStore.dark)
      const whiteThemePlatformColor = getPlatformColorDef(label?.color, false)
      const borderColor = dynamicPlatformColor.color
      const backgroundColor = whiteThemePlatformColor.background || 'white'
      const textColor = whiteThemePlatformColor.title || 'black'

      selectedLabel = {
        ...label,
        borderColor,
        backgroundColor,
        textColor,
        stripId
      } as any

      lastAppliedLabel = null
      startSegmentStrip = null
      firstClickTimestamp = null
      secondClickTimestamp = null      
    } else {
      selectedLabel = null
      lastAppliedLabel = null
      startSegmentStrip = null
      firstClickTimestamp = null
      secondClickTimestamp = null
    }
  }

  const collectSegmentsForSnapping = (stripId: string, stripIndex: number): ExistingSegment[] => {
    if (!episodeLabellingStore) {
      return []
    }

    const storeValue = get(episodeLabellingStore)
    const stripStore: StripLabellingConfig | undefined = storeValue?.strips?.[stripId]
    let existingLabels: ExistingSegment[] = []

    if (stripStore?.allLabels) {
      existingLabels = Object.values(stripStore.allLabels)
        .flat()
        .filter((label): label is ExistingSegment => Boolean(label)) as ExistingSegment[]
    }

    if (stripIndex !== -1) {
      const extraTimeContext = (episodeStripRefs[stripIndex] as any)?.getExtraTimeContext?.()
      if (extraTimeContext?.moreSecondsLoaded && extraTimeContext.nextStripAllLabels) {
        const nextStripLabels = Object.values(extraTimeContext.nextStripAllLabels)
          .flat()
          .filter((label): label is ExistingSegment => Boolean(label)) as ExistingSegment[]
        existingLabels = existingLabels.concat(nextStripLabels)
      }
    }

    return existingLabels
  }

  const snapClickTimestamp = (timestamp: number, stripId: string, stripIndex: number): number => {
    if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
      return timestamp
    }

    const existingLabels = collectSegmentsForSnapping(stripId, stripIndex)
    if (existingLabels.length === 0) {
      return timestamp
    }

    return snapTimestampToExistingSegments(timestamp, existingLabels).snappedTimestamp
  }

  const triggerSnapVisualFeedback = (stripIndex: number, target: 'first' | 'second', didSnap: boolean) => {
    if (!didSnap || stripIndex === -1) {
      return
    }

    const stripRef = episodeStripRefs[stripIndex]
    stripRef?.triggerSnapAnimation?.(target)
  }

  const onStripClick = (event: MouseEvent, ctx: any, stripId: string) => {
    if (selectedLabel === null) return

    // Get the click position relative to the HTML div
    const target = event.currentTarget as HTMLElement
    if (!target) return

    const rect = target.getBoundingClientRect()
    const offsetX = event.clientX - rect.left

    const xVal = ctx.xScale.invert(offsetX)
    const timestamp = xVal instanceof Date ? xVal.getTime() : xVal
    const stripIndex = episode?.strips ? episode.strips.findIndex((strip) => strip.id === stripId) : -1
    let snappedTimestamp = snapClickTimestamp(timestamp, stripId, stripIndex)
    let didSnap = snappedTimestamp !== timestamp

    if (firstClickTimestamp === null) {
      startSegmentStrip = stripId
      firstClickTimestamp = snappedTimestamp
      triggerSnapVisualFeedback(stripIndex, 'first', didSnap)
    } else {
      const previousFirstTimestamp = firstClickTimestamp
      if (snappedTimestamp === firstClickTimestamp) {
        snappedTimestamp = timestamp
        didSnap = false
      }

      if (snappedTimestamp < previousFirstTimestamp) {
        secondClickTimestamp = firstClickTimestamp
        firstClickTimestamp = snappedTimestamp
        startSegmentStrip = stripId
        triggerSnapVisualFeedback(stripIndex, 'first', didSnap)
      } else {
        secondClickTimestamp = snappedTimestamp
        triggerSnapVisualFeedback(stripIndex, 'second', didSnap)
      }

      // Find the strip index and trigger validation on the correct strip
      const startStripIndex = episode?.strips?.findIndex((strip) => strip.id === startSegmentStrip) ?? -1
      if (
        startStripIndex !== -1 &&
        episodeStripRefs[startStripIndex] &&
        firstClickTimestamp !== null &&
        secondClickTimestamp !== null
      ) {
        episodeStripRefs[startStripIndex].triggerSegmentValidation(firstClickTimestamp, secondClickTimestamp)
      }
    }
  }

  const onLeftOrRightStripClick = (event: MouseEvent, ctx: any, side: 'left' | 'right', stripId: string) => {
    if (selectedLabel === null) return

    const strip = episode?.strips.find((strip) => strip.id === stripId)

    if (!strip) return

    const stripTimestamps = strip.timestamps

    if (
      (side === 'left' && firstClickTimestamp === stripTimestamps[0]) ||
      (side === 'right' && firstClickTimestamp === stripTimestamps[stripTimestamps.length - 1])
    ) {
      return
    }

    if (firstClickTimestamp === null) {
      firstClickTimestamp = side === 'left' ? stripTimestamps[0] : stripTimestamps[stripTimestamps.length - 1]
    } else {
      secondClickTimestamp = side === 'left' ? stripTimestamps[0] : stripTimestamps[stripTimestamps.length - 1]
    }

    if(firstClickTimestamp !== null && secondClickTimestamp !== null && secondClickTimestamp < firstClickTimestamp ) {
      const tmp = firstClickTimestamp
      firstClickTimestamp = secondClickTimestamp
      secondClickTimestamp = tmp
    }

    // Find the strip index and trigger validation on the correct strip
    const stripIndex = episode?.strips.findIndex((strip) => strip.id === startSegmentStrip)
    if (stripIndex != null && stripIndex !== -1 && episodeStripRefs[stripIndex] && firstClickTimestamp !== null && secondClickTimestamp !== null) {
      episodeStripRefs[stripIndex].triggerSegmentValidation(firstClickTimestamp, secondClickTimestamp)
    }
  }

  const handleApproveSegmentation = (overrideSelectedLabel?: LabelWithColors, overrideFirstClick?: number, overrideSecondClick?: number, extraSecondsContext?: { moreSecondsLoaded: boolean, originalStripEndTime: number, currentStripId: string }) => {
    let selectedLabell = overrideSelectedLabel ?? selectedLabel
    let firstTimestamp = overrideFirstClick ?? firstClickTimestamp
    let secondTimestamp = overrideSecondClick ?? secondClickTimestamp

    if (!selectedLabell || firstTimestamp === null || secondTimestamp === null) {
      selectedLabel = null
      lastAppliedLabel = null
      firstClickTimestamp = null
      secondClickTimestamp = null
      return
    }

    const labelStart = Math.min(firstTimestamp, secondTimestamp)
    const labelEnd = Math.max(firstTimestamp, secondTimestamp)

    const currentTime = new Date().getTime()

    // First, check if this segment spans multiple strips using original boundaries
    // We need to handle cross-strip segments correctly regardless of which strip is in extra seconds mode
    const affectedStrips = episode?.strips.filter(strip => {
      const stripStart = strip.timestamps[0]
      const stripEnd = strip.timestamps[strip.timestamps.length - 1]
      
      const segmentValidation = segmentValidations.find((validation) => validation.stripId === strip.id)

      if(segmentValidation && !segmentValidation.isValid) { 
        return false;
      }
      
      // Check if segment overlaps with this strip's original boundaries
      return !(labelEnd <= stripStart || labelStart >= stripEnd)
    })
    
    if(affectedStrips == null) return

    // If segment spans multiple strips, handle with cross-strip logic using proper boundaries
    if (affectedStrips.length > 1) {
      // Sort strips by their start time to ensure proper ordering
      affectedStrips.sort((a, b) => a.timestamps[0] - b.timestamps[0])
      
      affectedStrips.forEach((strip, index) => {
        const stripStart = strip.timestamps[0]
        const stripEnd = strip.timestamps[strip.timestamps.length - 1]
        
        let segmentStart: number
        let segmentEnd: number
        
        if (index === 0) {
          // First strip: from segment start to the boundary with next strip
          segmentStart = labelStart
          // Use the start of the next strip as the boundary (this is the true split point)
          const nextStrip = affectedStrips[index + 1]
          const boundaryPoint = nextStrip ? nextStrip.timestamps[0] : stripEnd
          segmentEnd = Math.min(boundaryPoint, labelEnd)
        } else if (index === affectedStrips.length - 1) {
          // Last strip: from the boundary to segment end
          segmentStart = Math.max(stripStart, labelStart)
          segmentEnd = labelEnd
        } else {
          // Middle strip (rare case): from previous boundary to next boundary
          const nextStrip = affectedStrips[index + 1]
          const boundaryPoint = nextStrip ? nextStrip.timestamps[0] : stripEnd
          segmentStart = stripStart
          segmentEnd = boundaryPoint
        }
        
        // Only add if there's actually a segment portion in this strip
        if (segmentStart < segmentEnd) {
          episodeLabellingStoreActions.addRemoveLabelStrip(strip.id, {
            label: selectedLabell.value,
            timestamp: currentTime,
            start: segmentStart,
            end: segmentEnd
          })
        }
      })
    }
    // Single strip segment - check if we're in extra seconds mode for special handling
    else if (episode != null && extraSecondsContext?.moreSecondsLoaded) {
      const currentStripIndex = episode.strips.findIndex(strip => strip.id === extraSecondsContext.currentStripId)
      
      if (currentStripIndex !== -1) {
        const currentStrip = episode.strips[currentStripIndex]
        const currentStripStart = currentStrip.timestamps[0]
        const currentStripOriginalEnd = extraSecondsContext.originalStripEndTime
        
        // Determine if this segment crosses strip boundaries (using original boundaries)
        const crossesForward = labelEnd > currentStripOriginalEnd
        const crossesBackward = labelStart < currentStripStart
        
        if (crossesForward) {
          // Segment extends from current strip into next strip
          // Add portion to current strip (start to original boundary)
          episodeLabellingStoreActions.addRemoveLabelStrip(extraSecondsContext.currentStripId, {
            label: selectedLabell.value,
            timestamp: currentTime,
            start: labelStart,
            end: currentStripOriginalEnd
          })

          // Add portion to next strip (original boundary to end)
          const nextStripIndex = currentStripIndex + 1
          if (nextStripIndex < episode.strips.length) {
            const nextStrip = episode.strips[nextStripIndex]
            
            episodeLabellingStoreActions.addRemoveLabelStrip(nextStrip.id, {
              label: selectedLabell.value,
              timestamp: currentTime,
              start: currentStripOriginalEnd,
              end: labelEnd
            })
          }
        } else if (crossesBackward) {
          // Segment extends from previous strip into current strip
          const previousStripIndex = currentStripIndex - 1
          if (previousStripIndex >= 0) {
            const previousStrip = episode.strips[previousStripIndex]
            const previousStripOriginalEnd = previousStrip.timestamps[previousStrip.timestamps.length - 1]
            
            // Add portion to previous strip (start to previous strip's original end)
            episodeLabellingStoreActions.addRemoveLabelStrip(previousStrip.id, {
              label: selectedLabell.value,
              timestamp: currentTime,
              start: labelStart,
              end: previousStripOriginalEnd
            })
            
            // Add portion to current strip (current strip start to end)
            episodeLabellingStoreActions.addRemoveLabelStrip(extraSecondsContext.currentStripId, {
              label: selectedLabell.value,
              timestamp: currentTime,
              start: currentStripStart,
              end: labelEnd
            })
          }
        } else {
          // Segment is entirely within current strip's original boundaries
          episodeLabellingStoreActions.addRemoveLabelStrip(extraSecondsContext.currentStripId, {
            label: selectedLabell.value,
            timestamp: currentTime,
            start: labelStart,
            end: labelEnd
          })
        }
      }
    } 
    // Single strip: add entire segment to that strip
    else if (affectedStrips.length > 0) {
      episodeLabellingStoreActions.addRemoveLabelStrip(affectedStrips[0].id, {
        label: selectedLabell.value,
        timestamp: currentTime,
        start: labelStart,
        end: labelEnd
      })
    }
    // If no strips are affected and at least one strip is invalid, do nothing
    else if (affectedStrips.length === 0 && segmentValidations.some((validation) => !validation.isValid)) {
      return
    }

    lastAppliedLabel = selectedLabel
    selectedLabel = null
    startSegmentStrip = null
    firstClickTimestamp = null
    secondClickTimestamp = null
    
    if(episode != null) {
      // Dispatch event to notify parent that a label was applied to this episode
      dispatch('label-applied', { episodeId: episode.episode_id })
    }
  }

  const handleDisapproveSegmentation = () => {
    firstClickTimestamp = null
    secondClickTimestamp = null
  }

  const handleSnapLeft = (
    stripStore: StripLabellingConfig, 
    strip: Strip, 
    extraTimeContext?: {
      moreSecondsLoaded: boolean
      originalStripEndTime: number
      nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]>
    }
  ) => {
    if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
      return
    }

    if (firstClickTimestamp !== null && secondClickTimestamp !== null) {
      const segmentStart = Math.min(firstClickTimestamp, secondClickTimestamp)
      const segmentEnd = Math.max(firstClickTimestamp, secondClickTimestamp)
      const previousStart = segmentStart

      // Get existing labels from current strip
      let existingLabels = stripStore?.labels ? Object.values(stripStore.labels).flat() : []
      
      // If in extra time mode, also include labels from next strip that might be relevant for snapping
      if (extraTimeContext?.moreSecondsLoaded && extraTimeContext.nextStripAllLabels) {
        const nextStripLabels = Object.values(extraTimeContext.nextStripAllLabels).flat()
        existingLabels = [...existingLabels, ...nextStripLabels]
      }

      const snapResult = snapToClosestEndTimestamp(segmentStart, segmentEnd, existingLabels)

      let newStart = snapResult.snappedTimestamp
      if (snapResult.snappedTimestamp >= segmentStart) {
        newStart = strip.timestamps[0]
      }
      

      secondClickTimestamp = Math.max(firstClickTimestamp, secondClickTimestamp)
      firstClickTimestamp = newStart

      const didSnap = newStart !== previousStart
      const stripIndex = episode?.strips ? episode.strips.findIndex((stripItem) => stripItem.id === strip.id) : -1
      triggerSnapVisualFeedback(stripIndex, 'first', didSnap)
    }
  }

  const handleSnapRight = (
    stripStore: StripLabellingConfig, 
    strip: Strip,
    extraTimeContext?: {
      moreSecondsLoaded: boolean
      originalStripEndTime: number
      nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]>
    }
  ) => {
    if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
      return
    }

    if (firstClickTimestamp !== null && secondClickTimestamp !== null) {

      const segmentStart = Math.min(firstClickTimestamp, secondClickTimestamp)
      const segmentEnd = Math.max(firstClickTimestamp, secondClickTimestamp)

      // Get existing labels from current strip
      let existingLabels = stripStore?.labels ? Object.values(stripStore.labels).flat() : []
      
      // If in extra time mode, also include labels from next strip that might be relevant for snapping
      if (extraTimeContext?.moreSecondsLoaded && extraTimeContext.nextStripAllLabels) {
        const nextStripLabels = Object.values(extraTimeContext.nextStripAllLabels).flat()
        existingLabels = [...existingLabels, ...nextStripLabels]
      }

      const snapResult = snapToClosestStartTimestamp(segmentStart, segmentEnd, existingLabels)

      let newEnd = snapResult.snappedTimestamp
      if (snapResult.snappedTimestamp <= segmentEnd) {
        newEnd = strip.timestamps[strip.timestamps.length - 1]
      }

      const originalEnd = segmentEnd
      let didSnap = newEnd !== originalEnd
      if (didSnap && newEnd === segmentStart) {
        newEnd = originalEnd
        didSnap = false
      }

      firstClickTimestamp = Math.min(firstClickTimestamp, secondClickTimestamp)
      secondClickTimestamp = newEnd

      const stripIndex = episode?.strips ? episode.strips.findIndex((stripItem) => stripItem.id === strip.id) : -1
      triggerSnapVisualFeedback(stripIndex, 'second', didSnap)
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'n') {
      e.stopPropagation()
    }
  }

  $: createActivity(episodeId, stripId, project)
  $: if (isEpisodeActivity) {
    getEpisodeHistory(episodeId ?? '', catalog)
  } else {
    getStripHistory(stripId ?? '', catalog)
  }
  $: isPreview && getEpisodeAndPrepareForPreview(episodeId ?? '', stripId ?? '', catalog)
  $: episodeLabellingStore = episodeStore?.episodeLabellingStore
  $: episodeLabellingStoreActions = episodeStore?.episodeLabellingStoreActions
</script>

<div class="modal-content" class:preview={isPreview} on:keydown={handleKeyDown} role="presentation">
  {#if loading || activityLoading || episodeLoading}
    <div class="flex justify-center items-center p-8 episodes-loading">
      <Spinner size="x-large" />
    </div>
  {:else}
    {#if isPreview && (episode != null || strip != null)}
      <div class="preview-container">
        {#if isEpisodeActivity && episode != null}
          <EpisodeCard
            {episode}
            {labels}
            triageLabels={[]}
            viewMode="card"
            catalog={project.datasetID ?? ''}
            {project}
            store={episodeStore}
            {annotatorAccessLevel}
          />
        {:else if strip != null && episode != null}
          {@const stripIndex = episode?.strips.findIndex((s) => s.id === strip?.id)}
          <EpisodeStrip
            {strip}
            stripId={strip.id}
            nextStripId={nextStrip?.id ?? ''}
            episodeId={episode.episode_id}
            episodeStore={episodeStore.episodeLabellingStore}
            episodeStoreActions={episodeStore.episodeLabellingStoreActions}
            stripValues={strip.voltages}
            stripTimestamps={strip.timestamps}
            nextStripTimestamps={nextStrip?.timestamps ?? []}
            nextStripValues={nextStrip?.voltages ?? []}
            triageLabels={[]}
            hasActivity
            {labels}
            {catalog}
            {project}
            {annotatorAccessLevel}
            {onStripClick}
            {onLeftOrRightStripClick}
            {onStripLabelSelect}
            {handleApproveSegmentation}
            {handleDisapproveSegmentation}
            {handleSnapLeft}
            {handleSnapRight}
            bind:firstClickTimestamp
            bind:secondClickTimestamp
            bind:selectedLabel
            bind:lastAppliedLabel
            bind:annotationsView
            bind:this={episodeStripRefs[stripIndex]}
          />
        {/if}
      </div>
    {/if}
    <div
      class="wrapper"
      class:wrapper-preview={isPreview}
      class:wrapper-preview-strip={isPreview && !isEpisodeActivity}
    >
      <div class="activity-container" bind:this={activityBoundary}>
        {#if activity != null}
          <Component
            is={activityPlugin.component.Activity}
            props={{
              object: activity,
              showCommenInput: true,
              shouldScroll: true,
              focusIndex: 1000,
              boundary: activityBoundary
            }}
          />
        {/if}
      </div>
      <div class="vertical-divider" />
      <div class="history-container">
        {#if history.length > 0}
          <Scroller>
            {#each history as entry, index (index)}
              <div class="history-entry">
                <LabellingHistoryEntry {entry} {labels} {isEpisodeActivity} {blurExternalActivity} />
              </div>
              {#if index !== history.length - 1}
                <div class="horizontal-divider" />
              {/if}
            {/each}
          </Scroller>
        {:else}
          <div class="flex justify-center items-center h-full">
            <Label label={annotationTool.string.NoHistory} />
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>

<style lang="scss">
  .modal-content {
    width: 60vw;
    height: 60vh;
    max-height: 60vh;
  }

  .preview {
    padding: 20px;
    width: 100% !important;
    height: calc(100vh - 38px) !important;
    max-height: calc(100vh - 38px) !important;
  }

  .preview-container {
    overflow-y: auto;
    max-height: 50%;
    margin-bottom: 1rem;
  }

  .wrapper {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    height: 100%;
  }

  .wrapper-preview {
    height: 48%;
  }

  .wrapper-preview-strip {
    height: calc(100% - 450px);
  }

  .activity-container {
    display: flex;
    flex: 5;
  }

  .history-container {
    display: flex;
    flex: 2;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 60vh;
  }

  .horizontal-divider {
    width: 100%;
    height: 1px;
    background-color: var(--theme-divider-color);
  }

  .vertical-divider {
    width: 1px;
    height: 100%;
    background-color: var(--theme-divider-color);
  }

  :global(.activity-container) {
    width: 100%;
  }

  :global(.activity-container .antiSection) {
    width: 100%;
    height: 100%;
  }

  :global(.activity-container .p-activity) {
    width: 100%;
    height: 100%;
  }

  :global(.activity-container .ref-input) {
    padding-bottom: 0 !important;
  }

  :global(.activity-container .ref-input .root) {
    display: none !important;
  }

  :global(.activity-container .activityMessage-actionPopup > button:nth-child(4)) {
    display: none !important;
  }

  :global(.activity-container .antiSection-header__tag) {
    display: none !important;
  }

  :global(.activity-container .w-4) {
    display: none !important;
  }

  :global(.activity-container .buttons-group) {
    display: none !important;
  }

  :global(.activity-container .p-activity) {
    height: 100%;
  }
</style>
