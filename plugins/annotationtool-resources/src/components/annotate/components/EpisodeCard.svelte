<script lang="ts">
  import {
    Label as LabelType,
    LabelWithColors,
    TriageLabel as TriageLabelType,
    SegmentInteresting,
    SegmentComment
  } from '@hcengineering/annotationtool'
  import chunter from '@hcengineering/chunter'
  import { getClient } from '@hcengineering/presentation'
  import { Project } from '@hcengineering/tasker'
  import {
    Button,
    ButtonIcon,
    CheckBox,
    closePopup,
    Icon,
    Label,
    showPopup,
    getPlatformColorDef,
    themeStore,
    tooltip as tp
  } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import { accountUuidByPrimaryPersonIdStore } from '@hcengineering/contact-resources'
  import { createEventDispatcher, tick, onDestroy } from 'svelte'
  import annotationTool from '../../../plugin'
  import { Episode, EpisodeLabellingStoreConfig, StripLabellingConfig, Strip } from '../../../types'
  import { getEpisodeMetaItems } from '../../../utils'
  import { generateShuffledArray1to21 } from '../../../utils/labelLayoutUtils'
  import {
    snapToClosestEndTimestamp,
    snapToClosestStartTimestamp,
    snapTimestampToExistingSegments
  } from '../../../utils/segmentSnapping'
  import { determineDefaultLabelingMode } from '../../../utils/labelModeUtils'
  import { buildEpisodeDeepLink, copyToClipboard } from '../../../utils/deepLinkUtils'
  import AnnotationLabel from '../../settings/components/AnnotationLabel.svelte'
  import UserActivityModal from '../../user-activity/UserActivityModal.svelte'
  import { getUnlabeledStrips } from '../../../utils/episodeValidityUtils'
  import { ValidationResult, ExistingSegment } from '../../../utils/segmentValidation'
  import EpisodeStrip from './EpisodeStrip.svelte'
  import { PersonId, AccountUuid } from '@hcengineering/core'

  export let episode: Episode
  export let store: EpisodeLabellingStoreConfig
  export let labels: LabelType[]
  export let triageLabels: TriageLabelType[]
  export let topMargin: boolean = false
  export let viewMode: 'list' | 'card' = 'card'
  export let isConflictResolution: boolean = false
  export let catalog: string
  export let project: Project
  export let annotatorAccessLevel: string
  export let deepLinkStripId: string | null = null

  let segmentComments: SegmentComment[] = []
  let segmentInteresting: SegmentInteresting[] = []

  // Multi-strip segmenting variables
  let selectedLabel: LabelWithColors | null = null
  let lastAppliedLabel: LabelWithColors | null = null
  let startSegmentStrip: string | null = null
  let firstClickTimestamp: number | null = null
  let secondClickTimestamp: number | null = null
  
  // Undo tracking with TTL
  let lastAppliedSegments: Array<{ stripId: string; label: string; start: number; end: number; timestamp: number }> = []
  let lastAppliedTime: number = 0
  const UNDO_TTL_MS = 10000 // 10 seconds
  
  // Undo success banner
  let showUndoSuccessBanner: boolean = false
  let undoBannerTimeout: ReturnType<typeof setTimeout> | undefined
  let undoSelectionPending: boolean = false

  // Point label success banner
  let showPointLabelBanner: boolean = false
  let pointLabelBannerTimeout: ReturnType<typeof setTimeout> | undefined

  const client = getClient()
  const dispatch = createEventDispatcher()

  const shuffledColors = generateShuffledArray1to21()

  let isExpanded: boolean = true
  let hasEpisodeActivity: boolean = false
  let stripActivity: string[] = []
  let zoomLevel: number = viewMode === 'card' ? 0 : 0.75
  let swipeSpeed: number = 25
  let episodeStripRefs: EpisodeStrip[] = []
  let segmentValidations: ValidationResult[] = []
  const SNAP_HIGHLIGHT_TTL = 1500
  let stripSnapFlags: Record<string, { first: boolean; second: boolean }> = {}
  let snappedSegmentHighlights: Record<string, { id: number; start: number; end: number; until: number }[]> = {}
  let snapHighlightIdCounter = 0
  const snapHighlightTimeouts: Record<number, ReturnType<typeof setTimeout>> = {}
  $: annotationsView = (isConflictResolution ? 'all' : 'my') as 'my' | 'all'
  let handledDeepLinkStripId: string | null = null
  let episodeLinkCopied = false
  let episodeCopyTimeout: ReturnType<typeof setTimeout> | undefined
  let episodeYMin: number = -1
  let episodeYMax: number = 1

  const { episodeLabellingStore, episodeLabellingStoreActions } = store

  $: {
    let min = Infinity
    let max = -Infinity

    for (const strip of episode?.strips ?? []) {
      if (!Array.isArray(strip?.voltages)) continue
      for (const raw of strip.voltages ?? []) {
        const value = Number(raw)
        if (!Number.isFinite(value)) continue
        if (value < min) min = value
        if (value > max) max = value
      }
    }

    if (min === Infinity || max === -Infinity) {
      episodeYMin = -1
      episodeYMax = 1
    } else {
      episodeYMin = min
      episodeYMax = max
    }
  }

  const getEpisodeActivity = async (episodeId: string, projectId: string, catalog: string) => {
    if (!episodeId || !projectId || !catalog) return

    try {
      hasEpisodeActivity = false
      stripActivity = []

      const activities = await client.findAll(annotationTool.class.AnnotationActivity, {
        episodeId,
        projectId: projectId as any,
        datasetId: catalog
      })

      if (!activities || activities.length === 0) {
        hasEpisodeActivity = false
        stripActivity = []

        return
      }

      const episodeActivityId = activities.find((activity) => !activity.stripId)?._id
      const activityIds = activities.map((activity) => activity._id)

      const activityMessages = (await client.findAll(chunter.class.ChatMessage, {
        attachedTo: { $in: activityIds }
      })) as any

      activityMessages.forEach((message: any) => {
        if (message.attachedTo === episodeActivityId) {
          hasEpisodeActivity = true
        } else {
          const stripActivityID = activityIds.find((id) => id === message.attachedTo)
          const stripID = activities.find((activity) => activity._id === stripActivityID)?.stripId
          stripActivity.push(stripID ?? '')
        }
      })

      stripActivity = [...stripActivity]
    } catch (error) {
      console.error(error)
    }
  }

  const getSegmentInteresting = async (episode: Episode) => {
    if (!episode.episode_id || !episode.strips.length) {
      segmentInteresting = []
      return
    }

    try {
      const segmentInterestingResponse = await client.findAll(annotationTool.class.SegmentInteresting, {
        stripId: { $in: episode.strips.map((strip) => strip.id) } as any
      })

      segmentInteresting = segmentInterestingResponse.map((segment) => {
        return {
          ...segment,
          createdBy: $accountUuidByPrimaryPersonIdStore.get(segment.createdBy as PersonId) as any
        }
      })

    } catch (error) {
      console.error(error)
      segmentInteresting = []
    }
  }

  const getSegmentComments = async (episode: Episode) => {
    if (!episode.episode_id || !episode.strips.length) {
      segmentComments = []
      return
    }

    try {
      segmentComments = await client.findAll(annotationTool.class.SegmentComment, {
        stripId: { $in: episode.strips.map((strip) => strip.id) } as any
      })
    } catch (error) {
      console.error(error)
      segmentComments = []
    }
  }

  const handleOnKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      isExpanded = true
    }
  }

  const handleOpenEpisodeActivity = () => {
    showPopup(UserActivityModal, {
      episodeId: episode.episode_id,
      stripId: undefined,
      catalog,
      labels: labels,
      hidden: false,
      project,
      annotatorAccessLevel,
      onCancel: closePopup
    })
  }

  const handleEpisodeActivityKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleOpenEpisodeActivity()
    }
  }

  // Function to navigate to first unlabeled strip (no dialog)
  const showValidationError = (unlabeledStrips: string[]) => {
    // Navigate to the first unlabeled strip with visual highlighting
    navigateToStrip(unlabeledStrips[0])
  }

  // Function to navigate to a specific strip
  const navigateToStrip = (stripId: string) => {
    const stripElement = document.querySelector(`[data-strip-id="${stripId}"]`)
    if (stripElement) {
      stripElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })

      // Add simple red border highlight
      stripElement.classList.add('validation-error-highlight')
      setTimeout(() => {
        stripElement.classList.remove('validation-error-highlight')
      }, 5000)
    }
  }

  const validateAllStripsAnnotated = () => {
    if ($episodeLabellingStore.isDoneEpisode) return

    const unlabeledStrips = getUnlabeledStrips(episode, $episodeLabellingStore)
    if (unlabeledStrips.length > 0) {
      showValidationError(unlabeledStrips)

      return false
    }

    return true
  }

  $: if (deepLinkStripId && deepLinkStripId !== handledDeepLinkStripId) {
    const belongsToEpisode = episode.strips.some((strip) => strip.id === deepLinkStripId)

    if (belongsToEpisode) {
      handledDeepLinkStripId = deepLinkStripId
      isExpanded = true

      tick().then(() => {
        navigateToStrip(deepLinkStripId)
      })
    }
  }

  const handleEpisodeMarkedAsDone = (isDone: boolean) => {
    episodeLabellingStoreActions.addRemoveEpisodeDone(isDone)
    dispatch('episode-marked-as-done', { isDone, episodeId: episode.episode_id })
  }

  const handleCopyEpisodeLink = async () => {
    const link = buildEpisodeDeepLink(episode.episode_id)
    if (!link) {
      return
    }

    const copied = await copyToClipboard(link)
    if (!copied) {
      return
    }

    episodeLinkCopied = true
    if (episodeCopyTimeout) {
      clearTimeout(episodeCopyTimeout)
    }
    episodeCopyTimeout = setTimeout(() => {
      episodeLinkCopied = false
    }, 500)
  }

  const handleCopyEpisodeButtonClick = (event: Event) => {
    event.stopPropagation()
    handleCopyEpisodeLink()
  }

  function handleStripZoomOut() {
    if (zoomLevel >= 1) return

    zoomLevel += 0.25
  }

  function handleStripZoomIn() {
    if (zoomLevel <= 0) return

    zoomLevel -= 0.25
  }

  const onStripLabelSelect = (label: LabelType, stripId: string) => {
    if (selectedLabel?.label !== label.label) {
      const dynamicPlatformColor = getPlatformColorDef(label?.color, $themeStore.dark)
      const whiteThemePlatformColor = getPlatformColorDef(label?.color, false)
      const borderColor = dynamicPlatformColor.color
      const backgroundColor = whiteThemePlatformColor.background || 'white'
      const textColor = whiteThemePlatformColor.title || 'black'

      selectedLabel = {
        ...label,
        borderColor,
        backgroundColor,
        textColor,
        stripId
      } as any
      undoSelectionPending = false

      lastAppliedLabel = null
      startSegmentStrip = null
      firstClickTimestamp = null
      secondClickTimestamp = null
      stripSnapFlags = {}
    } else {
      selectedLabel = null
      lastAppliedLabel = null
      startSegmentStrip = null
      firstClickTimestamp = null
      secondClickTimestamp = null
      undoSelectionPending = false
      stripSnapFlags = {}
    }
  }

  const collectSegmentsForSnapping = (stripId: string, stripIndex: number): ExistingSegment[] => {
    const stripStore = $episodeLabellingStore.strips[stripId]
    let existingLabels: ExistingSegment[] = []

    if (stripStore?.allLabels) {
      existingLabels = Object.values(stripStore.allLabels)
        .flat()
        .filter((label): label is ExistingSegment => Boolean(label)) as ExistingSegment[]
    }

    if (stripIndex !== -1) {
      const extraTimeContext = (episodeStripRefs[stripIndex] as any)?.getExtraTimeContext?.()
      if (extraTimeContext?.moreSecondsLoaded && extraTimeContext.nextStripAllLabels) {
        const nextStripLabels = Object.values(extraTimeContext.nextStripAllLabels)
          .flat()
          .filter((label): label is ExistingSegment => Boolean(label)) as ExistingSegment[]
        existingLabels = existingLabels.concat(nextStripLabels)
      }
    }

    return existingLabels
  }

  const snapClickTimestamp = (timestamp: number, stripId: string, stripIndex: number): number => {
    if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
      return timestamp
    }

    const existingLabels = collectSegmentsForSnapping(stripId, stripIndex)
    if (existingLabels.length === 0) {
      return timestamp
    }

    return snapTimestampToExistingSegments(timestamp, existingLabels).snappedTimestamp
  }

  const triggerSnapVisualFeedback = (stripIndex: number, target: 'first' | 'second', didSnap: boolean) => {
    if (!didSnap || stripIndex === -1) {
      return
    }

    const stripIdForIndex = episode.strips[stripIndex]?.id
    if (!stripIdForIndex) {
      return
    }

    const existingFlags = stripSnapFlags[stripIdForIndex] ?? { first: false, second: false }
    const updatedFlags = { ...existingFlags, [target]: true }
    stripSnapFlags = { ...stripSnapFlags, [stripIdForIndex]: updatedFlags }

    const stripRef = episodeStripRefs[stripIndex]
    stripRef?.triggerSnapAnimation?.(target)
  }

  const setHighlightsForStrip = (
    stripId: string,
    highlights: { id: number; start: number; end: number; until: number }[]
  ) => {
    if (highlights.length === 0) {
      const { [stripId]: _removed, ...rest } = snappedSegmentHighlights
      snappedSegmentHighlights = rest
    } else {
      snappedSegmentHighlights = {
        ...snappedSegmentHighlights,
        [stripId]: highlights
      }
    }
  }

  const clearSnapHighlightForSegment = (stripId: string, start: number, end: number) => {
    const currentHighlights = snappedSegmentHighlights[stripId]
    if (!currentHighlights?.length) {
      return
    }

    const remaining: { id: number; start: number; end: number; until: number }[] = []
    currentHighlights.forEach((highlight) => {
      if (highlight.start === start && highlight.end === end) {
        if (snapHighlightTimeouts[highlight.id]) {
          clearTimeout(snapHighlightTimeouts[highlight.id])
          delete snapHighlightTimeouts[highlight.id]
        }
      } else {
        remaining.push(highlight)
      }
    })

    setHighlightsForStrip(stripId, remaining)
  }

  const registerSnapHighlight = (stripId: string, start: number, end: number) => {
    const flags = stripSnapFlags[stripId]
    if (!flags || (!flags.first && !flags.second)) {
      return
    }

    const highlightId = ++snapHighlightIdCounter
    const until = Date.now() + SNAP_HIGHLIGHT_TTL
    const currentHighlights = snappedSegmentHighlights[stripId] ?? []
    currentHighlights.forEach((item) => {
      if (item.start === start && item.end === end) {
        if (snapHighlightTimeouts[item.id]) {
          clearTimeout(snapHighlightTimeouts[item.id])
          delete snapHighlightTimeouts[item.id]
        }
      }
    })
    const filteredHighlights = currentHighlights.filter((item) => !(item.start === start && item.end === end))
    const updatedHighlights = [...filteredHighlights, { id: highlightId, start, end, until }]

    setHighlightsForStrip(stripId, updatedHighlights)

    if (snapHighlightTimeouts[highlightId]) {
      clearTimeout(snapHighlightTimeouts[highlightId])
    }

    snapHighlightTimeouts[highlightId] = setTimeout(() => {
      const current = snappedSegmentHighlights[stripId] ?? []
      const remaining = current.filter((item) => item.id !== highlightId)
      setHighlightsForStrip(stripId, remaining)
      delete snapHighlightTimeouts[highlightId]
    }, SNAP_HIGHLIGHT_TTL)

    stripSnapFlags = {
      ...stripSnapFlags,
      [stripId]: { first: false, second: false }
    }
  }

  const onStripClick = (event: MouseEvent, ctx: any, stripId: string) => {
    if (selectedLabel === null) return

    if (selectedLabel.stripId !== stripId) {
      selectedLabel = {
        ...selectedLabel,
        stripId
      }
    }

    // Get the click position relative to the HTML div
    const target = event.currentTarget as HTMLElement
    if (!target) return

    const rect = target.getBoundingClientRect()
    const offsetX = event.clientX - rect.left

    const xVal = ctx.xScale.invert(offsetX)
    const timestamp = xVal instanceof Date ? xVal.getTime() : xVal
    const stripIndex = episode.strips.findIndex((strip) => strip.id === stripId)
    let snappedTimestamp = snapClickTimestamp(timestamp, stripId, stripIndex)
    let didSnap = snappedTimestamp !== timestamp

    if (firstClickTimestamp === null) {
      startSegmentStrip = stripId
      firstClickTimestamp = snappedTimestamp
      stripSnapFlags = { ...stripSnapFlags, [stripId]: { first: false, second: false } }
      triggerSnapVisualFeedback(stripIndex, 'first', didSnap)

      // In point mode, immediately set second timestamp and auto-apply
      if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
        secondClickTimestamp = snappedTimestamp

        // Find the strip index and trigger validation
        if (stripIndex !== -1) {
          const result = episodeStripRefs[stripIndex].triggerSegmentValidation(snappedTimestamp, snappedTimestamp)
          if (result?.isValid === false) {
            return
          }
          handleApproveSegmentation()
        }
      }
    } else {
      // Segment mode - second click
      const previousFirstTimestamp = firstClickTimestamp
      if (snappedTimestamp === firstClickTimestamp) {
        snappedTimestamp = timestamp
        didSnap = false
      }

      if (snappedTimestamp < previousFirstTimestamp) {
        secondClickTimestamp = firstClickTimestamp
        firstClickTimestamp = snappedTimestamp
        startSegmentStrip = stripId
        triggerSnapVisualFeedback(stripIndex, 'first', didSnap)
      } else {
        secondClickTimestamp = snappedTimestamp
        triggerSnapVisualFeedback(stripIndex, 'second', didSnap)
      }

      episode.strips.forEach((strip, stripIndex) => {
        if(firstClickTimestamp === null || secondClickTimestamp === null) return

        const stripStart = strip.timestamps[0]
        const stripEnd = strip.timestamps[strip.timestamps.length - 1]

        const isAffected = firstClickTimestamp <= stripEnd && secondClickTimestamp >= stripStart

        if (isAffected) {
          const start = firstClickTimestamp < stripStart ? stripStart : firstClickTimestamp
          const end = secondClickTimestamp > stripEnd ? stripEnd : secondClickTimestamp
          const result = episodeStripRefs[stripIndex].triggerSegmentValidation(start, end)
          if (result?.isValid === false) {
            // mark invalid for this strip
            segmentValidations[stripIndex] = result
          }
        }
      })
      
      // Auto-confirm segment after validation
      const allValid = episode.strips.every((strip, stripIndex) => {
        if(firstClickTimestamp === null || secondClickTimestamp === null) return true
        const stripStart = strip.timestamps[0]
        const stripEnd = strip.timestamps[strip.timestamps.length - 1]
        const isAffected = firstClickTimestamp <= stripEnd && secondClickTimestamp >= stripStart
        if (isAffected) {
          return segmentValidations[stripIndex]?.isValid !== false
        }
        return true
      })
      if (allValid) {
        handleApproveSegmentation()
      }
    }
  }

  const onLeftOrRightStripClick = (event: MouseEvent, ctx: any, side: 'left' | 'right', stripId: string) => {
    if (selectedLabel === null) return

    if (selectedLabel.stripId !== stripId) {
      selectedLabel = {
        ...selectedLabel,
        stripId
      }
    }

    const strip = episode.strips.find((strip) => strip.id === stripId)

    if (!strip) return

    const stripTimestamps = strip.timestamps

    if (
      (side === 'left' && firstClickTimestamp === stripTimestamps[0]) ||
      (side === 'right' && firstClickTimestamp === stripTimestamps[stripTimestamps.length - 1])
    ) {
      return
    }

    if (firstClickTimestamp === null) {
      const timestamp = side === 'left' ? stripTimestamps[0] : stripTimestamps[stripTimestamps.length - 1]
      firstClickTimestamp = timestamp
      startSegmentStrip = stripId
      stripSnapFlags = { ...stripSnapFlags, [stripId]: { first: false, second: false } }
      
      // In point mode, immediately apply (only if valid)
      if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
        secondClickTimestamp = timestamp
        
        const stripIndex = episode.strips.findIndex((strip) => strip.id === stripId)
        if (stripIndex !== -1) {
          const result = episodeStripRefs[stripIndex].triggerSegmentValidation(timestamp, timestamp)
          if (result?.isValid === false) {
            return
          }
          handleApproveSegmentation()
        }
      }
    } else {
      secondClickTimestamp = side === 'left' ? stripTimestamps[0] : stripTimestamps[stripTimestamps.length - 1]
    }

    if (firstClickTimestamp !== null && secondClickTimestamp !== null && secondClickTimestamp < firstClickTimestamp) {
      const tmp = firstClickTimestamp
      firstClickTimestamp = secondClickTimestamp
      secondClickTimestamp = tmp
    }

    // Find the strip index and trigger validation on the correct strip
    const stripIndex = episode.strips.findIndex((strip) => strip.id === startSegmentStrip)
    if (
      stripIndex !== -1 &&
      episodeStripRefs[stripIndex] &&
      firstClickTimestamp !== null &&
      secondClickTimestamp !== null
    ) {
      episodeStripRefs[stripIndex].triggerSegmentValidation(firstClickTimestamp, secondClickTimestamp)
      
      // Auto-confirm in segment mode
      if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'segment') {
        tick().then(() => {
          if (segmentValidations[stripIndex]?.isValid !== false) {
            handleApproveSegmentation()
          }
        })
      }
    }
  }

  const handleApproveSegmentation = (
    overrideSelectedLabel?: LabelWithColors,
    overrideFirstClick?: number,
    overrideSecondClick?: number,
    extraSecondsContext?: { moreSecondsLoaded: boolean; originalStripEndTime: number; currentStripId: string }
  ) => {
    let selectedLabell = overrideSelectedLabel ?? selectedLabel
    let firstTimestamp = overrideFirstClick ?? firstClickTimestamp
    let secondTimestamp = overrideSecondClick ?? secondClickTimestamp

    if (!selectedLabell || firstTimestamp === null || secondTimestamp === null) {
      firstClickTimestamp = null
      secondClickTimestamp = null
      undoSelectionPending = false
      return
    }

    const appliedLabel = selectedLabell
    const labelStart = Math.min(firstTimestamp, secondTimestamp)
    const labelEnd = Math.max(firstTimestamp, secondTimestamp)

    const currentTime = new Date().getTime()
    
    // Clear the undo stack and prepare to track new segments
    lastAppliedSegments = []
    lastAppliedTime = Date.now()

    const addSegmentToStrip = (targetStripId: string, start: number, end: number) => {
      if (start > end) {
        return
      }

      episodeLabellingStoreActions.addRemoveLabelStrip(targetStripId, {
        label: selectedLabell.value,
        timestamp: currentTime,
        start,
        end
      })

      // Track for undo
      lastAppliedSegments.push({
        stripId: targetStripId,
        label: selectedLabell.value,
        start,
        end,
        timestamp: currentTime
      })

      registerSnapHighlight(targetStripId, start, end)
    }

    // First, check if this segment spans multiple strips using original boundaries
    // We need to handle cross-strip segments correctly regardless of which strip is in extra seconds mode
    const affectedStrips = episode.strips.filter(strip => {
      const stripStart = strip.timestamps[0]
      const stripEnd = strip.timestamps[strip.timestamps.length - 1]
      
      const segmentValidation = segmentValidations.find((validation) => validation.stripId === strip.id)

      if(segmentValidation && !segmentValidation.isValid) { 
        return false;
      }

      // Check if segment overlaps with this strip's original boundaries
      return !(labelEnd <= stripStart || labelStart >= stripEnd)
    })

    // If segment spans multiple strips, handle with cross-strip logic using proper boundaries
    if (affectedStrips.length > 1) {
      // Sort strips by their start time to ensure proper ordering
      affectedStrips.sort((a, b) => a.timestamps[0] - b.timestamps[0])

      affectedStrips.forEach((strip, index) => {
        const stripStart = strip.timestamps[0]
        const stripEnd = strip.timestamps[strip.timestamps.length - 1]

        let segmentStart: number
        let segmentEnd: number

        if (index === 0) {
          // First strip: from segment start to the boundary with next strip
          segmentStart = labelStart
          // Use the start of the next strip as the boundary (this is the true split point)
          const nextStrip = affectedStrips[index + 1]
          const boundaryPoint = nextStrip ? nextStrip.timestamps[0] : stripEnd
          segmentEnd = Math.min(boundaryPoint, labelEnd)
        } else if (index === affectedStrips.length - 1) {
          // Last strip: from the boundary to segment end
          segmentStart = Math.max(stripStart, labelStart)
          segmentEnd = labelEnd
        } else {
          // Middle strip (rare case): from previous boundary to next boundary
          const nextStrip = affectedStrips[index + 1]
          const boundaryPoint = nextStrip ? nextStrip.timestamps[0] : stripEnd
          segmentStart = stripStart
          segmentEnd = boundaryPoint
        }

        // Only add if there's actually a segment portion in this strip
        if (segmentStart <= segmentEnd) {
          addSegmentToStrip(strip.id, segmentStart, segmentEnd)
        }
      })
    }
    // Single strip segment - check if we're in extra seconds mode for special handling
    else if (extraSecondsContext?.moreSecondsLoaded) {
      const currentStripIndex = episode.strips.findIndex((strip) => strip.id === extraSecondsContext.currentStripId)

      if (currentStripIndex !== -1) {
        const currentStrip = episode.strips[currentStripIndex]
        const currentStripStart = currentStrip.timestamps[0]
        const currentStripOriginalEnd = extraSecondsContext.originalStripEndTime

        // Determine if this segment crosses strip boundaries (using original boundaries)
        const crossesForward = labelEnd > currentStripOriginalEnd
        const crossesBackward = labelStart < currentStripStart
        
        const clampAndAddSegment = (strip: Strip, start: number, end: number) => {
          const clampedStart = Math.max(start, strip.timestamps[0])
          const clampedEnd = Math.min(end, strip.timestamps[strip.timestamps.length - 1])

          if (clampedStart > clampedEnd) {
            return
          }

          addSegmentToStrip(strip.id, clampedStart, clampedEnd)
        }

        if (crossesForward) {
          // Segment extends from current strip into next strip
          if (labelStart < currentStripOriginalEnd) {
            clampAndAddSegment(currentStrip, labelStart, Math.min(labelEnd, currentStripOriginalEnd))
          }

          const nextStripIndex = currentStripIndex + 1
          if (nextStripIndex < episode.strips.length) {
            const nextStrip = episode.strips[nextStripIndex]
            clampAndAddSegment(nextStrip, Math.max(currentStripOriginalEnd, labelStart), labelEnd)
          }
        } else if (crossesBackward) {
          // Segment extends from previous strip into current strip
          const previousStripIndex = currentStripIndex - 1
          if (previousStripIndex >= 0) {
            const previousStrip = episode.strips[previousStripIndex]
            const previousStripOriginalEnd = previousStrip.timestamps[previousStrip.timestamps.length - 1]

            clampAndAddSegment(previousStrip, labelStart, Math.min(labelEnd, previousStripOriginalEnd))
          }

          clampAndAddSegment(currentStrip, Math.max(currentStripStart, labelStart), labelEnd)
        } else {
          // Segment is entirely within current strip's original boundaries
          clampAndAddSegment(currentStrip, labelStart, labelEnd)
        }
      }
    }
    // Single strip: add entire segment to that strip
    else if (affectedStrips.length > 0) {
      addSegmentToStrip(affectedStrips[0].id, labelStart, labelEnd)
    }
    // If no strips are affected and at least one strip is invalid, do nothing
    else if (affectedStrips.length === 0 && segmentValidations.some((validation) => !validation.isValid)) {
      return
    }

    const stripIdToFocus = affectedStrips.length > 0 ? affectedStrips[affectedStrips.length - 1].id : startSegmentStrip
    const updatedLabelStripId = stripIdToFocus ?? appliedLabel?.stripId ?? null
    const lastAppliedWithStrip = appliedLabel
      ? {
          ...appliedLabel,
          stripId: updatedLabelStripId ?? appliedLabel.stripId
        }
      : null

    lastAppliedLabel = lastAppliedWithStrip ?? null

    startSegmentStrip = null
    firstClickTimestamp = null
    secondClickTimestamp = null

    if (stripIdToFocus) {
      stripSnapFlags = { ...stripSnapFlags, [stripIdToFocus]: { first: false, second: false } }
    }

    // Refocus the strip to ensure keyboard shortcuts work immediately
    if (stripIdToFocus) {
      tick().then(() => {
        const stripIndex = episode.strips.findIndex((strip) => strip.id === stripIdToFocus)
        if (stripIndex !== -1 && episodeStripRefs[stripIndex]) {
          (episodeStripRefs[stripIndex] as any)?.focusStrip?.()
        }
      })
    }

    if (undoSelectionPending) {
      selectedLabel = null
      undoSelectionPending = false
    } else if (lastAppliedWithStrip) {
      selectedLabel = lastAppliedWithStrip
    }

    // Show point label success banner if this was a point label
    if (selectedLabell && determineDefaultLabelingMode(selectedLabell) === 'point') {
      showPointLabelBanner = true
      if (pointLabelBannerTimeout) {
        clearTimeout(pointLabelBannerTimeout)
      }
      pointLabelBannerTimeout = setTimeout(() => {
        showPointLabelBanner = false
      }, 3000)
    }

    // Dispatch event to notify parent that a label was applied to this episode
    dispatch('label-applied', { episodeId: episode.episode_id })
  }

  const handleDisapproveSegmentation = () => {
    firstClickTimestamp = null
    secondClickTimestamp = null
    undoSelectionPending = false
    if (startSegmentStrip) {
      stripSnapFlags = { ...stripSnapFlags, [startSegmentStrip]: { first: false, second: false } }
    }
    startSegmentStrip = null
  }
  
  const handleUndoLastSegment = () => {
    if (lastAppliedSegments.length === 0) return
    
    // Check if TTL has expired (10 seconds)
    const now = Date.now()
    if (now - lastAppliedTime > UNDO_TTL_MS) {
      // TTL expired, clear the undo stack and return
      lastAppliedSegments = []
      lastAppliedTime = 0
      undoSelectionPending = false
      return
    }
    
    // Get the label that was removed to reselect it
    const removedLabelValue = lastAppliedSegments[0]?.label
    const labelToReselect = labels.find(l => l.value === removedLabelValue)
    
    // Remove all segments that were added in the last action
    lastAppliedSegments.forEach(segment => {
      episodeLabellingStoreActions.addRemoveLabelStrip(segment.stripId, {
        label: segment.label,
        timestamp: segment.timestamp,
        start: segment.start,
        end: segment.end
      })

      clearSnapHighlightForSegment(segment.stripId, segment.start, segment.end)
    })
    
    // Reselect the removed label so user can apply it again
    if (labelToReselect) {
      const dynamicPlatformColor = getPlatformColorDef(labelToReselect?.color, $themeStore.dark)
      const whiteThemePlatformColor = getPlatformColorDef(labelToReselect?.color, false)
      const borderColor = dynamicPlatformColor.color
      const backgroundColor = whiteThemePlatformColor.background || 'white'
      const textColor = whiteThemePlatformColor.title || 'black'

      selectedLabel = {
        ...labelToReselect,
        borderColor,
        backgroundColor,
        textColor,
        stripId: lastAppliedSegments[0]?.stripId // Set the strip ID to show selection at strip level
      } as any
      undoSelectionPending = true
      
      // Reset timestamps and segment tracking for fresh label application
      firstClickTimestamp = null
      secondClickTimestamp = null
      startSegmentStrip = null
    } else {
      undoSelectionPending = false
    }
    
    // Show success banner for 3 seconds
    showUndoSuccessBanner = true
    if (undoBannerTimeout) {
      clearTimeout(undoBannerTimeout)
    }
    undoBannerTimeout = setTimeout(() => {
      showUndoSuccessBanner = false
    }, 3000)
    
    // Clear the undo stack
    lastAppliedSegments = []
    lastAppliedTime = 0
  }

  const handleSnapLeft = (
    stripStore: StripLabellingConfig,
    strip: Strip,
    extraTimeContext?: {
      moreSecondsLoaded: boolean
      originalStripEndTime: number
      nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]>
    }
  ) => {
    if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
      return
    }

    if (firstClickTimestamp !== null && secondClickTimestamp !== null) {
      const segmentStart = Math.min(firstClickTimestamp, secondClickTimestamp)
      const segmentEnd = Math.max(firstClickTimestamp, secondClickTimestamp)
      const previousStart = segmentStart

      // Get existing labels from current strip
      let existingLabels = stripStore?.labels ? Object.values(stripStore.labels).flat() : []

      // If in extra time mode, also include labels from next strip that might be relevant for snapping
      if (extraTimeContext?.moreSecondsLoaded && extraTimeContext.nextStripAllLabels) {
        const nextStripLabels = Object.values(extraTimeContext.nextStripAllLabels).flat()
        existingLabels = [...existingLabels, ...nextStripLabels]
      }

      const snapResult = snapToClosestEndTimestamp(segmentStart, segmentEnd, existingLabels)

      let newStart = snapResult.snappedTimestamp
      if (snapResult.snappedTimestamp >= segmentStart) {
        newStart = strip.timestamps[0]
      }

      secondClickTimestamp = Math.max(firstClickTimestamp, secondClickTimestamp)
      firstClickTimestamp = newStart

      const didSnap = newStart !== previousStart
      const stripIndex = episode.strips.findIndex((stripItem) => stripItem.id === strip.id)
      triggerSnapVisualFeedback(stripIndex, 'first', didSnap)
    }
  }

  const handleSnapRight = (
    stripStore: StripLabellingConfig,
    strip: Strip,
    extraTimeContext?: {
      moreSecondsLoaded: boolean
      originalStripEndTime: number
      nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]>
    }
  ) => {
    if (selectedLabel && determineDefaultLabelingMode(selectedLabel) === 'point') {
      return
    }

    if (firstClickTimestamp !== null && secondClickTimestamp !== null) {
      const segmentStart = Math.min(firstClickTimestamp, secondClickTimestamp)
      const segmentEnd = Math.max(firstClickTimestamp, secondClickTimestamp)

      // Get existing labels from current strip
      let existingLabels = stripStore?.labels ? Object.values(stripStore.labels).flat() : []

      // If in extra time mode, also include labels from next strip that might be relevant for snapping
      if (extraTimeContext?.moreSecondsLoaded && extraTimeContext.nextStripAllLabels) {
        const nextStripLabels = Object.values(extraTimeContext.nextStripAllLabels).flat()
        existingLabels = [...existingLabels, ...nextStripLabels]
      }

      const snapResult = snapToClosestStartTimestamp(segmentStart, segmentEnd, existingLabels)

      let newEnd = snapResult.snappedTimestamp
      if (snapResult.snappedTimestamp <= segmentEnd) {
        newEnd = strip.timestamps[strip.timestamps.length - 1]
      }

      const originalEnd = segmentEnd
      let didSnap = newEnd !== originalEnd
      if (didSnap && newEnd === segmentStart) {
        newEnd = originalEnd
        didSnap = false
      }

      firstClickTimestamp = Math.min(firstClickTimestamp, secondClickTimestamp)
      secondClickTimestamp = newEnd

      const stripIndex = episode.strips.findIndex((stripItem) => stripItem.id === strip.id)
      triggerSnapVisualFeedback(stripIndex, 'second', didSnap)
    }
  }

  const exitLabelingMode = () => {
    selectedLabel = null
    startSegmentStrip = null
    firstClickTimestamp = null
    secondClickTimestamp = null
    undoSelectionPending = false
    segmentValidations = segmentValidations.map((validation) => ({
      stripId: validation?.stripId,
      isValid: true
    }))
    stripSnapFlags = {}
  }

  const handleEpisodeKeyDown = async (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (selectedLabel && firstClickTimestamp != null && secondClickTimestamp === null) {
        secondClickTimestamp = firstClickTimestamp

        const segmentStart = firstClickTimestamp
        const stripIndex = episode.strips.findIndex(
          (strip) =>
            segmentStart >= strip.timestamps[0] &&
            segmentStart <= strip.timestamps[strip.timestamps.length - 1]
        )

        if (stripIndex !== -1) {
          const stripRef = episodeStripRefs[stripIndex] as any
          const result = stripRef?.triggerSegmentValidation?.(firstClickTimestamp, secondClickTimestamp)
          if (result?.isValid === false) {
            return
          }
          // If valid or undefined, proceed
          handleApproveSegmentation()
          return
        }
      }
      // If no selected label or timestamps already set, fall back to normal behavior
      handleApproveSegmentation()
    } else if (e.key === 'Escape') {
      exitLabelingMode()
      e.preventDefault()
      e.stopPropagation()
    } else if (e.key === 'q' || e.key === 'Q') {
      handleApproveSegmentation()
    } else if (e.key === 'x') {
      handleDisapproveSegmentation()
    } else if (e.key === 'z' || e.key === 'Z') {
      handleUndoLastSegment()
    } else if (e.key === 'n' && !selectedLabel && lastAppliedLabel) {
      // Global 'n' key handler - reselect the last applied label
      selectedLabel = lastAppliedLabel
      undoSelectionPending = false
    } else if (e.key === 'ArrowLeft' && firstClickTimestamp != null && secondClickTimestamp != null) {
      // Find the strip containing the current segment start and call handleSnapLeft
      const segmentStart = Math.min(firstClickTimestamp, secondClickTimestamp)
      const stripIndex = episode.strips.findIndex(
        (strip) => segmentStart >= strip.timestamps[0] && segmentStart <= strip.timestamps[strip.timestamps.length - 1]
      )
      if (stripIndex !== -1) {
        const stripId = episode.strips[stripIndex].id
        const stripStore = $episodeLabellingStore.strips[stripId]
        const extraTimeContext = (episodeStripRefs[stripIndex] as any)?.getExtraTimeContext?.()
        handleSnapLeft(stripStore, episode.strips[stripIndex], extraTimeContext)
      }
    } else if (e.key === 'ArrowRight' && firstClickTimestamp != null && secondClickTimestamp != null) {
      // Find the strip containing the current segment end and call handleSnapRight
      const segmentEnd = Math.max(firstClickTimestamp, secondClickTimestamp)
      const stripIndex = episode.strips.findIndex(
        (strip) => segmentEnd >= strip.timestamps[0] && segmentEnd <= strip.timestamps[strip.timestamps.length - 1]
      )
      if (stripIndex !== -1) {
        const stripId = episode.strips[stripIndex].id
        const stripStore = $episodeLabellingStore.strips[stripId]
        const extraTimeContext = (episodeStripRefs[stripIndex] as any)?.getExtraTimeContext?.()
        handleSnapRight(stripStore, episode.strips[stripIndex], extraTimeContext)
      }
    }
  }

  onDestroy(() => {
    if (episodeCopyTimeout) {
      clearTimeout(episodeCopyTimeout)
    }
    if (undoBannerTimeout) {
      clearTimeout(undoBannerTimeout)
    }
    if (pointLabelBannerTimeout) {
      clearTimeout(pointLabelBannerTimeout)
    }
    Object.values(snapHighlightTimeouts).forEach((timeout) => clearTimeout(timeout))
  })

  $: getEpisodeActivity(episode.episode_id, project._id, catalog)
  $: getSegmentComments(episode)
  $: getSegmentInteresting(episode)
</script>

<div
  id={episode.episode_id}
  class="episode-card shadow-md"
  class:top-margin={topMargin}
  data-episode-id={episode.episode_id}
  on:keydown={handleEpisodeKeyDown}
  role="button"
  tabindex="-1"
>
  <div class="episode-header">
    <div
      class="flex w-full justify-between items-stretch h-full episode-header-wrapper"
      class:episode-header-divider={isExpanded}
    >
      <div class="episode-meta">
        {#each getEpisodeMetaItems(episode) as item, index (index)}
          <!-- svelte-ignore a11y-no-noninteractive-tabindex -->
          <div
            class="episode-meta-item"
            class:clickable={index === 0}
            class:has-activity={index === 0 && hasEpisodeActivity}
            use:tp={item.tooltip}
            on:click={index === 0 ? handleOpenEpisodeActivity : undefined}
            on:keydown={index === 0 ? handleEpisodeActivityKeyDown : undefined}
            tabindex={index === 0 ? 0 : undefined}
            role={index === 0 ? 'button' : undefined}
          >
            <Icon icon={item.icon} size="medium" />
            <span>{item.label}</span>
            {#if index === 0}
              <ButtonIcon
                icon={view.icon.CopyLink}
                size="small"
                kind="tertiary"
                iconProps={{ style: 'color: var(--theme-text-editor-palette-text-gray)!important;' }}
                pressed={episodeLinkCopied}
                tooltip={{
                  label: episodeLinkCopied ? annotationTool.string.LinkCopied : annotationTool.string.CopyEpisodeLink
                }}
                on:click={handleCopyEpisodeButtonClick}
              />
            {/if}
          </div>
        {/each}
      </div>
      <div class="episode-meta-controls">
        {#if isExpanded}
          <ButtonIcon
            icon={annotationTool.icon.ZoomOut}
            size="small"
            kind="tertiary"
            disabled={zoomLevel >= 1}
            on:click={handleStripZoomOut}
          />
          <ButtonIcon
            icon={annotationTool.icon.ZoomIn}
            size="small"
            kind="tertiary"
            disabled={zoomLevel <= 0}
            on:click={handleStripZoomIn}
          />
          <div class="vertical-divider" />
          <ButtonIcon
            icon={annotationTool.icon.EpisodeCollapse}
            iconSize="medium"
            size="small"
            kind="tertiary"
            tooltip={{ label: annotationTool.string.CollapseEpisode }}
            on:click={() => (isExpanded = false)}
          />
        {:else}
          <ButtonIcon
            icon={annotationTool.icon.EpisodeExpand}
            iconSize="medium"
            size="small"
            kind="tertiary"
            tooltip={{ label: annotationTool.string.ExpandEpisode }}
            on:click={() => (isExpanded = true)}
          />
        {/if}
        </div>
      </div>
      {#if isExpanded}
      <div class="episode-annotation-container">
        <div class="episode-labels-container">
          <div class="episode-labels">
            {#each labels as label (label.value)}
              <AnnotationLabel
                {label}
                selected={$episodeLabellingStore.episodeLabels.some((l) => l.label === label.value)}
                approved={$episodeLabellingStore.episodeApprovedLabels.some((l) => l.label === label.value)}
                disapproved={$episodeLabellingStore.episodeDisapprovedLabels.some((l) => l.label === label.value)}
                numSelected={isConflictResolution
                  ? $episodeLabellingStore.episodeLabels.filter((l) => l.label === label.value).length
                  : 0}
                selectable
                width="125px"
                onSelect={() =>
                  episodeLabellingStoreActions.addRemoveEpisodeLabel({
                    label: label.value,
                    timestamp: new Date().getTime()
                  })}
                allowDelete={false}
                {isConflictResolution}
                onApprove={() =>
                  episodeLabellingStoreActions.approveEpisodeLabel({
                    label: label.value,
                    timestamp: new Date().getTime()
                  })}
                onDisapprove={() =>
                  episodeLabellingStoreActions.disapproveEpisodeLabel({
                    label: label.value,
                    timestamp: new Date().getTime()
                  })}
              />
            {/each}
          </div>
          <div class="flex items-center flex-gap-2">
            {#if !isConflictResolution}
              <CheckBox
                size="large"
                checked={$episodeLabellingStore.isInterestingEpisode}
                on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeInteresting(ev.detail)}
              />
              <Label label={annotationTool.string.IsInterestingEpisode} />
              <div class="vertical-divider" />
              <CheckBox
                size="large"
                validateFn={validateAllStripsAnnotated}
                checked={$episodeLabellingStore.isDoneEpisode}
                on:value={(ev) => handleEpisodeMarkedAsDone(ev.detail)}
              />
              <Label label={annotationTool.string.IsDoneEpisode} />
            {:else}
              <CheckBox
                size="large"
                checked={$episodeLabellingStore.isConflictResolvedEpisode}
                on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeConflictResolved(ev.detail)}
              />
              <Label label={annotationTool.string.IsConflictResolvedEpisode} />
            {/if}
            <div class="vertical-divider" />
            <Button
              icon={annotationTool.icon.Delete}
              label={annotationTool.string.ClearAll}
              size="medium"
              kind="negative"
              on:click={() => episodeLabellingStoreActions.resetEpisodeLabelling()}
            />
          </div>
        </div>
      </div>
      {#if showUndoSuccessBanner}
        <div class="undo-success-banner">
          ✓ Undo Successful - Last label removed
        </div>
      {/if}
      {#if showPointLabelBanner}
        <div class="point-label-success-banner">
          ✓ Point Label Saved
        </div>
      {/if}
    {/if}
  </div>
  {#if isExpanded}
    <div class:episode-list-view={viewMode === 'list'} class:episode-card-view={viewMode === 'card'}>
      {#each episode.strips as strip, index (index)}
        {@const hasActivity = stripActivity.includes(strip.id)}
        {@const comments = segmentComments.filter((comment) => comment.stripId === strip.id)}
        {@const interesting = segmentInteresting.filter((interesting) => interesting.stripId === strip.id)}
        <EpisodeStrip
          {strip}
          stripId={strip.id}
          nextStripId={episode.strips[index + 1]?.id}
          episodeId={episode.episode_id}
          episodeStore={episodeLabellingStore}
          episodeStoreActions={episodeLabellingStoreActions}
          stripValues={strip.voltages}
          stripTimestamps={strip.timestamps}
          nextStripValues={episode.strips[index + 1]?.voltages}
          nextStripTimestamps={episode.strips[index + 1]?.timestamps}
          nextStripAllLabels={episode.strips[index + 1]?.id
            ? $episodeLabellingStore.strips[episode.strips[index + 1].id]?.allLabels || {}
            : {}}
          episodeLevelZoom={zoomLevel}
          globalYMin={episodeYMin}
          globalYMax={episodeYMax}
          segmentComments={comments}
          segmentInteresting={interesting}
          {annotatorAccessLevel}
          {hasActivity}
          {labels}
          {triageLabels}
          {viewMode}
          {isConflictResolution}
          {catalog}
          {project}
          {shuffledColors}
          {onStripClick}
          {onLeftOrRightStripClick}
          {onStripLabelSelect}
          {handleApproveSegmentation}
          {handleDisapproveSegmentation}
          {handleSnapLeft}
          {handleSnapRight}
          bind:firstClickTimestamp
          bind:secondClickTimestamp
          bind:selectedLabel
          bind:lastAppliedLabel
          bind:annotationsView
          bind:swipeSpeed
          bind:this={episodeStripRefs[index]}
          bind:segmentValidation={segmentValidations[index]}
          snapHighlights={snappedSegmentHighlights[strip.id] ?? []}
        />
      {/each}
    </div>
    <div class="horizontal-divider" style="margin-top: 0.5rem;" />
    <div class="episode-annotation-container">
      <div class="episode-labels-container">
        <div class="episode-meta-item" style="font-weight: 500;">
          <Icon icon={annotationTool.icon.Key} size="medium" />
          <span>{episode.episode_id}</span>
        </div>
        <div class="flex items-center flex-gap-2">
          {#if !isConflictResolution}
            <CheckBox
              size="large"
              checked={$episodeLabellingStore.isInterestingEpisode}
              on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeInteresting(ev.detail)}
            />
            <Label label={annotationTool.string.IsInterestingEpisode} />
            <div class="vertical-divider" />
            <CheckBox
              size="large"
              validateFn={validateAllStripsAnnotated}
              checked={$episodeLabellingStore.isDoneEpisode}
              on:value={(ev) => handleEpisodeMarkedAsDone(ev.detail)}
            />
            <Label label={annotationTool.string.IsDoneEpisode} />
          {:else}
            <CheckBox
              size="large"
              checked={$episodeLabellingStore.isConflictResolvedEpisode}
              on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeConflictResolved(ev.detail)}
            />
            <Label label={annotationTool.string.IsConflictResolvedEpisode} />
          {/if}
          <div class="vertical-divider" />
          <Button
            icon={annotationTool.icon.Delete}
            label={annotationTool.string.ClearAll}
            size="medium"
            kind="negative"
            on:click={() => episodeLabellingStoreActions.resetEpisodeLabelling()}
          />
        </div>
      </div>
    </div>
  {:else}
    <div
      class="episode-collapsed cursor-pointer"
      role="button"
      tabindex="-1"
      aria-label={annotationTool.string.ExpandEpisode}
      on:click={() => (isExpanded = true)}
      on:keydown={handleOnKeyDown}
      use:tp={{ label: annotationTool.string.ExpandEpisode }}
    >
      <Icon icon={annotationTool.icon.EpisodeCollapse} size="medium" />
      <Label label={annotationTool.string.EpisodeIsCollapsed} />
    </div>
  {/if}
</div>

<style lang="scss">
  .episode-card {
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.75rem;
    padding: 0.5rem;
    margin: 0.5rem 0.5rem 3rem 0.5rem;
  }

  .top-margin {
    margin-top: 3rem;
  }

  .episode-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
    min-height: 48px;
    background-color: var(--theme-mention-bg-color-notransparent);
    padding: 0.5rem;
    margin: -0.5rem -0.5rem 0.5rem -0.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;

    // Sticky header when scrolling - ensure it's always above strip overlays
    position: sticky;
    top: 0;
    z-index: 1001;
    isolation: isolate;
  }

  .episode-header-wrapper {
    container-type: inline-size;
  }

  .episode-id {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    font-size: 16px;
  }

  .horizontal-divider {
    width: 100%;
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .vertical-divider {
    height: 30px;
    border-right: 1px solid var(--theme-divider-color);
  }

  .episode-header-divider {
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .episode-meta {
    container-type: inline-size;
    display: grid;
    grid-template-columns: auto max-content auto auto;
    grid-template-rows: repeat(2, auto);
    width: 100%;
    gap: 1rem;
    width: 100%;
    font-weight: 500;

    @container (min-width: 900px) {
      grid-template-columns: auto max-content auto auto auto auto auto auto;
      grid-template-rows: unset;
    }
  }

  .episode-meta-controls {
    display: flex;
    flex-direction: row;
    gap: 0.25rem;
  }

  .episode-buttons {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    justify-self: stretch;
    align-items: flex-end;
  }

  .episode-meta-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-wrap: nowrap;
  }

  .copy-link-button {
    color: var(--theme-text-editor-palette-text-gray);
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;

    &:hover,
    &:focus-visible,
    &[aria-pressed='true'] {
      opacity: 1;
    }
  }

  .has-activity span {
    position: relative;
  }

  .has-activity span::after {
    content: '';
    position: absolute;
    top: 0;
    right: -10px;
    width: 8px;
    height: 8px;
    background-color: var(--highlight-red);
    border-radius: 100%;
  }

  .episode-list-view {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
    z-index: 0;
  }

  .episode-card-view {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    overflow-x: auto;
    position: relative;
    z-index: 0;
  }

  .episode-collapsed {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    font-weight: 600;
  }

  .episode-annotation-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
    margin-top: 0.5rem;

    :global(button) {
      margin: auto 0;
    }
  }

  .vertical-divider {
    border-right: 1px solid var(--theme-divider-color);
  }

  .episode-labels-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
  }

  .episode-labels-text {
    display: flex;
    justify-content: space-between;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .episode-labels {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .clickable:hover {
    cursor: pointer;
  }

  /* Simple red border for validation errors */
  :global(.validation-error-highlight) {
    border: 2px solid var(--highlight-red) !important;
  }

  .undo-success-banner {
    width: calc(100% + 1rem);
    font-size: 15px;
    font-weight: 600;
    padding: 8px 16px;
    background: #28a745;
    color: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px 0 rgba(0, 50, 0, 0.1);
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;
    hyphens: auto;
    margin: 8px -0.5rem -0.5rem -0.5rem;
    text-align: center;
    animation: slideDown 0.3s ease-out;
  }

  .point-label-success-banner {
    width: calc(100% + 1rem);
    font-size: 15px;
    font-weight: 600;
    padding: 8px 16px;
    background: #28a745;
    color: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px 0 rgba(0, 50, 0, 0.1);
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;
    hyphens: auto;
    margin: 8px -0.5rem -0.5rem -0.5rem;
    text-align: center;
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
