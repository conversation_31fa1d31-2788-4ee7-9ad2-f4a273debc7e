<script lang="ts">
  import annotationTool from '../../../plugin'
  import at from '@hcengineering/annotationtool'
  import type { Label as LabelType, SegmentInteresting, SegmentComment } from '@hcengineering/annotationtool'
  import { tooltip, getPlatformColorDef, Icon, showPopup, closePopup } from '@hcengineering/ui'
  import core from '@hcengineering/core'
  import { getUserSectionYOffset, getLabelYPosition } from '../../../utils/labelLayoutUtils'
  import { Avatar, employeeByAccountStore } from '@hcengineering/contact-resources'
  import type { LabelWithTrack } from '../../../utils/labelLayoutUtils'
  import { StripLabellingConfig } from '../../../types'
  import { getClient } from '@hcengineering/presentation'
  import EpisodeStripSegmentNote from './EpisodeStripSegmentNote.svelte'
  import { determineDefaultLabelingMode } from '../../../utils/labelModeUtils'

  export let labelsWithTracks: Record<string, LabelWithTrack[]>
  export let isMultiUserMode: boolean
  export let shuffledColors: number[]
  export let height: number
  export let width: number
  export let xScale: any
  export let stripId: string
  export let nextStripId: string = ''
  export let moreSecondsLoaded: boolean = false
  export let originalStripEndTime: number = 0
  export let episodeStoreActions: any
  export let labels: LabelType[]
  export let currentAccount: string
  export let isConflictResolution: boolean
  export let stripStore: StripLabellingConfig
  export let onApprove: any
  export let onDisapprove: any
  export let segmentComments: SegmentComment[] = []
  export let segmentInteresting: SegmentInteresting[] = []
  export let snapHighlights: { id: number; start: number; end: number; until: number }[] = []

  const client = getClient()

  const handleMarkSegmentAsInteresting = (labelWithTrack: any) => {
    if (
      segmentInteresting.some((s) => s.createdBy === currentAccount && s.segmentTimestamp === labelWithTrack.timestamp)
    ) {
      const segmentToRemove = segmentInteresting.find((s) => s.segmentTimestamp === labelWithTrack.timestamp)

      if (segmentToRemove) {
        segmentInteresting = segmentInteresting.filter(
          (s) => s.createdBy !== currentAccount || s.segmentTimestamp !== labelWithTrack.timestamp
        )
        client.removeDoc(at.class.SegmentInteresting, core.space.Space, segmentToRemove._id)
      }
    } else {
      const id = crypto.randomUUID()

      segmentInteresting.push({
        _id: id,
        stripId: stripId,
        segmentTimestamp: labelWithTrack.timestamp,
        _class: at.class.SegmentInteresting,
        space: core.space.Space,
        createdBy: currentAccount
      } as any)

      client.createDoc(
        at.class.SegmentInteresting,
        core.space.Space,
        {
          stripId: stripId,
          segmentTimestamp: labelWithTrack.timestamp
        },
        id as any
      )
    }

    segmentInteresting = [...segmentInteresting]
  }

  const handleAddSegmentNote = (
    event: MouseEvent,
    comment: SegmentComment | undefined,
    labelWithTrack: any,
    userId: string
  ) => {
    showPopup(EpisodeStripSegmentNote, { note: comment }, event?.currentTarget as HTMLElement, (result) => {
      if (result && result.note && userId === currentAccount) {
        if (comment) {
          client.updateDoc(at.class.SegmentComment, core.space.Space, comment._id, {
            comment: result.note ?? ''
          })

          const idx = segmentComments.findIndex((c) => c._id === comment._id)
          segmentComments[idx].comment = result.note ?? ''
        } else {
          const id = crypto.randomUUID()
          client.createDoc(
            at.class.SegmentComment,
            core.space.Space,
            {
              stripId: stripId,
              segmentTimestamp: labelWithTrack.timestamp,
              comment: result.note ?? ''
            },
            id as any
          )

          segmentComments.push({
            _id: id as any,
            stripId: stripId,
            segmentTimestamp: labelWithTrack.timestamp,
            comment: result.note ?? '',
            _class: at.class.SegmentComment,
            space: core.space.Space,
            createdBy: currentAccount as any
          } as any)

          segmentComments = [...segmentComments]
        }
      } else {
        closePopup()
      }
    })
  }
</script>

{#each Object.entries(labelsWithTracks) as [userId, userLabels], userIndex}
  {@const userSectionOffset = isMultiUserMode ? getUserSectionYOffset(userId, labelsWithTracks, 24, 4, 2, 6) : 0}
  {@const multiUserColorIndex = shuffledColors[userIndex]}

  <!-- Add separator line before each user section (except the first) -->
  {#if isMultiUserMode && userIndex > 0}
    {@const separatorY = height + 28 + userSectionOffset - (2 + 6)}
    <!-- Center line in separator area -->
    <div
      class="absolute"
      style="
        left: 0px;
        top: {separatorY}px;
        width: {width}px;
        height: 1px;
        background-color: var(--theme-divider-color);
        pointer-events: none;
      "
    ></div>
  {/if}

  {#each userLabels as labelWithTrack}
    {@const userColor = isMultiUserMode ? multiUserColorIndex : (labelWithTrack.color ?? 0)}
    {@const platformColor = getPlatformColorDef(userColor, false)}
    {@const backgroundColor = platformColor.background}
    {@const textColor = platformColor.title}
    {@const yPosition = getLabelYPosition(labelWithTrack.track, height + 28, 24, 4, userSectionOffset)}
    {@const isInteresting = segmentInteresting.some(
      (s) => s.createdBy === userId && Math.round(s.segmentTimestamp) === Math.round(labelWithTrack.timestamp)
    )}
    {@const comment = segmentComments.find(
      (s) => s.createdBy === userId && s.segmentTimestamp === labelWithTrack.timestamp
    )}

    {@const approved = stripStore.approvedLabels.some(
      (l) =>
        l.label === labels.find((l) => l.label === labelWithTrack.label)?.value &&
        l.start === labelWithTrack.start &&
        l.end === labelWithTrack.end
    )}
    {@const disapproved = stripStore.disapprovedLabels.some(
      (l) =>
        l.label === labels.find((l) => l.label === labelWithTrack.label)?.value &&
        l.start === labelWithTrack.start &&
        l.end === labelWithTrack.end
    )}

    {@const stripEndTime = moreSecondsLoaded ? originalStripEndTime + 5000 : originalStripEndTime}
    {@const labelStart = labelWithTrack.start}
    {@const labelEnd = Math.min(labelWithTrack.end, stripEndTime)}

    {@const xScaleStart = xScale(labelStart)}
    {@const xScaleEnd = xScale(labelEnd)}
    {@const currentLabel = labels.find((l) => l.label === labelWithTrack.label)}
    {@const isPointSegment = currentLabel && determineDefaultLabelingMode(currentLabel) === 'point'}
    {@const dotSize = 8}
    {@const segmentWidth = isPointSegment ? dotSize : Math.max(xScaleEnd - xScaleStart, 1)}
    {@const segmentHeight = isPointSegment ? dotSize : 24}
    {@const segmentLeft = isPointSegment ? Math.max(xScaleStart - segmentWidth / 2, 0) : xScaleStart}
    {@const segmentTop = isPointSegment ? yPosition + (24 - segmentHeight) / 2 : yPosition}
    {@const segmentRightEdge = segmentLeft + segmentWidth}
    {@const buttonTop = segmentTop - (20 - segmentHeight) / 2}
    {@const noteButtonTop = buttonTop + 2}
    {@const labelTextLeft = segmentLeft + (isMultiUserMode ? 28 : 8)}
    {@const labelTextMaxWidth = Math.max(0, segmentWidth - (isMultiUserMode ? 60 : 40))}
    {@const deleteOffset = isPointSegment ? 40 : 25}
    {@const interestingOffset = isPointSegment ? 60 : 45}
    {@const noteOffset = isPointSegment ? 80 : 65}
    {@const otherNoteOffset = isPointSegment ? (isInteresting ? 80 : 60) : isInteresting ? 45 : 25}
    {@const otherDeleteOffset = isPointSegment ? 40 : 25}
    {@const snapHighlight = snapHighlights.find(
      (highlight) => highlight.start === labelWithTrack.start && highlight.end === labelWithTrack.end
    )}

    {#if labelWithTrack.start < stripEndTime}
      <!-- Main label or point marker -->
      <div
        class="segment-shape absolute rounded"
        class:point-segment={isPointSegment}
        class:segment-snapped={!!snapHighlight}
        style="left: {segmentLeft}px; top: {segmentTop}px; width: {segmentWidth}px; height: {segmentHeight}px; background-color: {backgroundColor}; pointer-events: all;"
        use:tooltip={{ nonIntlLabel: labelWithTrack.label ?? '' }}
      />

      {#if snapHighlight}
        <div
          class="segment-snap-overlay absolute rounded"
          class:point-segment={isPointSegment}
          style="
          left: {segmentLeft}px;
          top: {segmentTop}px;
          width: {segmentWidth}px;
          height: {segmentHeight}px;
        "
        ></div>
      {/if}

      {#if isMultiUserMode && !isPointSegment}
        {@const person = $employeeByAccountStore.get(userId)}
        {@const formattedUserName = person?.name ? person.name.split(',').reverse().join(' ') : 'Unknown User'}

        <!-- Avatar container -->
        <div
          class="absolute flex items-center justify-center"
          style="
          left: {segmentLeft + 4}px;
          top: {segmentTop + 4}px;
          width: 16px;
          height: 16px;
          pointer-events: all;
        "
          use:tooltip={{ nonIntlLabel: formattedUserName }}
        >
          <div style="transform: scale(0.89);">
            <Avatar {person} size="tiny" name={person?.name} />
          </div>
        </div>
      {/if}

      {#if !isPointSegment}
        <!-- Label text -->
        <div
          class="absolute strip-label-text flex items-center"
          style="
          left: {labelTextLeft}px;
          top: {segmentTop}px;
          height: 24px;
          color: {textColor};
          font-size: 13px;
          font-weight: 500;
          line-height: 1;
          letter-spacing: 0.01em;
          pointer-events: none;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: {labelTextMaxWidth}px;
          display: flex;
          align-items: center;
        "
        >
          <span style="display: block; line-height: 1;">
            {labelWithTrack.label}{approved ? ' - APPROVED ✓' : disapproved ? ' - REJECTED ✗' : ''}
          </span>
        </div>
      {/if}

      {#if !isConflictResolution && userId === currentAccount}
        <!-- Delete button -->
        <button
          class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
          style="
            left: {segmentRightEdge - deleteOffset}px;
            top: {buttonTop}px;
            width: 24px;
            height: 20px;
            background: transparent;
            border: none;
            color: {textColor};
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            pointer-events: all;
          "
          on:click={(e) => {
            e.stopPropagation()
            // Determine which strip this segment belongs to
            const isNextStripSegment = moreSecondsLoaded && labelStart >= originalStripEndTime
            const targetStripId = isNextStripSegment ? nextStripId : stripId

            episodeStoreActions.addRemoveLabelStrip(targetStripId, {
              ...labelWithTrack,
              label: labels.find((l) => l.label === labelWithTrack.label)?.value ?? ''
            })
          }}
          on:keydown={(e) => {
            e.stopPropagation()
            e.preventDefault()
          }}
          aria-label="Delete label {labelWithTrack.label}"
          use:tooltip={{ label: annotationTool.string.DeleteSegmentationLabel, props: { label: labelWithTrack.label } }}
        >
          ✗
        </button>
        <!-- Interesting button -->
        <button
          class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
          style="
            left: {segmentRightEdge - interestingOffset}px;
            top: {buttonTop}px;
            width: 24px;
            height: 20px;
            background: transparent;
            border: none;
            color: {textColor};
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            pointer-events: all;
          "
          on:click={() => handleMarkSegmentAsInteresting(labelWithTrack)}
          on:keydown={() => void 0}
          aria-label="Mark segment as interesting {labelWithTrack.label}"
          use:tooltip={{
            label: isInteresting
              ? annotationTool.string.RemoveMarkSegmentAsInteresting
              : annotationTool.string.MarkSegmentAsInteresting
          }}
        >
          <Icon
            icon={isInteresting ? annotationTool.icon.LightBulbOn : annotationTool.icon.LightBulbOff}
            size="small"
          />
        </button>
        <!-- Segment note button -->
        <button
          class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
          class:has-activity={!!comment?.comment}
          style="
          left: {segmentRightEdge - noteOffset}px;
          top: {noteButtonTop}px;
          width: 24px;
          height: 20px;
          background: transparent;
          border: none;
          color: {textColor};
          cursor: pointer;
          font-size: 12px;
          font-weight: bold;
          pointer-events: all;
        "
          on:click={(e) => handleAddSegmentNote(e, comment, labelWithTrack, userId)}
          on:keydown={() => void 0}
          aria-label="Segment note {labelWithTrack.label}"
          use:tooltip={{
            label: annotationTool.string.SegmentNote
          }}
        >
          <Icon icon={annotationTool.icon.SegmentComment} size="small" />
        </button>
      {/if}
      <!-- Interesting indicator for other users -->
      {#if !isConflictResolution && userId !== currentAccount && isInteresting}
        <!-- Interesting button -->
        <button
          class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
          style="
            left: {segmentRightEdge - otherDeleteOffset}px;
            top: {buttonTop}px;
            width: 24px;
            height: 20px;
            background: transparent;
            border: none;
            color: {textColor};
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            pointer-events: all;
          "
          aria-label="Segment is marked as interesting {labelWithTrack.label}"
          use:tooltip={{
            label: annotationTool.string.MarkedAsInteresting
          }}
        >
          <Icon icon={annotationTool.icon.LightBulbOn} size="small" />
        </button>
      {/if}
      <!-- Note button for other users -->
      {#if !isConflictResolution && userId !== currentAccount && !!comment?.comment}
        <button
          class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
          class:has-activity={!!comment?.comment}
          style="
            left: {segmentRightEdge - otherNoteOffset}px;
            top: {noteButtonTop}px;
            width: 24px;
            height: 20px;
            background: transparent;
            border: none;
            color: {textColor};
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            pointer-events: all;
          "
          on:click={(e) => handleAddSegmentNote(e, comment, labelWithTrack, userId)}
          on:keydown={() => void 0}
          aria-label="Segment is marked as interesting {labelWithTrack.label}"
          use:tooltip={{ label: annotationTool.string.SegmentNote }}
        >
          <Icon icon={annotationTool.icon.SegmentComment} size="small" />
        </button>
      {/if}
      <!-- Conflict resolution buttons -->
      {#if isConflictResolution}
        {#if !approved}
          <!-- Approve button -->
          <button
            class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
            style="
            left: {segmentRightEdge - interestingOffset}px;
            top: {buttonTop}px;
            width: 24px;
            height: 20px;
            background: transparent;
            border: none;
            color: {textColor};
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            pointer-events: all;
          "
            on:click={() => onApprove(labelWithTrack, userId)}
            on:keydown={() => void 0}
            aria-label="Approve label {labelWithTrack.label}"
            use:tooltip={{ label: annotationTool.string.Approve, props: { label: labelWithTrack.label } }}
          >
            ✓
          </button>
        {/if}
        {#if !disapproved}
          <!-- Reject button -->
          <button
            class="absolute strip-label-button flex items-center justify-center hover:bg-black hover:bg-opacity-10"
            style="
            left: {segmentRightEdge - deleteOffset}px;
            top: {buttonTop}px;
            width: 24px;
            height: 20px;
            background: transparent;
            border: none;
            color: {textColor};
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            pointer-events: all;
          "
            on:click={() => onDisapprove(labelWithTrack, userId)}
            on:keydown={() => void 0}
            aria-label="Reject label {labelWithTrack.label}"
            use:tooltip={{ label: annotationTool.string.Reject, props: { label: labelWithTrack.label } }}
          >
            ✗
          </button>
        {/if}
      {/if}
    {/if}
  {/each}
{/each}

<style lang="scss">
  .has-activity::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 0;
    width: 5px;
    height: 5px;
    background-color: var(--highlight-red);
    border-radius: 100%;
  }

  .segment-shape.point-segment {
    border-radius: 9999px;
    min-width: 8px;
    min-height: 8px;
  }

  .segment-shape.segment-snapped {
    box-shadow:
      0 0 0 2px rgba(255, 255, 255, 0.9),
      0 0 10px rgba(0, 0, 0, 0.25);
    transition: box-shadow 0.2s ease-out;
  }

  .segment-snap-overlay {
    pointer-events: none;
    border: 2px solid var(--primary-button-default);
    animation: segment-snap-pulse 700ms ease-out forwards;
    opacity: 0;
    z-index: 2;
    border-radius: inherit;
  }

  .segment-snap-overlay.point-segment {
    border-radius: 9999px;
  }

  @keyframes segment-snap-pulse {
    0% {
      opacity: 0.85;
      transform: scale(1);
    }
    60% {
      opacity: 0.5;
      transform: scale(1.05);
    }
    100% {
      opacity: 0;
      transform: scale(1.12);
    }
  }
</style>
