<script lang="ts">
  import { Label as LabelType, TriageLabel as TriageLabelType, LabelWithColors, SegmentInteresting, SegmentComment } from '@hcengineering/annotationtool'
  import { getCurrentAccount } from '@hcengineering/core'
  import { Project } from '@hcengineering/tasker'
  import { Button, ButtonIcon, CheckBox, closePopup, Icon, Label, showPopup, themeStore } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import { scaleTime } from 'd3-scale'
  import { Axis, Canvas, Chart, Highlight, Rect, Rule, Spline, Svg } from 'layerchart'
  import { State } from 'svelte-ux'
import { onDestroy, tick } from 'svelte'
  import annotationTool from '../../../plugin'
  import { EpisodeLabellingStoreConfig } from '../../../types'
  import {
    assignLabelsToTracks,
    calculateLabelAreaHeight,
    getLabelsWithPositions
  } from '../../../utils/labelLayoutUtils'
  import AnnotationLabel from '../../settings/components/AnnotationLabel.svelte'
  import UserActivityModal from '../../user-activity/UserActivityModal.svelte'
  import EpisodeStripSegments from './EpisodeStripSegments.svelte'
  import EpisodeStripGrid from './EpisodeStripGrid.svelte'
  import EpisodeStripClickOverlays from './EpisodeStripClickOverlays.svelte'
  import EpisodeStripCaliper from './EpisodeStripCaliper.svelte'
  import { millimeterToPx } from '../../../utils'
  import { determineDefaultLabelingMode } from '../../../utils/labelModeUtils'
  import EpisodeStripRuler from './EpisodeStripRuler.svelte'
  import {
    type ExistingSegment,
    validateSegment,
    type ValidationResult,
    getMutuallyExclusiveLabels,
    findTimestampIndex
  } from '../../../utils/segmentValidation'
  import { labelPositionSettingsStore } from '../../../labelPositionSettingsStore'
  import { StripLabellingConfig, Strip } from '../../../types'
  import Lazy from 'svelte-lazy'
  import { buildEpisodeDeepLink, copyToClipboard } from '../../../utils/deepLinkUtils'

  export let strip: Strip
  export let stripId: string
  export let nextStripId: string
  export let episodeId: string
  export let stripValues: string[] = []
  export let stripTimestamps: number[] = []
  export let nextStripValues: string[] = []
  export let nextStripTimestamps: number[] = []
  export let nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]> =
    {}
  export let labels: LabelType[]
  export let triageLabels: TriageLabelType[]
  export let viewMode: 'card' | 'list' = 'card'
  export let isConflictResolution: boolean = false
  export let catalog: string
  export let project: Project
  export let hasActivity: boolean = false
  export let episodeStore: EpisodeLabellingStoreConfig['episodeLabellingStore']
  export let episodeStoreActions: EpisodeLabellingStoreConfig['episodeLabellingStoreActions']
  export let annotatorAccessLevel: string
  export let episodeLevelZoom: number = 0
  export let annotationsView: 'my' | 'all' = 'my'
  export let swipeSpeed: number = 25
  export let shuffledColors: number[] = []
  export let segmentComments: SegmentComment[] = []
  export let segmentInteresting: SegmentInteresting[] = []
  export let globalYMin: number | null = null
  export let globalYMax: number | null = null
  export let snapHighlights: { id: number; start: number; end: number; until: number }[] = []
  
  // Multi-strip segmenting variables
  export let selectedLabel: any = null
  export let lastAppliedLabel: any = null
  export let firstClickTimestamp: number | null = null
  export let secondClickTimestamp: number | null = null
  export let segmentValidation: ValidationResult = { isValid: true, stripId: stripId }
  export let onStripClick: (
    e: MouseEvent,
    params: {
      xScale: any
      yScale: any
      padding: { left?: number; right?: number; top?: number; bottom?: number }
      width: number
      height: number
    },
    stripId: string
  ) => void
  export let onLeftOrRightStripClick: (
    e: MouseEvent,
    params: {
      xScale: any
      yScale: any
      padding: { left?: number; right?: number; top?: number; bottom?: number }
      width: number
      height: number
    },
    direction: 'left' | 'right',
    stripId: string
  ) => void
  export let onStripLabelSelect: (label: LabelType, stripId: string) => void = () => {}
  export let handleApproveSegmentation: (
    overrideSelectedLabel?: LabelWithColors,
    overrideFirstClick?: number,
    overrideSecondClick?: number,
    extraSecondsContext?: { moreSecondsLoaded: boolean; originalStripEndTime: number; currentStripId: string }
  ) => void = () => {}
  export let handleDisapproveSegmentation: () => void = () => {}
  export let handleSnapLeft: ((
    newStripStore: StripLabellingConfig, 
    strip: Strip, 
    extraTimeContext?: {
      moreSecondsLoaded: boolean
      originalStripEndTime: number
      nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]>
    }
  ) => void) | undefined = undefined
  export let handleSnapRight: ((
    newStripStore: StripLabellingConfig, 
    strip: Strip,
    extraTimeContext?: {
      moreSecondsLoaded: boolean
      originalStripEndTime: number
      nextStripAllLabels: Record<string, { label: string; timestamp: number; start?: number; end?: number }[]>
    }
  ) => void) | undefined = undefined

  const currentAccount = getCurrentAccount()?.uuid

  $: stripStore = $episodeStore.strips[stripId]

  $: zoomLevel = episodeLevelZoom

  let resetZoomFn: any = undefined
  let lastStripTimestampIndex = stripTimestamps.length
  let moreSecondsLoaded: boolean = false
  let showRuler: boolean = false
  let showCaliper: boolean = false
  let caliperHeight: number = 300
  let caliperWidth: number = 949
  let stripLinkCopied: boolean = false
  let stripCopyTimeout: ReturnType<typeof setTimeout> | undefined
  const SNAP_ANIMATION_DURATION = 250
  let firstSnapAnimating = false
  let secondSnapAnimating = false
  let firstSnapTimeout: ReturnType<typeof setTimeout> | undefined
  let secondSnapTimeout: ReturnType<typeof setTimeout> | undefined

  $: stripLength = stripTimestamps?.[stripTimestamps.length - 1] - stripTimestamps?.[0]
  $: stripSecs = moreSecondsLoaded ? 15000 : 10000

  // Value domain used to derive the Y domain (vertical zoom)
  $: numericValues = stripValues.map((v) => Number(v)).filter((v) => Number.isFinite(v))
  let localYMin = -1
  let localYMax = 1
  let hasGlobalExtents = false
  $: {
    if (numericValues.length === 0) {
      localYMin = -1
      localYMax = 1
    } else {
      let min = Infinity
      let max = -Infinity
      for (const value of numericValues) {
        if (value < min) min = value
        if (value > max) max = value
      }
      localYMin = min === Infinity ? -1 : min
      localYMax = max === -Infinity ? 1 : max
    }
  }
  $: hasGlobalExtents = Number.isFinite(globalYMin) && Number.isFinite(globalYMax)
  let yMin = -1
  let yMax = 1
  $: {
    const baseMin = hasGlobalExtents ? (globalYMin as number) : localYMin
    const baseMax = hasGlobalExtents ? (globalYMax as number) : localYMax
    if (baseMin === baseMax) {
      yMin = baseMin - 1
      yMax = baseMax + 1
    } else {
      yMin = baseMin
      yMax = baseMax
    }
  }
  $: amplitude = Math.max(Math.abs(yMin), Math.abs(yMax)) || 1
  $: zoomFactor = amplitude * zoomLevel
  $: yDomain = [yMin - zoomFactor, yMax + zoomFactor]

  $: dateSeriesData = stripValues.map((value, index) => ({
    date: stripTimestamps[index],
    value: value == null ? null : Number(value)
  }))

  $: labelSelectedOnCurrentStrip = (selectedLabel as any)?.stripId === stripId

  // Filter labels based on user level and view mode
  $: filteredAllLabels = (() => {
    const allLabels = stripStore?.allLabels ?? {}

    // For basic users (non-level2) or level2 users in 'my' mode: only show current user's labels
    if (annotatorAccessLevel !== 'level2' || annotationsView === 'my') {
      return currentAccount ? { [currentAccount]: allLabels[currentAccount] || [] } : {}
    }

    // For level2 users in 'all' mode: show all labels, but prioritize current user's labels
    if (annotationsView === 'all') {
      const result: Record<string, any[]> = {}

      // Add current user's labels first
      if (currentAccount && allLabels[currentAccount]) {
        result[currentAccount] = allLabels[currentAccount]
      }

      // Add other users' labels
      Object.entries(allLabels).forEach(([userId, userLabels]) => {
        if (userId !== currentAccount) {
          result[userId] = userLabels
        }
      })

      return result
    }

    return allLabels
  })()

  $: labelsWithPositions = getLabelsWithPositions(
    filteredAllLabels,
    nextStripAllLabels,
    originalStripEndTime,
    moreSecondsLoaded,
    nextStripId,
    labels
  )

  // Assign labels to tracks to avoid visual overlaps
  $: labelsWithTracks = assignLabelsToTracks(labelsWithPositions)

  // Determine if we're in multi-user mode
  $: isMultiUserMode = annotatorAccessLevel === 'level2' && annotationsView === 'all'

  // Calculate the total height needed for the label area
  $: labelAreaHeight = calculateLabelAreaHeight(labelsWithTracks, 24, 4, isMultiUserMode, 2, 6)

  // Calculate the strip boundary time (original end time without more seconds)
  $: originalStripEndTime = stripTimestamps[lastStripTimestampIndex - 1]

  $: if (stripLength < (moreSecondsLoaded ? 14500 : 9500) && dateSeriesData.length > 1) {
    // Fill dateSeriesData until stripTimestamps[0] + 10000 or 15000, following the same frequency
    const freq = dateSeriesData[1].date - dateSeriesData[0].date
    let lastDate = dateSeriesData[dateSeriesData.length - 1].date
    let nextIndex = dateSeriesData.length
    const endDate = stripTimestamps[0] + stripSecs
    while (lastDate + freq <= endDate) {
      lastDate += freq
      dateSeriesData.push({
        date: lastDate,
        value: null
      })
      nextIndex++
    }
  }


export function triggerSegmentValidation(firstTimeStamp: number, secondTimeStamp: number): ValidationResult | undefined {
    // Use all users' labels for mutual exclusion checks to prevent conflicts across annotators
    const allLabelsFromStore = stripStore?.allLabels ?? {}
    const allSegments = Object.values(allLabelsFromStore)
      .flat()
      .filter((segment) => segment.start != null && segment.end != null)
      .map((segment) => ({
        label: segment.label,
        timestamp: segment.timestamp,
        start: segment.start,
        end: segment.end
      })) as ExistingSegment[]

    if (secondTimeStamp != null && firstTimeStamp != null) {
      const result = validateSegment(
        stripId,
        selectedLabel as any,
        firstTimeStamp,
        secondTimeStamp,
        stripTimestamps,
        allSegments,
        labels,
        currentAccount
      )
      segmentValidation = result
      return result
    } else if (secondTimeStamp === null) {
      segmentValidation = { isValid: true, stripId }
      return segmentValidation
    }
    return segmentValidation
  }

  export function getExtraTimeContext() {
    return {
      moreSecondsLoaded,
      originalStripEndTime,
      nextStripAllLabels
    }
  }

  export async function triggerSnapAnimation(target: 'first' | 'second') {
    if (target === 'first') {
      if (firstSnapTimeout) {
        clearTimeout(firstSnapTimeout)
      }
      firstSnapAnimating = false
      await tick()
      firstSnapAnimating = true
      firstSnapTimeout = setTimeout(() => {
        firstSnapAnimating = false
      }, SNAP_ANIMATION_DURATION)
    } else {
      if (secondSnapTimeout) {
        clearTimeout(secondSnapTimeout)
      }
      secondSnapAnimating = false
      await tick()
      secondSnapAnimating = true
      secondSnapTimeout = setTimeout(() => {
        secondSnapAnimating = false
      }, SNAP_ANIMATION_DURATION)
    }
  }
  
  let stripContainerElement: HTMLElement | undefined
  
  export function focusStrip() {
    if (stripContainerElement) {
      stripContainerElement.focus()
    }
  }

  function handleStripZoomOut() {
    if (zoomLevel >= 1) return

    zoomLevel += 0.25
  }

  function handleStripZoomIn() {
    if (zoomLevel <= 0) return

    zoomLevel -= 0.25
  }

  function handleStripExpand() {
    resetZoomFn(null)
    resetZoomFn = undefined
  }

  function handleLoadMoreSeconds() {
    const next5Seconds = stripTimestamps[stripTimestamps.length - 1] + 5000

    // Find the index of the value in nextStripTimestamps closest to 5 seconds
    let closest5SecondIndex = -1
    if (Array.isArray(nextStripTimestamps) && nextStripTimestamps.length > 0) {
      let minDiff = Infinity
      for (let i = 0; i < nextStripTimestamps.length; i++) {
        const diff = Math.abs(nextStripTimestamps[i] - next5Seconds)
        if (diff < minDiff) {
          minDiff = diff
          closest5SecondIndex = i
        }
      }
    }

    stripTimestamps = [...stripTimestamps, ...nextStripTimestamps.slice(1, closest5SecondIndex + 1)]
    stripValues = [...stripValues, ...nextStripValues.slice(1, closest5SecondIndex + 1)]
    strip.timestamps = stripTimestamps
    strip.voltages = stripValues
    moreSecondsLoaded = true
  }

  function handleUnloadMoreSeconds() {
    stripTimestamps = stripTimestamps.slice(0, lastStripTimestampIndex)
    stripValues = stripValues.slice(0, lastStripTimestampIndex)
    moreSecondsLoaded = false
  }

  const handleOpenStripActivity = () => {
    showPopup(UserActivityModal, {
      episodeId,
      stripId,
      catalog,
      labels: labels,
      hidden: false,
      project,
      annotatorAccessLevel,
      onCancel: closePopup
    })
  }

  const handleCopyStripLink = async () => {
    const link = buildEpisodeDeepLink(episodeId, stripId)
    if (!link) {
      return
    }

    const copied = await copyToClipboard(link)
    if (!copied) {
      return
    }

    stripLinkCopied = true
    if (stripCopyTimeout) {
      clearTimeout(stripCopyTimeout)
    }
    stripCopyTimeout = setTimeout(() => {
      stripLinkCopied = false
    }, 2000)
  }

  const handleStripKeyDown = async (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (selectedLabel && firstClickTimestamp != null && secondClickTimestamp === null) {
        secondClickTimestamp = firstClickTimestamp
        const result = triggerSegmentValidation(firstClickTimestamp, secondClickTimestamp)
        if (result?.isValid === false) {
          e.preventDefault()
          e.stopPropagation()
          return
        }
      }

      if (segmentValidation?.isValid !== false) {
        handleApproveSegmentation(undefined, undefined, undefined, {
          moreSecondsLoaded,
          originalStripEndTime,
          currentStripId: stripId
        })
      }

      e.preventDefault()
      e.stopPropagation()
      return
    }

    if (e.key === 'Escape') {
      selectedLabel = null
      firstClickTimestamp = null
      secondClickTimestamp = null
      segmentValidation = { isValid: true, stripId }
      e.preventDefault()
      return
    }

    if (e.key === 'n' && !selectedLabel && lastAppliedLabel && lastAppliedLabel.stripId === stripId) {
      selectedLabel = lastAppliedLabel
    } else if (e.key === 'e' && selectedLabel?.stripId === stripId) {
      handleApplyToEntireStrip()
    }

    // Check for label shortcuts
    const pressedKey = e.key.toLowerCase()
    const labelWithShortcut = labels.find((l) => l.shortcut && l.shortcut.toLowerCase() === pressedKey)
    if (labelWithShortcut && !e.ctrlKey && !e.metaKey && !e.altKey) {
      onStripLabelSelect(labelWithShortcut, stripId)
      e.preventDefault()
      return
    }
  }

  const handleCopyStripButtonClick = (event: Event) => {
    event.stopPropagation()
    handleCopyStripLink()
  }

  const handleStripActivityKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleOpenStripActivity()
    }
  }

  const handleApplyToEntireStrip = () => {
    if (!selectedLabel) {
      firstClickTimestamp = null
      secondClickTimestamp = null
      return
    }

    if (
      firstClickTimestamp &&
      firstClickTimestamp > stripTimestamps[0] &&
      firstClickTimestamp < stripTimestamps[lastStripTimestampIndex - 1]
    ) {
      firstClickTimestamp = stripTimestamps[0]
    } else if (
      secondClickTimestamp &&
      secondClickTimestamp > stripTimestamps[0] &&
      secondClickTimestamp < stripTimestamps[lastStripTimestampIndex - 1]
    ) {
      secondClickTimestamp = stripTimestamps[lastStripTimestampIndex - 1]
    } else {
      firstClickTimestamp = stripTimestamps[0]
      secondClickTimestamp = stripTimestamps[lastStripTimestampIndex - 1]
    }

    const firstClickIndex = findTimestampIndex(firstClickTimestamp != null ? firstClickTimestamp : -1, stripTimestamps)
    const secondClickIndex = findTimestampIndex(
      secondClickTimestamp != null ? secondClickTimestamp : -1,
      stripTimestamps
    )

    if (firstClickIndex !== -1 && secondClickIndex !== -1) {
      const mutuallyExclusiveLabels = getMutuallyExclusiveLabels(selectedLabel.value, labels)

      const existingLabelSegments: { start: number; end: number }[] = []
      mutuallyExclusiveLabels.forEach((label) => {
        existingLabelSegments.push(
          ...stripStore?.labels
            .filter((l) => l.label === label && l.start != null && l.end != null)
            .map((l) => ({
              start: findTimestampIndex(l.start != null ? l.start : 0, stripTimestamps),
              end: findTimestampIndex(l.end != null ? l.end : 0, stripTimestamps)
            }))
        )
      })

      // Find the index pairs (gaps) not covered by existingLabelSegments
      // 1. Sort and merge the existing segments
      const sortedSegments = existingLabelSegments
        .filter((seg) => seg.start != -1 && seg.end != -1)
        .map((seg) => ({
          start: Math.min(seg.start, seg.end),
          end: Math.max(seg.start, seg.end)
        }))
        .sort((a, b) => a.start - b.start)

      const mergedSegments = []
      for (const seg of sortedSegments) {
        if (!mergedSegments.length) {
          mergedSegments.push({ ...seg })
        } else {
          const last = mergedSegments[mergedSegments.length - 1]
          if (seg.start <= last.end + 1) {
            last.end = Math.max(last.end, seg.end)
          } else {
            mergedSegments.push({ ...seg })
          }
        }
      }

      // 2. Find the gaps between merged segments
      const uncoveredIndexPairs = []
      let prevEnd = -1
      for (const seg of mergedSegments) {
        if (seg.start > prevEnd + 1) {
          uncoveredIndexPairs.push({ start: prevEnd + 1, end: seg.start - 1 })
        }
        prevEnd = seg.end
      }

      if (prevEnd < stripTimestamps.length - 1) {
        uncoveredIndexPairs.push({ start: prevEnd + 1, end: stripTimestamps.length - 1 })
      }

      const labelSelected = selectedLabel

      uncoveredIndexPairs.forEach((pair) => {
        const result = triggerSegmentValidation(stripTimestamps[pair.start], stripTimestamps[pair.end])

        if (result?.isValid) {
          handleApproveSegmentation(labelSelected, stripTimestamps[pair.start], stripTimestamps[pair.end], {
            moreSecondsLoaded,
            originalStripEndTime,
            currentStripId: stripId
          })
        }
      })

      return
    }

    if (firstClickTimestamp && secondClickTimestamp) {
      const result = triggerSegmentValidation(firstClickTimestamp, secondClickTimestamp)

      if (result?.isValid) {
        handleApproveSegmentation(undefined, undefined, undefined, {
          moreSecondsLoaded,
          originalStripEndTime,
          currentStripId: stripId
        })
      }
    }
  }

  const onApprove = (labelTrack: any, userId: string) => {
    const labelValue = labels.find((l) => l.label === labelTrack.label)?.value ?? ''

    if (!labelValue) {
      return
    }

    episodeStoreActions.approveStripLabel(stripId, {
      label: labelValue,
      timestamp: new Date().getTime(),
      start: labelTrack.start,
      end: labelTrack.end
    })

    Object.entries(labelsWithTracks).forEach(([key, track]) => {
      track.forEach((t) => {
        if (key !== userId && t.label === labelTrack.label) {
          episodeStoreActions.disapproveStripLabel(stripId, {
            label: labelValue,
            timestamp: new Date().getTime(),
            start: t.start,
            end: t.end
          })
        }
      })
    })
  }

  const onDisapprove = (labelTrack: any) =>
    episodeStoreActions.disapproveStripLabel(stripId, {
      label: labels.find((l) => l.label === labelTrack.label)?.value ?? '',
      timestamp: new Date().getTime(),
      start: labelTrack.start,
      end: labelTrack.end
    })

  $: stripHasSegment = (() => {
    if (firstClickTimestamp === null && secondClickTimestamp === null) {
      return false
    }

    const stripStartTimestamp = stripTimestamps[0]
    const stripEndTimestamp = stripTimestamps[stripTimestamps.length - 1]

    if (firstClickTimestamp && secondClickTimestamp === null) {
      return firstClickTimestamp >= stripStartTimestamp && firstClickTimestamp <= stripEndTimestamp
    } else if (firstClickTimestamp && secondClickTimestamp) {
      const firstTimestamp = Math.min(firstClickTimestamp, secondClickTimestamp)
      const secondTimestamp = Math.max(firstClickTimestamp, secondClickTimestamp)

      return (
        (firstTimestamp >= stripStartTimestamp && firstTimestamp <= stripEndTimestamp) ||
        (secondTimestamp >= stripStartTimestamp && secondTimestamp <= stripEndTimestamp) ||
        (!(firstTimestamp <= stripStartTimestamp && secondTimestamp <= stripEndTimestamp) &&
          !(firstTimestamp >= stripStartTimestamp && secondTimestamp >= stripEndTimestamp))
      )
    }

    return false
  })()

  $: canApplyToEntireStrip =
    firstClickTimestamp &&
    firstClickTimestamp > stripTimestamps[0] &&
    firstClickTimestamp < stripTimestamps[stripTimestamps.length - 1] &&
    secondClickTimestamp &&
    secondClickTimestamp > stripTimestamps[0] &&
    secondClickTimestamp < stripTimestamps[stripTimestamps.length - 1]

  $: if ((firstClickTimestamp === null || secondClickTimestamp === null) && !segmentValidation?.isValid) {
    segmentValidation = { isValid: true }
  }

  // Get label position preference
  $: labelPositionSettings = $labelPositionSettingsStore
  $: labelPosition = labelPositionSettings[project._id] ?? 'left'

  // Multi-column layout configuration for left labels
  $: maxLabelsPerColumn = 7 // Maximum labels per column before creating a new column
  $: shouldUseMultiColumn = labelPosition === 'left' && labels.length > maxLabelsPerColumn
  $: numColumns = shouldUseMultiColumn ? Math.ceil(labels.length / maxLabelsPerColumn) : 1
  $: labelsPerColumn = shouldUseMultiColumn ? Math.ceil(labels.length / numColumns) : labels.length

  // Split labels into columns
  $: labelColumns = (() => {
    if (!shouldUseMultiColumn) {
      return [labels]
    }

    const columns = []
    for (let i = 0; i < numColumns; i++) {
      const startIndex = i * labelsPerColumn
      const endIndex = Math.min(startIndex + labelsPerColumn, labels.length)
      columns.push(labels.slice(startIndex, endIndex))
    }
    return columns
  })()

  // Variables to track actual heights
  let graphContainerHeight = 0

  onDestroy(() => {
    if (stripCopyTimeout) {
      clearTimeout(stripCopyTimeout)
    }
    if (firstSnapTimeout) {
      clearTimeout(firstSnapTimeout)
    }
    if (secondSnapTimeout) {
      clearTimeout(secondSnapTimeout)
    }
  })
</script>

{#if stripStore}
  <div
    bind:this={stripContainerElement}
    class="strip-card shadow-md"
    class:strip-card-with-left-labels={labelPosition === 'left'}
    data-strip-id={stripId}
    on:keydown={handleStripKeyDown}
    tabindex="-1"
    role="button"
  >
    <div class="strip-header">
      <div
        class="strip-meta text-lg font-semi-bold cursor-pointer"
        on:click={handleOpenStripActivity}
        on:keydown={handleStripActivityKeyDown}
        tabindex="-1"
        role="button"
      >
        <div class="strip-number">
          <Icon icon={annotationTool.icon.Ecg} size="large" />
        </div>
        <span class="strip-id" class:has-activity={hasActivity}>
          {stripId}
        </span>
        <ButtonIcon
          icon={view.icon.CopyLink}
          size="small"
          kind="tertiary"
          iconProps={{ style: 'color: var(--theme-text-editor-palette-text-gray)!important;' }}
          pressed={stripLinkCopied}
          tooltip={{
            label: stripLinkCopied ? annotationTool.string.LinkCopied : annotationTool.string.CopyStripLink
          }}
          on:click={handleCopyStripButtonClick}
        />
        {#if !segmentValidation?.isValid}
          <div class="validation-error">
            ⚠ "{selectedLabel?.label}" {segmentValidation?.errorMessage}
          </div>
        {/if}
      </div>
      <div class="strip-labels strip-buttons">
        <Button
          size="small"
          kind="ghost"
          padding="4px"
          pressed={swipeSpeed === 25}
          on:click={() => (swipeSpeed = 25)}
          label={annotationTool.string['25mms']}
        />
        <Button
          size="small"
          kind="ghost"
          padding="4px"
          pressed={swipeSpeed === 50}
          on:click={() => (swipeSpeed = 50)}
          label={annotationTool.string['50mms']}
        />
        <div class="vertical-divider" />
        <ButtonIcon
          icon={annotationTool.icon.Ruler}
          pressed={showRuler}
          size="small"
          kind="tertiary"
          on:click={() => (showRuler = !showRuler)}
          tooltip={{ label: annotationTool.string.Ruler }}
        />
        <ButtonIcon
          icon={annotationTool.icon.Caliper}
          pressed={showCaliper}
          size="small"
          kind="tertiary"
          on:click={() => (showCaliper = !showCaliper)}
          tooltip={{ label: annotationTool.string.Caliper }}
        />
        <div class="vertical-divider" />
        {#if !isConflictResolution && annotatorAccessLevel === 'level2'}
          <ButtonIcon
            icon={annotationTool.icon.MyAnnotations}
            pressed={annotationsView === 'my'}
            size="small"
            kind="tertiary"
            on:click={() => (annotationsView = 'my')}
            tooltip={{ label: annotationTool.string.MyAnnotations }}
          />
          <ButtonIcon
            icon={annotationTool.icon.AllAnnotations}
            pressed={annotationsView === 'all'}
            size="small"
            kind="tertiary"
            on:click={() => (annotationsView = 'all')}
            tooltip={{ label: annotationTool.string.AllAnnotations }}
          />
          <div class="vertical-divider" />
        {/if}
        <CheckBox
          size="large"
          checked={stripStore.isInterestingStrip}
          on:value={(ev) => episodeStoreActions.addRemoveInterestingStrip(stripId, ev.detail)}
        />
        <Label label={annotationTool.string.IsInterestingStrip} />
      </div>
    </div>
    <div class="horizontal-divider" />

    <!-- Main content area with conditional layout based on label position -->
    <div class="strip-content" class:strip-content-with-left-labels={labelPosition === 'left'}>
      <!-- Left labels section (only when labelPosition is 'left') -->
      {#if labelPosition === 'left'}
        <div class="strip-labels-left" class:strip-labels-left-multi-column={shouldUseMultiColumn}>
          <div
            class="strip-labels-left-container"
            class:strip-labels-left-container-multi-column={shouldUseMultiColumn}
          >
            {#each labelColumns as columnLabels, columnIndex}
              <div class="strip-labels-column">
                {#each columnLabels as label (label.value)}
                  <AnnotationLabel
                    {label}
                    selected={stripStore.labels.some((l) => l.label === label.value) ||
                      (selectedLabel?.value === label.value && selectedLabel.stripId === stripId)}
                    segmentationSelected={selectedLabel?.value === label.value && selectedLabel.stripId === stripId}
                    numSelected={isConflictResolution
                      ? stripStore.labels.filter((l) => l.label === label.value).length
                      : 0}
                    selectable
                    width="100%"
                    onSelect={() => onStripLabelSelect(label, stripId)}
                    allowDelete={false}
                  />
                {/each}
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Graph section -->
      <div
        class="strip-graph strip-graph-card overflow-x-auto"
        class:strip-graph-with-left-labels={labelPosition === 'left'}
        bind:clientHeight={graphContainerHeight}
      >
        <div class="relative mx-auto" style={`width: ${millimeterToPx(swipeSpeed * (stripSecs / 1000)) + 40}px;`}>
          {#if showRuler}
            <EpisodeStripRuler
              lengthCm={swipeSpeed + (stripSecs - 10000) / 1000}
              width={millimeterToPx(swipeSpeed * (stripSecs / 1000)) + 40}
            />
          {/if}
          <Lazy height={300 + (Object.keys(labelsWithTracks).length > 0 ? Math.max(32, 28 + labelAreaHeight) : 0)} fadeOption={null}>
            <State initial={[null, null]} let:value={xDomain} let:set>
              <div
                class="h-[300px] p-4 border rounded"
                style={`margin-bottom: ${Object.keys(labelsWithTracks).length > 0 ? Math.max(32, 28 + labelAreaHeight) : 0}px;`}
              >
                {#if showCaliper}
                  <EpisodeStripCaliper
                    width={caliperWidth}
                    height={caliperHeight}
                    {stripTimestamps}
                    {showRuler}
                    {xDomain}
                    onRemove={() => (showCaliper = false)}
                  />
                {/if}
                <Chart
                  data={dateSeriesData}
                  x="date"
                  xScale={scaleTime()}
                  {xDomain}
                  y="value"
                  {yDomain}
                  yNice
                  padding={{ left: 4, bottom: 8 }}
                  tooltip={{ mode: 'bisect-x' }}
                  brush={selectedLabel == null
                    ? {
                        resetOnEnd: true,
                        onbrushend: (e) => {
                          // @ts-expect-error
                          set(e.xDomain)
                          resetZoomFn = set
                        },
                        classes: { range: 'ecg-brush-range', handle: 'ecg-brush-handles' }
                      }
                    : undefined}
                  let:width
                  let:height
                  let:padding
                  let:xScale
                  let:yScale
                >
                  <Svg>
                    <Axis
                      placement="left"
                      format={(d) => `${d} mV`}
                      rule
                      ticks={Math.max(4, Math.round(height / (width / 50) / (viewMode === 'card' ? 2 : 1)))}
                      tickLength={8}
                      classes={{
                        rule: 'ecg-graph-axis-color',
                        tick: 'ecg-graph-axis-color',
                        tickLabel: 'ecg-graph-axis-label-hidden'
                      }}
                    />
                    <Axis
                      placement="bottom"
                      format={(d) => `${resetZoomFn ? (d / 1000).toFixed(1) : ~~(d / 1000)}s`}
                      rule
                      ticks={10}
                      tickLabelProps={{ textAnchor: 'start', dx: 8, dy: 4 }}
                      tickLength={18}
                      classes={{
                        rule: 'ecg-graph-axis-color',
                        tick: 'ecg-graph-axis-color',
                        tickLabel: $themeStore.dark ? 'ecg-graph-axis-label-white' : 'ecg-graph-axis-label-black'
                      }}
                    />

                    {#if firstClickTimestamp && selectedLabel && stripHasSegment}
                      <Rule
                        x={firstClickTimestamp < stripTimestamps[0] ? stripTimestamps[0] : firstClickTimestamp}
                        class={`segmentation-rule${firstSnapAnimating ? ' segmentation-rule-snapped' : ''}`}
                        style={`stroke: ${selectedLabel.textColor}`}
                      />
                    {/if}
                    {#if secondClickTimestamp && selectedLabel && stripHasSegment}
                      <Rule
                        x={secondClickTimestamp > stripTimestamps[stripTimestamps.length - 1]
                          ? stripTimestamps[stripTimestamps.length - 1]
                          : secondClickTimestamp}
                        class={`segmentation-rule${secondSnapAnimating ? ' segmentation-rule-snapped' : ''}`}
                        style={`stroke: ${selectedLabel.textColor}`}
                      />
                    {/if}
                    {#if firstClickTimestamp && secondClickTimestamp && selectedLabel && stripHasSegment}
                      {@const firstTimestamp =
                        firstClickTimestamp < stripTimestamps[0] ? stripTimestamps[0] : firstClickTimestamp}
                      {@const secondTimestamp =
                        secondClickTimestamp > stripTimestamps[stripTimestamps.length - 1]
                          ? stripTimestamps[stripTimestamps.length - 1]
                          : secondClickTimestamp}
                      {@const x = firstTimestamp}
                      {@const width = Math.abs(xScale(secondTimestamp) - xScale(firstTimestamp))}
                      <Rect
                        x={xScale(x)}
                        y={0}
                        {width}
                        {height}
                        style={`fill: ${selectedLabel.backgroundColor}; fill-opacity: 0.5;`}
                      />
                    {/if}

                    {#if moreSecondsLoaded}
                      <Rule x={stripTimestamps[lastStripTimestampIndex]} class="more-seconds-rule" />
                    {/if}

                    <Highlight
                      lines={{
                        class: 'ecg-graph-highlight-line-color'
                      }}
                    />
                  </Svg>

                  <EpisodeStripGrid
                    {width}
                    {height}
                    {padding}
                    {xScale}
                    {yScale}
                    {viewMode}
                    bind:innerHeight={caliperHeight}
                    bind:innerWidth={caliperWidth}
                  />

                  <Canvas>
                    <Spline strokeWidth={1} class="ecg-graph-stroke-color" />
                  </Canvas>

                  <!-- HTML/CSS Label Segments Overlay -->
                  <div class="absolute inset-0" style="pointer-events: none;">
                    <EpisodeStripSegments
                      {labelsWithTracks}
                      {isMultiUserMode}
                      {shuffledColors}
                      {height}
                      {width}
                      {xScale}
                      {stripId}
                      {nextStripId}
                      {moreSecondsLoaded}
                      {originalStripEndTime}
                      {episodeStoreActions}
                      {labels}
                      {currentAccount}
                      {isConflictResolution}
                      {stripStore}
                      {segmentComments}
                      {segmentInteresting}
                      {onApprove}
                      {onDisapprove}
                      {snapHighlights}
                    />
                  </div>

                  <EpisodeStripClickOverlays
                    {stripId}
                    {width}
                    {height}
                    {padding}
                    {xScale}
                    {yScale}
                    {onStripClick}
                    {onLeftOrRightStripClick}
                  />

                  <!-- Selected segment action buttons -->
                  {#if firstClickTimestamp && secondClickTimestamp && selectedLabel && stripHasSegment}
                    {@const top = 10}
                    {@const left =
                      xScale(firstClickTimestamp < stripTimestamps[0] ? stripTimestamps[0] : firstClickTimestamp) + 10}
                    <div
                      class="absolute z-10 flex gap-1"
                      style="top: {top}px; left: {left}px;"
                      role="button"
                      tabindex="-1"
                      aria-label="Accept or reject segmentation"
                      on:mousemove|stopPropagation|preventDefault
                      on:mousedown|stopPropagation|preventDefault
                      on:mouseup|stopPropagation|preventDefault
                      on:mouseenter|stopPropagation|preventDefault
                      on:mouseleave|stopPropagation|preventDefault
                      on:mouseover|stopPropagation|preventDefault
                      on:mouseout|stopPropagation|preventDefault
                      on:focus|stopPropagation|preventDefault
                      on:blur|stopPropagation|preventDefault
                    >
                      <!-- Accept button hidden since we now auto-confirm -->
                      <!-- <ButtonIcon
                        icon={annotationTool.icon.CheckmarkCircle}
                        size="small"
                        on:click={() =>
                          handleApproveSegmentation(undefined, undefined, undefined, {
                            moreSecondsLoaded,
                            originalStripEndTime,
                            currentStripId: stripId
                          })}
                        disabled={!segmentValidation?.isValid}
                        tooltip={{ label: annotationTool.string.DoneD }}
                      /> -->
                      <ButtonIcon
                        icon={annotationTool.icon.XCircle}
                        size="small"
                        on:click={handleDisapproveSegmentation}
                        tooltip={{ label: annotationTool.string.ClearX }}
                      />
                      <!-- Apply to entire strip button hidden for now -->
                      <!-- {#if canApplyToEntireStrip}
                        <ButtonIcon
                          icon={annotationTool.icon.ApplyToWholeEpisode}
                          size="small"
                          on:click={handleApplyToEntireStrip}
                          disabled={!segmentValidation?.isValid}
                          tooltip={{ label: annotationTool.string.ApplyToEntireStripE }}
                        />
                      {/if} -->

                      <!-- Snap buttons hidden for now -->
                      <!-- {#if handleSnapLeft || handleSnapRight}
                        {#if handleSnapLeft}
                          <ButtonIcon
                            icon={annotationTool.icon.ChevronDoubleLeft}
                            size="small"
                            on:click={() => handleSnapLeft(stripStore, strip, {
                              moreSecondsLoaded,
                              originalStripEndTime,
                              nextStripAllLabels
                            })}
                            tooltip={{ label: annotationTool.string.SnapLeft }}
                          />
                        {/if}
                        {#if handleSnapRight}
                          <ButtonIcon
                            icon={annotationTool.icon.ChevronDoubleRight}
                            size="small"
                            on:click={() => handleSnapRight(stripStore, strip, {
                              moreSecondsLoaded,
                              originalStripEndTime,
                              nextStripAllLabels
                            })}
                            tooltip={{ label: annotationTool.string.SnapRight }}
                          />
                        {/if}
                      {/if} -->
                    </div>
                  {/if}
                </Chart>
              </div>
            </State>
          </Lazy>
          <!-- Apply to entire strip button hidden for now -->
          <!-- {#if selectedLabel && ((firstClickTimestamp === null && secondClickTimestamp === null && labelSelectedOnCurrentStrip) || canApplyToEntireStrip)}
            <div class="strip-apply-to-whole-strip" class:strip-apply-to-whole-strip-ruler={showRuler}>
              <ButtonIcon
                icon={annotationTool.icon.ApplyToWholeEpisode}
                size="small"
                on:click={handleApplyToEntireStrip}
                disabled={!segmentValidation?.isValid}
                tooltip={{ label: annotationTool.string.ApplyToEntireStripE }}
              />
            </div>
          {/if} -->
          {#if !selectedLabel && lastAppliedLabel && lastAppliedLabel.stripId === stripId}
            <div class="strip-apply-to-whole-strip" class:strip-apply-to-whole-strip-ruler={showRuler}>
              <ButtonIcon
                icon={annotationTool.icon.LabelPlus}
                size="small"
                on:click={() => {
                  selectedLabel = lastAppliedLabel
                }}
                tooltip={{ label: annotationTool.string.AddNewSegment }}
              />
            </div>
          {/if}
          <div class="strip-zoomout" class:strip-zoomout-ruler={showRuler}>
            <ButtonIcon
              icon={annotationTool.icon.ZoomOut}
              size="small"
              disabled={zoomLevel >= 1}
              on:click={handleStripZoomOut}
              tooltip={{ label: annotationTool.string.ZoomOut }}
            />
            <ButtonIcon
              icon={annotationTool.icon.ZoomIn}
              size="small"
              disabled={zoomLevel <= 0}
              on:click={handleStripZoomIn}
              tooltip={{ label: annotationTool.string.ZoomIn }}
            />
            {#if resetZoomFn}
              <ButtonIcon
                icon={annotationTool.icon.Expand}
                size="small"
                on:click={handleStripExpand}
                tooltip={{ label: annotationTool.string.ExtendStrip }}
              />
            {/if}
            {#if !moreSecondsLoaded && nextStripValues?.length > 0 && nextStripTimestamps?.length > 0}
              <ButtonIcon
                icon={annotationTool.icon.LoadMoreSeconds}
                size="small"
                on:click={handleLoadMoreSeconds}
                tooltip={{ label: annotationTool.string.ExtendStrip }}
              />
            {/if}
            {#if moreSecondsLoaded}
              <ButtonIcon
                icon={annotationTool.icon.UnloadMoreSeconds}
                size="small"
                on:click={handleUnloadMoreSeconds}
                tooltip={{ label: annotationTool.string.ReduceStrip }}
              />
            {/if}
          </div>
        </div>
      </div>

      <!-- Bottom labels section (only when labelPosition is 'below') -->
      {#if labelPosition === 'below'}
        <div class="horizontal-divider" style="margin: 0 0 8px 0;" />
        <div class="strip-labels-container" style="padding: 0 0.25rem 0.5rem 0.25rem;">
          <div class="strip-labels justify-between flex-nowrap overflow-x-auto">
            {#each labels as label (label.value)}
              <AnnotationLabel
                {label}
                selected={stripStore.labels.some((l) => l.label === label.value) ||
                  (selectedLabel?.value === label.value && selectedLabel.stripId === stripId)}
                segmentationSelected={selectedLabel?.value === label.value && selectedLabel.stripId === stripId}
                numSelected={isConflictResolution ? stripStore.labels.filter((l) => l.label === label.value).length : 0}
                selectable
                width="100%"
                onSelect={() => onStripLabelSelect(label, stripId)}
                allowDelete={false}
              />
            {/each}
          </div>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style lang="scss">
  .strip-card {
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.75rem;
    margin: 0.5rem 0.5rem;
  }

  .strip-header {
    width: 100%;
    display: flex;
    gap: 16px;
    padding: 0.5rem;
    flex-wrap: nowrap;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
    padding: 0.5rem;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
    justify-content: space-between;
    align-items: center;
    background-color: var(--theme-comp-header-color);
    background-color: var(--theme-comp-header-color);
  }
  .horizontal-divider {
    width: 100%;
    border-bottom: 1px solid var(--theme-divider-color);
  }
  .vertical-divider {
    display: flex;
    align-self: stretch;
    width: 1px;
    border-left: 1px solid var(--theme-divider-color);
  }
  .strip-number {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }
  .strip-id {
    position: relative;
    font-size: 14px;
    font-weight: 600;
    color: var(--theme-text-editor-palette-text-gray);
  }
  .has-activity::after {
    content: '';
    position: absolute;
    top: 4px;
    right: -10px;
    width: 8px;
    height: 8px;
    background-color: var(--highlight-red);
    border-radius: 100%;
  }
  .strip-meta {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    flex-wrap: nowrap;
    gap: 8px;
    min-width: 0;
  }
  .copy-link-button {
    color: var(--theme-text-editor-palette-text-gray);
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
  }
  .copy-link-button:hover,
  .copy-link-button:focus-visible,
  .copy-link-button[aria-pressed='true'] {
    opacity: 1;
  }
  .copy-link-button {
    color: var(--theme-text-editor-palette-text-gray);
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
  }
  .copy-link-button:hover,
  .copy-link-button:focus-visible,
  .copy-link-button[aria-pressed='true'] {
    opacity: 1;
  }
  .strip-labels-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0 0.25rem 0.25rem 0.25rem;
  }
  .strip-labels {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    overflow: visible;
  }

  .strip-buttons {
    flex-wrap: nowrap;
  }

  /* New styles for left label positioning */
  .strip-content {
    display: flex;
    flex-direction: column;
  }

  .strip-content-with-left-labels {
    flex-direction: row;
    align-items: flex-start;
  }

  .strip-labels-left {
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
    border-right: 1px solid var(--theme-divider-color);
    background-color: var(--theme-bg-accent-color);
    overflow-y: auto;
    min-width: 180px; /* Minimum width for single column */
    max-width: 320px; /* Maximum width to prevent taking too much space */
  }

  .strip-labels-left-multi-column {
    min-width: 240px; /* Wider minimum for multi-column */
    max-width: 400px; /* Wider maximum for multi-column */
  }

  .strip-labels-left-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    min-height: fit-content;
  }

  .strip-labels-left-container-multi-column {
    flex-direction: row;
    gap: 12px;
    align-items: flex-start;
  }

  .strip-labels-column {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    flex: 1;
    min-width: 0; /* Allow columns to shrink */
  }

  .strip-graph-with-left-labels {
    flex: 1;
    margin-left: 0;
  }
  .strip-graph {
    width: 100%;
    display: flex;
    gap: 0.5rem;
    padding: 0 0.25rem 0.5rem 0.25rem;
  }
  .strip-graph-card {
    flex-direction: column;
  }
  .strip-zoomout {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1000;
  }
  .strip-zoomout-ruler {
    top: 48px;
  }
  .strip-apply-to-whole-strip {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    position: absolute;
    top: 8px;
    left: 24px;
    z-index: 1000;
  }
  .strip-apply-to-whole-strip-ruler {
    top: 48px;
  }

  .validation-error {
    flex-basis: 100%;
    width: 100%;
    min-width: 0;
    font-size: 15px;
    font-weight: 600;
    padding: 2px 12px;
    background: #ca4242;
    color: white;
    border-radius: 6px;
    box-shadow: 0 1px 4px 0 rgba(50, 0, 0, 0.04);
    word-break: break-word;
    white-space: normal;
    overflow-wrap: break-word;
    hyphens: auto;
    margin-left: 16px;
    order: 10;
  }

  :global(.more-seconds-rule) {
    box-sizing: border-box !important;
    stroke-linecap: round !important;
    stroke-dasharray: 4 !important;
    stroke-width: 2 !important;
    stroke: var(--negative-button-disabled-color) !important;
  }
  :global(.segmentation-rule) {
    box-sizing: border-box !important;
    stroke-linecap: round !important;
    stroke-width: 2 !important;
  }
  :global(.segmentation-rule-snapped) {
    animation: segmentation-rule-snap 250ms ease-out;
    stroke-width: 3 !important;
  }
  @keyframes segmentation-rule-snap {
    0% {
      stroke-opacity: 1;
      stroke-width: 2;
    }
    50% {
      stroke-opacity: 0.35;
      stroke-width: 4;
    }
    100% {
      stroke-opacity: 1;
      stroke-width: 2;
    }
  }
  :global(.strip-label-text tspan) {
    font-size: 0.875rem;
    font-weight: 600;
  }
  :global(.strip-label-button tspan) {
    cursor: pointer;
    font-size: 1rem;
    font-weight: 800;
    transform: scaleX(1.25);
    transition: all 0.2s ease;
  }
  :global(.strip-label-button:hover tspan) {
    transform: scaleX(1.25) scale(1.2);
    opacity: 0.8;
  }
  :global(.ecg-graph-stroke-color) {
    stroke: var(--primary-button-default) !important;
  }
  :global(.ecg-brush-handles) {
    background-color: var(--secondary-button-pressed) !important;
  }
  :global(.ecg-brush-range) {
    background-color: var(--primary-button-transparent) !important;
  }
  :global(.ecg-graph-tooltip-background) {
    background-color: var(--theme-tooltip-color) !important;
  }
  :global(.ecg-graph-tooltip-text) {
    color: var(--theme-tooltip-bg) !important;
  }
  :global(.ecg-graph-highlight-line-color) {
    stroke: var(--primary-button-default) !important;
    stroke-width: 1px !important;
  }
  :global(.ecg-graph-axis-color) {
    stroke: var(--theme-navpanel-icons-color) !important;
  }
  :global(.ecg-graph-grid-color) {
    stroke: var(--theme-overlay-color) !important;
  }
  :global(.ecg-graph-grid-color-secondary) {
    stroke: var(--theme-divider-color) !important;
  }
  :global(.ecg-graph-axis-label-black) {
    stroke: unset !important;
    fill: black !important;
  }
  :global(.ecg-graph-axis-label-white) {
    stroke: unset !important;
    fill: white !important;
  }
  :global(.ecg-graph-axis-label-hidden) {
    display: none !important;
  }
  :global(.strip-labels button.ghost) {
    font-weight: 600 !important;
  }
</style>
