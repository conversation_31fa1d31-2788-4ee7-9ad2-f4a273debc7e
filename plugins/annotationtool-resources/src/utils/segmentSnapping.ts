import { type ExistingSegment } from './segmentValidation'

export interface SnapResult {
  snappedTimestamp: number
  foundLabel?: ExistingSegment
}

/**
 * Snap a timestamp to the closest existing segment boundary (start or end) within a tolerance window.
 * Falls back to the original timestamp when no nearby boundary is found.
 */
export function snapTimestampToExistingSegments(
  timestamp: number,
  existingLabels: ExistingSegment[],
  thresholdMs: number = 200
): SnapResult {
  let bestTimestamp = timestamp
  let bestLabel: ExistingSegment | undefined
  let closestDistance = thresholdMs + 1

  for (const label of existingLabels) {
    const candidateTimestamps: number[] = []

    if (typeof label.start === 'number') {
      candidateTimestamps.push(label.start)
    }
    if (typeof label.end === 'number') {
      candidateTimestamps.push(label.end)
    }

    for (const candidate of candidateTimestamps) {
      const distance = Math.abs(candidate - timestamp)
      if (distance <= thresholdMs && distance < closestDistance) {
        closestDistance = distance
        bestTimestamp = candidate
        bestLabel = label
      }
    }
  }

  return {
    snappedTimestamp: bestTimestamp,
    foundLabel: bestLabel
  }
}

/**
 * Snaps the start of a segment to the closest end timestamp of existing labels
 * 
 * @param segmentStart - The current start timestamp of the segment being created
 * @param segmentEnd - The current end timestamp of the segment being created  
 * @param existingLabels - Array of existing labels with start/end timestamps
 * @returns The snapped timestamp (+1 from closest end) or original if no labels found
 */
export function snapToClosestEndTimestamp(
  segmentStart: number,
  segmentEnd: number,
  existingLabels: ExistingSegment[]
): SnapResult {
  // Find all labels with valid end timestamps that could be snap targets
  const candidateLabels = existingLabels.filter(label => 
    label.end != null
  )

  if (candidateLabels.length === 0) {
    return { snappedTimestamp: segmentStart }
  }

  // Find the closest label end to snap to (end + 1)
  let bestSnapTarget: ExistingSegment | null = null
  let bestSnapTimestamp = segmentStart
  let closestDistance = Infinity

  for (const label of candidateLabels) {
    const potentialSnapTimestamp = label.end! + 1
    
    // Calculate distance based on the actual label end position
    const distance = Math.abs(segmentStart - label.end!)
    
    // Find the closest label end to snap to
    if (distance < closestDistance) {
      closestDistance = distance
      bestSnapTarget = label
      bestSnapTimestamp = potentialSnapTimestamp
    }
  }

  if (bestSnapTarget) {
    return { 
      snappedTimestamp: bestSnapTimestamp,
      foundLabel: bestSnapTarget
    }
  }

  return { snappedTimestamp: segmentStart }
}

/**
 * Snaps the end of a segment to the closest start timestamp of existing labels
 * 
 * @param segmentStart - The current start timestamp of the segment being created
 * @param segmentEnd - The current end timestamp of the segment being created
 * @param existingLabels - Array of existing labels with start/end timestamps  
 * @returns The snapped timestamp (-1 from closest start) or original if no labels with start >= segmentStart found
 */
export function snapToClosestStartTimestamp(
  segmentStart: number,
  segmentEnd: number,
  existingLabels: ExistingSegment[]
): SnapResult {
  // Find all labels with valid start timestamps that could be snap targets
  // Only consider labels whose start is after our segment start
  const candidateLabels = existingLabels.filter(label => 
    label.start != null && 
    label.start >= segmentStart
  )

  if (candidateLabels.length === 0) {
    return { snappedTimestamp: segmentEnd }
  }

  // Find the closest label start to snap to (start - 1)
  let bestSnapTarget: ExistingSegment | null = null
  let bestSnapTimestamp = segmentEnd
  let closestDistance = Infinity

  for (const label of candidateLabels) {
    const potentialSnapTimestamp = label.start! - 1
    
    // Calculate distance based on the actual label start position
    const distance = Math.abs(segmentEnd - label.start!)
    
    // Find the closest label start to snap to
    if (distance < closestDistance) {
      closestDistance = distance
      bestSnapTarget = label
      bestSnapTimestamp = potentialSnapTimestamp
    }
  }

  if (bestSnapTarget) {
    return { 
      snappedTimestamp: bestSnapTimestamp,
      foundLabel: bestSnapTarget
    }
  }

  return { snappedTimestamp: segmentEnd }
}
