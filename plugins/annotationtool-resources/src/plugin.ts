import { mergeIds } from '@hcengineering/platform'
import annotationTool, { annotationToolId } from '@hcengineering/annotationtool'
import type { IntlString } from '@hcengineering/platform'

export default mergeIds(annotationToolId, annotationTool, {
    string: {
        Title: '' as IntlString,
        Annotate: '' as IntlString,
        Dashboard: '' as IntlString,
        Activity: '' as IntlString,
        AnnotationActivityMessages: '' as IntlString,
        Filter: '' as IntlString,
        FilterTooltip: '' as IntlString,
        AnnotateFilters: '' as IntlString,
        CloseFilters: '' as IntlString,
        ClearFilters: '' as IntlString,
        ClinicFilter: '' as IntlString,
        PatientFilter: '' as IntlString,
        SelectClinicFirst: '' as IntlString,
        Manufacturer: '' as IntlString,
        UnlabelledPatients: '' as IntlString,
        LabelFilter: '' as IntlString,
        EpisodeFilter: '' as IntlString,
        SelectConflictStatus: '' as IntlString,
        LabelledBy: '' as IntlString,
        FromDate: '' as IntlString,
        ToDate: '' as IntlString,
        SelectClinic: '' as IntlString,
        SelectPatient: '' as IntlString,
        SelectManufacturer: '' as IntlString,
        SelectLabel: '' as IntlString,
        SelectLabelledBy: '' as IntlString,
        NotLabelledBy: '' as IntlString,    
        SelectNotLabelledBy: '' as IntlString,
        SelectFromDate: '' as IntlString,
        SelectToDate: '' as IntlString,
        FirstPage: '' as IntlString,
        PreviousPage: '' as IntlString,
        NextPage: '' as IntlString,
        LastPage: '' as IntlString,
        Strips: '' as IntlString,
        Strip: '' as IntlString,
        NotSpecified: '' as IntlString,
        TooltipPatientDetails: '' as IntlString,
        TooltipManufacturerDetails: '' as IntlString,
        TooltipEpisodeId: '' as IntlString,
        TooltipDeviceLabel: '' as IntlString,
        TooltipReportTime: '' as IntlString,
        TooltipEpisodeLength: '' as IntlString,
        TooltipStrips: '' as IntlString,
        AnnotateWholeEpisode: '' as IntlString,
        SettingsTitle: '' as IntlString,
        LabelsConfiguration: '' as IntlString,
        TriageConfiguration: '' as IntlString,
        CreateLabel: '' as IntlString,
        CreateLabelButton: '' as IntlString,
        CreateTriageLabel: '' as IntlString,
        CreateTriageLabelButton: '' as IntlString,
        AddLabel: '' as IntlString,
        NoLabels: '' as IntlString,
        NoTriageLabels: '' as IntlString,
        LabelPlaceholder: '' as IntlString, 
        TriagePlaceholder: '' as IntlString,
        LabelColor: '' as IntlString,
        LabelShortcut: '' as IntlString,
        ShortcutPlaceholder: '' as IntlString,
        SelectLabels: '' as IntlString,
        SelectTriage: '' as IntlString,
        StripID: '' as IntlString,
        EpisodeIDFilter: '' as IntlString,
        SelectEpisodeID: '' as IntlString,
        Labels: '' as IntlString,
        Triage: '' as IntlString,
        EpisodeLabels: '' as IntlString,
        EpisodeTriage: '' as IntlString,
        IsInterestingEpisode: '' as IntlString,
        IsInterestingStrip: '' as IntlString,
        IsDoneEpisode: '' as IntlString,
        IsConflictResolvedEpisode: '' as IntlString,
        DescribeEpisode: '' as IntlString,
        EpisodeActivity: '' as IntlString,
        StripActivity: '' as IntlString,
        EpisodeViewMode: '' as IntlString,
        EpisodeListView: '' as IntlString,
        EpisodeCardView: '' as IntlString,  
        NumberOfEpisodesPerPage: '' as IntlString,
        Close: '' as IntlString,
        CollapseEpisode: '' as IntlString,
        ExpandEpisode: '' as IntlString,
        EpisodeIsCollapsed: '' as IntlString,
        Annotators: '' as IntlString,
        AnnotatorAccessLevel: '' as IntlString,
        AnnotatorLevelLabel: '' as IntlString,
        AnnotatorLevel1: '' as IntlString,
        AnnotatorLevel2: '' as IntlString,
        ConflictResolvingAnnotator: '' as IntlString,
        TableSettings: '' as IntlString,
        TableSettingsTotalPatients: '' as IntlString,
        TableSettingsTotalEpisodes: '' as IntlString,
        TableSettingsTotalEcgStrips: '' as IntlString,
        TableSettingsTooltip: '' as IntlString,
        HierchicalSections: '' as IntlString,
        TableSettingsClinic: '' as IntlString,
        TableSettingsManufacturer: '' as IntlString,
        TableSettingsDeviceModel: '' as IntlString,
        TableSettingsDeviceLabel: '' as IntlString,
        TableSettingsDoctorLabel: '' as IntlString,
        LeftColumnDistributor: '' as IntlString,
        LeftColumnDistributorEpisodeLength: '' as IntlString,
        LeftColumnDistributorDeviceLabel: '' as IntlString,
        LeftColumnDistributorDoctorLabel: '' as IntlString,
        EpisodeLengthFrom: '' as IntlString,
        EpisodeLengthTo: '' as IntlString,
        SelectEpisodeLengthFrom: '' as IntlString,
        SelectEpisodeLengthTo: '' as IntlString,
        LessThan1Minute: '' as IntlString,
        "1Minute": '' as IntlString,
        "3Minutes": '' as IntlString,
        "5Minutes": '' as IntlString,
        DashboardNoData: '' as IntlString,
        Sorting: '' as IntlString,
        SelectSorting: '' as IntlString,
        SubmitLabels: '' as IntlString,
        SubmittedLabels: '' as IntlString,
        NotLabelledEpisodesWarning: '' as IntlString,
        OfflineModeBanner: '' as IntlString,
        NoEpisodesFound: '' as IntlString,
        ErrorLoadingEpisodes: '' as IntlString,
        PaginationInfo: '' as IntlString,
        NoOptionsAvailable: '' as IntlString,
        SavingChanges: '' as IntlString,
        ChangesSaved: '' as IntlString,
        None: '' as IntlString,
        LabelledOnly: '' as IntlString,
        Conflict: '' as IntlString,
        Approve: '' as IntlString,
        Reject: '' as IntlString,
        Added: '' as IntlString,
        Removed: '' as IntlString,
        Approved: '' as IntlString,
        Disapproved: '' as IntlString,
        StripTitle: '' as IntlString,
        NoHistory: '' as IntlString,
        AnnotationActivityNotificationTitle: '' as IntlString,
        AnnotationActivityNotificationSubtitle: '' as IntlString,
        ByDay: '' as IntlString,
        Week: '' as IntlString,
        Total: '' as IntlString,
        ClearAll: '' as IntlString,
        CopyEpisodeLink: '' as IntlString,
        CopyStripLink: '' as IntlString,
        LinkCopied: '' as IntlString,
        NumDoneEpisodes: '' as IntlString,
        AcceptSegmentation: '' as IntlString,
        RejectSegmentation: '' as IntlString,
        DoneD: '' as IntlString,
        ClearX: '' as IntlString,
        ApplyToEntireStripE: '' as IntlString,
        MyAnnotations: '' as IntlString,
        AllAnnotations: '' as IntlString,
        AddTriageLabel: '' as IntlString,
        AddTriageLabelButton: '' as IntlString,
        DeleteSegmentationLabel: '' as IntlString,
        "25mms": '' as IntlString,
        "50mms": '' as IntlString,
        Ruler: '' as IntlString,
        Caliper: '' as IntlString,
        RemoveCaliper: '' as IntlString,
        RulesConfiguration: '' as IntlString,
        CreateRule: '' as IntlString,
        CreateRuleButton: '' as IntlString,
        NoRules: '' as IntlString,
        RulePlaceholder: '' as IntlString,
        RuleType: '' as IntlString,
        RuleID: '' as IntlString,
        TargetLabels: '' as IntlString,
        AddTargetLabel: '' as IntlString,
        DurationConstraints: '' as IntlString,
        MinDuration: '' as IntlString,
        MaxDuration: '' as IntlString,
        ZoomIn: '' as IntlString,
        ZoomOut: '' as IntlString,
        ZoomReset: '' as IntlString,
        ExtendStrip: '' as IntlString,
        ReduceStrip: '' as IntlString,
        AddNewSegment: '' as IntlString,
        LabelPosition: '' as IntlString,
        LabelPositionBelow: '' as IntlString,
        LabelPositionLeft: '' as IntlString,
        SnapLeft: '' as IntlString,
        SnapRight: '' as IntlString,
        LabelIDFilter: '' as IntlString,
        SelectLabelID: '' as IntlString,
        NoStats: '' as IntlString,
        MarkSegmentAsInteresting: '' as IntlString,
        RemoveMarkSegmentAsInteresting: '' as IntlString,
        MarkedAsInteresting: '' as IntlString,
        SegmentNote: '' as IntlString,
        SegmentMode: '' as IntlString,
        PointMode: '' as IntlString,
        LabelingMode: '' as IntlString,
    }
})
