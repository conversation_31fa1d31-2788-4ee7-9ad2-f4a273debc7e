//
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import type { Contact, Employee } from '@hcengineering/contact'
import type { Arr, AttachedDoc, Class, Doc, Markup, Mixin, Ref, Type } from '@hcengineering/core'
import { NotificationType } from '@hcengineering/notification'
import type { Asset, IntlString, Plugin } from '@hcengineering/platform'
import { plugin } from '@hcengineering/platform'
import { Viewlet } from '@hcengineering/view'
import type { SettingsCategory } from '@hcengineering/setting'

/**
 * @public
 */
export interface Office extends Doc {
  name: string
  description: string
  address: string
  city: string
  country: string
  timezone: string
  avatar?: string | null
  departments?: number
  managers: Arr<Ref<Employee>>
}

/**
 * @public
 */
export interface Department extends Doc {
  name: string
  description: string
  parent?: Ref<Department>
  office?: Ref<Office>
  avatar?: string | null
  teamLead: Ref<Employee> | null
  attachments?: number
  comments?: number
  channels?: number
  members: Ref<Employee>[]
  subscribers?: Arr<Ref<Contact>>
  managers: Arr<Ref<Employee>>
}

/**
 * @public
 */
/**
 * @public
 */
export enum EmploymentType {
  FullTime = 'full_time',
  PartTime = 'part_time',
  Contractor = 'contractor',
  Intern = 'intern',
  Temporary = 'temporary'
}

export interface Staff extends Employee {
  department: Ref<Department>
  /**
   * Job title shown across HR surfaces. Mirrors employee position but keeps HR-specific overrides.
   */
  jobTitle?: string | null
  /**
   * Employment classification (full-time, part-time, contractor, etc.).
   */
  employmentType?: EmploymentType | null
  /**
   * Direct manager reference for reporting chains when it differs from department lead.
   */
  manager?: Ref<Employee> | null
  /**
   * Primary work location or office label.
   */
  location?: string | null
  /**
   * Weekly scheduled hours used for capacity planning.
   */
  workHoursPerWeek?: number | null
  /**
   * Start date in workspace timezone (UTC milliseconds).
   */
  hireDate?: number | null
  /**
   * Contract end or termination date when available (UTC milliseconds).
   */
  terminationDate?: number | null
  /**
   * Probation end date used for HR reminders (UTC milliseconds).
   */
  probationEndDate?: number | null

  ftePercent?: number | null

  costCenter?: string | null
}

/**
 * @public
 */
export interface RequestType extends Doc {
  label: IntlString
  icon: Asset
  value: number
  color: number
}

/**
 * @public
 */
export interface TzDate {
  year: number
  month: number
  day: number
  offset: number
}

/**
 * @public
 */
export enum RequestStatus {
  Draft = 'draft',
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
  Cancelled = 'cancelled'
}

/**
 * @public
 */
export interface PublicHoliday extends Doc {
  title: string
  description: string
  date: TzDate
  department: Ref<Department>
}

/**
 * @public
 */
export interface Request extends AttachedDoc {
  attachedTo: Ref<Staff>

  attachedToClass: Ref<Class<Staff>>

  department: Ref<Department>

  type: Ref<RequestType>

  description: Markup
  comments?: number
  attachments?: number

  // Date always in UTC
  tzDate: TzDate
  tzDueDate: TzDate

  // Approval workflow
  status: RequestStatus
  submittedDate?: number
  approver?: Ref<Employee>
  approvedBy?: Ref<Employee>
  approvalDate?: number
  approvalComment?: Markup
  requestedDays?: number
  halfDayStart?: boolean
  halfDayEnd?: boolean
}

export enum TimeOffAccrualMethod {
  None = 'none',
  LumpSum = 'lump_sum',
  Periodic = 'periodic',
  Hourly = 'hourly'
}

export enum TimeOffAccrualFrequency {
  Monthly = 'monthly',
  Quarterly = 'quarterly',
  Yearly = 'yearly',
  Anniversary = 'anniversary',
  PerPayPeriod = 'per_pay_period'
}

export enum TimeOffTransactionKind {
  Accrual = 'accrual',
  Usage = 'usage',
  Adjustment = 'adjustment',
  Expiration = 'expiration',
  Carryover = 'carryover'
}

export interface TimeOffPolicy extends Doc {
  title: string
  description: Markup
  requestType: Ref<RequestType>
  department?: Ref<Department>
  active: boolean
  accrualMethod: TimeOffAccrualMethod
  accrualFrequency?: TimeOffAccrualFrequency
  accrualRate?: number
  accrualCap?: number
  accrualStartMonth?: number
  accrualStartDay?: number
  carryoverLimit?: number
  carryoverExpiryDays?: number
  allowNegativeBalance: boolean
  allowHalfDays: boolean
  waitingPeriodDays?: number
  defaultApprover?: Ref<Employee>
  autoApprove: boolean
  autoApproveMaxDays?: number
}

export interface TimeOffBalance extends Doc {
  staff: Ref<Staff>
  policy: Ref<TimeOffPolicy>
  balance: number
  pending: number
  carryover: number
  lastAccruedAt?: number
  effectiveDate?: number
}

export interface TimeOffTransaction extends Doc {
  staff: Ref<Staff>
  policy: Ref<TimeOffPolicy>
  sourceRequest?: Ref<Request>
  kind: TimeOffTransactionKind
  amount: number
  note?: Markup
  effectiveDate: number
  recordedBy?: Ref<Employee>
}

/**
 * @public
 * Employee Document/Contract - stores employment contracts, agreements, NDAs, etc.
 */
export enum DocumentType {
  Contract = 'contract',
  OfferLetter = 'offer_letter',
  NDA = 'nda',
  Agreement = 'agreement',
  Policy = 'policy',
  Certificate = 'certificate',
  Other = 'other'
}

export enum DocumentStatus {
  Draft = 'draft',
  Active = 'active',
  Expired = 'expired',
  Revoked = 'revoked'
}

export interface EmployeeDocument extends AttachedDoc {
  attachedTo: Ref<Staff>
  attachedToClass: Ref<Class<Staff>>
  title: string
  description?: Markup
  documentType: DocumentType
  status: DocumentStatus
  effectiveDate?: number
  expiryDate?: number
  signedDate?: number
  fileUrl?: string
  uploadedBy: Ref<Employee>
}

/**
 * @public
 * Compensation Record - tracks salary, bonuses, and compensation changes
 */
export enum PayFrequency {
  Monthly = 'monthly',
  BiWeekly = 'bi_weekly',
  Weekly = 'weekly',
  Annual = 'annual'
}

export enum CompensationType {
  Salary = 'salary',
  Bonus = 'bonus',
  Commission = 'commission',
  Equity = 'equity',
  Other = 'other'
}

export interface CompensationRecord extends AttachedDoc {
  attachedTo: Ref<Staff>
  attachedToClass: Ref<Class<Staff>>
  compensationType: CompensationType
  amount: number
  currency: string
  payFrequency?: PayFrequency
  effectiveDate: number
  endDate?: number
  reason?: Markup
  approvedBy?: Ref<Employee>
  notes?: Markup
}

/**
 * @public
 * Performance Review - tracks employee performance reviews and feedback
 */
export enum ReviewStatus {
  NotStarted = 'not_started',
  InProgress = 'in_progress',
  Completed = 'completed',
  Cancelled = 'cancelled'
}

export enum ReviewType {
  Annual = 'annual',
  MidYear = 'mid_year',
  Probation = 'probation',
  ProjectBased = 'project_based',
  Custom = 'custom'
}

export interface PerformanceReview extends AttachedDoc {
  attachedTo: Ref<Staff>
  attachedToClass: Ref<Class<Staff>>
  title: string
  reviewType: ReviewType
  status: ReviewStatus
  reviewPeriodStart: number
  reviewPeriodEnd: number
  dueDate?: number
  completedDate?: number
  reviewer: Ref<Employee>
  overallRating?: number
  strengths?: Markup
  areasForImprovement?: Markup
  goals?: Markup
  feedback?: Markup
  employeeComments?: Markup
}

/**
 * @public
 * Employee Benefit - tracks benefits enrollment and eligibility
 */
export enum BenefitType {
  HealthInsurance = 'health_insurance',
  DentalInsurance = 'dental_insurance',
  VisionInsurance = 'vision_insurance',
  LifeInsurance = 'life_insurance',
  Retirement401k = 'retirement_401k',
  StockOptions = 'stock_options',
  GymMembership = 'gym_membership',
  Other = 'other'
}

export enum BenefitStatus {
  Eligible = 'eligible',
  Enrolled = 'enrolled',
  Declined = 'declined',
  Terminated = 'terminated'
}

export interface EmployeeBenefit extends AttachedDoc {
  attachedTo: Ref<Staff>
  attachedToClass: Ref<Class<Staff>>
  benefitName: string
  benefitType: BenefitType
  status: BenefitStatus
  provider?: string
  enrollmentDate?: number
  effectiveDate?: number
  terminationDate?: number
  employeeContribution?: number
  employerContribution?: number
  currency?: string
  notes?: Markup
}

export interface AttendanceRecord extends Doc {
  staff: Ref<Staff>
  office?: Ref<Office>
  department?: Ref<Department>
  clockIn: number
  clockOut?: number
  source: 'manual' | 'device' | 'api'
  notes?: Markup
}

export interface AttendanceSchedule extends Doc {
  office?: Ref<Office>
  department?: Ref<Department>
  label: string
  timezone?: string
  startMinutes: number
  endMinutes: number
  lunchStartMinutes?: number
  lunchEndMinutes?: number
  workingDays?: number[]
}

/**
 * @public
 * Onboarding/Offboarding Task - tracks checklist items for employee lifecycle
 */
export enum TaskCategory {
  Onboarding = 'onboarding',
  Offboarding = 'offboarding'
}

export enum TaskStatus {
  NotStarted = 'not_started',
  InProgress = 'in_progress',
  Completed = 'completed',
  Skipped = 'skipped'
}

export interface EmployeeLifecycleTask extends AttachedDoc {
  attachedTo: Ref<Staff>
  attachedToClass: Ref<Class<Staff>>
  category: TaskCategory
  title: string
  description?: Markup
  status: TaskStatus
  assignee?: Ref<Employee>
  dueDate?: number
  completedDate?: number
  completedBy?: Ref<Employee>
  order: number
}

/**
 * @public
 * HR Policy - configurable rules that can be automated via n8n
 */
export interface Policy extends Doc {
  title: string
  description: Markup
  department?: Ref<Department>
  active: boolean
  triggerType?: string // For n8n integration later (e.g., 'contract_ending', 'anniversary', etc.)
  triggerConfig?: string // JSON config for n8n
  attachments?: number
  comments?: number
}

/**
 * @public
 */
export enum WorkflowStatus {
  Draft = 'draft',
  Active = 'active',
  Completed = 'completed',
  Archived = 'archived'
}

/**
 * @public
 * HR Workflow - manual processes like onboarding, offboarding
 */
export interface Workflow extends Doc {
  title: string
  description: Markup
  status: WorkflowStatus
  department?: Ref<Department>
  assignee?: Ref<Employee>
  dueDate?: number
  attachments?: number
  comments?: number
  steps?: number
}

/**
 * @public
 * Workflow Step - individual tasks within a workflow
 */
export interface WorkflowStep extends AttachedDoc {
  attachedTo: Ref<Workflow>
  attachedToClass: Ref<Class<Workflow>>
  title: string
  description: Markup
  completed: boolean
  completedBy?: Ref<Employee>
  completedDate?: number
  dueDate?: number
  assignee?: Ref<Employee>
  order: number
}

/**
 * @public
 */
export const matricsHrId = 'matrics-hr' as Plugin

/**
 * @public
 */
const hr = plugin(matricsHrId, {
  string: {
    HRApplication: '' as IntlString,
    Department: '' as IntlString,
    Departments: '' as IntlString,
    ParentDepartmentLabel: '' as IntlString,
    Structure: '' as IntlString,
    OrgChart: '' as IntlString,
    All: '' as IntlString,
    CreateDepartment: '' as IntlString,
    CreateDepartmentLabel: '' as IntlString,
    DepartmentPlaceholder: '' as IntlString,
    TeamLead: '' as IntlString,
    ConfigLabel: '' as IntlString,
    ConfigDescription: '' as IntlString,
    SelectEmployee: '' as IntlString,
    UnAssignLead: '' as IntlString,
    MemberCount: '' as IntlString,
    AssignLead: '' as IntlString,
    TeamLeadTooltip: '' as IntlString,
    MoveStaff: '' as IntlString,
    MoveStaffDescr: '' as IntlString,
    AddEmployee: '' as IntlString,
    RequestType: '' as IntlString,
    Schedule: '' as IntlString,
    EditRequest: '' as IntlString,
    EditRequestType: '' as IntlString,
    ChooseNewType: '' as IntlString,
    UnchangeableType: '' as IntlString,
    CreateRequest: '' as IntlString,
    ExistingRequests: '' as IntlString,
    Today: '' as IntlString,
    NoEmployeesInDepartment: '' as IntlString,
    Summary: '' as IntlString,
    Staff: '' as IntlString,
    Members: '' as IntlString,
    NoMembers: '' as IntlString,
    AddMember: '' as IntlString,
    Title: '' as IntlString,
    Description: '' as IntlString,
    Inactive: '' as IntlString,
    PublicHoliday: '' as IntlString,
    PublicHolidays: '' as IntlString,
    MarkAsPublicHoliday: '' as IntlString,
    Manager: '' as IntlString,
    EditPublicHoliday: '' as IntlString,
    Managers: '' as IntlString,
    Dashboard: '' as IntlString,
    Overview: '' as IntlString,
    UpcomingHolidays: '' as IntlString,
    NoUpcomingHolidays: '' as IntlString,
    NoProfileData: '' as IntlString,
    NoEmergencyInfo: '' as IntlString,
    NoAssignedAssets: '' as IntlString,
    ManageHolidays: '' as IntlString,
    CurrentEmployee: '' as IntlString,
    Job: '' as IntlString,
    EmploymentInformation: '' as IntlString,
    EmploymentDates: '' as IntlString,
    Compensation: '' as IntlString,
    ContractsDocuments: '' as IntlString,
    JobHistory: '' as IntlString,
    UploadDocument: '' as IntlString,
    RequestChange: '' as IntlString,
    Employees: '' as IntlString,
    EmployeeManagement: '' as IntlString,
    EditEmployee: '' as IntlString,
    ViewEmployee: '' as IntlString,
    EmployeeDetails: '' as IntlString,
    PersonalInformation: '' as IntlString,
    EmployeeDirectory: '' as IntlString,
    SearchEmployees: '' as IntlString,
    FilterByDepartment: '' as IntlString,
    EmployeeCount: '' as IntlString,
    UpdateEmployeeInfo: '' as IntlString,
    ManageTimeOff: '' as IntlString,
    AdjustBalance: '' as IntlString,
    BalanceAdjustment: '' as IntlString,
    AdjustmentReason: '' as IntlString,
    AddDays: '' as IntlString,
    SubtractDays: '' as IntlString,
    ManualAdjustment: '' as IntlString,
    BalanceHistory: '' as IntlString,
    NoEmployeesFound: '' as IntlString,
    EmployeeDocument: '' as IntlString,
    EmployeeDocuments: '' as IntlString,
    DocumentType: '' as IntlString,
    DocumentTypeContract: '' as IntlString,
    DocumentTypeOfferLetter: '' as IntlString,
    DocumentTypeNDA: '' as IntlString,
    DocumentTypeAgreement: '' as IntlString,
    DocumentTypePolicy: '' as IntlString,
    DocumentTypeCertificate: '' as IntlString,
    DocumentTypeOther: '' as IntlString,
    DocumentStatus: '' as IntlString,
    DocumentStatusDraft: '' as IntlString,
    DocumentStatusActive: '' as IntlString,
    DocumentStatusExpired: '' as IntlString,
    DocumentStatusRevoked: '' as IntlString,
    EffectiveDate: '' as IntlString,
    ExpiryDate: '' as IntlString,
    SignedDate: '' as IntlString,
    UploadedBy: '' as IntlString,
    ViewDocument: '' as IntlString,
    DownloadDocument: '' as IntlString,
    CompensationRecord: '' as IntlString,
    CompensationRecords: '' as IntlString,
    CompensationHistory: '' as IntlString,
    CompensationType: '' as IntlString,
    CompensationTypeSalary: '' as IntlString,
    CompensationTypeBonus: '' as IntlString,
    CompensationTypeCommission: '' as IntlString,
    CompensationTypeEquity: '' as IntlString,
    CompensationTypeOther: '' as IntlString,
    PayFrequency: '' as IntlString,
    EndDate: '' as IntlString,
    Reason: '' as IntlString,
    AddCompensation: '' as IntlString,
    EditCompensation: '' as IntlString,
    PerformanceReview: '' as IntlString,
    PerformanceReviews: '' as IntlString,
    ReviewType: '' as IntlString,
    ReviewTypeAnnual: '' as IntlString,
    ReviewTypeMidYear: '' as IntlString,
    ReviewTypeProbation: '' as IntlString,
    ReviewTypeProjectBased: '' as IntlString,
    ReviewTypeCustom: '' as IntlString,
    ReviewStatus: '' as IntlString,
    ReviewStatusNotStarted: '' as IntlString,
    ReviewStatusInProgress: '' as IntlString,
    ReviewStatusCompleted: '' as IntlString,
    ReviewStatusCancelled: '' as IntlString,
    ReviewPeriodStart: '' as IntlString,
    ReviewPeriodEnd: '' as IntlString,
    DueDate: '' as IntlString,
    CompletedDate: '' as IntlString,
    Reviewer: '' as IntlString,
    OverallRating: '' as IntlString,
    Strengths: '' as IntlString,
    AreasForImprovement: '' as IntlString,
    Goals: '' as IntlString,
    Feedback: '' as IntlString,
    EmployeeComments: '' as IntlString,
    AddReview: '' as IntlString,
    EditReview: '' as IntlString,
    StartReview: '' as IntlString,
    CompleteReview: '' as IntlString,
    EmployeeBenefit: '' as IntlString,
    EmployeeBenefits: '' as IntlString,
    BenefitType: '' as IntlString,
    BenefitTypeHealthInsurance: '' as IntlString,
    BenefitTypeDentalInsurance: '' as IntlString,
    BenefitTypeVisionInsurance: '' as IntlString,
    BenefitTypeLifeInsurance: '' as IntlString,
    BenefitTypeRetirement401k: '' as IntlString,
    BenefitTypeStockOptions: '' as IntlString,
    BenefitTypeGymMembership: '' as IntlString,
    BenefitTypeOther: '' as IntlString,
    BenefitStatus: '' as IntlString,
    BenefitStatusEligible: '' as IntlString,
    BenefitStatusEnrolled: '' as IntlString,
    BenefitStatusDeclined: '' as IntlString,
    BenefitStatusTerminated: '' as IntlString,
    BenefitName: '' as IntlString,
    Provider: '' as IntlString,
    EnrollmentDate: '' as IntlString,
    EmployeeContribution: '' as IntlString,
    EmployerContribution: '' as IntlString,
    AddBenefit: '' as IntlString,
    EditBenefit: '' as IntlString,
    EnrollInBenefit: '' as IntlString,
    LifecycleTask: '' as IntlString,
    LifecycleTasks: '' as IntlString,
    Onboarding: '' as IntlString,
    Offboarding: '' as IntlString,
    TaskCategory: '' as IntlString,
    TaskCategoryOnboarding: '' as IntlString,
    TaskCategoryOffboarding: '' as IntlString,
    TaskStatus: '' as IntlString,
    TaskStatusNotStarted: '' as IntlString,
    TaskStatusInProgress: '' as IntlString,
    TaskStatusCompleted: '' as IntlString,
    TaskStatusSkipped: '' as IntlString,
    AddTask: '' as IntlString,
    EditTask: '' as IntlString,
    CompleteTask: '' as IntlString,
    SkipTask: '' as IntlString,
    OnboardingChecklist: '' as IntlString,
    OffboardingChecklist: '' as IntlString,
    Hello: '' as IntlString,
    DashboardSubtitle: '' as IntlString,
    Export: '' as IntlString,
    Separator: '' as IntlString,
    ChooseSeparator: '' as IntlString,
    Positions: '' as IntlString,
    WorkingDays: '' as IntlString,
    ReportedDays: '' as IntlString,
    Tasks: '' as IntlString,
    PTOs: '' as IntlString,
    JobTitle: '' as IntlString,
    EmploymentType: '' as IntlString,
    EmploymentTypeFullTime: '' as IntlString,
    EmploymentTypePartTime: '' as IntlString,
    EmploymentTypeContractor: '' as IntlString,
    EmploymentTypeIntern: '' as IntlString,
    EmploymentTypeTemporary: '' as IntlString,
    Location: '' as IntlString,
    WorkHoursPerWeek: '' as IntlString,
    HireDate: '' as IntlString,
    TerminationDate: '' as IntlString,
    ProbationEndDate: '' as IntlString,
    FTE: '' as IntlString,
    CostCenter: '' as IntlString,
    Attendance: '' as IntlString,
    AttendanceSchedule: '' as IntlString,
    ClockIn: '' as IntlString,
    ClockOut: '' as IntlString,
    CurrentlyInOffice: '' as IntlString,
    NotInOffice: '' as IntlString,
    WorkdayStart: '' as IntlString,
    WorkdayEnd: '' as IntlString,
    LunchStart: '' as IntlString,
    LunchEnd: '' as IntlString,
    VacationAllowance: '' as IntlString,
    VacationCarryover: '' as IntlString,
    SickAllowance: '' as IntlString,
    SickCarryover: '' as IntlString,
    PersonalAllowance: '' as IntlString,
    PersonalCarryover: '' as IntlString,
    PTOAllowance: '' as IntlString,
    PTOCarryover: '' as IntlString,
    PTOBalance: '' as IntlString,
    VacationBalance: '' as IntlString,
    SickBalance: '' as IntlString,
    PersonalBalance: '' as IntlString,
    Allowance: '' as IntlString,
    Carryover: '' as IntlString,
    TimeOffBalance: '' as IntlString,
    TimeOffUsed: '' as IntlString,
    TimeOffPending: '' as IntlString,
    TimeOffRemaining: '' as IntlString,
    PendingRequests: '' as IntlString,
    ApprovedTimeOff: '' as IntlString,
    TimeOffRequested: '' as IntlString,
    InsufficientBalance: '' as IntlString,
    RequestedDays: '' as IntlString,
    AvailableDays: '' as IntlString,
    TPD: '' as IntlString,
    EXTRa: '' as IntlString,
    Office: '' as IntlString,
    Offices: '' as IntlString,
    CreateOffice: '' as IntlString,
    EditOffice: '' as IntlString,
    Address: '' as IntlString,
    City: '' as IntlString,
    Country: '' as IntlString,
    Timezone: '' as IntlString,
    Status: '' as IntlString,
    Approver: '' as IntlString,
    ApprovedBy: '' as IntlString,
    ApprovalComment: '' as IntlString,
    Approve: '' as IntlString,
    Reject: '' as IntlString,
    Submit: '' as IntlString,
    Cancel: '' as IntlString,
    StatusPending: '' as IntlString,
    StatusApproved: '' as IntlString,
    StatusRejected: '' as IntlString,
    StatusDraft: '' as IntlString,
    StatusCancelled: '' as IntlString,
    RequestApproved: '' as IntlString,
    RequestRejected: '' as IntlString,
    RequestSubmitted: '' as IntlString,
    PendingApprovals: '' as IntlString,
    TimeOff: '' as IntlString,
    TimeOffPolicy: '' as IntlString,
    TimeOffPolicies: '' as IntlString,
    TimeOffTransaction: '' as IntlString,
    AccrualSettings: '' as IntlString,
    CarryoverRules: '' as IntlString,
    ApprovalRules: '' as IntlString,
    AccrualMethod: '' as IntlString,
    AccrualMethodNone: '' as IntlString,
    AccrualMethodLumpSum: '' as IntlString,
    AccrualMethodPeriodic: '' as IntlString,
    AccrualMethodHourly: '' as IntlString,
    AccrualFrequency: '' as IntlString,
    AccrualFrequencyMonthly: '' as IntlString,
    AccrualFrequencyQuarterly: '' as IntlString,
    AccrualFrequencyYearly: '' as IntlString,
    AccrualFrequencyAnniversary: '' as IntlString,
    AccrualFrequencyPerPayPeriod: '' as IntlString,
    AccrualRate: '' as IntlString,
    AccrualCap: '' as IntlString,
    AccrualStartMonth: '' as IntlString,
    AccrualStartDay: '' as IntlString,
    CarryoverLimit: '' as IntlString,
    CarryoverExpiryDays: '' as IntlString,
    CurrentBalance: '' as IntlString,
    PendingBalance: '' as IntlString,
    LastAccruedAt: '' as IntlString,
    AllowNegativeBalance: '' as IntlString,
    AllowHalfDays: '' as IntlString,
    WaitingPeriodDays: '' as IntlString,
    DefaultApprover: '' as IntlString,
    AutoApprove: '' as IntlString,
    AutoApproveMaxDays: '' as IntlString,
    HalfDayStart: '' as IntlString,
    HalfDayEnd: '' as IntlString,
    TransactionKind: '' as IntlString,
    Amount: '' as IntlString,
    Currency: '' as IntlString,
    RecordedBy: '' as IntlString,
    CreateTimeOffPolicy: '' as IntlString,
    EditTimeOffPolicy: '' as IntlString,
    Assets: '' as IntlString,
    Documents: '' as IntlString,
    Performance: '' as IntlString,
    Training: '' as IntlString,
    Benefits: '' as IntlString,
    EmergencyInfo: '' as IntlString,
    EmergencyContact: '' as IntlString,
    EmergencyPhone: '' as IntlString,
    EmergencyEmail: '' as IntlString,
    EmergencyRelationship: '' as IntlString,
    AssetName: '' as IntlString,
    AssetTag: '' as IntlString,
    SerialNumber: '' as IntlString,
    AssignedDate: '' as IntlString,
    AssignedShort: '' as IntlString,
    ReturnDate: '' as IntlString,
    ReturnShort: '' as IntlString,
    Condition: '' as IntlString,
    Notes: '' as IntlString,
    Policy: '' as IntlString,
    Policies: '' as IntlString,
    CreatePolicy: '' as IntlString,
    EditPolicy: '' as IntlString,
    Active: '' as IntlString,
    TriggerType: '' as IntlString,
    TriggerConfig: '' as IntlString,
    Workflow: '' as IntlString,
    Workflows: '' as IntlString,
    CreateWorkflow: '' as IntlString,
    EditWorkflow: '' as IntlString,
    WorkflowStep: '' as IntlString,
    Steps: '' as IntlString,
    Assignee: '' as IntlString,
    Completed: '' as IntlString,
    CompletedBy: '' as IntlString,
    Order: '' as IntlString,
    Remote: '' as IntlString,
    Overtime: '' as IntlString,
    Overtime2: '' as IntlString,
    PTO: '' as IntlString,
    PTO2: '' as IntlString,
    Vacation: '' as IntlString,
    Sick: '' as IntlString,
    Subscribers: '' as IntlString,
    Request: '' as IntlString,
    ProfileUpdate: '' as IntlString,
    EmergencyInfoUpdate: '' as IntlString,
    AssetAssignment: '' as IntlString,
    AssetReturn: '' as IntlString,
    RequestProfileChange: '' as IntlString,
    RequestEmergencyInfoChange: '' as IntlString,
    RequestAssetAssignment: '' as IntlString,
    RequestAssetReturn: '' as IntlString,
    RequestCreated: '' as IntlString,
    RequestUpdated: '' as IntlString,
    RequestRemoved: '' as IntlString,
    Payload: '' as IntlString,
    AddHoliday: '' as IntlString,
    NoHolidays: '' as IntlString,
    HolidayName: '' as IntlString,
    HolidayNamePlaceholder: '' as IntlString,
    HolidayDescription: '' as IntlString,
    HolidayDescriptionPlaceholder: '' as IntlString,
    DepartmentOptional: '' as IntlString,
    EditHoliday: '' as IntlString,
    Date: '' as IntlString
  },
  app: {
    HR: '' as Ref<Doc>
  },
  class: {
    Department: '' as Ref<Class<Department>>,
    Office: '' as Ref<Class<Office>>,
    Request: '' as Ref<Class<Request>>,
    RequestType: '' as Ref<Class<RequestType>>,
    TzDate: '' as Ref<Class<Type<TzDate>>>,
    PublicHoliday: '' as Ref<Class<PublicHoliday>>,
    Policy: '' as Ref<Class<Policy>>,
    TimeOffPolicy: '' as Ref<Class<TimeOffPolicy>>,
    TimeOffBalance: '' as Ref<Class<TimeOffBalance>>,
    TimeOffTransaction: '' as Ref<Class<TimeOffTransaction>>,
    AttendanceRecord: '' as Ref<Class<AttendanceRecord>>,
    AttendanceSchedule: '' as Ref<Class<AttendanceSchedule>>,
    Workflow: '' as Ref<Class<Workflow>>,
    WorkflowStep: '' as Ref<Class<WorkflowStep>>,
    AssignedAsset: '' as Ref<Class<Doc>>,
    EmployeeDocument: '' as Ref<Class<EmployeeDocument>>,
    CompensationRecord: '' as Ref<Class<CompensationRecord>>,
    PerformanceReview: '' as Ref<Class<PerformanceReview>>,
    EmployeeBenefit: '' as Ref<Class<EmployeeBenefit>>,
    EmployeeLifecycleTask: '' as Ref<Class<EmployeeLifecycleTask>>
  },
  mixin: {
    Staff: '' as Ref<Mixin<Staff>>
  },
  icon: {
    HR: '' as Asset,
    Department: '' as Asset,
    Office: '' as Asset,
    Structure: '' as Asset,
    OrgChart: '' as Asset,
    Members: '' as Asset,
    Vacation: '' as Asset,
    Sick: '' as Asset,
    PTO: '' as Asset,
    PTO2: '' as Asset,
    Remote: '' as Asset,
    Overtime: '' as Asset,
    Overtime2: '' as Asset,
    Policy: '' as Asset,
    Workflow: '' as Asset,
    PublicHoliday: '' as Asset,
    Approved: '' as Asset,
    Rejected: '' as Asset,
    Pending: '' as Asset,
    Document: '' as Asset,
    Compensation: '' as Asset,
    Performance: '' as Asset,
    Benefits: '' as Asset,
    Task: '' as Asset
  },
  ids: {
    Head: '' as Ref<Department>,
    Vacation: '' as Ref<RequestType>,
    Leave: '' as Ref<RequestType>,
    Sick: '' as Ref<RequestType>,
    PTO: '' as Ref<RequestType>,
    PTO2: '' as Ref<RequestType>,
    Remote: '' as Ref<RequestType>,
    Overtime: '' as Ref<RequestType>,
    Overtime2: '' as Ref<RequestType>,
    ProfileUpdate: '' as Ref<RequestType>,
    EmergencyInfoUpdate: '' as Ref<RequestType>,
    AssetAssignment: '' as Ref<RequestType>,
    AssetReturn: '' as Ref<RequestType>,
    CreateRequestNotification: '' as Ref<NotificationType>,
    UpdateRequestNotification: '' as Ref<NotificationType>,
    RemoveRequestNotification: '' as Ref<NotificationType>,
    CreatePublicHolidayNotification: '' as Ref<NotificationType>,
    RequestApprovedNotification: '' as Ref<NotificationType>,
    RequestRejectedNotification: '' as Ref<NotificationType>,
    RequestSubmittedNotification: '' as Ref<NotificationType>,
    PeopleCultureSettings: '' as Ref<SettingsCategory>
  },
  viewlet: {
    TableMember: '' as Ref<Viewlet>,
    StaffStats: '' as Ref<Viewlet>,
    OfficeTable: '' as Ref<Viewlet>,
    PolicyTable: '' as Ref<Viewlet>,
    WorkflowTable: '' as Ref<Viewlet>
  }
})

export * from './utils'
export * from './analytics'

/**
 * @public
 */
export default hr
