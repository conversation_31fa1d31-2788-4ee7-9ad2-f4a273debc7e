//
// Copyright © 2023 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//
import { TzDate } from '.'

/**
 * @public
 */
export function timeToTzDate (val: number): TzDate {
  const date = new Date(val)
  return {
    year: date.getFullYear(),
    month: date.getMonth(),
    day: date.getDate(),
    offset: date.getTimezoneOffset()
  }
}

/**
 * @public
 */
export function toTzDate (date: Date): TzDate {
  return {
    year: date.getFullYear(),
    month: date.getMonth(),
    day: date.getDate(),
    offset: date.getTimezoneOffset()
  }
}

/**
 * @public
 */
export function fromTzDate (tzDate: TzDate): number {
  return new Date().setFullYear(tzDate?.year ?? 0, tzDate.month, tzDate.day)
}

/**
 * @public
 */
export function tzDateEqual (tzDate: TzDate, tzDate2: TzDate): boolean {
  return tzDate.year === tzDate2.year && tzDate.month === tzDate2.month && tzDate.day === tzDate2.day
}

/**
 * @public
 */
export function tzDateCompare (tzDate1: TzDate, tzDate2: TzDate): number {
  if (tzDate1.year === tzDate2.year) {
    if (tzDate1.month === tzDate2.month) {
      return tzDate1.day - tzDate2.day
    } else {
      return tzDate1.month - tzDate2.month
    }
  } else {
    return tzDate1.year - tzDate2.year
  }
}

/**
 * @public
 * Returns ISO key yyyy-mm-dd for a TzDate
 */
export function tzDateKey (d: TzDate): string {
  const mm = String(d.month + 1).padStart(2, '0')
  const dd = String(d.day).padStart(2, '0')
  return `${d.year}-${mm}-${dd}`
}

/**
 * @public
 * Calculate business days between two TzDates inclusive of start and end, excluding weekends and holidays.
 */
export function businessDaysBetween (
  start: TzDate,
  end: TzDate,
  holidays?: Set<string>
): number {
  // Normalize order
  let s = start
  let e = end
  if (tzDateCompare(s, e) > 0) {
    s = end
    e = start
  }
  let count = 0
  const cur = new Date(s.year, s.month, s.day)
  const last = new Date(e.year, e.month, e.day)
  while (cur <= last) {
    const day = cur.getDay() // 0=Sun,6=Sat
    const key = `${cur.getFullYear()}-${String(cur.getMonth() + 1).padStart(2, '0')}-${String(cur.getDate()).padStart(2, '0')}`
    const isWeekend = day === 0 || day === 6
    const isHoliday = holidays?.has(key) ?? false
    if (!isWeekend && !isHoliday) count++
    cur.setDate(cur.getDate() + 1)
  }
  return count
}

/**
 * @public
 * Compute requested days for a period, considering half-day start/end.
 */
export function computeRequestedDays (
  start: TzDate,
  end: TzDate,
  opts?: { halfDayStart?: boolean, halfDayEnd?: boolean, holidays?: Set<string> }
): number {
  const base = businessDaysBetween(start, end, opts?.holidays)
  if (base === 0) return 0
  let adj = base
  if (opts?.halfDayStart) adj -= 0.5
  if (opts?.halfDayEnd && tzDateEqual(start, end) === false) adj -= 0.5
  if (opts?.halfDayStart && opts?.halfDayEnd && tzDateEqual(start, end)) adj = 0.5
  return Math.max(0, adj)
}
