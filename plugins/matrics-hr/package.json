{"name": "@hcengineering/matrics-hr", "version": "0.6.19", "main": "lib/index.js", "svelte": "src/index.ts", "types": "types/index.d.ts", "files": ["lib/**/*", "types/**/*", "tsconfig.json"], "author": "Anticrm Platform Contributors", "license": "EPL-2.0", "scripts": {"build": "compile", "build:watch": "compile", "format": "format src", "test": "jest --passWithNoTests --silent", "_phase:build": "compile transpile src", "_phase:test": "jest --passWithNoTests --silent", "_phase:format": "format src", "_phase:validate": "compile validate"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-n": "^15.4.0", "eslint": "^8.54.0", "@typescript-eslint/parser": "^6.11.0", "eslint-config-standard-with-typescript": "^40.0.0", "prettier": "^3.1.0", "typescript": "^5.8.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/jest": "^29.5.5"}, "dependencies": {"@hcengineering/contact": "^0.6.24", "@hcengineering/core": "^0.6.32", "@hcengineering/platform": "^0.6.11", "@hcengineering/view": "^0.6.13", "@hcengineering/notification": "^0.6.23", "@hcengineering/setting": "^0.6.17"}, "repository": "https://github.com/hcengineering/platform", "publishConfig": {"access": "public"}}