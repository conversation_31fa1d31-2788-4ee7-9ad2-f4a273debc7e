//
// Copyright © 2021 Anticrm Platform Contributors.
// 
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// 
// See the License for the specific language governing permissions and
// limitations under the License.
//

/* Common Colors */
* {
  --primary-button-color: #fff;
  --primary-button-content-color: rgba(255, 255, 255, .8);
  --primary-button-border: rgba(255, 255, 255, .09);
  --primary-button-outline: #5190EC;
  --primary-button-transparent: rgba(43, 81, 144, 0.2);

  --primary-button-default: #205DC2;
  --primary-button-hovered: #3575de;
  --primary-button-pressed: #1C52AB;
  --primary-button-focused: #205DC2;
  --secondary-button-color: rgba(0, 0, 0, .8);
  --secondary-button-default: #D3E1F8;
  --secondary-button-hovered: #BDD2F5;
  --secondary-button-pressed: #A7C3F1;
  --secondary-button-focused: #BDD2F5;
  --positive-button-default: #05A05C;
  --positive-button-hovered: #05AD63;
  --positive-button-pressed: #019253;
  --positive-button-focused: #05AD63;
  --positive-button-disabled: rgba(5, 160, 92, .15);
  --positive-button-disabled-color: rgba(5, 160, 92, .5);
  --negative-button-default: #CB4B42;
  --negative-button-hovered: #DC5148;
  --negative-button-pressed: #B34139;
  --negative-button-focused: #DC5148;
  --negative-button-disabled: rgba(203, 75, 66, .15);
  --negative-button-disabled-color: rgba(203, 75, 66, .5);

  --bg-positive-default: #05A05C;
  --bg-negative-default: #CB4B42;

  --white-color: #fff;
  --duotone-color: rgba(126, 134, 158, .25);

  --system-error-color: #EE7A7A;
  --system-error-60-color: rgba(238, 122, 122, .6); // #EE7A7A / 60%

  --activity-status-active: #34DB80;
  --activity-status-dnd: #D95757;
  --activity-status-busy: #FCC500;
  --activity-status-away: #9099A2;

  --primary-color-blue: #2c23d5;
  --grayscale-grey-03: #77818E;
  --primary-color-purple-01: #4c38bd;
  --primary-color-purple-02: #6452db;
  --primary-color-purple-03: #9D92C4;
  --primary-color-orange-01: #CC4726;
  --primary-color-orange-02: #F47758;
  --primary-color-skyblue: #93CAF3;
  --primary-color-pink: #FA8DA1;

  --highlight-blue-01: #0084FF;
  --highlight-red: #CB4B42;
  --highlight-red-hover: #ff967e;
  --highlight-red-press: #f96f50bd;

  --theme-state-negative-color: #CB4B42;
  --theme-state-negative-hover: #B2423A;
  --theme-state-negative-background-color: rgba(203, 75, 66, .15);
  --theme-state-negative-border-color: rgba(203, 75, 66, .15);
  --theme-state-positive-color: #05A05C;
  --theme-state-positive-hover: #04874E;
  --theme-state-positive-background-color: rgba(5, 160, 92, .1);
  --theme-state-positive-background-hover: rgba(5, 160, 92, .2);
  --theme-state-positive-border-color: rgba(5, 160, 92, .15);

  --text-editor-selected-node-background: rgba(43, 81, 144, 0.1);
  --text-editor-selected-node-color: #93CAF3;

  --text-editor-highlighted-node-warning-active-background-color: rgba(255, 203, 0, .24);
  --text-editor-highlighted-node-warning-background-color: rgba(255, 203, 0, .12);
  --text-editor-highlighted-node-warning-border-color: rgba(255, 203, 0, .35);

  --text-editor-highlighted-node-add-background-color: #DAEDDC;
  --text-editor-highlighted-node-add-font-color: #1C4220;

  --text-editor-highlighted-node-delete-background-color: #F6DCDA;
  --text-editor-highlighted-node-delete-font-color: #54201C;

  --text-editor-table-marker-color: #bebebf;

  --theme-clockface-sec-arrow: conic-gradient(at 50% -10px, rgba(255, 0, 0, 0), rgba(255, 0, 0, 0) 49%, #F47758 50%, rgba(255, 0, 0, 0) 51%, rgba(255, 0, 0, 0) 100%);
  --theme-clockface-sec-holder: #F47758;
}

/* Dark Theme */
.theme-dark {
  --theme-drawing-bg-color: #282834;

  --theme-text-primary-color: rgba(255, 255, 255, .8);
  --theme-text-placeholder-color: rgba(255, 255, 255, .4);

  --primary-button-disabled: rgba(255, 255, 255, .12);
  --primary-button-disabled-color: rgba(255, 255, 255, .4);
  --secondary-button-disabled: rgba(255, 255, 255, .12);
  --secondary-button-disabled-color: rgba(255, 255, 255, .4);

  --theme-button-default: rgba(255, 255, 255, .02);
  --theme-button-hovered: rgba(255, 255, 255, .04);
  --theme-button-pressed: rgba(255, 255, 255, .08);
  --theme-button-focused: rgba(255, 255, 255, .04);
  --theme-button-focused-border: rgba(255, 255, 255, .09);
  --theme-button-disabled: transparent;
  --theme-button-border: rgba(255, 255, 255, .09);

  --theme-breadcrumb-default: rgba(255, 255, 255, 0);
  --theme-breadcrumb-hovered: rgba(255, 255, 255, .08);
  --theme-breadcrumb-pressed: rgba(255, 255, 255, .1);
  --theme-button-icon-default: rgba(255, 255, 255, 0);
  --theme-button-icon-hovered: rgba(255, 255, 255, .06);
  --theme-button-icon-pressed: rgba(255, 255, 255, .1);

  --theme-button-contrast-color: #000;
  --theme-button-contrast-enabled: rgba(255, 255, 255, .8);
  --theme-button-contrast-hovered: #fff;
  --theme-button-contrast-pressed: rgba(255, 255, 255, .6);
  --theme-button-contrast-disabled: rgba(255, 255, 255, .6);
  --theme-button-contrast-disabled-color: rgba(0, 0, 0, .5);
  --theme-button-contrast-border: rgba(255, 255, 255, .2);

  --theme-button-container-color: #25262A;

  --theme-refinput-divider: rgba(255, 255, 255, .07);
  --theme-refinput-border: rgba(255, 255, 255, .1);

  // Be aware to update defineAlpha() function in colors.ts
  --theme-bg-color: #1A1A28;
  --theme-bg-accent-color: rgba(0, 0, 0, .08);
  --theme-bg-dark-color: rgba(0, 0, 0, .2);
  --theme-back-color: #0f0f18;
  --theme-overlay-color: rgba(0, 0, 0, .3);
  --theme-statusbar-color: #1A1928;
  --theme-navpanel-color: #14141F;
  --theme-navpanel-hovered: rgba(255, 255, 255, .04);
  --theme-navpanel-selected: rgba(255, 255, 255, .08);
  --theme-navpanel-divider: rgba(255, 255, 255, .1);
  --theme-navpanel-border: rgba(255, 255, 255, .1);
  --theme-navpanel-icons-color: #7F7F7F;
  --theme-navpanel-icons-divider: rgba(255, 255, 255, .11);
  --theme-navpanel-shadow-mobile: drop-shadow(0 0 3px rgba(0, 0, 0, .5));
  --theme-comp-header-color: #1F1F2C;
  --theme-divider-color: rgba(255, 255, 255, .06);
  --theme-bg-divider-color: #282834;
  --theme-mention-bg-color: rgba(55, 122, 230, 0.1);
  --theme-mention-focused-bg-color: rgba(55, 122, 230, 0.2);
  --theme-mention-bg-color-notransparent: rgb(29, 36, 59);
  --theme-broken-mention-color: rgba(255, 255, 255, .4);
  --theme-broken-mention-bg-color: rgba(255, 255, 255, .12);
  --theme-broken-mention-focused-bg-color: rgba(255, 255, 255, .2);

  --theme-trans-color: rgba(255, 255, 255, .3);
  --theme-darker-color: rgba(255, 255, 255, .4);
  --theme-halfcontent-color: rgba(255, 255, 255, .5);
  --theme-dark-color: rgba(255, 255, 255, .6);
  --theme-content-color: rgba(255, 255, 255, .8);
  --theme-caption-color: #FFF;
  --theme-link-color: #377AE6;

  --theme-list-border-color: rgba(255, 255, 255, .05);
  --theme-list-header-color: #C88C65;
  --theme-list-subheader-color: #262634;
  --theme-list-row-color: #21212F;
  --theme-list-divider-color: rgba(255, 255, 255, .09);
  --theme-list-subheader-divider: rgba(255, 255, 255, .06);

  --theme-list-button-color: #262633;
  --theme-list-button-hover: #2F2F3A;
  --theme-link-button-color: #262634;
  --theme-link-button-hover: #2F2F3B;

  --theme-table-border-color: rgba(255, 255, 255, .1);
  --theme-table-header-color: #1C1C29;
  --theme-table-row-color: #21212F;

  --theme-kanban-card-bg-color: rgba(222, 222, 240, .04);
  --theme-kanban-card-border: transparent;
  --theme-kanban-card-footer: rgba(217, 217, 217, .07);

  --theme-editbox-focus-color: rgba(255, 255, 255, .04);
  --theme-editbox-focus-border: #5190EC;
  --theme-tablist-color: rgba(0, 0, 0, .02);
  --theme-tablist-plain-color: #2A64C4; // Light
  --theme-tablist-plain-divider: rgba(255, 255, 255, .07); // Light invert
  --theme-checkbox-color: #000;
  --theme-checkbox-bg-color: #FFF;
  --theme-checkbox-border: rgba(0, 0, 0, .12);
  --theme-checkbox-disabled: #999;
  --theme-progress-color: #FFFFFF;
  --theme-popup-color: #2A2939;
  --theme-popup-trans-color: #2A2939ee;
  --theme-popup-trans-gradient: linear-gradient(to bottom, #2A2939ff, #2A293900);
  --theme-popup-hover: #333240;
  --theme-popup-divider: rgba(255, 255, 255, .09);
  --theme-popup-header: #3A3A47;
  --theme-popup-shadow: 0 0 .5rem rgba(0, 0, 0, .2);
  --theme-popup-checkicon: #FFFFFF99;
  --theme-popup-deactivated: #1A1A28;
  --theme-panel-color: #1A1A28;
  --theme-calendar-today-color: #fff;
  --theme-calendar-holiday-color: #eb5757;
  --theme-calendar-weekend-color: rgba(242, 153, 74, 1);
  --theme-calendar-today-bgcolor: rgba(32, 93, 194, .1);
  --theme-calendar-holiday-bgcolor: rgba(235, 87, 87, .1);
  --theme-calendar-weekend-bgcolor: rgba(242, 153, 74, .05);
  --theme-calendar-weekend-stroke-color: #444;
  --theme-calendar-event-caption-color: rgba(0, 0, 0, .6);
  --theme-calendar-event-available-color: rgba(55, 122, 230, .2);
  --theme-calendar-event-available-bgcolor: #f6f9fe;
  --theme-calendar-event-unavailable-color: rgba(244, 119, 88, .2);
  --theme-calendar-event-unavailable-bgcolor: #fdece7;

  --theme-diffview-block-header-color: rgba(56, 139, 253, 0.1);
  --theme-diffview-line-color: var(--theme-content-color);
  --theme-diffview-insert-line-color: rgba(46, 160, 67, 0.15);
  --theme-diffview-delete-line-color: rgba(248, 81, 73, 0.1);
  --theme-diffview-empty-line-color: rgba(110, 118, 129, 0.1);
  --theme-diffview-insert-color: rgb(63, 185, 80);
  --theme-diffview-delete-color: rgb(248, 81, 73);

  --theme-tooltip-color: rgba(255, 255, 255, .8);
  --theme-tooltip-bg: #353347;
  --theme-tooltip-key-bg: rgba(255, 255, 255, .08);

  --theme-inbox-notify: #F47758;
  --theme-inbox-people-notify: #2B5190;
  --theme-inbox-activity-bgcolor: #1A1A28;
  --theme-inbox-activitymsg-bgcolor: rgba(255, 255, 255, .03);
  --theme-inbox-activitymsg-divider: rgba(255, 255, 255, .1);
  --theme-inbox-activitymsg-border: rgba(255, 255, 255, .03);
  --theme-inbox-counter-bgcolor: rgba(255, 255, 255, .06);
  --theme-inbox-people-counter-bgcolor: rgba(43, 81, 144, .1);

  --theme-toggle-sw-color: #fff;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(120, 120, 128, 0.32);
  --theme-toggle-bg-hover: rgba(120, 120, 128, 0.64);
  --theme-toggle-on-bg-color: #205dc2;
  --theme-toggle-on-bg-hover: #1A53AF;
  --theme-radio-bg-color: #343442;

  --theme-error-color: #eb5757;
  --theme-urgent-color: #F5694A;
  --theme-warning-color: #f2994a;
  --theme-lost-color: #eb5757;
  --theme-won-color: #34DB80;
  --theme-caret-color: #fff;
  --theme-code-color: #DD812B;
  --theme-code-bg-color: #262F40;

  --theme-text-editor-note-anchor-bg-neutral: #2C2C2C;
  /* Gray, no saturation change needed */
  --theme-text-editor-note-anchor-bg-dangerous: #8F4040;
  --theme-text-editor-note-anchor-bg-dangerous-light: #8E6464;
  --theme-text-editor-note-anchor-bg-warning: #A88D4E;
  --theme-text-editor-note-anchor-bg-warning-light: #8A8666;
  --theme-text-editor-note-anchor-bg-positive: #596941;
  --theme-text-editor-note-anchor-bg-positive-light: #7B9589;
  --theme-text-editor-note-anchor-bg-primary: #688797;
  --theme-text-editor-note-anchor-bg-primary-light: #747C81;

  --text-editor-block-quote-color: #DA5701;
  --text-edtior-hr-border-color: rgba(255, 255, 255, 0.1);
  --text-editor-table-border-color: hsl(220, 6%, 40%);
  --text-editor-color-picker-outline: rgba(250, 222, 201, 0.3);

  --theme-text-editor-palette-text-gray: rgba(155, 155, 155, 1);
  --theme-text-editor-palette-text-brown: rgba(186, 133, 111, 1);
  --theme-text-editor-palette-text-orange: rgba(199, 125, 72, 1);
  --theme-text-editor-palette-text-yellow: rgba(202, 152, 73, 1);
  --theme-text-editor-palette-text-green: rgba(82, 158, 114, 1);
  --theme-text-editor-palette-text-blue: rgba(94, 135, 201, 1);
  --theme-text-editor-palette-text-purple: rgba(157, 104, 211, 1);
  --theme-text-editor-palette-text-pink: rgba(209, 87, 150, 1);
  --theme-text-editor-palette-text-red: rgba(223, 84, 82, 1);

  --theme-text-editor-palette-bg-gray: rgba(47, 47, 47, 1);
  --theme-text-editor-palette-bg-brown: rgba(74, 50, 40, 1);
  --theme-text-editor-palette-bg-orange: rgba(92, 59, 35, 1);
  --theme-text-editor-palette-bg-yellow: rgba(86, 67, 40, 1);
  --theme-text-editor-palette-bg-green: rgba(36, 61, 48, 1);
  --theme-text-editor-palette-bg-blue: rgba(20, 58, 78, 1);
  --theme-text-editor-palette-bg-purple: rgba(60, 45, 73, 1);
  --theme-text-editor-palette-bg-pink: rgba(78, 44, 60, 1);
  --theme-text-editor-palette-bg-red: rgba(82, 46, 42, 1);

  --accent-bg-color: #27282b;
  --accent-shadow: rgb(0 0 0 / 10%) 0px 2px 4px;

  --highlight-hover: #282834;
  --highlight-select: #252b3a;
  --highlight-select-border: #44506b;
  --highlight-select-hover: #2c3346;

  --scrollbar-bar-color: #35354a;
  --scrollbar-bar-hover: #8a8aa5;
  --scrollbar-track-color: #35354a;

  --dark-color: #62666d;
  --content-color: #8a8f98;
  --theme-accent-color: rgba(255, 255, 255, .8);
  --accent-color: #d7d8db;
  --caption-color: #f7f8f8;
  --white-color: #fff;

  --divider-color: #303236;
  --divider-trans-color: rgba(255, 255, 255, .12);
  --menu-bg-select: #2d2f36;
  --menu-bg-select-trans: #2d2f3665;
  --menu-icon-hover: #f3f3f8;
  --header-bg-color: linear-gradient(0deg, var(--accent-bg-color), #2d2e31);
  --popup-bg-color: linear-gradient(136.61deg, var(--accent-bg-color) 13.72%, #2d2e31 74.3%);
  --popup-bg-hover: #37373c;
  --popup-divider: #313236;
  --popup-shadow: rgb(0 0 0 / 50%) 0px 4px 24px;
  --popup-panel-shadow: rgb(0 0 0 / 55%) 0px 7px 24px;
  --popup-aside-shadow: rgb(0 0 0 / 25%) 0px 8px 16px;
  --card-shadow: rgb(0 0 0 / 50%) 0px 16px 70px;
  --card-overlay-color: rgba(28, 29, 31, .5);
  --avatar-bg-color: #4f5358;
  --avatar-border-color: rgba(255, 255, 255, .1);
  --tooltip-bg-color: #1f2023f0;

  --button-bg-color: #303236;
  --button-bg-hover: #37383b;
  --button-border-color: #3c3f44;
  --button-border-hover: #45484e;
  --button-shadow: rgb(0 0 0 / 15%) 0px 1px 1px 1px;
  --button-disabled-color: #313236;
  --noborder-bg-color: #313236;
  --noborder-bg-hover: #37383b;
  --primary-bg-color: #5e6ad2;
  --primary-bg-hover: #717ce1;
  --primary-edit-border-color: #6499ff;
  --primary-shadow: rgb(0 0 0 / 25%) 0px 1px 2px;
  --dangerous-bg-color: #eb5757;
  --dangerous-bg-hover: #ff6464;
  --dangerous-shadow: var(--dangerous-bg-color) 0px 0px 12px -1px;

  --incoming-msg: rgba(67, 67, 72, .3);
  --outcoming-msg: rgba(67, 67, 72, .6);

  --trans-content-05: rgba(138, 143, 152, .05);
  --trans-content-10: rgba(138, 143, 152, .1);
  --trans-content-20: rgba(138, 143, 152, .2);

  --text-editor-toc-default-color: rgba(255, 255, 255, 0.1);
  --text-editor-toc-hovered-color: rgba(255, 255, 255, 0.4);
  --text-editor-table-header-color: rgba(255, 255, 255, 0.06);

  --theme-clockface-back: radial-gradient(farthest-corner at 50% 0%, #bbb, #fff 100%);
  --theme-clockface-shadow: inset 0 -3px 10px #aaa;
  --theme-clockface-hours: #666;
  --theme-clockface-quarter: #31302e;
  --theme-clockface-min-arrow: conic-gradient(at 50% -10px, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0) 49%, #2F2F3A 50%, rgba(0, 0, 0, 0) 51%, rgba(0, 0, 0, 0) 100%);
  --theme-clockface-arrows-holder: radial-gradient(at top center, #2F2F3A, #555555);
  --theme-clockface-arrows-shadow: 0 0 1px white;

  --theme-link-preview-bg-color: #2B2D41;
  --theme-link-preview-description-color: #C1C9D6;
  --theme-link-preview-text-color: #FFFFFF;

  --theme-dialog-border-color: rgba(255, 255, 255, 0.1);
  --theme-dialog-background-color: #2a2938;
  --theme-dialog-back-color: #848484;
  --theme-icon-stroke: #e8e9e9;
  --theme-state-ghost-color: rgba(123, 123, 123, 0.6);
  --theme-state-ghost-background-color: rgba(123, 123, 123, 0.1);
  --theme-state-ghost-border-color: transparent;
  --theme-state-primary-color: #3070dc;
  --theme-state-primary-background-color: rgba(48, 112, 220, 0.1);
  --theme-state-primary-border-color: rgba(48, 112, 220, 0.15);
  --theme-state-regular-color: #7b7b7b;
  --theme-state-regular-background-color: rgba(123, 123, 123, 0.1);
  --theme-state-regular-border-color: rgba(123, 123, 123, 0.15);
  --theme-wizard-not-visited-color: #34343c;

  --calendar-event-back-color: #2B2D41;
  --calendar-event-border-color: rgba(43, 81, 144, 0.2);
  --calendar-event-handle-color: #2b5190;

  --theme-label-green-color: rgba(34, 197, 94, .8);
  --theme-label-green-bg-color: rgba(34, 197, 94, .08);
  --theme-label-green-border-color: rgba(34, 197, 94, .2);

  --theme-label-blue-color: rgba(59, 130, 246, .8);
  --theme-label-blue-bg-color: rgba(59, 130, 246, .08);
  --theme-label-blue-border-color: rgba(59, 130, 246, .2);

  --theme-label-gray-color: rgba(255, 255, 255, .6);
  --theme-label-gray-bg-color: rgba(255, 255, 255, .02);
  --theme-label-gray-border-color: rgba(255, 255, 255, .09);
}

/* Light Theme */
.theme-light {
  --theme-drawing-bg-color: #EEEEEE;

  --theme-text-primary-color: rgba(0, 0, 0, .8);
  --theme-text-placeholder-color: rgba(0, 0, 0, .4);

  --primary-button-disabled: rgba(0, 0, 0, .12);
  --primary-button-disabled-color: rgba(0, 0, 0, .4);
  --secondary-button-disabled: rgba(0, 0, 0, .12);
  --secondary-button-disabled-color: rgba(0, 0, 0, .4);

  --theme-button-default: rgba(0, 0, 0, .02);
  --theme-button-hovered: rgba(0, 0, 0, .04);
  --theme-button-pressed: rgba(0, 0, 0, .08);
  --theme-button-focused: rgba(0, 0, 0, .08);
  --theme-button-focused-border: #fff;
  --theme-button-disabled: rgba(0, 0, 0, .08);
  --theme-button-border: rgba(0, 0, 0, .09);

  --theme-breadcrumb-default: rgba(0, 0, 0, 0);
  --theme-breadcrumb-hovered: rgba(0, 0, 0, .08);
  --theme-breadcrumb-pressed: rgba(0, 0, 0, .1);
  --theme-button-icon-default: rgba(0, 0, 0, 0);
  --theme-button-icon-hovered: rgba(0, 0, 0, .06);
  --theme-button-icon-pressed: rgba(0, 0, 0, .1);

  --theme-button-contrast-color: #fff;
  --theme-button-contrast-enabled: rgba(0, 0, 0, .8);
  --theme-button-contrast-hovered: #000;
  --theme-button-contrast-pressed: rgba(0, 0, 0, .6);
  --theme-button-contrast-disabled: rgba(0, 0, 0, .6);
  --theme-button-contrast-disabled-color: rgba(255, 255, 255, .5);
  --theme-button-contrast-border: rgba(0, 0, 0, .2);

  --theme-button-container-color: #F1F1F1;

  --theme-refinput-divider: rgba(0, 0, 0, .07);
  --theme-refinput-border: rgba(0, 0, 0, .1);

  // Be aware to update defineAlpha() function in colors.ts
  --theme-bg-color: #F1F1F4;
  --theme-bg-accent-color: rgba(255, 255, 255, .08);
  --theme-bg-dark-color: rgba(255, 255, 255, .8);
  --theme-back-color: #D9D9DD;
  --theme-overlay-color: rgba(0, 0, 0, .2);
  --theme-statusbar-color: #FFF;
  --theme-navpanel-color: #FBFBFC;
  --theme-navpanel-hovered: rgba(0, 0, 0, .04);
  --theme-navpanel-selected: rgba(0, 0, 0, .08);
  --theme-navpanel-divider: rgba(0, 0, 0, .1);
  --theme-navpanel-border: rgba(0, 0, 0, .06);
  --theme-navpanel-icons-color: #7F7F7F;
  --theme-navpanel-icons-divider: rgba(0, 0, 0, .1);
  --theme-navpanel-shadow-mobile: drop-shadow(0 0 2px rgba(0, 0, 0, .2));
  --theme-comp-header-color: #FBFBFC;
  --theme-divider-color: rgba(0, 0, 0, .06);
  --theme-bg-divider-color: #E3E3E5;
  --theme-mention-bg-color: rgba(55, 122, 230, 0.1);
  --theme-mention-focused-bg-color: rgba(55, 122, 230, 0.2);
  --theme-mention-bg-color-notransparent: rgb(235, 242, 253);
  --theme-broken-mention-bg-color: rgba(0, 0, 0, .12);
  --theme-broken-mention-color: rgba(0, 0, 0, .4);
  --theme-broken-mention-focused-bg-color: rgba(0, 0, 0, .2);

  --theme-link-preview-bg-color: #E5E8F0;
  --theme-link-preview-description-color: #5A667E;
  --theme-link-preview-text-color: #0F121A;

  --theme-trans-color: rgba(0, 0, 0, .3);
  --theme-darker-color: rgba(0, 0, 0, .4);
  --theme-halfcontent-color: rgba(0, 0, 0, .5);
  --theme-dark-color: rgba(0, 0, 0, .6);
  --theme-content-color: rgba(0, 0, 0, .8);
  --theme-caption-color: #000;
  --theme-link-color: #377AE6;

  --theme-list-border-color: rgba(0, 0, 0, .09);
  --theme-list-header-color: red; //#ECD4CA;
  --theme-list-subheader-color: #EEEEF0;
  --theme-list-row-color: #F7F7F8;
  --theme-list-divider-color: rgba(0, 0, 0, .07);
  --theme-list-subheader-divider: rgba(0, 0, 0, .06);

  --theme-list-button-color: #F2F2F4;
  --theme-list-button-hover: #E8E8EA;
  --theme-link-button-color: #E5E5E7;
  --theme-link-button-hover: #DCDCDE;

  --theme-table-border-color: rgba(0, 0, 0, .1);
  --theme-table-header-color: #EFEFF2;
  --theme-table-row-color: #F4F4F6;

  --theme-kanban-card-bg-color: rgba(0, 0, 0, .03);
  --theme-kanban-card-border: rgba(0, 0, 0, .04);
  --theme-kanban-card-footer: rgba(0, 0, 0, .04);

  --theme-editbox-focus-color: rgba(0, 0, 0, .08);
  --theme-editbox-focus-border: #5190EC;
  --theme-tablist-color: rgba(0, 0, 0, .02);
  --theme-tablist-plain-color: #2A64C4;
  --theme-tablist-plain-divider: rgba(0, 0, 0, .07);
  --theme-checkbox-color: #000;
  --theme-checkbox-bg-color: #FFF;
  --theme-checkbox-border: rgba(0, 0, 0, .12);
  --theme-checkbox-disabled: #999;
  --theme-progress-color: rgba(0, 0, 0, .5);
  --theme-popup-color: #FFFFFF;
  --theme-popup-trans-color: #FFFFFFee;
  --theme-popup-trans-gradient: linear-gradient(to bottom, #FFFf, #FFF0);
  --theme-popup-hover: #EBEBEB;
  --theme-popup-divider: rgba(0, 0, 0, .09);
  --theme-popup-header: #EBEBEB;
  --theme-popup-shadow: 0 0 .5rem rgba(0, 0, 0, .2);
  --theme-popup-checkicon: #205DC2;
  --theme-popup-deactivated: #C7C8CA;
  --theme-panel-color: #FFFFFF;
  --theme-calendar-today-color: #000;
  --theme-calendar-holiday-color: #eb5757;
  --theme-calendar-weekend-color: rgba(242, 153, 74, 1);
  --theme-calendar-today-bgcolor: rgba(51, 157, 255, .1);
  --theme-calendar-holiday-bgcolor: rgba(235, 87, 87, .1);
  --theme-calendar-weekend-bgcolor: rgba(242, 153, 74, .1);
  --theme-calendar-weekend-stroke-color: #ddd;
  --theme-calendar-event-caption-color: rgba(0, 0, 0, .8);
  --theme-calendar-event-available-color: rgba(55, 122, 230, .2);
  --theme-calendar-event-available-bgcolor: #f6f9fe;
  --theme-calendar-event-unavailable-color: rgba(244, 119, 88, .2);
  --theme-calendar-event-unavailable-bgcolor: #fdece7;

  --theme-diffview-block-header-color: rgb(221, 244, 255);
  --theme-diffview-line-color: var(--theme-content-color);
  --theme-diffview-insert-line-color: rgb(230, 255, 236);
  --theme-diffview-delete-line-color: rgb(255, 235, 233);
  --theme-diffview-empty-line-color: rgba(234, 238, 242, 0.5);
  --theme-diffview-insert-color: rgb(26, 127, 55);
  --theme-diffview-delete-color: rgb(209, 36, 47);

  --theme-tooltip-color: #FFF;
  --theme-tooltip-bg: #444248;
  --theme-tooltip-key-bg: rgba(255, 255, 255, .08);

  --theme-inbox-notify: #F47758;
  --theme-inbox-people-notify: #2B5190;
  --theme-inbox-activity-bgcolor: #fff;
  --theme-inbox-activitymsg-bgcolor: #F2F2F2;
  --theme-inbox-activitymsg-divider: rgba(0, 0, 0, .1);
  --theme-inbox-activitymsg-border: rgba(0, 0, 0, .03);
  --theme-inbox-counter-bgcolor: rgba(0, 0, 0, .06);
  --theme-inbox-people-counter-bgcolor: rgba(43, 81, 144, .1);

  --theme-toggle-sw-color: #fff;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(120, 120, 128, 0.32);
  --theme-toggle-bg-hover: rgba(120, 120, 128, 0.64);
  --theme-toggle-on-bg-color: #205dc2;
  --theme-toggle-on-bg-hover: #1A53AF;
  --theme-radio-bg-color: #E5E5E5;

  --theme-error-color: #eb5757; // Dark
  --theme-urgent-color: #F5694A;
  --theme-warning-color: #f2994a; // Dark
  --theme-lost-color: #eb5757; // Dark
  --theme-won-color: #34DB80; // Dark
  --theme-caret-color: #669AFF;
  --theme-code-color: #B63F00;
  --theme-code-bg-color: rgba(0, 0, 0, .05);

  --theme-text-editor-note-anchor-bg-neutral: #F3F3F3;
  --theme-text-editor-note-anchor-bg-dangerous: #DF8D8B;
  --theme-text-editor-note-anchor-bg-dangerous-light: #EECECE;
  --theme-text-editor-note-anchor-bg-warning: #FDE5A4;
  --theme-text-editor-note-anchor-bg-warning-light: #FEF4D1;
  --theme-text-editor-note-anchor-bg-positive: #BED6AF;
  --theme-text-editor-note-anchor-bg-positive-light: #DEE9D9;
  --theme-text-editor-note-anchor-bg-primary: #AAC5E9;
  --theme-text-editor-note-anchor-bg-primary-light: #D5E5F5;

  --text-editor-block-quote-color: #DA5701;
  --text-edtior-hr-border-color: rgba(0, 0, 0, 0.1);
  --text-editor-table-border-color: #c9cbcd;
  --text-editor-color-picker-outline: rgb(227, 226, 224);

  --theme-text-editor-palette-text-gray: rgba(120, 119, 116, 1);
  --theme-text-editor-palette-text-brown: rgba(159, 107, 83, 1);
  --theme-text-editor-palette-text-orange: rgba(217, 115, 13, 1);
  --theme-text-editor-palette-text-yellow: rgba(203, 145, 47, 1);
  --theme-text-editor-palette-text-green: rgba(68, 131, 97, 1);
  --theme-text-editor-palette-text-blue: rgba(51, 126, 169, 1);
  --theme-text-editor-palette-text-purple: rgba(144, 101, 176, 1);
  --theme-text-editor-palette-text-pink: rgba(193, 76, 138, 1);
  --theme-text-editor-palette-text-red: rgba(212, 76, 71, 1);

  --theme-text-editor-palette-bg-gray: rgba(241, 241, 239, 1);
  --theme-text-editor-palette-bg-brown: rgba(244, 238, 238, 1);
  --theme-text-editor-palette-bg-orange: rgba(251, 236, 221, 1);
  --theme-text-editor-palette-bg-yellow: rgba(251, 243, 219, 1);
  --theme-text-editor-palette-bg-green: rgba(237, 243, 236, 1);
  --theme-text-editor-palette-bg-blue: rgba(231, 243, 248, 1);
  --theme-text-editor-palette-bg-purple: rgba(244, 240, 247, 0.8);
  --theme-text-editor-palette-bg-pink: rgba(249, 238, 243, 0.8);
  --theme-text-editor-palette-bg-red: rgba(253, 235, 236, 1);

  --accent-bg-color: #eff0f2; // HZ
  --accent-shadow: rgb(0 0 0 / 10%) 0px 2px 4px; // Dark

  --highlight-hover: #E8E8E9;
  --highlight-select: #f0f4ff;
  --highlight-select-border: #e6eaff;
  --highlight-select-hover: #e4ebff;

  --scrollbar-bar-color: #e0e0e0;
  --scrollbar-bar-hover: #90959d;
  --scrollbar-track-color: #e0e0e0;

  --dark-color: #90959d;
  --content-color: #3c4149;
  --accent-color: #282a30;
  --theme-accent-color: rgba(0, 0, 0, .8);
  --caption-color: #131416;
  --white-color: #fff;

  --divider-color: #e0e0e0;
  --divider-trans-color: rgba(0, 0, 0, .12);
  --menu-bg-select: #f0f3f9;
  --menu-bg-select-trans: #f0f3f965;
  --menu-icon-hover: #282a30;
  --header-bg-color: linear-gradient(0deg, #eee, #f6f6f6);
  --popup-bg-color: linear-gradient(136.61deg, #fff 13.72%, #f8f8f8 74.3%);
  --popup-bg-hover: #f0f3f9;
  --popup-divider: #eff1f4;
  --popup-shadow: rgb(0 0 0 / 20%) 0px 4px 24px; // Dark
  --popup-panel-shadow: rgb(0 0 0 / 10%) 0px 4px 18px;
  --popup-aside-shadow: rgb(0 0 0 / 25%) 0px 8px 16px;
  --card-shadow: rgb(0 0 0 / 50%) 0px 16px 70px;
  --card-overlay-color: rgba(144, 149, 157, .4);
  --avatar-bg-color: #e0e0e0; // HZ
  --avatar-border-color: transparent;
  --tooltip-bg-color: #fffffff0;

  --button-bg-color: #fff;
  --button-bg-hover: #f4f5f8;
  --button-border-color: #dfe1e4;
  --button-border-hover: #c9cbcd;
  --button-shadow: rgb(0 0 0 / 20%) 0px 1px 2px 1px;
  --button-disabled-color: #eff1f4;
  --noborder-bg-color: #eff1f4;
  --noborder-bg-hover: #f4f5f8;
  --primary-bg-color: #6e79d6;
  --primary-bg-hover: #5c67c7;
  --primary-edit-border-color: #2161dc;
  --primary-shadow: rgb(0 0 0 / 7%) 0px 1px 2px;
  --dangerous-bg-color: #eb5757;
  --dangerous-bg-hover: #d44e4e;
  --dangerous-shadow: var(--dangerous-bg-color) 0px 0px 12px -1px;

  --incoming-msg: rgba(67, 67, 72, .1);
  --outcoming-msg: rgba(67, 67, 72, .2);

  --trans-content-05: rgba(60, 65, 73, .05);
  --trans-content-10: rgba(60, 65, 73, .1);
  --trans-content-20: rgba(60, 65, 73, .2);

  --text-editor-toc-default-color: rgba(0, 0, 0, 0.1);
  --text-editor-toc-hovered-color: rgba(0, 0, 0, 0.4);
  --text-editor-table-header-color: rgba(0, 0, 0, 0.06);

  --theme-clockface-back: radial-gradient(farthest-corner at 50% 0%, #606060, #000 100%);
  --theme-clockface-shadow: inset 0 -3px 10px #000;
  --theme-clockface-hours: #999;
  --theme-clockface-quarter: #CECFD1;
  --theme-clockface-min-arrow: conic-gradient(at 50% -10px, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 49%, white 50%, rgba(255, 255, 255, 0) 51%, rgba(255, 255, 255, 0) 100%);
  --theme-clockface-arrows-holder: radial-gradient(at top center, #eee, #aaa);
  --theme-clockface-arrows-shadow: 0 0 1px black;

  --theme-icon-stroke: #1f212b;
  --theme-dialog-border-color: rgba(0, 0, 0, 0.1);
  --theme-dialog-background-color: #ffffff;
  --theme-dialog-back-color: #616161;
  --theme-state-ghost-color: rgba(123, 123, 123, 0.6);
  --theme-state-ghost-background-color: rgba(123, 123, 123, 0.1);
  --theme-state-ghost-border-color: transparent;
  --theme-state-primary-color: #3070dc;
  --theme-state-primary-background-color: rgba(48, 112, 220, 0.1);
  --theme-state-primary-border-color: rgba(48, 112, 220, 0.15);
  --theme-state-regular-color: #7b7b7b;
  --theme-state-regular-background-color: rgba(123, 123, 123, 0.1);
  --theme-state-regular-border-color: rgba(123, 123, 123, 0.15);
  --theme-wizard-not-visited-color: #e8e9e9;

  --calendar-event-back-color: #f3f6fb;
  --calendar-event-border-color: rgba(43, 81, 144, 0.2);
  --calendar-event-handle-color: #2b5190;

  --theme-button-contrast-color: #fff;
  --theme-button-contrast-enabled: rgba(0, 0, 0, .8);
  --theme-button-contrast-hovered: #000;
  --theme-button-contrast-pressed: rgba(0, 0, 0, .6);
  --theme-button-contrast-disabled: rgba(0, 0, 0, .6);
  --theme-button-contrast-disabled-color: rgba(255, 255, 255, .5);
  --theme-button-contrast-border: rgba(0, 0, 0, .2);

  --theme-label-green-color: rgba(22, 130, 60, 0.9);
  --theme-label-green-bg-color: rgba(22, 163, 74, .08);
  --theme-label-green-border-color: rgba(22, 163, 74, .15);

  --theme-label-blue-color: rgba(37, 99, 235, 0.9);
  --theme-label-blue-bg-color: rgba(59, 130, 246, .08);
  --theme-label-blue-border-color: rgba(59, 130, 246, .15);

  --theme-label-gray-color: rgba(0, 0, 0, .6);
  --theme-label-gray-bg-color: rgba(0, 0, 0, .02);
  --theme-label-gray-border-color: rgba(0, 0, 0, .09);
}

.theme-dark.theme-high-contrast {
  // Backgrounds
  --theme-bg-color: #000000;
  --theme-back-color: #000000;
  --theme-navpanel-color: #000000;
  --theme-comp-header-color: #000000;
  --theme-panel-color: #000000;
  --theme-statusbar-color: #000000;
  --theme-bg-accent-color: rgba(255, 255, 255, .15);
  --theme-bg-dark-color: rgba(255, 255, 255, .3);
  --theme-overlay-color: rgba(0, 0, 0, .9);

  // Dividers
  --theme-divider-color: #ffffff;
  --theme-bg-divider-color: #333333;

  // Text colors
  --theme-trans-color: rgba(255, 255, 255, .5);
  --theme-darker-color: rgba(255, 255, 255, .7);
  --theme-halfcontent-color: rgba(255, 255, 255, .8);
  --theme-dark-color: #e0e0e0;
  --theme-content-color: #f5f5f5;
  --theme-caption-color: #ffffff;
  --theme-link-color: #ffff00;
  --theme-text-primary-color: #ffffff;
  --theme-text-placeholder-color: rgba(255, 255, 255, .6);

  // Primary buttons
  --primary-button-default: #ffff00;
  --primary-button-hovered: #ffff66;
  --primary-button-pressed: #cccc00;
  --primary-button-focused: #ffff00;
  --primary-button-color: #000000;
  --primary-button-content-color: rgba(0, 0, 0, .8);
  --primary-button-border: rgba(255, 255, 255, .3);
  --primary-button-disabled: rgba(255, 255, 0, .3);
  --primary-button-disabled-color: rgba(255, 255, 0, .5);

  // Theme buttons
  --theme-button-default: #000000;
  --theme-button-hovered: #111111;
  --theme-button-pressed: #222222;
  --theme-button-focused: #111111;
  --theme-button-focused-border: #ffffff;
  --theme-button-border: #ffffff;
  --theme-button-disabled: #000000;
  --theme-button-contrast-color: #ffff00;
  --theme-button-contrast-enabled: #ffffff;
  --theme-button-contrast-hovered: #ffff00;
  --theme-button-contrast-pressed: rgba(255, 255, 0, .8);
  --theme-button-container-color: #111111;

  // Navigation
  --theme-navpanel-hovered: rgba(255, 255, 255, .1);
  --theme-navpanel-selected: rgba(255, 255, 255, .2);
  --theme-navpanel-divider: #ffffff;
  --theme-navpanel-border: #ffffff;
  --theme-navpanel-icons-color: #ffffff;
  --theme-navpanel-icons-divider: #ffffff;

  // Lists & Tables
  --theme-list-border-color: #ffffff;
  --theme-list-header-color: #111111;
  --theme-list-subheader-color: #000000;
  --theme-list-row-color: #000000;
  --theme-list-divider-color: rgba(255, 255, 255, .3);
  --theme-list-subheader-divider: rgba(255, 255, 255, .2);
  --theme-list-button-color: #000000;
  --theme-list-button-hover: #111111;
  --theme-link-button-color: #000000;
  --theme-link-button-hover: #111111;
  --theme-table-border-color: #ffffff;
  --theme-table-header-color: #000000;
  --theme-table-row-color: #000000;

  // Popups
  --theme-popup-color: #000000;
  --theme-popup-trans-color: #000000ee;
  --theme-popup-hover: #111111;
  --theme-popup-divider: #ffffff;
  --theme-popup-header: #000000;
  --theme-popup-shadow: 0 0 1rem rgba(255, 255, 255, .5);
  --theme-popup-checkicon: #ffff00;
  --theme-popup-deactivated: #000000;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(255, 255, 255, .1);
  --theme-editbox-focus-border: #ffff00;
  --theme-refinput-divider: #ffffff;
  --theme-refinput-border: #ffffff;

  // Tooltips
  --theme-tooltip-color: #000000;
  --theme-tooltip-bg: #ffff00;
  --theme-tooltip-key-bg: rgba(0, 0, 0, .2);

  // Calendar
  --theme-calendar-today-color: #ffff00;
  --theme-calendar-holiday-color: #ff6666;
  --theme-calendar-weekend-color: #ffffff;
  --theme-calendar-today-bgcolor: rgba(255, 255, 0, .2);
  --theme-calendar-holiday-bgcolor: rgba(255, 102, 102, .2);
  --theme-calendar-weekend-bgcolor: rgba(255, 255, 255, .1);
  --theme-calendar-weekend-stroke-color: #ffffff;
  --theme-calendar-event-caption-color: #ffffff;

  // Mentions
  --theme-mention-bg-color: rgba(255, 255, 0, .2);
  --theme-mention-focused-bg-color: rgba(255, 255, 0, .3);
  --theme-mention-bg-color-notransparent: #333300;

  // States
  --theme-error-color: #ff6666;
  --theme-warning-color: #ffff00;
  --theme-caret-color: #ffff00;
  --theme-checkbox-color: #000;
  --theme-checkbox-bg-color: #FFF;
  --theme-checkbox-border: #ffffff;
  --theme-progress-color: #ffff00;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(255, 255, 255, .05);
  --theme-kanban-card-border: #ffffff;
  --theme-kanban-card-footer: rgba(255, 255, 255, .1);

  // Code blocks
  --theme-code-color: #ffff00;
  --theme-code-bg-color: #111111;

  // Accent elements
  --accent-bg-color: #111111;
  --highlight-hover: #111111;
  --highlight-select: #222222;
  --highlight-select-border: #ffffff;
  --highlight-select-hover: #333333;

  // Scrollbars
  --scrollbar-bar-color: #666666;
  --scrollbar-bar-hover: #ffffff;
  --scrollbar-track-color: #333333;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(255, 255, 255, .12);
  --secondary-button-disabled-color: rgba(255, 255, 255, .4);
  --positive-button-disabled: rgba(255, 255, 0, .15);
  --positive-button-disabled-color: rgba(255, 255, 0, .5);
  --negative-button-disabled: rgba(255, 102, 102, .15);
  --negative-button-disabled-color: rgba(255, 102, 102, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(255, 255, 255, 0);
  --theme-breadcrumb-hovered: rgba(255, 255, 255, .1);
  --theme-breadcrumb-pressed: rgba(255, 255, 255, .15);
  --theme-button-icon-default: rgba(255, 255, 255, 0);
  --theme-button-icon-hovered: rgba(255, 255, 255, .08);
  --theme-button-icon-pressed: rgba(255, 255, 255, .12);

  // Tablist & toggles
  --theme-tablist-color: rgba(255, 255, 255, .02);
  --theme-tablist-plain-color: #ffff00;
  --theme-tablist-plain-divider: rgba(255, 255, 255, .1);
  --theme-toggle-sw-color: #fff;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(255, 255, 255, .32);
  --theme-toggle-bg-hover: rgba(255, 255, 255, .64);
  --theme-toggle-on-bg-color: #ffff00;
  --theme-toggle-on-bg-hover: #ffff66;
  --theme-radio-bg-color: #111111;

  // Inbox
  --theme-inbox-notify: #ffff00;
  --theme-inbox-people-notify: #ffff00;
  --theme-inbox-activity-bgcolor: #000000;
  --theme-inbox-activitymsg-bgcolor: rgba(255, 255, 255, .05);
  --theme-inbox-activitymsg-divider: #ffffff;
  --theme-inbox-activitymsg-border: rgba(255, 255, 255, .05);
  --theme-inbox-counter-bgcolor: rgba(255, 255, 255, .08);
  --theme-inbox-people-counter-bgcolor: rgba(255, 255, 0, .15);

  // Text editor
  --theme-drawing-bg-color: #111111;
  --text-editor-block-quote-color: #ffff00;
  --text-edtior-hr-border-color: rgba(255, 255, 255, .15);
  --text-editor-table-border-color: #ffffff;
  --theme-urgent-color: #ff9966;
  --theme-lost-color: #ff6666;
  --theme-won-color: #66ff66;
}

.theme-dark.theme-midnight {
  // Backgrounds
  --theme-bg-color: #050816;
  --theme-back-color: #02030a;
  --theme-navpanel-color: #020617;
  --theme-comp-header-color: #020617;
  --theme-panel-color: #020617;
  --theme-statusbar-color: #020617;
  --theme-bg-accent-color: rgba(30, 58, 138, .15);
  --theme-bg-dark-color: rgba(30, 58, 138, .3);
  --theme-overlay-color: rgba(0, 0, 0, .85);

  // Dividers
  --theme-divider-color: rgba(148, 163, 184, 0.4);
  --theme-bg-divider-color: #111827;

  // Text colors
  --theme-trans-color: rgba(148, 163, 184, .4);
  --theme-darker-color: rgba(203, 213, 225, .5);
  --theme-halfcontent-color: rgba(203, 213, 225, .7);
  --theme-dark-color: rgba(226, 232, 240, .8);
  --theme-content-color: rgba(241, 245, 249, .9);
  --theme-caption-color: #f1f5f9;
  --theme-link-color: #60a5fa;
  --theme-text-primary-color: #f1f5f9;
  --theme-text-placeholder-color: rgba(148, 163, 184, .6);

  // Primary buttons
  --primary-button-default: #2563eb;
  --primary-button-hovered: #3b82f6;
  --primary-button-pressed: #1d4ed8;
  --primary-button-focused: #2563eb;
  --primary-button-color: #ffffff;
  --primary-button-content-color: rgba(255, 255, 255, .9);
  --primary-button-border: rgba(96, 165, 250, .3);
  --primary-button-disabled: rgba(37, 99, 235, .3);
  --primary-button-disabled-color: rgba(96, 165, 250, .5);

  // Theme buttons
  --theme-button-default: rgba(15, 23, 42, 0.6);
  --theme-button-hovered: rgba(30, 64, 175, 0.6);
  --theme-button-pressed: rgba(15, 23, 42, 0.8);
  --theme-button-focused: rgba(30, 64, 175, 0.6);
  --theme-button-focused-border: rgba(96, 165, 250, .4);
  --theme-button-border: rgba(148, 163, 184, 0.6);
  --theme-button-disabled: rgba(15, 23, 42, 0.3);
  --theme-button-contrast-color: #f1f5f9;
  --theme-button-contrast-enabled: rgba(59, 130, 246, .9);
  --theme-button-contrast-hovered: #60a5fa;
  --theme-button-contrast-pressed: #3b82f6;
  --theme-button-container-color: #0f172a;

  // Navigation
  --theme-navpanel-hovered: rgba(30, 58, 138, .15);
  --theme-navpanel-selected: rgba(30, 58, 138, .3);
  --theme-navpanel-divider: rgba(148, 163, 184, .2);
  --theme-navpanel-border: rgba(148, 163, 184, .15);
  --theme-navpanel-icons-color: #94a3b8;
  --theme-navpanel-icons-divider: rgba(148, 163, 184, .2);

  // Lists & Tables
  --theme-list-border-color: rgba(148, 163, 184, .15);
  --theme-list-header-color: #0f172a;
  --theme-list-subheader-color: #020617;
  --theme-list-row-color: #0f172a;
  --theme-list-divider-color: rgba(148, 163, 184, .2);
  --theme-list-subheader-divider: rgba(148, 163, 184, .15);
  --theme-list-button-color: #0f172a;
  --theme-list-button-hover: #1e293b;
  --theme-link-button-color: #0f172a;
  --theme-link-button-hover: #1e293b;
  --theme-table-border-color: rgba(148, 163, 184, .2);
  --theme-table-header-color: #0f172a;
  --theme-table-row-color: #020617;

  // Popups
  --theme-popup-color: #0f172a;
  --theme-popup-trans-color: #0f172aee;
  --theme-popup-hover: #1e293b;
  --theme-popup-divider: rgba(148, 163, 184, .2);
  --theme-popup-header: #020617;
  --theme-popup-shadow: 0 0 1.5rem rgba(0, 0, 0, .6);
  --theme-popup-checkicon: #60a5fa;
  --theme-popup-deactivated: #020617;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(30, 58, 138, .15);
  --theme-editbox-focus-border: #60a5fa;
  --theme-refinput-divider: rgba(148, 163, 184, .2);
  --theme-refinput-border: rgba(148, 163, 184, .3);

  // Tooltips
  --theme-tooltip-color: #f1f5f9;
  --theme-tooltip-bg: #1e293b;
  --theme-tooltip-key-bg: rgba(96, 165, 250, .15);

  // Calendar
  --theme-calendar-today-color: #60a5fa;
  --theme-calendar-holiday-color: #f87171;
  --theme-calendar-weekend-color: #94a3b8;
  --theme-calendar-today-bgcolor: rgba(96, 165, 250, .15);
  --theme-calendar-holiday-bgcolor: rgba(248, 113, 113, .15);
  --theme-calendar-weekend-bgcolor: rgba(148, 163, 184, .05);
  --theme-calendar-weekend-stroke-color: #1e293b;
  --theme-calendar-event-caption-color: #e2e8f0;

  // Mentions
  --theme-mention-bg-color: rgba(96, 165, 250, .15);
  --theme-mention-focused-bg-color: rgba(96, 165, 250, .25);
  --theme-mention-bg-color-notransparent: #1e3a5f;

  // States
  --theme-error-color: #f87171;
  --theme-warning-color: #fbbf24;
  --theme-caret-color: #60a5fa;
  --theme-checkbox-color: #020617;
  --theme-checkbox-bg-color: #f1f5f9;
  --theme-checkbox-border: rgba(148, 163, 184, .3);
  --theme-progress-color: #60a5fa;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(30, 58, 138, .08);
  --theme-kanban-card-border: rgba(148, 163, 184, .15);
  --theme-kanban-card-footer: rgba(148, 163, 184, .1);

  // Code blocks
  --theme-code-color: #93c5fd;
  --theme-code-bg-color: #0f172a;

  // Accent elements
  --accent-bg-color: #020617;
  --highlight-hover: #0f172a;
  --highlight-select: #1e293b;
  --highlight-select-border: rgba(96, 165, 250, .4);
  --highlight-select-hover: #1e3a5f;

  // Scrollbars
  --scrollbar-bar-color: #1e293b;
  --scrollbar-bar-hover: #475569;
  --scrollbar-track-color: #0f172a;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(148, 163, 184, .12);
  --secondary-button-disabled-color: rgba(148, 163, 184, .4);
  --positive-button-disabled: rgba(96, 165, 250, .15);
  --positive-button-disabled-color: rgba(96, 165, 250, .5);
  --negative-button-disabled: rgba(248, 113, 113, .15);
  --negative-button-disabled-color: rgba(248, 113, 113, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(148, 163, 184, 0);
  --theme-breadcrumb-hovered: rgba(30, 58, 138, .1);
  --theme-breadcrumb-pressed: rgba(30, 58, 138, .15);
  --theme-button-icon-default: rgba(148, 163, 184, 0);
  --theme-button-icon-hovered: rgba(148, 163, 184, .06);
  --theme-button-icon-pressed: rgba(148, 163, 184, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(148, 163, 184, .02);
  --theme-tablist-plain-color: #60a5fa;
  --theme-tablist-plain-divider: rgba(148, 163, 184, .07);
  --theme-toggle-sw-color: #f1f5f9;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(148, 163, 184, .32);
  --theme-toggle-bg-hover: rgba(148, 163, 184, .64);
  --theme-toggle-on-bg-color: #2563eb;
  --theme-toggle-on-bg-hover: #3b82f6;
  --theme-radio-bg-color: #0f172a;

  // Inbox
  --theme-inbox-notify: #60a5fa;
  --theme-inbox-people-notify: #3b82f6;
  --theme-inbox-activity-bgcolor: #020617;
  --theme-inbox-activitymsg-bgcolor: rgba(148, 163, 184, .03);
  --theme-inbox-activitymsg-divider: rgba(148, 163, 184, .1);
  --theme-inbox-activitymsg-border: rgba(148, 163, 184, .03);
  --theme-inbox-counter-bgcolor: rgba(148, 163, 184, .06);
  --theme-inbox-people-counter-bgcolor: rgba(37, 99, 235, .1);

  // Text editor
  --theme-drawing-bg-color: #0f172a;
  --text-editor-block-quote-color: #60a5fa;
  --text-edtior-hr-border-color: rgba(148, 163, 184, .1);
  --text-editor-table-border-color: rgba(148, 163, 184, .3);
  --theme-urgent-color: #fbbf24;
  --theme-lost-color: #f87171;
  --theme-won-color: #4ade80;
}

.theme-dark.theme-forest {
  // Backgrounds
  --theme-bg-color: #0b1210;
  --theme-back-color: #050807;
  --theme-navpanel-color: #071712;
  --theme-comp-header-color: #071712;
  --theme-panel-color: #071712;
  --theme-statusbar-color: #052e16;
  --theme-bg-accent-color: rgba(22, 163, 74, .15);
  --theme-bg-dark-color: rgba(22, 163, 74, .3);
  --theme-overlay-color: rgba(0, 0, 0, .85);

  // Dividers
  --theme-divider-color: rgba(34, 197, 94, 0.35);
  --theme-bg-divider-color: #052e16;

  // Text colors
  --theme-trans-color: rgba(134, 239, 172, .4);
  --theme-darker-color: rgba(187, 247, 208, .5);
  --theme-halfcontent-color: rgba(187, 247, 208, .7);
  --theme-dark-color: rgba(220, 252, 231, .8);
  --theme-content-color: rgba(240, 253, 244, .9);
  --theme-caption-color: #f0fdf4;
  --theme-link-color: #4ade80;
  --theme-text-primary-color: #f0fdf4;
  --theme-text-placeholder-color: rgba(134, 239, 172, .6);

  // Primary buttons
  --primary-button-default: #22c55e;
  --primary-button-hovered: #4ade80;
  --primary-button-pressed: #16a34a;
  --primary-button-focused: #22c55e;
  --primary-button-color: #ffffff;
  --primary-button-content-color: rgba(255, 255, 255, .9);
  --primary-button-border: rgba(74, 222, 128, .3);
  --primary-button-disabled: rgba(34, 197, 94, .3);
  --primary-button-disabled-color: rgba(74, 222, 128, .5);

  // Theme buttons
  --theme-button-default: rgba(6, 95, 70, 0.6);
  --theme-button-hovered: rgba(22, 163, 74, 0.6);
  --theme-button-pressed: rgba(6, 78, 59, 0.8);
  --theme-button-focused: rgba(22, 163, 74, 0.6);
  --theme-button-focused-border: rgba(74, 222, 128, .4);
  --theme-button-border: rgba(34, 197, 94, 0.6);
  --theme-button-disabled: rgba(6, 95, 70, 0.3);
  --theme-button-contrast-color: #f0fdf4;
  --theme-button-contrast-enabled: rgba(34, 197, 94, .9);
  --theme-button-contrast-hovered: #4ade80;
  --theme-button-contrast-pressed: #22c55e;
  --theme-button-container-color: #052e16;

  // Navigation
  --theme-navpanel-hovered: rgba(22, 163, 74, .15);
  --theme-navpanel-selected: rgba(22, 163, 74, .3);
  --theme-navpanel-divider: rgba(34, 197, 94, .2);
  --theme-navpanel-border: rgba(34, 197, 94, .15);
  --theme-navpanel-icons-color: #86efac;
  --theme-navpanel-icons-divider: rgba(34, 197, 94, .2);

  // Lists & Tables
  --theme-list-border-color: rgba(34, 197, 94, .15);
  --theme-list-header-color: #052e16;
  --theme-list-subheader-color: #071712;
  --theme-list-row-color: #052e16;
  --theme-list-divider-color: rgba(34, 197, 94, .2);
  --theme-list-subheader-divider: rgba(34, 197, 94, .15);
  --theme-list-button-color: #052e16;
  --theme-list-button-hover: #064e3b;
  --theme-link-button-color: #052e16;
  --theme-link-button-hover: #064e3b;
  --theme-table-border-color: rgba(34, 197, 94, .2);
  --theme-table-header-color: #052e16;
  --theme-table-row-color: #071712;

  // Popups
  --theme-popup-color: #052e16;
  --theme-popup-trans-color: #052e16ee;
  --theme-popup-hover: #064e3b;
  --theme-popup-divider: rgba(34, 197, 94, .2);
  --theme-popup-header: #071712;
  --theme-popup-shadow: 0 0 1.5rem rgba(0, 0, 0, .6);
  --theme-popup-checkicon: #4ade80;
  --theme-popup-deactivated: #071712;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(22, 163, 74, .15);
  --theme-editbox-focus-border: #4ade80;
  --theme-refinput-divider: rgba(34, 197, 94, .2);
  --theme-refinput-border: rgba(34, 197, 94, .3);

  // Tooltips
  --theme-tooltip-color: #f0fdf4;
  --theme-tooltip-bg: #064e3b;
  --theme-tooltip-key-bg: rgba(74, 222, 128, .15);

  // Calendar
  --theme-calendar-today-color: #4ade80;
  --theme-calendar-holiday-color: #f87171;
  --theme-calendar-weekend-color: #86efac;
  --theme-calendar-today-bgcolor: rgba(74, 222, 128, .15);
  --theme-calendar-holiday-bgcolor: rgba(248, 113, 113, .15);
  --theme-calendar-weekend-bgcolor: rgba(134, 239, 172, .05);
  --theme-calendar-weekend-stroke-color: #064e3b;
  --theme-calendar-event-caption-color: #dcfce7;

  // Mentions
  --theme-mention-bg-color: rgba(74, 222, 128, .15);
  --theme-mention-focused-bg-color: rgba(74, 222, 128, .25);
  --theme-mention-bg-color-notransparent: #14532d;

  // States
  --theme-error-color: #f87171;
  --theme-warning-color: #fbbf24;
  --theme-caret-color: #4ade80;
  --theme-checkbox-color: #071712;
  --theme-checkbox-bg-color: #f0fdf4;
  --theme-checkbox-border: rgba(34, 197, 94, .3);
  --theme-progress-color: #4ade80;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(22, 163, 74, .08);
  --theme-kanban-card-border: rgba(34, 197, 94, .15);
  --theme-kanban-card-footer: rgba(34, 197, 94, .1);

  // Code blocks
  --theme-code-color: #86efac;
  --theme-code-bg-color: #052e16;

  // Accent elements
  --accent-bg-color: #052e16;
  --highlight-hover: #064e3b;
  --highlight-select: #065f46;
  --highlight-select-border: rgba(74, 222, 128, .4);
  --highlight-select-hover: #14532d;

  // Scrollbars
  --scrollbar-bar-color: #064e3b;
  --scrollbar-bar-hover: #047857;
  --scrollbar-track-color: #052e16;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(134, 239, 172, .12);
  --secondary-button-disabled-color: rgba(134, 239, 172, .4);
  --positive-button-disabled: rgba(74, 222, 128, .15);
  --positive-button-disabled-color: rgba(74, 222, 128, .5);
  --negative-button-disabled: rgba(248, 113, 113, .15);
  --negative-button-disabled-color: rgba(248, 113, 113, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(134, 239, 172, 0);
  --theme-breadcrumb-hovered: rgba(22, 163, 74, .1);
  --theme-breadcrumb-pressed: rgba(22, 163, 74, .15);
  --theme-button-icon-default: rgba(134, 239, 172, 0);
  --theme-button-icon-hovered: rgba(134, 239, 172, .06);
  --theme-button-icon-pressed: rgba(134, 239, 172, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(134, 239, 172, .02);
  --theme-tablist-plain-color: #4ade80;
  --theme-tablist-plain-divider: rgba(34, 197, 94, .07);
  --theme-toggle-sw-color: #f0fdf4;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(134, 239, 172, .32);
  --theme-toggle-bg-hover: rgba(134, 239, 172, .64);
  --theme-toggle-on-bg-color: #22c55e;
  --theme-toggle-on-bg-hover: #4ade80;
  --theme-radio-bg-color: #052e16;

  // Inbox
  --theme-inbox-notify: #4ade80;
  --theme-inbox-people-notify: #22c55e;
  --theme-inbox-activity-bgcolor: #052e16;
  --theme-inbox-activitymsg-bgcolor: rgba(34, 197, 94, .03);
  --theme-inbox-activitymsg-divider: rgba(34, 197, 94, .1);
  --theme-inbox-activitymsg-border: rgba(34, 197, 94, .03);
  --theme-inbox-counter-bgcolor: rgba(34, 197, 94, .06);
  --theme-inbox-people-counter-bgcolor: rgba(34, 197, 94, .1);

  // Text editor
  --theme-drawing-bg-color: #052e16;
  --text-editor-block-quote-color: #4ade80;
  --text-edtior-hr-border-color: rgba(34, 197, 94, .1);
  --text-editor-table-border-color: rgba(34, 197, 94, .3);
  --theme-urgent-color: #fbbf24;
  --theme-lost-color: #f87171;
  --theme-won-color: #86efac;
}

.theme-light.theme-sand {
  // Backgrounds
  --theme-bg-color: #f5f1e8;
  --theme-back-color: #e7ddc8;
  --theme-navpanel-color: #fbf7ef;
  --theme-comp-header-color: #fbf7ef;
  --theme-panel-color: #ffffff;
  --theme-statusbar-color: #fffcf5;
  --theme-bg-accent-color: rgba(146, 64, 14, .08);
  --theme-bg-dark-color: rgba(146, 64, 14, .2);
  --theme-overlay-color: rgba(120, 70, 30, .25);

  // Dividers
  --theme-divider-color: #e2d6c5;
  --theme-bg-divider-color: #e9dece;

  // Text colors
  --theme-trans-color: rgba(120, 70, 30, .35);
  --theme-darker-color: rgba(92, 52, 20, .5);
  --theme-halfcontent-color: rgba(78, 42, 12, .6);
  --theme-dark-color: rgba(68, 32, 8, .7);
  --theme-content-color: rgba(52, 20, 4, .85);
  --theme-caption-color: #451a03;
  --theme-link-color: #b45309;
  --theme-text-primary-color: #451a03;
  --theme-text-placeholder-color: rgba(120, 70, 30, .5);

  // Primary buttons
  --primary-button-default: #d97706;
  --primary-button-hovered: #f59e0b;
  --primary-button-pressed: #b45309;
  --primary-button-focused: #d97706;
  --primary-button-color: #ffffff;
  --primary-button-content-color: rgba(255, 255, 255, .95);
  --primary-button-border: rgba(217, 119, 6, .25);
  --primary-button-disabled: rgba(217, 119, 6, .25);
  --primary-button-disabled-color: rgba(180, 83, 9, .5);

  // Theme buttons
  --theme-button-default: rgba(146, 64, 14, 0.08);
  --theme-button-hovered: rgba(146, 64, 14, 0.16);
  --theme-button-pressed: rgba(146, 64, 14, 0.24);
  --theme-button-focused: rgba(146, 64, 14, 0.16);
  --theme-button-focused-border: rgba(180, 83, 9, .3);
  --theme-button-border: rgba(146, 64, 14, 0.3);
  --theme-button-disabled: rgba(146, 64, 14, 0.05);
  --theme-button-contrast-color: #451a03;
  --theme-button-contrast-enabled: rgba(217, 119, 6, .15);
  --theme-button-contrast-hovered: #f59e0b;
  --theme-button-contrast-pressed: #d97706;
  --theme-button-container-color: #fef3c7;

  // Navigation
  --theme-navpanel-hovered: rgba(146, 64, 14, .08);
  --theme-navpanel-selected: rgba(146, 64, 14, .15);
  --theme-navpanel-divider: rgba(146, 64, 14, .15);
  --theme-navpanel-border: rgba(146, 64, 14, .12);
  --theme-navpanel-icons-color: #92400e;
  --theme-navpanel-icons-divider: rgba(146, 64, 14, .15);

  // Lists & Tables
  --theme-list-border-color: rgba(146, 64, 14, .12);
  --theme-list-header-color: #fef3c7;
  --theme-list-subheader-color: #fef9e7;
  --theme-list-row-color: #fffcf5;
  --theme-list-divider-color: rgba(146, 64, 14, .1);
  --theme-list-subheader-divider: rgba(146, 64, 14, .08);
  --theme-list-button-color: #fef9e7;
  --theme-list-button-hover: #fef3c7;
  --theme-link-button-color: #fef9e7;
  --theme-link-button-hover: #fef3c7;
  --theme-table-border-color: rgba(146, 64, 14, .15);
  --theme-table-header-color: #fef9e7;
  --theme-table-row-color: #fffcf5;

  // Popups
  --theme-popup-color: #ffffff;
  --theme-popup-trans-color: #ffffffee;
  --theme-popup-hover: #fef9e7;
  --theme-popup-divider: rgba(146, 64, 14, .12);
  --theme-popup-header: #fef3c7;
  --theme-popup-shadow: 0 0 1rem rgba(120, 70, 30, .25);
  --theme-popup-checkicon: #d97706;
  --theme-popup-deactivated: #e7ddc8;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(146, 64, 14, .08);
  --theme-editbox-focus-border: #f59e0b;
  --theme-refinput-divider: rgba(146, 64, 14, .12);
  --theme-refinput-border: rgba(146, 64, 14, .2);

  // Tooltips
  --theme-tooltip-color: #fffcf5;
  --theme-tooltip-bg: #78350f;
  --theme-tooltip-key-bg: rgba(255, 255, 255, .15);

  // Calendar
  --theme-calendar-today-color: #b45309;
  --theme-calendar-holiday-color: #dc2626;
  --theme-calendar-weekend-color: #92400e;
  --theme-calendar-today-bgcolor: rgba(217, 119, 6, .12);
  --theme-calendar-holiday-bgcolor: rgba(220, 38, 38, .12);
  --theme-calendar-weekend-bgcolor: rgba(146, 64, 14, .06);
  --theme-calendar-weekend-stroke-color: #f5f1e8;
  --theme-calendar-event-caption-color: #78350f;

  // Mentions
  --theme-mention-bg-color: rgba(217, 119, 6, .12);
  --theme-mention-focused-bg-color: rgba(217, 119, 6, .2);
  --theme-mention-bg-color-notransparent: #fed7aa;

  // States
  --theme-error-color: #dc2626;
  --theme-warning-color: #f59e0b;
  --theme-caret-color: #d97706;
  --theme-checkbox-color: #451a03;
  --theme-checkbox-bg-color: #ffffff;
  --theme-checkbox-border: rgba(146, 64, 14, .25);
  --theme-progress-color: #d97706;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(146, 64, 14, .04);
  --theme-kanban-card-border: rgba(146, 64, 14, .1);
  --theme-kanban-card-footer: rgba(146, 64, 14, .08);

  // Code blocks
  --theme-code-color: #92400e;
  --theme-code-bg-color: #fef3c7;

  // Accent elements
  --accent-bg-color: #f3e7d3;
  --highlight-hover: #fef3c7;
  --highlight-select: #fef9e7;
  --highlight-select-border: rgba(217, 119, 6, .3);
  --highlight-select-hover: #fed7aa;

  // Scrollbars
  --scrollbar-bar-color: #e9dece;
  --scrollbar-bar-hover: #d4b896;
  --scrollbar-track-color: #f5f1e8;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(146, 64, 14, .12);
  --secondary-button-disabled-color: rgba(146, 64, 14, .4);
  --positive-button-disabled: rgba(217, 119, 6, .15);
  --positive-button-disabled-color: rgba(217, 119, 6, .5);
  --negative-button-disabled: rgba(220, 38, 38, .15);
  --negative-button-disabled-color: rgba(220, 38, 38, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(146, 64, 14, 0);
  --theme-breadcrumb-hovered: rgba(146, 64, 14, .08);
  --theme-breadcrumb-pressed: rgba(146, 64, 14, .12);
  --theme-button-icon-default: rgba(146, 64, 14, 0);
  --theme-button-icon-hovered: rgba(146, 64, 14, .06);
  --theme-button-icon-pressed: rgba(146, 64, 14, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(146, 64, 14, .02);
  --theme-tablist-plain-color: #d97706;
  --theme-tablist-plain-divider: rgba(146, 64, 14, .07);
  --theme-toggle-sw-color: #451a03;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(146, 64, 14, .32);
  --theme-toggle-bg-hover: rgba(146, 64, 14, .64);
  --theme-toggle-on-bg-color: #d97706;
  --theme-toggle-on-bg-hover: #f59e0b;
  --theme-radio-bg-color: #fef3c7;

  // Inbox
  --theme-inbox-notify: #d97706;
  --theme-inbox-people-notify: #b45309;
  --theme-inbox-activity-bgcolor: #fef9e7;
  --theme-inbox-activitymsg-bgcolor: rgba(146, 64, 14, .03);
  --theme-inbox-activitymsg-divider: rgba(146, 64, 14, .1);
  --theme-inbox-activitymsg-border: rgba(146, 64, 14, .03);
  --theme-inbox-counter-bgcolor: rgba(146, 64, 14, .06);
  --theme-inbox-people-counter-bgcolor: rgba(146, 64, 14, .1);

  // Text editor
  --theme-drawing-bg-color: #fef3c7;
  --text-editor-block-quote-color: #d97706;
  --text-edtior-hr-border-color: rgba(146, 64, 14, .1);
  --text-editor-table-border-color: rgba(146, 64, 14, .2);
  --theme-urgent-color: #f59e0b;
  --theme-lost-color: #dc2626;
  --theme-won-color: #16a34a;
}

.theme-light.theme-sky {
  // Backgrounds
  --theme-bg-color: #ecf5ff;
  --theme-back-color: #dbeafe;
  --theme-navpanel-color: #f3f8ff;
  --theme-comp-header-color: #f3f8ff;
  --theme-panel-color: #ffffff;
  --theme-statusbar-color: #f8fbff;
  --theme-bg-accent-color: rgba(37, 99, 235, .08);
  --theme-bg-dark-color: rgba(37, 99, 235, .2);
  --theme-overlay-color: rgba(30, 64, 175, .25);

  // Dividers
  --theme-divider-color: #d1e2ff;
  --theme-bg-divider-color: #c7ddff;

  // Text colors
  --theme-trans-color: rgba(30, 64, 175, .35);
  --theme-darker-color: rgba(30, 58, 138, .5);
  --theme-halfcontent-color: rgba(29, 78, 216, .6);
  --theme-dark-color: rgba(30, 64, 175, .7);
  --theme-content-color: rgba(30, 58, 138, .85);
  --theme-caption-color: #1e3a8a;
  --theme-link-color: #2563eb;
  --theme-text-primary-color: #1e3a8a;
  --theme-text-placeholder-color: rgba(30, 64, 175, .5);

  // Primary buttons
  --primary-button-default: #2563eb;
  --primary-button-hovered: #3b82f6;
  --primary-button-pressed: #1d4ed8;
  --primary-button-focused: #2563eb;
  --primary-button-color: #ffffff;
  --primary-button-content-color: rgba(255, 255, 255, .95);
  --primary-button-border: rgba(37, 99, 235, .25);
  --primary-button-disabled: rgba(37, 99, 235, .25);
  --primary-button-disabled-color: rgba(37, 99, 235, .5);

  // Theme buttons
  --theme-button-default: rgba(37, 99, 235, 0.08);
  --theme-button-hovered: rgba(37, 99, 235, 0.16);
  --theme-button-pressed: rgba(37, 99, 235, 0.24);
  --theme-button-focused: rgba(37, 99, 235, 0.16);
  --theme-button-focused-border: rgba(37, 99, 235, .3);
  --theme-button-border: rgba(37, 99, 235, 0.3);
  --theme-button-disabled: rgba(37, 99, 235, 0.05);
  --theme-button-contrast-color: #1e3a8a;
  --theme-button-contrast-enabled: rgba(37, 99, 235, .15);
  --theme-button-contrast-hovered: #3b82f6;
  --theme-button-contrast-pressed: #2563eb;
  --theme-button-container-color: #dbeafe;

  // Navigation
  --theme-navpanel-hovered: rgba(37, 99, 235, .08);
  --theme-navpanel-selected: rgba(37, 99, 235, .15);
  --theme-navpanel-divider: rgba(37, 99, 235, .15);
  --theme-navpanel-border: rgba(37, 99, 235, .12);
  --theme-navpanel-icons-color: #1e40af;
  --theme-navpanel-icons-divider: rgba(37, 99, 235, .15);

  // Lists & Tables
  --theme-list-border-color: rgba(37, 99, 235, .12);
  --theme-list-header-color: #dbeafe;
  --theme-list-subheader-color: #eff6ff;
  --theme-list-row-color: #f8fbff;
  --theme-list-divider-color: rgba(37, 99, 235, .1);
  --theme-list-subheader-divider: rgba(37, 99, 235, .08);
  --theme-list-button-color: #eff6ff;
  --theme-list-button-hover: #dbeafe;
  --theme-link-button-color: #eff6ff;
  --theme-link-button-hover: #dbeafe;
  --theme-table-border-color: rgba(37, 99, 235, .15);
  --theme-table-header-color: #eff6ff;
  --theme-table-row-color: #f8fbff;

  // Popups
  --theme-popup-color: #ffffff;
  --theme-popup-trans-color: #ffffffee;
  --theme-popup-hover: #eff6ff;
  --theme-popup-divider: rgba(37, 99, 235, .12);
  --theme-popup-header: #dbeafe;
  --theme-popup-shadow: 0 0 1rem rgba(30, 64, 175, .25);
  --theme-popup-checkicon: #2563eb;
  --theme-popup-deactivated: #c7ddff;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(37, 99, 235, .08);
  --theme-editbox-focus-border: #3b82f6;
  --theme-refinput-divider: rgba(37, 99, 235, .12);
  --theme-refinput-border: rgba(37, 99, 235, .2);

  // Tooltips
  --theme-tooltip-color: #f8fbff;
  --theme-tooltip-bg: #1e3a8a;
  --theme-tooltip-key-bg: rgba(255, 255, 255, .15);

  // Calendar
  --theme-calendar-today-color: #2563eb;
  --theme-calendar-holiday-color: #dc2626;
  --theme-calendar-weekend-color: #1e40af;
  --theme-calendar-today-bgcolor: rgba(37, 99, 235, .12);
  --theme-calendar-holiday-bgcolor: rgba(220, 38, 38, .12);
  --theme-calendar-weekend-bgcolor: rgba(37, 99, 235, .06);
  --theme-calendar-weekend-stroke-color: #ecf5ff;
  --theme-calendar-event-caption-color: #1e3a8a;

  // Mentions
  --theme-mention-bg-color: rgba(37, 99, 235, .12);
  --theme-mention-focused-bg-color: rgba(37, 99, 235, .2);
  --theme-mention-bg-color-notransparent: #bfdbfe;

  // States
  --theme-error-color: #dc2626;
  --theme-warning-color: #f59e0b;
  --theme-caret-color: #2563eb;
  --theme-checkbox-color: #1e3a8a;
  --theme-checkbox-bg-color: #ffffff;
  --theme-checkbox-border: rgba(37, 99, 235, .25);
  --theme-progress-color: #2563eb;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(37, 99, 235, .04);
  --theme-kanban-card-border: rgba(37, 99, 235, .1);
  --theme-kanban-card-footer: rgba(37, 99, 235, .08);

  // Code blocks
  --theme-code-color: #1e40af;
  --theme-code-bg-color: #dbeafe;

  // Accent elements
  --accent-bg-color: #e0edff;
  --highlight-hover: #dbeafe;
  --highlight-select: #eff6ff;
  --highlight-select-border: rgba(37, 99, 235, .3);
  --highlight-select-hover: #bfdbfe;

  // Scrollbars
  --scrollbar-bar-color: #c7ddff;
  --scrollbar-bar-hover: #93c5fd;
  --scrollbar-track-color: #ecf5ff;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(37, 99, 235, .12);
  --secondary-button-disabled-color: rgba(37, 99, 235, .4);
  --positive-button-disabled: rgba(37, 99, 235, .15);
  --positive-button-disabled-color: rgba(37, 99, 235, .5);
  --negative-button-disabled: rgba(220, 38, 38, .15);
  --negative-button-disabled-color: rgba(220, 38, 38, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(37, 99, 235, 0);
  --theme-breadcrumb-hovered: rgba(37, 99, 235, .08);
  --theme-breadcrumb-pressed: rgba(37, 99, 235, .12);
  --theme-button-icon-default: rgba(37, 99, 235, 0);
  --theme-button-icon-hovered: rgba(37, 99, 235, .06);
  --theme-button-icon-pressed: rgba(37, 99, 235, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(37, 99, 235, .02);
  --theme-tablist-plain-color: #2563eb;
  --theme-tablist-plain-divider: rgba(37, 99, 235, .07);
  --theme-toggle-sw-color: #1e3a8a;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(37, 99, 235, .32);
  --theme-toggle-bg-hover: rgba(37, 99, 235, .64);
  --theme-toggle-on-bg-color: #2563eb;
  --theme-toggle-on-bg-hover: #3b82f6;
  --theme-radio-bg-color: #dbeafe;

  // Inbox
  --theme-inbox-notify: #2563eb;
  --theme-inbox-people-notify: #1e40af;
  --theme-inbox-activity-bgcolor: #eff6ff;
  --theme-inbox-activitymsg-bgcolor: rgba(37, 99, 235, .03);
  --theme-inbox-activitymsg-divider: rgba(37, 99, 235, .1);
  --theme-inbox-activitymsg-border: rgba(37, 99, 235, .03);
  --theme-inbox-counter-bgcolor: rgba(37, 99, 235, .06);
  --theme-inbox-people-counter-bgcolor: rgba(37, 99, 235, .1);

  // Text editor
  --theme-drawing-bg-color: #dbeafe;
  --text-editor-block-quote-color: #2563eb;
  --text-edtior-hr-border-color: rgba(37, 99, 235, .1);
  --text-editor-table-border-color: rgba(37, 99, 235, .2);
  --theme-urgent-color: #f59e0b;
  --theme-lost-color: #dc2626;
  --theme-won-color: #16a34a;
}

.theme-dark.theme-dusk {
  // Backgrounds
  --theme-bg-color: #050316;
  --theme-back-color: #030014;
  --theme-navpanel-color: #0b1020;
  --theme-comp-header-color: #111827;
  --theme-panel-color: #0b1020;
  --theme-statusbar-color: #0b1020;
  --theme-bg-accent-color: rgba(129, 140, 248, .18);
  --theme-bg-dark-color: rgba(129, 140, 248, .32);
  --theme-overlay-color: rgba(15, 23, 42, .88);

  // Dividers
  --theme-divider-color: rgba(148, 163, 184, .4);
  --theme-bg-divider-color: #111827;

  // Text colors
  --theme-trans-color: rgba(148, 163, 184, .45);
  --theme-darker-color: rgba(203, 213, 225, .6);
  --theme-halfcontent-color: rgba(203, 213, 225, .75);
  --theme-dark-color: rgba(226, 232, 240, .88);
  --theme-content-color: rgba(241, 245, 249, .96);
  --theme-caption-color: #f9fafb;
  --theme-link-color: #a855f7;
  --theme-text-primary-color: #f9fafb;
  --theme-text-placeholder-color: rgba(148, 163, 184, .7);

  // Primary buttons
  --primary-button-default: #a855f7;
  --primary-button-hovered: #c084fc;
  --primary-button-pressed: #7c3aed;
  --primary-button-focused: #a855f7;
  --primary-button-color: #0b1020;
  --primary-button-content-color: rgba(15, 23, 42, .9);
  --primary-button-border: rgba(167, 139, 250, .4);
  --primary-button-disabled: rgba(129, 140, 248, .3);
  --primary-button-disabled-color: rgba(167, 139, 250, .6);

  // Theme buttons
  --theme-button-default: rgba(15, 23, 42, .7);
  --theme-button-hovered: rgba(76, 29, 149, .75);
  --theme-button-pressed: rgba(30, 64, 175, .9);
  --theme-button-focused: rgba(76, 29, 149, .82);
  --theme-button-focused-border: rgba(167, 139, 250, .55);
  --theme-button-border: rgba(148, 163, 184, .5);
  --theme-button-disabled: rgba(15, 23, 42, .4);
  --theme-button-contrast-color: #f9fafb;
  --theme-button-contrast-enabled: rgba(167, 139, 250, .9);
  --theme-button-contrast-hovered: #c084fc;
  --theme-button-contrast-pressed: #a855f7;
  --theme-button-container-color: #020617;

  // Navigation
  --theme-navpanel-hovered: rgba(129, 140, 248, .18);
  --theme-navpanel-selected: rgba(129, 140, 248, .28);
  --theme-navpanel-divider: rgba(148, 163, 184, .25);
  --theme-navpanel-border: rgba(148, 163, 184, .18);
  --theme-navpanel-icons-color: #e5e7eb;
  --theme-navpanel-icons-divider: rgba(148, 163, 184, .32);

  // Lists & Tables
  --theme-list-border-color: rgba(148, 163, 184, .18);
  --theme-list-header-color: #1f2937;
  --theme-list-subheader-color: #020617;
  --theme-list-row-color: #020617;
  --theme-list-divider-color: rgba(148, 163, 184, .22);
  --theme-list-subheader-divider: rgba(148, 163, 184, .17);
  --theme-list-button-color: #020617;
  --theme-list-button-hover: #111827;
  --theme-link-button-color: #020617;
  --theme-link-button-hover: #111827;
  --theme-table-border-color: rgba(148, 163, 184, .25);
  --theme-table-header-color: #020617;
  --theme-table-row-color: #020617;

  // Popups
  --theme-popup-color: #020617;
  --theme-popup-trans-color: #020617ee;
  --theme-popup-hover: #0b1020;
  --theme-popup-divider: rgba(148, 163, 184, .22);
  --theme-popup-header: #111827;
  --theme-popup-shadow: 0 0 1.5rem rgba(15, 23, 42, .8);
  --theme-popup-checkicon: #a855f7;
  --theme-popup-deactivated: #020617;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(129, 140, 248, .18);
  --theme-editbox-focus-border: #a855f7;
  --theme-refinput-divider: rgba(148, 163, 184, .24);
  --theme-refinput-border: rgba(148, 163, 184, .32);

  // Tooltips
  --theme-tooltip-color: #f9fafb;
  --theme-tooltip-bg: #111827;
  --theme-tooltip-key-bg: rgba(167, 139, 250, .22);

  // Calendar
  --theme-calendar-today-color: #a855f7;
  --theme-calendar-holiday-color: #fb7185;
  --theme-calendar-weekend-color: #e5e7eb;
  --theme-calendar-today-bgcolor: rgba(129, 140, 248, .22);
  --theme-calendar-holiday-bgcolor: rgba(248, 113, 113, .2);
  --theme-calendar-weekend-bgcolor: rgba(30, 64, 175, .18);
  --theme-calendar-weekend-stroke-color: #111827;
  --theme-calendar-event-caption-color: #e5e7eb;

  // Mentions
  --theme-mention-bg-color: rgba(129, 140, 248, .2);
  --theme-mention-focused-bg-color: rgba(129, 140, 248, .3);
  --theme-mention-bg-color-notransparent: #312e81;

  // States
  --theme-error-color: #fb7185;
  --theme-warning-color: #fbbf24;
  --theme-caret-color: #a855f7;
  --theme-checkbox-color: #020617;
  --theme-checkbox-bg-color: #f9fafb;
  --theme-checkbox-border: rgba(148, 163, 184, .35);
  --theme-progress-color: #a855f7;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(129, 140, 248, .08);
  --theme-kanban-card-border: rgba(148, 163, 184, .2);
  --theme-kanban-card-footer: rgba(148, 163, 184, .16);

  // Code blocks
  --theme-code-color: #e5e7eb;
  --theme-code-bg-color: #020617;

  // Accent elements
  --accent-bg-color: #020617;
  --highlight-hover: #0b1020;
  --highlight-select: #111827;
  --highlight-select-border: rgba(129, 140, 248, .5);
  --highlight-select-hover: #1f2937;

  // Scrollbars
  --scrollbar-bar-color: #1f2937;
  --scrollbar-bar-hover: #4b5563;
  --scrollbar-track-color: #020617;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(167, 139, 250, .12);
  --secondary-button-disabled-color: rgba(167, 139, 250, .4);
  --positive-button-disabled: rgba(129, 140, 248, .15);
  --positive-button-disabled-color: rgba(129, 140, 248, .5);
  --negative-button-disabled: rgba(248, 113, 113, .15);
  --negative-button-disabled-color: rgba(248, 113, 113, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(167, 139, 250, 0);
  --theme-breadcrumb-hovered: rgba(129, 140, 248, .1);
  --theme-breadcrumb-pressed: rgba(129, 140, 248, .15);
  --theme-button-icon-default: rgba(167, 139, 250, 0);
  --theme-button-icon-hovered: rgba(167, 139, 250, .06);
  --theme-button-icon-pressed: rgba(167, 139, 250, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(167, 139, 250, .02);
  --theme-tablist-plain-color: #a855f7;
  --theme-tablist-plain-divider: rgba(148, 163, 184, .07);
  --theme-toggle-sw-color: #f9fafb;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(148, 163, 184, .32);
  --theme-toggle-bg-hover: rgba(148, 163, 184, .64);
  --theme-toggle-on-bg-color: #a855f7;
  --theme-toggle-on-bg-hover: #c084fc;
  --theme-radio-bg-color: #020617;

  // Inbox
  --theme-inbox-notify: #a855f7;
  --theme-inbox-people-notify: #c084fc;
  --theme-inbox-activity-bgcolor: #020617;
  --theme-inbox-activitymsg-bgcolor: rgba(148, 163, 184, .03);
  --theme-inbox-activitymsg-divider: rgba(148, 163, 184, .1);
  --theme-inbox-activitymsg-border: rgba(148, 163, 184, .03);
  --theme-inbox-counter-bgcolor: rgba(148, 163, 184, .06);
  --theme-inbox-people-counter-bgcolor: rgba(129, 140, 248, .1);

  // Text editor
  --theme-drawing-bg-color: #020617;
  --text-editor-block-quote-color: #a855f7;
  --text-edtior-hr-border-color: rgba(148, 163, 184, .1);
  --text-editor-table-border-color: rgba(148, 163, 184, .3);
  --theme-urgent-color: #fbbf24;
  --theme-lost-color: #fb7185;
  --theme-won-color: #4ade80;
}

.theme-dark.theme-neon {
  // Backgrounds
  --theme-bg-color: #020617;
  --theme-back-color: #020617;
  --theme-navpanel-color: #020617;
  --theme-comp-header-color: #020617;
  --theme-panel-color: #020617;
  --theme-statusbar-color: #020617;
  --theme-bg-accent-color: rgba(34, 211, 238, .16);
  --theme-bg-dark-color: rgba(244, 63, 94, .25);
  --theme-overlay-color: rgba(0, 0, 0, .9);

  // Dividers
  --theme-divider-color: rgba(148, 163, 184, .45);
  --theme-bg-divider-color: #020617;

  // Text colors
  --theme-trans-color: rgba(148, 163, 184, .5);
  --theme-darker-color: rgba(203, 213, 225, .65);
  --theme-halfcontent-color: rgba(203, 213, 225, .8);
  --theme-dark-color: rgba(226, 232, 240, .9);
  --theme-content-color: rgba(241, 245, 249, .98);
  --theme-caption-color: #f9fafb;
  --theme-link-color: #22d3ee;
  --theme-text-primary-color: #f9fafb;
  --theme-text-placeholder-color: rgba(148, 163, 184, .7);

  // Primary buttons
  --primary-button-default: #22d3ee;
  --primary-button-hovered: #38bdf8;
  --primary-button-pressed: #0ea5e9;
  --primary-button-focused: #22d3ee;
  --primary-button-color: #020617;
  --primary-button-content-color: rgba(15, 23, 42, .9);
  --primary-button-border: rgba(34, 211, 238, .4);
  --primary-button-disabled: rgba(34, 211, 238, .28);
  --primary-button-disabled-color: rgba(56, 189, 248, .6);

  // Theme buttons
  --theme-button-default: rgba(15, 23, 42, .7);
  --theme-button-hovered: rgba(248, 250, 252, .08);
  --theme-button-pressed: rgba(248, 250, 252, .14);
  --theme-button-focused: rgba(248, 250, 252, .1);
  --theme-button-focused-border: rgba(34, 211, 238, .5);
  --theme-button-border: rgba(148, 163, 184, .45);
  --theme-button-disabled: rgba(15, 23, 42, .4);
  --theme-button-contrast-color: #f9fafb;
  --theme-button-contrast-enabled: rgba(34, 211, 238, .9);
  --theme-button-contrast-hovered: #38bdf8;
  --theme-button-contrast-pressed: #22d3ee;
  --theme-button-container-color: #020617;

  // Navigation
  --theme-navpanel-hovered: rgba(34, 211, 238, .14);
  --theme-navpanel-selected: rgba(34, 211, 238, .22);
  --theme-navpanel-divider: rgba(148, 163, 184, .32);
  --theme-navpanel-border: rgba(148, 163, 184, .2);
  --theme-navpanel-icons-color: #f9fafb;
  --theme-navpanel-icons-divider: rgba(148, 163, 184, .35);

  // Lists & Tables
  --theme-list-border-color: rgba(148, 163, 184, .24);
  --theme-list-header-color: #020617;
  --theme-list-subheader-color: #020617;
  --theme-list-row-color: #020617;
  --theme-list-divider-color: rgba(148, 163, 184, .28);
  --theme-list-subheader-divider: rgba(148, 163, 184, .2);
  --theme-list-button-color: #020617;
  --theme-list-button-hover: #020617;
  --theme-link-button-color: #020617;
  --theme-link-button-hover: #020617;
  --theme-table-border-color: rgba(148, 163, 184, .28);
  --theme-table-header-color: #020617;
  --theme-table-row-color: #020617;

  // Popups
  --theme-popup-color: #020617;
  --theme-popup-trans-color: #020617ee;
  --theme-popup-hover: #020617;
  --theme-popup-divider: rgba(148, 163, 184, .32);
  --theme-popup-header: #020617;
  --theme-popup-shadow: 0 0 1.5rem rgba(0, 0, 0, .9);
  --theme-popup-checkicon: #22d3ee;
  --theme-popup-deactivated: #020617;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(34, 211, 238, .18);
  --theme-editbox-focus-border: #22d3ee;
  --theme-refinput-divider: rgba(148, 163, 184, .32);
  --theme-refinput-border: rgba(148, 163, 184, .4);

  // Tooltips
  --theme-tooltip-color: #f9fafb;
  --theme-tooltip-bg: #020617;
  --theme-tooltip-key-bg: rgba(34, 211, 238, .22);

  // Calendar
  --theme-calendar-today-color: #22d3ee;
  --theme-calendar-holiday-color: #fb7185;
  --theme-calendar-weekend-color: #e5e7eb;
  --theme-calendar-today-bgcolor: rgba(34, 211, 238, .22);
  --theme-calendar-holiday-bgcolor: rgba(248, 113, 113, .2);
  --theme-calendar-weekend-bgcolor: rgba(15, 23, 42, .35);
  --theme-calendar-weekend-stroke-color: #020617;
  --theme-calendar-event-caption-color: #e5e7eb;

  // Mentions
  --theme-mention-bg-color: rgba(34, 211, 238, .22);
  --theme-mention-focused-bg-color: rgba(34, 211, 238, .3);
  --theme-mention-bg-color-notransparent: #164e63;

  // States
  --theme-error-color: #fb7185;
  --theme-warning-color: #fbbf24;
  --theme-caret-color: #22d3ee;
  --theme-checkbox-color: #020617;
  --theme-checkbox-bg-color: #f9fafb;
  --theme-checkbox-border: rgba(148, 163, 184, .4);
  --theme-progress-color: #22d3ee;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(34, 211, 238, .1);
  --theme-kanban-card-border: rgba(148, 163, 184, .35);
  --theme-kanban-card-footer: rgba(148, 163, 184, .2);

  // Code blocks
  --theme-code-color: #22d3ee;
  --theme-code-bg-color: #020617;

  // Accent elements
  --accent-bg-color: #020617;
  --highlight-hover: #020617;
  --highlight-select: #020617;
  --highlight-select-border: rgba(34, 211, 238, .5);
  --highlight-select-hover: #020617;

  // Scrollbars
  --scrollbar-bar-color: #0f172a;
  --scrollbar-bar-hover: #1e293b;
  --scrollbar-track-color: #020617;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(34, 211, 238, .12);
  --secondary-button-disabled-color: rgba(34, 211, 238, .4);
  --positive-button-disabled: rgba(34, 211, 238, .15);
  --positive-button-disabled-color: rgba(34, 211, 238, .5);
  --negative-button-disabled: rgba(248, 113, 113, .15);
  --negative-button-disabled-color: rgba(248, 113, 113, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(34, 211, 238, 0);
  --theme-breadcrumb-hovered: rgba(34, 211, 238, .1);
  --theme-breadcrumb-pressed: rgba(34, 211, 238, .15);
  --theme-button-icon-default: rgba(34, 211, 238, 0);
  --theme-button-icon-hovered: rgba(34, 211, 238, .06);
  --theme-button-icon-pressed: rgba(34, 211, 238, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(34, 211, 238, .02);
  --theme-tablist-plain-color: #22d3ee;
  --theme-tablist-plain-divider: rgba(148, 163, 184, .07);
  --theme-toggle-sw-color: #f9fafb;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(148, 163, 184, .32);
  --theme-toggle-bg-hover: rgba(148, 163, 184, .64);
  --theme-toggle-on-bg-color: #22d3ee;
  --theme-toggle-on-bg-hover: #38bdf8;
  --theme-radio-bg-color: #020617;

  // Inbox
  --theme-inbox-notify: #22d3ee;
  --theme-inbox-people-notify: #38bdf8;
  --theme-inbox-activity-bgcolor: #020617;
  --theme-inbox-activitymsg-bgcolor: rgba(148, 163, 184, .03);
  --theme-inbox-activitymsg-divider: rgba(148, 163, 184, .1);
  --theme-inbox-activitymsg-border: rgba(148, 163, 184, .03);
  --theme-inbox-counter-bgcolor: rgba(148, 163, 184, .06);
  --theme-inbox-people-counter-bgcolor: rgba(34, 211, 238, .1);

  // Text editor
  --theme-drawing-bg-color: #020617;
  --text-editor-block-quote-color: #22d3ee;
  --text-edtior-hr-border-color: rgba(148, 163, 184, .1);
  --text-editor-table-border-color: rgba(148, 163, 184, .3);
  --theme-urgent-color: #fbbf24;
  --theme-lost-color: #fb7185;
  --theme-won-color: #4ade80;
}

.theme-light.theme-blossom {
  // Backgrounds
  --theme-bg-color: #fdf2f8;
  --theme-back-color: #f9e0ec;
  --theme-navpanel-color: #fef4f9;
  --theme-comp-header-color: #fdf2f8;
  --theme-panel-color: #ffffff;
  --theme-statusbar-color: #fff7fb;
  --theme-bg-accent-color: rgba(236, 72, 153, .08);
  --theme-bg-dark-color: rgba(219, 39, 119, .2);
  --theme-overlay-color: rgba(190, 24, 93, .18);

  // Dividers
  --theme-divider-color: #f9c9dd;
  --theme-bg-divider-color: #fbd5e7;

  // Text colors
  --theme-trans-color: rgba(131, 24, 67, .35);
  --theme-darker-color: rgba(131, 24, 67, .58);
  --theme-halfcontent-color: rgba(159, 18, 57, .72);
  --theme-dark-color: rgba(159, 18, 57, .82);
  --theme-content-color: rgba(131, 24, 67, .9);
  --theme-caption-color: #9f1239;
  --theme-link-color: #db2777;
  --theme-text-primary-color: #9f1239;
  --theme-text-placeholder-color: rgba(190, 24, 93, .6);

  // Primary buttons
  --primary-button-default: #ec4899;
  --primary-button-hovered: #f472b6;
  --primary-button-pressed: #db2777;
  --primary-button-focused: #ec4899;
  --primary-button-color: #ffffff;
  --primary-button-content-color: rgba(255, 255, 255, .98);
  --primary-button-border: rgba(236, 72, 153, .32);
  --primary-button-disabled: rgba(236, 72, 153, .25);
  --primary-button-disabled-color: rgba(219, 39, 119, .55);

  // Theme buttons
  --theme-button-default: rgba(236, 72, 153, .08);
  --theme-button-hovered: rgba(236, 72, 153, .16);
  --theme-button-pressed: rgba(236, 72, 153, .24);
  --theme-button-focused: rgba(236, 72, 153, .16);
  --theme-button-focused-border: rgba(219, 39, 119, .32);
  --theme-button-border: rgba(236, 72, 153, .3);
  --theme-button-disabled: rgba(236, 72, 153, .06);
  --theme-button-contrast-color: #9f1239;
  --theme-button-contrast-enabled: rgba(236, 72, 153, .9);
  --theme-button-contrast-hovered: #f472b6;
  --theme-button-contrast-pressed: #ec4899;
  --theme-button-container-color: #ffe4f1;

  // Navigation
  --theme-navpanel-hovered: rgba(236, 72, 153, .1);
  --theme-navpanel-selected: rgba(236, 72, 153, .18);
  --theme-navpanel-divider: rgba(236, 72, 153, .16);
  --theme-navpanel-border: rgba(236, 72, 153, .12);
  --theme-navpanel-icons-color: #9f1239;
  --theme-navpanel-icons-divider: rgba(236, 72, 153, .16);

  // Lists & Tables
  --theme-list-border-color: rgba(236, 72, 153, .16);
  --theme-list-header-color: #ffe4f1;
  --theme-list-subheader-color: #fff0f7;
  --theme-list-row-color: #fff7fb;
  --theme-list-divider-color: rgba(236, 72, 153, .1);
  --theme-list-subheader-divider: rgba(236, 72, 153, .1);
  --theme-list-button-color: #fff0f7;
  --theme-list-button-hover: #ffe4f1;
  --theme-link-button-color: #fff0f7;
  --theme-link-button-hover: #ffe4f1;
  --theme-table-border-color: rgba(236, 72, 153, .16);
  --theme-table-header-color: #fff0f7;
  --theme-table-row-color: #fff7fb;

  // Popups
  --theme-popup-color: #ffffff;
  --theme-popup-trans-color: #ffffffee;
  --theme-popup-hover: #fff0f7;
  --theme-popup-divider: rgba(236, 72, 153, .14);
  --theme-popup-header: #ffe4f1;
  --theme-popup-shadow: 0 0 1rem rgba(190, 24, 93, .25);
  --theme-popup-checkicon: #ec4899;
  --theme-popup-deactivated: #f9e0ec;

  // Editbox & inputs
  --theme-editbox-focus-color: rgba(236, 72, 153, .12);
  --theme-editbox-focus-border: #ec4899;
  --theme-refinput-divider: rgba(236, 72, 153, .16);
  --theme-refinput-border: rgba(236, 72, 153, .22);

  // Tooltips
  --theme-tooltip-color: #fdf2f8;
  --theme-tooltip-bg: #9f1239;
  --theme-tooltip-key-bg: rgba(255, 255, 255, .18);

  // Calendar
  --theme-calendar-today-color: #db2777;
  --theme-calendar-holiday-color: #dc2626;
  --theme-calendar-weekend-color: #9f1239;
  --theme-calendar-today-bgcolor: rgba(236, 72, 153, .14);
  --theme-calendar-holiday-bgcolor: rgba(220, 38, 38, .12);
  --theme-calendar-weekend-bgcolor: rgba(236, 72, 153, .08);
  --theme-calendar-weekend-stroke-color: #fdf2f8;
  --theme-calendar-event-caption-color: #9f1239;

  // Mentions
  --theme-mention-bg-color: rgba(236, 72, 153, .12);
  --theme-mention-focused-bg-color: rgba(236, 72, 153, .2);
  --theme-mention-bg-color-notransparent: #fed7e2;

  // States
  --theme-error-color: #dc2626;
  --theme-warning-color: #f59e0b;
  --theme-caret-color: #ec4899;
  --theme-checkbox-color: #9f1239;
  --theme-checkbox-bg-color: #ffffff;
  --theme-checkbox-border: rgba(190, 24, 93, .25);
  --theme-progress-color: #ec4899;

  // Kanban cards
  --theme-kanban-card-bg-color: rgba(236, 72, 153, .04);
  --theme-kanban-card-border: rgba(236, 72, 153, .14);
  --theme-kanban-card-footer: rgba(236, 72, 153, .12);

  // Code blocks
  --theme-code-color: #9f1239;
  --theme-code-bg-color: #ffe4f1;

  // Accent elements
  --accent-bg-color: #fbe7f2;
  --highlight-hover: #ffe4f1;
  --highlight-select: #fff0f7;
  --highlight-select-border: rgba(236, 72, 153, .3);
  --highlight-select-hover: #fed7e2;

  // Scrollbars
  --scrollbar-bar-color: #fbd5e7;
  --scrollbar-bar-hover: #f9a8d4;
  --scrollbar-track-color: #fdf2f8;

  // Secondary/Positive/Negative buttons
  --secondary-button-disabled: rgba(236, 72, 153, .12);
  --secondary-button-disabled-color: rgba(236, 72, 153, .4);
  --positive-button-disabled: rgba(236, 72, 153, .15);
  --positive-button-disabled-color: rgba(236, 72, 153, .5);
  --negative-button-disabled: rgba(220, 38, 38, .15);
  --negative-button-disabled-color: rgba(220, 38, 38, .5);

  // Breadcrumbs & button icons
  --theme-breadcrumb-default: rgba(236, 72, 153, 0);
  --theme-breadcrumb-hovered: rgba(236, 72, 153, .08);
  --theme-breadcrumb-pressed: rgba(236, 72, 153, .12);
  --theme-button-icon-default: rgba(236, 72, 153, 0);
  --theme-button-icon-hovered: rgba(236, 72, 153, .06);
  --theme-button-icon-pressed: rgba(236, 72, 153, .1);

  // Tablist & toggles
  --theme-tablist-color: rgba(236, 72, 153, .02);
  --theme-tablist-plain-color: #ec4899;
  --theme-tablist-plain-divider: rgba(236, 72, 153, .07);
  --theme-toggle-sw-color: #9f1239;
  --theme-toggle-on-sw-color: #fff;
  --theme-toggle-bg-color: rgba(236, 72, 153, .32);
  --theme-toggle-bg-hover: rgba(236, 72, 153, .64);
  --theme-toggle-on-bg-color: #ec4899;
  --theme-toggle-on-bg-hover: #f472b6;
  --theme-radio-bg-color: #ffe4f1;

  // Inbox
  --theme-inbox-notify: #ec4899;
  --theme-inbox-people-notify: #db2777;
  --theme-inbox-activity-bgcolor: #fff0f7;
  --theme-inbox-activitymsg-bgcolor: rgba(236, 72, 153, .03);
  --theme-inbox-activitymsg-divider: rgba(236, 72, 153, .1);
  --theme-inbox-activitymsg-border: rgba(236, 72, 153, .03);
  --theme-inbox-counter-bgcolor: rgba(236, 72, 153, .06);
  --theme-inbox-people-counter-bgcolor: rgba(236, 72, 153, .1);

  // Text editor
  --theme-drawing-bg-color: #ffe4f1;
  --text-editor-block-quote-color: #db2777;
  --text-edtior-hr-border-color: rgba(236, 72, 153, .1);
  --text-editor-table-border-color: rgba(236, 72, 153, .2);
  --theme-urgent-color: #f59e0b;
  --theme-lost-color: #dc2626;
  --theme-won-color: #16a34a;
}

.theme-light.theme-lavender {
  --theme-bg-color: #f5f3ff;
  --theme-back-color: #e0e7ff;
  --theme-navpanel-color: #f5f3ff;
  --theme-comp-header-color: #ede9fe;
  --theme-panel-color: #ffffff;
  --theme-divider-color: #e0e7ff;
  --theme-bg-divider-color: #e5e7eb;
  --accent-bg-color: #e0e7ff;

  --theme-content-color: #312e81;
  --theme-caption-color: #312e81;
  --theme-dark-color: #3730a3;
  --theme-link-color: #6366f1;

  --primary-button-default: #8b5cf6;
  --primary-button-hovered: #a855f7;
  --primary-button-pressed: #7c3aed;
  --primary-button-focused: #8b5cf6;

  --theme-button-default: rgba(129, 140, 248, .08);
  --theme-button-hovered: rgba(129, 140, 248, .16);
  --theme-button-pressed: rgba(129, 140, 248, .24);
  --theme-button-border: rgba(129, 140, 248, .3);
  --theme-button-focused-border: rgba(129, 140, 248, .32);
  --theme-navpanel-hovered: rgba(129, 140, 248, .1);
  --theme-navpanel-selected: rgba(129, 140, 248, .18);
  --theme-list-border-color: rgba(129, 140, 248, .16);
  --theme-list-header-color: #e0e7ff;
  --theme-popup-color: #ffffff;
  --theme-popup-hover: #eef2ff;
  --theme-editbox-focus-border: #8b5cf6;
  --theme-tooltip-bg: #312e81;
  --theme-calendar-today-color: #6366f1;
  --theme-checkbox-bg-color: #ffffff;
  --theme-caret-color: #6366f1;
  --theme-code-bg-color: #e0e7ff;
  --scrollbar-bar-color: #d4d4ff;
  --theme-inbox-notify: #8b5cf6;
  --text-editor-block-quote-color: #6366f1;
  --theme-statusbar-color: #f9f5ff;
  --theme-bg-accent-color: rgba(129, 140, 248, .08);
  --theme-text-primary-color: #312e81;
  --theme-text-placeholder-color: rgba(55, 48, 163, .6);
  --primary-button-color: #ffffff;
  --primary-button-border: rgba(129, 140, 248, .32);
}

.theme-light.theme-snow {
  --theme-bg-color: #f9fafb;
  --theme-back-color: #e5e7eb;
  --theme-navpanel-color: #f3f4f6;
  --theme-comp-header-color: #f9fafb;
  --theme-panel-color: #ffffff;
  --theme-divider-color: #e5e7eb;
  --theme-bg-divider-color: #e5e7eb;
  --accent-bg-color: #e5e7eb;

  --theme-content-color: #111827;
  --theme-caption-color: #111827;
  --theme-dark-color: #374151;
  --theme-link-color: #0ea5e9;

  --primary-button-default: #0ea5e9;
  --primary-button-hovered: #38bdf8;
  --primary-button-pressed: #0284c7;
  --primary-button-focused: #0ea5e9;

  --theme-button-default: rgba(148, 163, 184, .1);
  --theme-button-hovered: rgba(148, 163, 184, .18);
  --theme-button-pressed: rgba(148, 163, 184, .26);
  --theme-button-border: rgba(148, 163, 184, .3);
  --theme-button-focused-border: rgba(148, 163, 184, .32);
  --theme-navpanel-hovered: rgba(148, 163, 184, .14);
  --theme-navpanel-selected: rgba(148, 163, 184, .22);
  --theme-list-border-color: rgba(148, 163, 184, .18);
  --theme-list-header-color: #e5e7eb;
  --theme-popup-color: #ffffff;
  --theme-popup-hover: #f3f4f6;
  --theme-editbox-focus-border: #0ea5e9;
  --theme-tooltip-bg: #111827;
  --theme-calendar-today-color: #0ea5e9;
  --theme-checkbox-bg-color: #ffffff;
  --theme-caret-color: #0ea5e9;
  --theme-code-bg-color: #e5e7eb;
  --scrollbar-bar-color: #d1d5db;
  --theme-inbox-notify: #0ea5e9;
  --text-editor-block-quote-color: #0ea5e9;
  --theme-statusbar-color: #f9fafb;
  --theme-bg-accent-color: rgba(148, 163, 184, .08);
  --theme-text-primary-color: #111827;
  --theme-text-placeholder-color: rgba(107, 114, 128, .75);
  --primary-button-color: #ffffff;
  --primary-button-border: rgba(14, 165, 233, .32);
  --primary-button-outline: #1d4ed8;
  --theme-editbox-focus-color: rgba(37, 99, 235, .12);
  --theme-checkbox-color: #111827;
  --theme-checkbox-border: rgba(148, 163, 184, .45);
  --theme-checkbox-disabled: #9ca3af;
}

.theme-light.theme-neo-brutalism {
  --theme-bg-color: #fef9c3;
  --theme-back-color: #fef3c7;
  --theme-navpanel-color: #fef3c7;
  --theme-comp-header-color: #fef9c3;
  --theme-panel-color: #ffffff;
  --theme-divider-color: #facc15;
  --theme-bg-divider-color: #fde68a;
  --accent-bg-color: #fef08a;

  --theme-content-color: #111827;
  --theme-caption-color: #111827;
  --theme-dark-color: #1f2937;
  --theme-link-color: #e11d48;

  --primary-button-default: #f97316;
  --primary-button-hovered: #fb923c;
  --primary-button-pressed: #ea580c;
  --primary-button-focused: #f97316;

  --theme-button-default: rgba(249, 115, 22, .06);
  --theme-button-hovered: rgba(249, 115, 22, .14);
  --theme-button-pressed: rgba(249, 115, 22, .22);
  --theme-button-border: rgba(234, 88, 12, .3);
  --theme-button-focused-border: rgba(234, 88, 12, .4);
  --theme-navpanel-hovered: rgba(245, 158, 11, .12);
  --theme-navpanel-selected: rgba(245, 158, 11, .18);
  --theme-list-border-color: rgba(148, 163, 184, .25);
  --theme-list-header-color: #fef3c7;
  --theme-popup-color: #ffffff;
  --theme-popup-hover: #fffbeb;
  --theme-editbox-focus-border: #f97316;
  --theme-tooltip-bg: #111827;
  --theme-calendar-today-color: #f97316;
  --theme-checkbox-bg-color: #ffffff;
  --theme-caret-color: #e11d48;
  --theme-code-bg-color: #fef3c7;
  --scrollbar-bar-color: #fbbf24;
  --theme-inbox-notify: #f97316;
  --text-editor-block-quote-color: #e11d48;
  --theme-statusbar-color: #fef9c3;
  --theme-bg-accent-color: rgba(250, 204, 21, .2);
  --theme-text-primary-color: #111827;
  --theme-text-placeholder-color: rgba(120, 53, 15, .7);
  --primary-button-color: #ffffff;
  --primary-button-border: rgba(249, 115, 22, .32);
  --primary-button-outline: #e11d48;
  --theme-editbox-focus-color: rgba(249, 115, 22, .12);
  --theme-checkbox-color: #111827;
  --theme-checkbox-border: rgba(120, 53, 15, .35);
  --theme-checkbox-disabled: #a16207;
}

.theme-light.theme-skeuomorphism {
  --theme-bg-color: #f5f5f4;
  --theme-back-color: #e5e7eb;
  --theme-navpanel-color: #e5e7eb;
  --theme-comp-header-color: #f5f5f4;
  --theme-panel-color: #ffffff;
  --theme-divider-color: #d4d4d4;
  --theme-bg-divider-color: #e5e7eb;
  --accent-bg-color: #e5e7eb;

  --theme-content-color: #111827;
  --theme-caption-color: #111827;
  --theme-dark-color: #374151;
  --theme-link-color: #2563eb;

  --primary-button-default: #2563eb;
  --primary-button-hovered: #3b82f6;
  --primary-button-pressed: #1d4ed8;
  --primary-button-focused: #2563eb;

  --theme-button-default: rgba(148, 163, 184, .08);
  --theme-button-hovered: rgba(148, 163, 184, .16);
  --theme-button-pressed: rgba(148, 163, 184, .24);
  --theme-button-border: rgba(148, 163, 184, .35);
  --theme-button-focused-border: rgba(37, 99, 235, .35);
  --theme-navpanel-hovered: rgba(148, 163, 184, .14);
  --theme-navpanel-selected: rgba(148, 163, 184, .22);
  --theme-list-border-color: rgba(148, 163, 184, .2);
  --theme-list-header-color: #e5e7eb;
  --theme-popup-color: #ffffff;
  --theme-popup-hover: #f3f4f6;
  --theme-editbox-focus-border: #2563eb;
  --theme-tooltip-bg: #111827;
  --theme-calendar-today-color: #2563eb;
  --theme-checkbox-bg-color: #ffffff;
  --theme-caret-color: #2563eb;
  --theme-code-bg-color: #e5e7eb;
  --scrollbar-bar-color: #cbd5f5;
  --theme-inbox-notify: #2563eb;
  --text-editor-block-quote-color: #2563eb;
  --theme-statusbar-color: #f5f5f4;
  --theme-bg-accent-color: rgba(148, 163, 184, .12);
  --theme-text-primary-color: #111827;
  --theme-text-placeholder-color: rgba(107, 114, 128, .78);
  --primary-button-color: #ffffff;
  --primary-button-border: rgba(37, 99, 235, .32);
}