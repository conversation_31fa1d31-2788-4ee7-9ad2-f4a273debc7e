//
// © 2023 Hardcore Engineering, Inc. All Rights Reserved.
// Licensed under the Eclipse Public License v2.0 (SPDX: EPL-2.0).
//

/* Common Colors */
* {
  --global-accent-IconColor: #6796FF;
  --global-on-accent-TextColor: #FFFFFF;
  --global-higlight-Color: #CB4B42;

  --global-online-color: #05A05C;

  --global-ui-hover-OverlayColor: #15307226;
  --global-ui-active-OverlayColor: #15307233;

  --button-accent-LabelColor: #fff;
  --button-disabled-LabelColor: #8b97ad;
  --button-accent-IconColor: #fff;
  --button-disabled-IconColor: #8b97ad;
  --button-primary-BackgroundColor: #3364e2;
  --button-primary-BorderColor: #d1d5de1a;
  --button-primary-hover-BackgroundColor: #6191fe;
  --button-primary-active-BackgroundColor: #2553cf;
  --button-primary-loading-LabelColor: #6191fe;
  --button-negative-loading-LabelColor: #ff9187;
  --button-negative-BackgroundColor: #CB4B42;
  --button-negative-BorderColor: #d1d5de26;
  --button-negative-hover-BackgroundColor: #DC5148;
  --button-negative-active-BackgroundColor: #B34139;

  --selector-active-BackgroundColor: #3364e2;
  --selector-IconColor: #ffffff;
  --selector-disabled-IconColor: #8b97ad;

  --tag-on-accent-PorpoiseText: #FFFFFF;
  --tag-accent-SunshineBackground: #FFBD2E;

  --border-color-global-error-border-color: #fb6863;

  --border-talk-indication-primary: #4683EA;
}

/* Dark Theme */
.theme-dark {
  --global-ui-BackgroundColor: 1530720D;
  --global-ui-BorderColor: #A5BDFF1A;
  --global-ui-hover-BackgroundColor: #A5BDFF1A;
  --global-ui-active-BackgroundColor: #A5BDFF26;
  --global-ui-highlight-BackgroundColor: #A5BDFF0D;
  --global-ui-hover-highlight-BackgroundColor: #A5BDFF26;
  --global-surface-01-BackgroundColor: #131925;
  --global-surface-01-BorderColor: #1F2737;
  --global-surface-01-hover-BackgroundColor: #19202E;
  --global-surface-02-BackgroundColor: #19202E;
  --global-surface-02-BorderColor: #262F40;
  --global-surface-03-hover-BackgroundColor: #19202E;
  --global-subtle-ui-BorderColor: #A5BDFF0D;
  --global-subtle-BackgroundColor: #072790;
  --global-popover-BackgroundColor: #262F40;
  --global-popover-hover-BackgroundColor: #1F2737;
  --global-popover-BorderColor: #A5BDFF1A;
  --global-primary-LinkColor: #4D7FF5;
  --global-primary-IconColor: #ffffff;
  --global-primary-TextColor: #FFFFFF;
  --global-secondary-TextColor: #C1C9D6;
  --global-tertiary-TextColor: #8E99AF;
  --global-disabled-TextColor: #5A667E;
  --global-accent-TextColor: #4D7FF5;
  --global-error-TextColor: #FF6359;
  --global-focus-BorderColor: #2A59D6;
  --global-focus-inset-BorderColor: #0D121C;
  --global-popover-ShadowColor: #0E131E59;
  --global-modal-ShadowColor: #0E131E73;
  --global-accent-SkyText: #B9D1F5;
  --global-accent-BackgroundColor: #204DC8;
  --global-on-nuance-TextColor: #041d7d;

  --global-no-priority-PriorityColor: #8E99AF;
  --global-low-PriorityColor: #6493FF;
  --global-medium-PriorityColor: #FFBD2E;
  --global-high-PriorityColor: #F6684B;
  --global-urgent-PriorityColor: #F6684B;
  --global-disabled-PriorityColor: #5A667E;

  --tag-on-subtle-PorpoiseText: #F2F4F6;
  --tag-subtle-PorpoiseBackground: #343F49;
  --tag-nuance-SunshineBackground: #262F40;
  --tag-accent-SunshineText: #FFBD2E;
  --tag-nuance-SkyBackground: #1F2737;

  --icon-disabled-IconColor: #394358;

  --border-talk-indication-secondary: #25262A;

  /** Buttons **/
  --button-subtle-LabelColor: #fff;
  --button-subtle-IconColor: #fff;
  --button-disabled-BackgroundColor: #d1d5de0d;
  --button-primary-loading-LabelColor: #6191fe;
  --button-secondary-BackgroundColor: #d1d5de0d;
  --button-secondary-BorderColor: #d1d5de1a;
  --button-secondary-hover-BackgroundColor: #A5BDFF1A;
  --button-secondary-active-BackgroundColor: #d1d5de26;
  --button-tertiary-hover-BackgroundColor: #d1d5de1a;
  --button-tertiary-active-BackgroundColor: #d1d5de26;
  --button-menu-active-BorderColor: #d9dee6;

  /** Editbox **/
  --input-BackgroundColor: #a5bdff0d;
  --input-hover-BackgroundColor: #a5bdff1a;
  --input-BorderColor: #a5bdff0d;
  --input-TextColor: #ffffff;
  --input-LabelColor: #ffffff;
  --input-filled-LabelColor: #8b97ad;
  --input-PlaceholderColor: #8b97ad;
  --input-hover-PlaceholderColor: #ffffff;
  --input-focus-PlaceholderColor: #556178;
  --input-HelperColor: #8b97ad;
  --input-error-BorderColor: #fb6863;
  --input-search-IconColor: #ffffff;

  /** Checkboxes **/
  --selector-BackgroundColor: #a5bdff0d;
  --selector-BorderColor: #d9dee6;
  --selector-off-BackgroundColor: #556178;
  --selector-hover-overlay-BackgroundColor: #a5bdff1a;
  --selector-disabled-BackgroundColor: #a5bdff1a;
  --selector-disabled-BorderColor: #a5bdff0d;

  --love-active-call-color-1: #5190EC;
  --love-active-call-color-2: #F47758;
  --love-active-call-transform: scaleY(0.25) scaleX(0.4);
  --love-active-call-filter: blur(17px);

  --global-offline-color: #d1d5de;

  --avatar-counter-BackgroundColor: #141523
}

/* Light Theme */
.theme-light {
  --global-ui-BackgroundColor: #1530720D;
  --global-ui-BorderColor: #1530721A;
  --global-ui-hover-BackgroundColor: #1530721A;
  --global-ui-active-BackgroundColor: #A5BDFF40;
  --global-ui-highlight-BackgroundColor: #A5BDFF26;
  --global-ui-hover-highlight-BackgroundColor: #A5BDFF40;
  --global-surface-01-BackgroundColor: #F8F9FA;
  --global-surface-01-BorderColor: #DDE1E9;
  --global-surface-01-hover-BackgroundColor: #EBEEF2;
  --global-surface-02-BackgroundColor: #FFFFFF;
  --global-surface-02-BorderColor: #EBEEF2;
  --global-surface-03-hover-BackgroundColor: #F8F9FA;
  --global-subtle-ui-BorderColor: #1530720D;
  --global-subtle-BackgroundColor: #A8C8FF;
  --global-popover-BackgroundColor: #131925;
  --global-popover-hover-BackgroundColor: #1F2737;
  --global-popover-BorderColor: #A5BDFF26;
  --global-primary-LinkColor: #3566E2;
  --global-primary-IconColor: #0f121a;
  --global-primary-TextColor: #0F121A;
  --global-secondary-TextColor: #5A667E;
  --global-tertiary-TextColor: #7B879E;
  --global-disabled-TextColor: #A1ABBF;
  --global-accent-TextColor: #3566E2;
  --global-error-TextColor: #A40A1B;
  --global-focus-BorderColor: #204DC8;
  --global-focus-inset-BorderColor: #FFFFFF;
  --global-popover-ShadowColor: #0E131E1F;
  --global-modal-ShadowColor: #0E131E14;
  --global-accent-SkyText: #B9D1F5;
  --global-accent-BackgroundColor: #3566E2;
  --global-on-nuance-TextColor: #2553cf;

  --global-no-priority-PriorityColor: #7B879E;
  --global-low-PriorityColor: #3566E2;
  --global-medium-PriorityColor: #FF9838;
  --global-high-PriorityColor: #E9403D;
  --global-urgent-PriorityColor: #E9403D;
  --global-disabled-PriorityColor: #A1ABBF;

  --tag-on-subtle-PorpoiseText: #293139;
  --tag-subtle-PorpoiseBackground: #C8D1D9;
  --tag-nuance-SunshineBackground: #FEF2E2;
  --tag-accent-SunshineText: #8E5E00;
  --tag-nuance-SkyBackground: #EEF4FD;

  --icon-disabled-IconColor: #B3BCCC;

  --border-talk-indication-secondary: #F1F1F1;

  /** Buttons **/
  --button-subtle-LabelColor: #000;
  --button-subtle-IconColor: #000;
  --button-disabled-BackgroundColor: #1725470d;
  --button-primary-loading-LabelColor: #95baff;
  --button-secondary-BackgroundColor: #1725470d;
  --button-secondary-BorderColor: #1725471a;
  --button-secondary-hover-BackgroundColor: #1725471a;
  --button-secondary-active-BackgroundColor: #17254726;
  --button-tertiary-hover-BackgroundColor: #1725471a;
  --button-tertiary-active-BackgroundColor: #17254726;
  --button-menu-active-BorderColor: #0f121a;

  /** Editbox **/
  --input-BackgroundColor: #1530720d;
  --input-hover-BackgroundColor: #1530721a;
  --input-BorderColor: #1530720d;
  --input-TextColor: #0f121a;
  --input-LabelColor: #0f121a;
  --input-filled-LabelColor: #556178;
  --input-PlaceholderColor: #556178;
  --input-hover-PlaceholderColor: #0f121a;
  --input-focus-PlaceholderColor: #8b97ad;
  --input-HelperColor: #556178;
  --input-error-BorderColor: #e34748;
  --input-search-IconColor: #0f121a;

  /** Checkboxes **/
  --selector-BackgroundColor: #1530720d;
  --selector-BorderColor: #0f121a;
  --selector-off-BackgroundColor: #cbd2dd;
  --selector-hover-overlay-BackgroundColor: #1530721a;
  --selector-disabled-BackgroundColor: #1530721a;
  --selector-disabled-BorderColor: #1530720d;

  --love-active-call-color-1: #205DC2;
  --love-active-call-color-2: #e34748;
  --love-active-call-filter: blur(10px);
  --love-active-call-transform: scaleY(0.3) scaleX(0.42);

  --global-offline-color: #5A667E;

  --avatar-counter-BackgroundColor: #F1F1F5;
}

.theme-dark.theme-high-contrast {
  --global-ui-BackgroundColor: #000000;
  --global-ui-BorderColor: #FFFFFF;
  --global-ui-hover-BackgroundColor: #111111;
  --global-ui-active-BackgroundColor: #222222;
  --global-ui-highlight-BackgroundColor: #333333;
  --global-ui-hover-highlight-BackgroundColor: #444444;
  --global-surface-01-BackgroundColor: #000000;
  --global-surface-01-BorderColor: #FFFFFF;
  --global-surface-02-BackgroundColor: #000000;
  --global-surface-02-BorderColor: #FFFFFF;
  --global-primary-TextColor: #FFFFFF;
  --global-secondary-TextColor: #E5E5E5;
  --global-tertiary-TextColor: #B3B3B3;
  --global-primary-LinkColor: #FFFF00;
  --global-accent-TextColor: #FFFF00;
  --global-error-TextColor: #FF6666;

  --button-subtle-LabelColor: #fff;
  --button-subtle-IconColor: #fff;
  --button-disabled-BackgroundColor: #111111;
  --button-disabled-LabelColor: #666666;
  --input-BackgroundColor: #000000;
  --input-hover-BackgroundColor: #111111;
  --input-BorderColor: #FFFFFF;
  --input-TextColor: #FFFFFF;
  --input-LabelColor: #FFFFFF;
  --input-PlaceholderColor: #B3B3B3;
  --selector-BackgroundColor: #000000;
  --selector-BorderColor: #FFFFFF;
}

.theme-dark.theme-midnight {
  --global-ui-BackgroundColor: #020617;
  --global-ui-BorderColor: rgba(148, 163, 184, .3);
  --global-ui-hover-BackgroundColor: rgba(30, 58, 138, .2);
  --global-ui-active-BackgroundColor: rgba(30, 58, 138, .3);
  --global-surface-01-BackgroundColor: #020617;
  --global-surface-01-BorderColor: rgba(148, 163, 184, .2);
  --global-surface-02-BackgroundColor: #0f172a;
  --global-surface-02-BorderColor: rgba(148, 163, 184, .15);
  --global-primary-TextColor: #f1f5f9;
  --global-secondary-TextColor: #cbd5e1;
  --global-tertiary-TextColor: #94a3b8;
  --global-primary-LinkColor: #60A5FA;
  --global-accent-TextColor: #60A5FA;
  --global-error-TextColor: #f87171;

  --button-subtle-LabelColor: #f1f5f9;
  --button-subtle-IconColor: #f1f5f9;
  --button-disabled-BackgroundColor: rgba(15, 23, 42, .3);
  --button-disabled-LabelColor: #64748b;
  --input-BackgroundColor: rgba(15, 23, 42, .6);
  --input-hover-BackgroundColor: rgba(30, 58, 138, .2);
  --input-BorderColor: rgba(148, 163, 184, .3);
  --input-TextColor: #f1f5f9;
  --input-LabelColor: #f1f5f9;
  --input-PlaceholderColor: #94a3b8;
  --selector-BackgroundColor: rgba(15, 23, 42, .6);
  --selector-BorderColor: rgba(148, 163, 184, .4);
}

.theme-dark.theme-forest {
  --global-ui-BackgroundColor: #052e16;
  --global-ui-BorderColor: rgba(34, 197, 94, .3);
  --global-ui-hover-BackgroundColor: rgba(22, 163, 74, .2);
  --global-ui-active-BackgroundColor: rgba(22, 163, 74, .3);
  --global-surface-01-BackgroundColor: #052e16;
  --global-surface-01-BorderColor: rgba(34, 197, 94, .2);
  --global-surface-02-BackgroundColor: #064e3b;
  --global-surface-02-BorderColor: rgba(34, 197, 94, .15);
  --global-primary-TextColor: #f0fdf4;
  --global-secondary-TextColor: #dcfce7;
  --global-tertiary-TextColor: #86efac;
  --global-primary-LinkColor: #4ADE80;
  --global-accent-TextColor: #4ADE80;
  --global-error-TextColor: #f87171;

  --button-subtle-LabelColor: #f0fdf4;
  --button-subtle-IconColor: #f0fdf4;
  --button-disabled-BackgroundColor: rgba(6, 95, 70, .3);
  --button-disabled-LabelColor: #14532d;
  --input-BackgroundColor: rgba(6, 95, 70, .6);
  --input-hover-BackgroundColor: rgba(22, 163, 74, .2);
  --input-BorderColor: rgba(34, 197, 94, .3);
  --input-TextColor: #f0fdf4;
  --input-LabelColor: #f0fdf4;
  --input-PlaceholderColor: #86efac;
  --selector-BackgroundColor: rgba(6, 95, 70, .6);
  --selector-BorderColor: rgba(34, 197, 94, .4);
}

.theme-light.theme-sand {
  --global-ui-BackgroundColor: #f3e7d3;
  --global-ui-BorderColor: rgba(146, 64, 14, .2);
  --global-ui-hover-BackgroundColor: rgba(146, 64, 14, .12);
  --global-ui-active-BackgroundColor: rgba(146, 64, 14, .2);
  --global-surface-01-BackgroundColor: #f5f1e8;
  --global-surface-01-BorderColor: rgba(146, 64, 14, .15);
  --global-surface-02-BackgroundColor: #fbf7ef;
  --global-surface-02-BorderColor: rgba(146, 64, 14, .12);
  --global-primary-TextColor: #451a03;
  --global-secondary-TextColor: #78350f;
  --global-tertiary-TextColor: #92400e;
  --global-primary-LinkColor: #B45309;
  --global-accent-TextColor: #d97706;
  --global-error-TextColor: #dc2626;

  --button-subtle-LabelColor: #451a03;
  --button-subtle-IconColor: #451a03;
  --button-disabled-BackgroundColor: rgba(146, 64, 14, .08);
  --button-disabled-LabelColor: #a16207;
  --input-BackgroundColor: rgba(146, 64, 14, .05);
  --input-hover-BackgroundColor: rgba(146, 64, 14, .12);
  --input-BorderColor: rgba(146, 64, 14, .2);
  --input-TextColor: #451a03;
  --input-LabelColor: #451a03;
  --input-PlaceholderColor: #92400e;
  --selector-BackgroundColor: rgba(146, 64, 14, .08);
  --selector-BorderColor: rgba(146, 64, 14, .25);
}

.theme-light.theme-sky {
  --global-ui-BackgroundColor: #e0edff;
  --global-ui-BorderColor: rgba(37, 99, 235, .2);
  --global-ui-hover-BackgroundColor: rgba(37, 99, 235, .12);
  --global-ui-active-BackgroundColor: rgba(37, 99, 235, .2);
  --global-surface-01-BackgroundColor: #ecf5ff;
  --global-surface-01-BorderColor: rgba(37, 99, 235, .15);
  --global-surface-02-BackgroundColor: #f3f8ff;
  --global-surface-02-BorderColor: rgba(37, 99, 235, .12);
  --global-primary-TextColor: #1e3a8a;
  --global-secondary-TextColor: #1e40af;
  --global-tertiary-TextColor: #3b82f6;
  --global-primary-LinkColor: #2563EB;
  --global-accent-TextColor: #2563eb;
  --global-error-TextColor: #dc2626;

  --button-subtle-LabelColor: #1e3a8a;
  --button-subtle-IconColor: #1e3a8a;
  --button-disabled-BackgroundColor: rgba(37, 99, 235, .08);
  --button-disabled-LabelColor: #60a5fa;
  --input-BackgroundColor: rgba(37, 99, 235, .05);
  --input-hover-BackgroundColor: rgba(37, 99, 235, .12);
  --input-BorderColor: rgba(37, 99, 235, .2);
  --input-TextColor: #1e3a8a;
  --input-LabelColor: #1e3a8a;
  --input-PlaceholderColor: #3b82f6;
  --selector-BackgroundColor: rgba(37, 99, 235, .08);
  --selector-BorderColor: rgba(37, 99, 235, .25);
}

.theme-dark.theme-dusk {
  --global-ui-BackgroundColor: #030712;
  --global-ui-BorderColor: rgba(168, 85, 247, .25);
  --global-ui-hover-BackgroundColor: rgba(168, 85, 247, .12);
  --global-ui-active-BackgroundColor: rgba(168, 85, 247, .2);
  --global-surface-01-BackgroundColor: #0f0a1e;
  --global-surface-01-BorderColor: rgba(168, 85, 247, .18);
  --global-surface-02-BackgroundColor: #1a0f2e;
  --global-surface-02-BorderColor: rgba(168, 85, 247, .15);
  --global-primary-TextColor: #e9d5ff;
  --global-secondary-TextColor: #d8b4fe;
  --global-tertiary-TextColor: #c084fc;
  --global-primary-LinkColor: #a855f7;
  --global-accent-TextColor: #a855f7;
  --global-error-TextColor: #fb7185;

  --button-subtle-LabelColor: #e9d5ff;
  --button-subtle-IconColor: #e9d5ff;
  --button-disabled-BackgroundColor: rgba(168, 85, 247, .08);
  --button-disabled-LabelColor: #a78bfa;
  --input-BackgroundColor: rgba(168, 85, 247, .05);
  --input-hover-BackgroundColor: rgba(168, 85, 247, .12);
  --input-BorderColor: rgba(168, 85, 247, .25);
  --input-TextColor: #e9d5ff;
  --input-LabelColor: #e9d5ff;
  --input-PlaceholderColor: #c084fc;
  --selector-BackgroundColor: rgba(168, 85, 247, .08);
  --selector-BorderColor: rgba(168, 85, 247, .3);
}

.theme-dark.theme-neon {
  --global-ui-BackgroundColor: #020617;
  --global-ui-BorderColor: rgba(34, 211, 238, .28);
  --global-ui-hover-BackgroundColor: rgba(34, 211, 238, .14);
  --global-ui-active-BackgroundColor: rgba(34, 211, 238, .22);
  --global-surface-01-BackgroundColor: #0f172a;
  --global-surface-01-BorderColor: rgba(34, 211, 238, .2);
  --global-surface-02-BackgroundColor: #1e293b;
  --global-surface-02-BorderColor: rgba(34, 211, 238, .18);
  --global-primary-TextColor: #f9fafb;
  --global-secondary-TextColor: #e5e7eb;
  --global-tertiary-TextColor: #38bdf8;
  --global-primary-LinkColor: #22d3ee;
  --global-accent-TextColor: #22d3ee;
  --global-error-TextColor: #fb7185;

  --button-subtle-LabelColor: #f9fafb;
  --button-subtle-IconColor: #f9fafb;
  --button-disabled-BackgroundColor: rgba(34, 211, 238, .08);
  --button-disabled-LabelColor: #38bdf8;
  --input-BackgroundColor: rgba(34, 211, 238, .05);
  --input-hover-BackgroundColor: rgba(34, 211, 238, .12);
  --input-BorderColor: rgba(34, 211, 238, .28);
  --input-TextColor: #f9fafb;
  --input-LabelColor: #f9fafb;
  --input-PlaceholderColor: #38bdf8;
  --selector-BackgroundColor: rgba(34, 211, 238, .08);
  --selector-BorderColor: rgba(34, 211, 238, .32);
}

.theme-light.theme-blossom {
  --global-ui-BackgroundColor: #fdf2f8;
  --global-ui-BorderColor: rgba(236, 72, 153, .2);
  --global-ui-hover-BackgroundColor: rgba(236, 72, 153, .12);
  --global-ui-active-BackgroundColor: rgba(236, 72, 153, .2);
  --global-surface-01-BackgroundColor: #fff0f7;
  --global-surface-01-BorderColor: rgba(236, 72, 153, .15);
  --global-surface-02-BackgroundColor: #fff7fb;
  --global-surface-02-BorderColor: rgba(236, 72, 153, .12);
  --global-primary-TextColor: #9f1239;
  --global-secondary-TextColor: #be185d;
  --global-tertiary-TextColor: #db2777;
  --global-primary-LinkColor: #db2777;
  --global-accent-TextColor: #ec4899;
  --global-error-TextColor: #dc2626;

  --button-subtle-LabelColor: #9f1239;
  --button-subtle-IconColor: #9f1239;
  --button-disabled-BackgroundColor: rgba(236, 72, 153, .08);
  --button-disabled-LabelColor: #f472b6;
  --input-BackgroundColor: rgba(236, 72, 153, .05);
  --input-hover-BackgroundColor: rgba(236, 72, 153, .12);
  --input-BorderColor: rgba(236, 72, 153, .2);
  --input-TextColor: #9f1239;
  --input-LabelColor: #9f1239;
  --input-PlaceholderColor: #db2777;
  --selector-BackgroundColor: rgba(236, 72, 153, .08);
  --selector-BorderColor: rgba(236, 72, 153, .25);
}

.theme-light.theme-lavender {
  --global-ui-BackgroundColor: #f5f3ff;
  --global-ui-BorderColor: rgba(129, 140, 248, .2);
  --global-ui-hover-BackgroundColor: rgba(129, 140, 248, .12);
  --global-ui-active-BackgroundColor: rgba(129, 140, 248, .2);
  --global-surface-01-BackgroundColor: #eef2ff;
  --global-surface-01-BorderColor: rgba(129, 140, 248, .15);
  --global-surface-02-BackgroundColor: #f9f5ff;
  --global-surface-02-BorderColor: rgba(129, 140, 248, .12);
  --global-primary-TextColor: #312e81;
  --global-secondary-TextColor: #3730a3;
  --global-tertiary-TextColor: #6366f1;
  --global-primary-LinkColor: #6366f1;
  --global-accent-TextColor: #8b5cf6;
  --global-error-TextColor: #dc2626;

  --button-subtle-LabelColor: #312e81;
  --button-subtle-IconColor: #312e81;
  --button-disabled-BackgroundColor: rgba(129, 140, 248, .08);
  --button-disabled-LabelColor: #a78bfa;
  --input-BackgroundColor: rgba(129, 140, 248, .05);
  --input-hover-BackgroundColor: rgba(129, 140, 248, .12);
  --input-BorderColor: rgba(129, 140, 248, .2);
  --input-TextColor: #312e81;
  --input-LabelColor: #312e81;
  --input-PlaceholderColor: #6366f1;
  --selector-BackgroundColor: rgba(129, 140, 248, .08);
  --selector-BorderColor: rgba(129, 140, 248, .25);
}

.theme-light.theme-snow {
  --global-ui-BackgroundColor: #f9fafb;
  --global-ui-BorderColor: rgba(148, 163, 184, .2);
  --global-ui-hover-BackgroundColor: rgba(148, 163, 184, .12);
  --global-ui-active-BackgroundColor: rgba(148, 163, 184, .2);
  --global-surface-01-BackgroundColor: #f3f4f6;
  --global-surface-01-BorderColor: rgba(148, 163, 184, .15);
  --global-surface-02-BackgroundColor: #f9fafb;
  --global-surface-02-BorderColor: rgba(148, 163, 184, .12);
  --global-primary-TextColor: #111827;
  --global-secondary-TextColor: #374151;
  --global-tertiary-TextColor: #6b7280;
  --global-primary-LinkColor: #0ea5e9;
  --global-accent-TextColor: #0ea5e9;
  --global-error-TextColor: #dc2626;

  --button-subtle-LabelColor: #111827;
  --button-subtle-IconColor: #111827;
  --button-disabled-BackgroundColor: rgba(148, 163, 184, .08);
  --button-disabled-LabelColor: #94a3b8;
  --input-BackgroundColor: rgba(148, 163, 184, .05);
  --input-hover-BackgroundColor: rgba(148, 163, 184, .12);
  --input-BorderColor: rgba(148, 163, 184, .2);
  --input-TextColor: #111827;
  --input-LabelColor: #111827;
  --input-PlaceholderColor: #6b7280;
  --selector-BackgroundColor: rgba(148, 163, 184, .08);
  --selector-BorderColor: rgba(148, 163, 184, .25);
}

.theme-light.theme-neo-brutalism {
  /* Core UI Colors */
  --global-ui-BackgroundColor: #FFF5EB;
  --global-ui-BorderColor: #000000;
  --global-ui-hover-BackgroundColor: #FFD6A5;
  --global-ui-active-BackgroundColor: #FF9F43;
  --global-ui-highlight-BackgroundColor: #FFD6A5;
  --global-ui-hover-highlight-BackgroundColor: #FF9F43;
  
  /* Surfaces */
  --global-surface-01-BackgroundColor: #FFFFFF;
  --global-surface-01-BorderColor: #000000;
  --global-surface-01-hover-BackgroundColor: #F0F0F0;
  --global-surface-02-BackgroundColor: #FFF0F5;
  --global-surface-02-BorderColor: #000000;
  --global-surface-03-hover-BackgroundColor: #FFF5EB;
  
  /* Subtle & Popovers */
  --global-subtle-ui-BorderColor: #000000;
  --global-subtle-BackgroundColor: #A6E3E9;
  --global-popover-BackgroundColor: #FFFFFF;
  --global-popover-hover-BackgroundColor: #F0F0F0;
  --global-popover-BorderColor: #000000;
  
  /* Text & Icons */
  --global-primary-LinkColor: #0000FF;
  --global-primary-IconColor: #000000;
  --global-primary-TextColor: #000000;
  --global-secondary-TextColor: #000000;
  --global-tertiary-TextColor: #333333;
  --global-disabled-TextColor: #666666;
  --global-accent-TextColor: #FF00FF;
  --global-error-TextColor: #FF0000;
  
  /* Focus & Shadows */
  --global-focus-BorderColor: #000000;
  --global-focus-inset-BorderColor: #FFFFFF;
  --global-popover-ShadowColor: #000000;
  --global-modal-ShadowColor: #000000;
  
  /* Accents */
  --global-accent-SkyText: #000000;
  --global-accent-BackgroundColor: #FF9F43;
  --global-on-nuance-TextColor: #000000;

  /* Priorities */
  --global-no-priority-PriorityColor: #999999;
  --global-low-PriorityColor: #54A0FF;
  --global-medium-PriorityColor: #FF9F43;
  --global-high-PriorityColor: #FF6B6B;
  --global-urgent-PriorityColor: #EE5253;
  --global-disabled-PriorityColor: #CCCCCC;

  /* Tags */
  --tag-on-subtle-PorpoiseText: #000000;
  --tag-subtle-PorpoiseBackground: #C8D6E5;
  --tag-nuance-SunshineBackground: #FECA57;
  --tag-accent-SunshineText: #000000;
  --tag-nuance-SkyBackground: #48DBFB;

  --icon-disabled-IconColor: #666666;
  --border-talk-indication-secondary: #000000;

  /* Buttons */
  --button-subtle-LabelColor: #000000;
  --button-subtle-IconColor: #000000;
  --button-disabled-BackgroundColor: #CCCCCC;
  --button-disabled-LabelColor: #666666;
  --button-primary-loading-LabelColor: #000000;
  --button-secondary-BackgroundColor: #FFFFFF;
  --button-secondary-BorderColor: #000000;
  --button-secondary-hover-BackgroundColor: #FF9F43;
  --button-secondary-active-BackgroundColor: #FF6B6B;
  --button-tertiary-hover-BackgroundColor: #FFD6A5;
  --button-tertiary-active-BackgroundColor: #FF9F43;
  --button-menu-active-BorderColor: #000000;

  /* Inputs */
  --input-BackgroundColor: #FFFFFF;
  --input-hover-BackgroundColor: #F0F0F0;
  --input-BorderColor: #000000;
  --input-TextColor: #000000;
  --input-LabelColor: #000000;
  --input-filled-LabelColor: #333333;
  --input-PlaceholderColor: #666666;
  --input-hover-PlaceholderColor: #000000;
  --input-focus-PlaceholderColor: #333333;
  --input-HelperColor: #333333;
  --input-error-BorderColor: #FF0000;
  --input-search-IconColor: #000000;

  /* Selectors/Checkboxes */
  --selector-BackgroundColor: #FFFFFF;
  --selector-BorderColor: #000000;
  --selector-off-BackgroundColor: #CCCCCC;
  --selector-hover-overlay-BackgroundColor: #FFD6A5;
  --selector-disabled-BackgroundColor: #EEEEEE;
  --selector-disabled-BorderColor: #000000;

  /* Misc */
  --love-active-call-color-1: #54A0FF;
  --love-active-call-color-2: #FF6B6B;
  --love-active-call-filter: none;
  --love-active-call-transform: none;
  --global-offline-color: #666666;
  --avatar-counter-BackgroundColor: #FFFFFF;

  /* Neo-Brutalism Specific Styling Overrides */
  --shadow-hard: 4px 4px 0px 0px #000000;
  --border-thick: 2px solid #000000;
  --radius-none: 0px;

  /* Force high contrast borders and shadows on elements if possible */
  --theme-popup-shadow: 5px 5px 0px 0px #000;
  --button-shadow: 3px 3px 0px 0px #000;
  --card-shadow: 6px 6px 0px 0px #000;
  --theme-button-border: 2px solid #000;
  --theme-popup-border: 2px solid #000;
  
  /* Attempt to override generic radius variables if they exist in the system */
  --border-radius-small: 0px;
  --border-radius-medium: 0px;
  --border-radius-large: 0px;
  --border-radius-circle: 0px;
}

.theme-light.theme-skeuomorphism {
  /* Core UI Colors */
  --global-ui-BackgroundColor: #E0E5EC;
  --global-ui-BorderColor: rgba(255, 255, 255, 0.4);
  --global-ui-hover-BackgroundColor: linear-gradient(145deg, #E0E5EC, #ffffff);
  --global-ui-active-BackgroundColor: #E0E5EC;
  --global-ui-highlight-BackgroundColor: #D1D9E6;
  --global-ui-hover-highlight-BackgroundColor: #ffffff;

  /* Surfaces */
  --global-surface-01-BackgroundColor: #E0E5EC;
  --global-surface-01-BorderColor: rgba(163, 177, 198, 0.3);
  --global-surface-01-hover-BackgroundColor: #E5EAF0;
  --global-surface-02-BackgroundColor: #E0E5EC;
  --global-surface-02-BorderColor: rgba(163, 177, 198, 0.2);
  --global-surface-03-hover-BackgroundColor: #E5EAF0;

  /* Subtle & Popovers */
  --global-subtle-ui-BorderColor: rgba(255, 255, 255, 0.5);
  --global-subtle-BackgroundColor: #D1D9E6;
  --global-popover-BackgroundColor: #E0E5EC;
  --global-popover-hover-BackgroundColor: #E5EAF0;
  --global-popover-BorderColor: rgba(255, 255, 255, 0.6);

  /* Text & Icons */
  --global-primary-LinkColor: #6D5DF1;
  --global-primary-IconColor: #4D5B7C;
  --global-primary-TextColor: #4D5B7C;
  --global-secondary-TextColor: #7D8CA5;
  --global-tertiary-TextColor: #A3B1C6;
  --global-disabled-TextColor: #A3B1C6;
  --global-accent-TextColor: #6D5DF1;
  --global-error-TextColor: #E9403D;

  /* Focus & Shadows */
  --global-focus-BorderColor: #6D5DF1;
  --global-focus-inset-BorderColor: #E0E5EC;
  --global-popover-ShadowColor: #A3B1C6;
  --global-modal-ShadowColor: #A3B1C6;

  /* Accents */
  --global-accent-SkyText: #6D5DF1;
  --global-accent-BackgroundColor: #E0E5EC;
  --global-on-nuance-TextColor: #4D5B7C;

  /* Priorities */
  --global-no-priority-PriorityColor: #A3B1C6;
  --global-low-PriorityColor: #6D5DF1;
  --global-medium-PriorityColor: #FF9F43;
  --global-high-PriorityColor: #E9403D;
  --global-urgent-PriorityColor: #E9403D;
  --global-disabled-PriorityColor: #A3B1C6;

  /* Tags */
  --tag-on-subtle-PorpoiseText: #4D5B7C;
  --tag-subtle-PorpoiseBackground: #D1D9E6;
  --tag-nuance-SunshineBackground: #E0E5EC;
  --tag-accent-SunshineText: #FF9F43;
  --tag-nuance-SkyBackground: #E0E5EC;

  --icon-disabled-IconColor: #A3B1C6;
  --border-talk-indication-secondary: #E0E5EC;

  /* Buttons */
  --button-subtle-LabelColor: #4D5B7C;
  --button-subtle-IconColor: #4D5B7C;
  --button-disabled-BackgroundColor: #E0E5EC;
  --button-disabled-LabelColor: #A3B1C6;
  --button-primary-loading-LabelColor: #6D5DF1;
  --button-secondary-BackgroundColor: #E0E5EC;
  --button-secondary-BorderColor: transparent;
  --button-secondary-hover-BackgroundColor: linear-gradient(145deg, #ffffff, #E0E5EC);
  --button-secondary-active-BackgroundColor: inset 3px 3px 6px #b8b9be, inset -3px -3px 6px #ffffff;
  --button-tertiary-hover-BackgroundColor: linear-gradient(145deg, #ffffff, #E0E5EC);
  --button-tertiary-active-BackgroundColor: inset 2px 2px 5px #b8b9be, inset -2px -2px 5px #ffffff;
  --button-menu-active-BorderColor: #D1D9E6;

  /* Inputs */
  --input-BackgroundColor: inset 2px 2px 5px #BABECC, inset -5px -5px 10px #ffffff;
  --input-hover-BackgroundColor: inset 1px 1px 2px #BABECC, inset -1px -1px 2px #ffffff;
  --input-BorderColor: transparent;
  --input-TextColor: #4D5B7C;
  --input-LabelColor: #4D5B7C;
  --input-filled-LabelColor: #7D8CA5;
  --input-PlaceholderColor: #A3B1C6;
  --input-hover-PlaceholderColor: #7D8CA5;
  --input-focus-PlaceholderColor: #7D8CA5;
  --input-HelperColor: #7D8CA5;
  --input-error-BorderColor: #E9403D;
  --input-search-IconColor: #4D5B7C;

  /* Selectors */
  --selector-BackgroundColor: #E0E5EC;
  --selector-BorderColor: transparent;
  --selector-off-BackgroundColor: #A3B1C6;
  --selector-hover-overlay-BackgroundColor: rgba(255,255,255,0.5);
  --selector-disabled-BackgroundColor: #E0E5EC;
  --selector-disabled-BorderColor: transparent;

  /* Misc */
  --love-active-call-color-1: #6D5DF1;
  --love-active-call-color-2: #E9403D;
  --love-active-call-filter: blur(8px);
  --love-active-call-transform: scale(0.95);
  --global-offline-color: #A3B1C6;
  --avatar-counter-BackgroundColor: #E0E5EC;

  /* Skeuomorphism Specifics */
  --shadow-soft-out: 5px 5px 10px #a3b1c6, -5px -5px 10px #ffffff;
  --shadow-soft-in: inset 5px 5px 10px #a3b1c6, inset -5px -5px 10px #ffffff;
  
  --theme-popup-shadow: 8px 8px 16px #a3b1c6, -8px -8px 16px #ffffff;
  --button-shadow: 5px 5px 10px #a3b1c6, -5px -5px 10px #ffffff;
  --card-shadow: 9px 9px 16px #a3b1c6, -9px -9px 16px #ffffff;
  
  /* Gradients for buttons */
  --button-primary-BackgroundColor: linear-gradient(145deg, #6D5DF1, #5b4ec9);
  --button-primary-hover-BackgroundColor: linear-gradient(145deg, #5b4ec9, #6D5DF1);
  --button-primary-active-BackgroundColor: inset 3px 3px 6px #4a3fa3, inset -3px -3px 6px #8e7dff;
}

.theme-light.theme-liquid-glass {
  /* Base Glass Effect Variables */
  --glass-bg: rgba(255, 255, 255, 0.15);
  --glass-border: 1px solid rgba(255, 255, 255, 0.8);
  --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.2), inset 0 4px 20px rgba(255, 255, 255, 0.3);
  --glass-backdrop: blur(12px) saturate(180%);

  /* Core UI Colors */
  --global-ui-BackgroundColor: rgba(255, 255, 255, 0.1);
  --global-ui-BorderColor: rgba(255, 255, 255, 0.4);
  --global-ui-hover-BackgroundColor: rgba(255, 255, 255, 0.2);
  --global-ui-active-BackgroundColor: rgba(255, 255, 255, 0.3);
  --global-ui-highlight-BackgroundColor: rgba(255, 255, 255, 0.25);
  --global-ui-hover-highlight-BackgroundColor: rgba(255, 255, 255, 0.35);
  
  /* Surfaces */
  --global-surface-01-BackgroundColor: rgba(255, 255, 255, 0.1);
  --global-surface-01-BorderColor: rgba(255, 255, 255, 0.3);
  --global-surface-01-hover-BackgroundColor: rgba(255, 255, 255, 0.2);
  --global-surface-02-BackgroundColor: rgba(255, 255, 255, 0.05);
  --global-surface-02-BorderColor: rgba(255, 255, 255, 0.2);
  --global-surface-03-hover-BackgroundColor: rgba(255, 255, 255, 0.15);
  
  /* Subtle & Popovers */
  --global-subtle-ui-BorderColor: rgba(255, 255, 255, 0.3);
  --global-subtle-BackgroundColor: rgba(255, 255, 255, 0.2);
  --global-popover-BackgroundColor: rgba(255, 255, 255, 0.65);
  --global-popover-hover-BackgroundColor: rgba(255, 255, 255, 0.75);
  --global-popover-BorderColor: rgba(255, 255, 255, 0.5);
  
  /* Text & Icons */
  --global-primary-LinkColor: #0066CC;
  --global-primary-IconColor: #000000;
  --global-primary-TextColor: #000000;
  --global-secondary-TextColor: rgba(0, 0, 0, 0.7);
  --global-tertiary-TextColor: rgba(0, 0, 0, 0.5);
  --global-disabled-TextColor: rgba(0, 0, 0, 0.3);
  --global-accent-TextColor: #0066CC;
  --global-error-TextColor: #FF3333;

  /* Focus & Shadows */
  --global-focus-BorderColor: rgba(0, 102, 204, 0.6);
  --global-focus-inset-BorderColor: rgba(255, 255, 255, 0.8);
  --global-popover-ShadowColor: rgba(31, 38, 135, 0.15);
  --global-modal-ShadowColor: rgba(31, 38, 135, 0.25);

  /* Accents */
  --global-accent-SkyText: #0066CC;
  --global-accent-BackgroundColor: rgba(0, 102, 204, 0.2);
  --global-on-nuance-TextColor: #003366;

  /* Priorities */
  --global-no-priority-PriorityColor: rgba(0, 0, 0, 0.4);
  --global-low-PriorityColor: #0066CC;
  --global-medium-PriorityColor: #FF9900;
  --global-high-PriorityColor: #FF3333;
  --global-urgent-PriorityColor: #CC0000;
  --global-disabled-PriorityColor: rgba(0, 0, 0, 0.2);

  /* Tags */
  --tag-on-subtle-PorpoiseText: #000000;
  --tag-subtle-PorpoiseBackground: rgba(255, 255, 255, 0.3);
  --tag-nuance-SunshineBackground: rgba(255, 200, 0, 0.2);
  --tag-accent-SunshineText: #996600;
  --tag-nuance-SkyBackground: rgba(0, 102, 204, 0.1);

  --icon-disabled-IconColor: rgba(0, 0, 0, 0.3);
  --border-talk-indication-secondary: rgba(255, 255, 255, 0.5);

  /* Buttons */
  --button-subtle-LabelColor: #000000;
  --button-subtle-IconColor: #000000;
  --button-disabled-BackgroundColor: rgba(255, 255, 255, 0.1);
  --button-disabled-LabelColor: rgba(0, 0, 0, 0.3);
  --button-primary-loading-LabelColor: #0066CC;
  --button-secondary-BackgroundColor: rgba(255, 255, 255, 0.2);
  --button-secondary-BorderColor: rgba(255, 255, 255, 0.4);
  --button-secondary-hover-BackgroundColor: rgba(255, 255, 255, 0.3);
  --button-secondary-active-BackgroundColor: rgba(255, 255, 255, 0.4);
  --button-tertiary-hover-BackgroundColor: rgba(255, 255, 255, 0.2);
  --button-tertiary-active-BackgroundColor: rgba(255, 255, 255, 0.3);
  --button-menu-active-BorderColor: rgba(255, 255, 255, 0.6);

  /* Inputs */
  --input-BackgroundColor: rgba(255, 255, 255, 0.2);
  --input-hover-BackgroundColor: rgba(255, 255, 255, 0.3);
  --input-BorderColor: rgba(255, 255, 255, 0.5);
  --input-TextColor: #000000;
  --input-LabelColor: #000000;
  --input-filled-LabelColor: rgba(0, 0, 0, 0.6);
  --input-PlaceholderColor: rgba(0, 0, 0, 0.5);
  --input-hover-PlaceholderColor: #000000;
  --input-focus-PlaceholderColor: rgba(0, 0, 0, 0.7);
  --input-HelperColor: rgba(0, 0, 0, 0.6);
  --input-error-BorderColor: #FF3333;
  --input-search-IconColor: #000000;

  /* Selectors */
  --selector-BackgroundColor: rgba(255, 255, 255, 0.2);
  --selector-BorderColor: rgba(255, 255, 255, 0.5);
  --selector-off-BackgroundColor: rgba(0, 0, 0, 0.2);
  --selector-hover-overlay-BackgroundColor: rgba(255, 255, 255, 0.3);
  --selector-disabled-BackgroundColor: rgba(255, 255, 255, 0.1);
  --selector-disabled-BorderColor: rgba(255, 255, 255, 0.2);

  /* Misc */
  --love-active-call-color-1: #0066CC;
  --love-active-call-color-2: #FF3333;
  --love-active-call-filter: blur(15px);
  --love-active-call-transform: scale(1.1);
  --global-offline-color: rgba(0, 0, 0, 0.4);
  --avatar-counter-BackgroundColor: rgba(255, 255, 255, 0.5);

  /* Apply Glass Effect to major containers */
  --theme-popup-bg: rgba(255, 255, 255, 0.65);
  --theme-popup-backdrop-filter: blur(20px) saturate(180%);
  --theme-popup-border: 1px solid rgba(255, 255, 255, 0.5);
  --theme-popup-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);

  --theme-panel-bg: rgba(255, 255, 255, 0.4);
  --theme-panel-backdrop-filter: blur(15px) saturate(150%);
  
  /* Liquid Shine Simulation */
  --liquid-shine: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 50%, rgba(255,255,255,0.4) 100%);
}