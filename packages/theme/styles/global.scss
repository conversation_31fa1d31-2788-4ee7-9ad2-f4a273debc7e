//
// Copyright © 2021 Anticrm Platform Contributors.
// 
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// 
// See the License for the specific language governing permissions and
// limitations under the License.
//

@import "./_vars.scss";
@import "./_colors.scss";
@import "./_lumia-colors.scss";
@import "./_layouts.scss";
@import "./_print.scss";
@import "./common.scss";
@import "./button.scss";
@import "./editors.scss";
@import "./components.scss";
@import "./dialogs.scss";
@import "./popups.scss";
@import "./mixins.scss";
@import "./panel.scss";
@import "./prose.scss";
@import "./tables.scss";
@import "./_text-editor.scss";

@import "./love.scss";

@import "./mono.scss";
@import "./noto-color.scss";

@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 400;
  src: local('IBM Plex Sans'),
    local('IBMPlexSans'),
    url('../fonts/complete/woff2/IBMPlexSans-Regular.woff2') format('woff2'),
    url('../fonts/complete/woff/IBMPlexSans-Regular.woff') format('woff');
}

@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 500;
  src: local('IBM Plex Sans Medium'),
    local('IBMPlexSans-Medium'),
    url('../fonts/complete/woff2/IBMPlexSans-Medium.woff2') format('woff2'),
    url('../fonts/complete/woff/IBMPlexSans-Medium.woff') format('woff');
}

@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 600;
  src: local('IBM Plex Sans SemiBold'),
    local('IBMPlexSans-SemiBold'),
    url('../fonts/complete/woff2/IBMPlexSans-SemiBold.woff2') format('woff2'),
    url('../fonts/complete/woff/IBMPlexSans-SemiBold.woff') format('woff');
}

@font-face {
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  src: local('IBM Plex Sans Bold'),
    local('IBMPlexSans-Bold'),
    url('../fonts/complete/woff2/IBMPlexSans-Bold.woff2') format('woff2'),
    url('../fonts/complete/woff/IBMPlexSans-Bold.woff') format('woff');
}

* {
  box-sizing: border-box;
  touch-action: manipulation;
  scrollbar-color: var(--theme-navpanel-divider) transparent;
  scrollbar-width: none;
  --body-font-size: .875rem;
  --panel-aside-width: 25rem; // 20rem;
  --font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
  --mono-font: 'IBM Plex Mono', monospace;
  --timing-shadow: cubic-bezier(0, .65, .35, 1);
  --timing-main: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --timing-rotate: cubic-bezier(.28, 1.92, .39, .56);
  --timing-clock: cubic-bezier(.35, 2.1, .79, .71);
  // transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &::after,
  &::before {
    box-sizing: border-box;
  }
}

:root {
  .plyr__volume {
    position: relative;
  }

  .plyr__volume input[data-plyr="volume"] {
    display: none;
    height: 2rem;
    position: absolute;
    right: -3rem;
    top: -1rem;
    transform-origin: left;
    transform: rotate(-90deg);
  }

  .plyr__volume:hover input[data-plyr="volume"],
  .plyr__volume input[data-plyr="volume"]:hover {
    display: block;
  }

  --plyr-color-main: #ffffff;
  --plyr-video-control-background-hover: #d3d3d3a2;
  --app-height: 100%;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar:horizontal {
  height: 6px;
}

::-webkit-scrollbar-track,
.panel-instance .scroll .scroll::-webkit-scrollbar-track {
  margin: 6px;
  // background-color: var(--scrollbar-bar-color);
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-bar-color);
  border-radius: .25rem;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-bar-hover);
  border-radius: .25rem;
}

::-webkit-scrollbar-corner {
  background-color: var(--scrollbar-bar-color);
  border-radius: .25rem;
}

.kanban-container .scroll::-webkit-scrollbar-track {
  margin: 2.5rem;
}

.normal-font {
  font-size: 16px;
}

.small-font {
  font-size: 14px;
}

.font-ibm-plex {
  --font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
}

.font-system {
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.font-inter {
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.font-helvetica {
  --font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  font-family: var(--font-family);
  font-style: normal;
  font-weight: 400;
  font-size: var(--body-font-size);
  color: var(--content-color);
  background-color: var(--theme-bg-color);
  user-select: none;

  --status-bar-height: 36px;
  --status-bar-normal-height: 36px;

  @media (max-width: 480px) {
    &:has(.workbench-container) {
      --status-bar-height: 70px;
    }
  }
}

html,
body {
  height: var(--app-height);
  width: 100%;
  overflow: hidden;
  // touch-action: none;

  @media print {
    overflow: visible !important;
  }
}