//
// Copyright © 2021 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

/* Typography */
.font-regular-11,
.font-medium-11,
.font-regular-12,
.font-medium-12,
.font-caps-medium-12,
.font-bold-12,
.font-regular-14,
.font-medium-14,
.font-bold-14,
.paragraph-regular-14,
.heading-medium-16,
.heading-bold-16,
.heading-ui-H2,
.heading-medium-20,
.heading-bold-20 {
  font-family: var(--font-family);
  font-style: normal;

  &:not(.secondary, .tertiary) {
    color: var(--global-primary-TextColor);
  }

  &.secondary {
    color: var(--global-secondary-TextColor);
  }

  &.tertiary {
    color: var(--global-tertiary-TextColor);
  }

  &:not(.line-height-auto) {
    line-height: 1rem;
  }
}

.font-regular-11,
.font-medium-11 {
  font-size: 0.6875rem;
}

.font-regular-12,
.font-medium-12,
.font-caps-medium-12,
.font-bold-12 {
  font-size: 0.75rem;
}

.font-regular-14,
.font-medium-14,
.font-bold-14,
.paragraph-regular-14 {
  font-size: 0.875rem;
}

.font-regular-11,
.font-regular-12,
.font-regular-14,
.paragraph-regular-14 {
  font-weight: 400;
}

.font-medium-11,
.font-medium-12,
.font-caps-medium-12,
.font-medium-14,
.heading-medium-16,
.heading-medium-20 {
  font-weight: 500;
}

.heading-ui-H2 {
  font-weight: 600;
}

.font-bold-12,
.font-bold-14,
.heading-bold-16,
.heading-bold-20 {
  font-weight: 700;
}

.heading-medium-16,
.heading-bold-16 {
  font-size: 1rem;
  // line-height: 1.125rem;
}

.heading-ui-H2 {
  font-size: 1.125rem;
  line-height: 1.25rem;
}

.heading-medium-20,
.heading-bold-20 {
  font-size: 1.25rem;
  line-height: 1.5rem;
}

.paragraph-regular-14 {
  line-height: 1.25rem;
  color: var(--global-tertiary-TextColor);
}

.font-caps-medium-12 {
  text-transform: uppercase;
}

/* Panels */
* {
  --app-panel-width: 4.25rem;
}

.antiPanel-application {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--theme-navpanel-color);

  &.vertical {
    flex-direction: column;
    min-width: var(--app-panel-width);
    width: var(--app-panel-width);
    height: 100%;
    border-right: 1px solid var(--theme-navpanel-divider);
  }

  &.horizonatl {
    min-height: var(--app-panel-width);
    height: var(--app-panel-width);
    width: 100%;
    border-top: 1px solid var(--theme-navpanel-divider);
  }

  &.lastDivider {
    border-color: var(--theme-navpanel-border);
  }
}

.normal-font .antiPanel-application .app .icon-container.noty {
  clip-path: url(#notify-normal);
}

.small-font .antiPanel-application .app .icon-container.noty {
  clip-path: url(#notify-small);
}

.antiPanel-navigator,
.antiPanel-component {
  display: flex;
  height: 100%;
  min-height: 0;

  &.header {
    background-color: var(--theme-comp-header-color);
  }

  &.filled {
    background-color: var(--theme-bg-color);
  }

  &.filledNav {
    background-color: var(--theme-navpanel-color) !important;
  }

  &.border-left {
    border-left: 1px solid var(--theme-divider-color);
  }

  &.border-right {
    border-right: 1px solid var(--theme-divider-color);
  }
}

.antiPanel-navigator {
  position: relative;
  min-width: 12.5rem;
  max-width: 22.5rem;
  width: 17.5rem;

  &:not(.second) {
    background-color: var(--theme-navpanel-color);
  }

  &.second.float {
    background-color: var(--theme-navpanel-color);
    filter: drop-shadow(2px 0 5px rgba(0, 0, 0, .2));
    z-index: 460;

    &:not(.inner) {
      position: fixed;
      top: calc(var(--status-bar-height) + 3.5rem + 1px);
      height: calc(100% - var(--status-bar-height) - 3.5rem - 2px);
      border-left: 1px solid var(--theme-divider-color);

      &.portrait {
        left: 0;
      }

      &.landscape {
        left: var(--app-panel-width);
      }
    }

    &.inner {
      position: absolute;
      top: 3.5rem;
      left: 0;
      height: calc(100% - 3.5rem);
    }
  }

  &.fly:not(.second) {
    position: fixed;
    top: calc(var(--status-bar-height) + 1px);
    height: calc(100% - var(--status-bar-height) - 2px);
    background-color: var(--theme-navpanel-color);
    z-index: 450;
    filter: drop-shadow(2px 0 5px rgba(0, 0, 0, .2));

    &.portrait {
      left: 0;
    }

    &.landscape {
      left: var(--app-panel-width);
    }
  }
}

@media (max-width: 480px) {
  .mobile-theme {

    .mobile-wrapper,
    .antiPanel-navigator {
      overflow: hidden;
      border: 1px solid var(--theme-divider-color);
      border-radius: var(--medium-BorderRadius);
    }

    .antiPanel-navigator {
      top: var(--status-bar-height);
      height: calc(100% - var(--status-bar-height) - var(--app-panel-width));

      .antiSeparator {
        display: none;
      }

      &.fly {
        width: calc(100% - 3.5rem) !important;
        filter: var(--theme-navpanel-shadow-mobile);
      }

      &.second.float.fly {
        height: calc(100% - var(--status-bar-height) - var(--app-panel-width) - 3.5rem);
      }

      &.fly:not(.second) {
        top: var(--status-bar-height);
        height: calc(100% - var(--status-bar-height) - var(--app-panel-width));
      }
    }
  }
}

.antiPanel-component {
  overflow: hidden;
  flex-direction: column;
  flex-grow: 1;
}

.antiPanel-component.aside {
  min-width: 30rem;
  width: 30rem;
  max-width: 30rem;
}

.antiPanel-wrap__content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;

  &.hidden {
    overflow: hidden;
  }
}

/* Navigation */
.antiNav-header {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin: .75rem;
  padding: 0 .75rem;
  height: 2rem;
  font-weight: 500;
  font-size: 1.125rem;
  color: var(--theme-content-color);
}

.antiNav-subheader {
  display: flex;
  justify-content: stretch;
  align-items: stretch;
  margin: 0 .75rem .75rem;
  height: 2rem;
}

.antiNav-element {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin: 0 .75rem;
  padding: 0 .75rem;
  height: 2rem;
  min-width: 0;
  border-radius: .375rem;
  cursor: pointer;

  .an-element__icon {
    flex-shrink: 0;
    margin-right: .5rem;
    color: var(--theme-dark-color);

    &.folder {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 1.25rem;
      height: 1.25rem;
      background-color: var(--theme-navpanel-selected);
      border-radius: .25rem;
    }
  }

  .an-element__label {
    overflow: hidden;
    min-width: 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: var(--theme-content-color);

    &.title {
      font-weight: 500;
      font-size: .625rem;
      letter-spacing: 1px;
      text-transform: uppercase;
    }

    &:not(.title) {
      font-size: .8125rem;
    }

    &.bold {
      font-weight: 600;
      color: var(--theme-caption-color);
    }
  }

  .an-element__tool {
    flex-shrink: 0;
    width: 1rem;
    height: 1rem;
    border-radius: .25rem;

    &.arrow {
      color: var(--theme-trans-color);

      &.hidden {
        display: none;
      }

      &>* {
        transform-origin: center;
        transform: rotate(0deg);
        transition: transform .2s ease;
      }
    }

    &:not(.arrow) {
      display: none;
      margin-left: .5rem;
      color: var(--theme-dark-color);

      &+& {
        margin-left: .125rem;
      }
    }

    &:hover,
    &.pressed {
      color: var(--theme-caption-color);
    }

    &:hover {
      background-color: var(--theme-button-hovered);
    }

    &.pressed {
      background-color: var(--theme-button-pressed);
    }
  }

  .an-element__grow {
    flex-grow: 1;
    min-width: 0;
  }

  &.indent:not(.tree, .parent) {
    padding-left: 2.5rem;
  }

  &:not(.tree) {
    .an-element__tool.arrow {
      margin-left: .25rem;
      // background-color: transparent !important;
    }
  }

  &.tree {
    .an-element__icon {
      margin-right: .375rem;
    }

    .an-element__tool.arrow {
      margin-right: .125rem;
    }

    .an-element__tool.arrow.empty {
      background-color: transparent !important;
    }
  }

  &:not(.tree):hover,
  &:not(.tree).hovered,
  &:not(.tree).selected {

    .an-element__icon,
    .an-element__label {
      color: var(--theme-caption-color);
    }
  }

  &:hover,
  &.hovered,
  &.selected {
    .an-element__icon.folder {
      background-color: transparent;
    }

    .an-element__tool,
    .an-element__tool.hidden {
      display: block;
    }
  }

  &:hover,
  &.hovered {
    background-color: var(--theme-navpanel-hovered);
  }

  &.selected {
    background-color: var(--theme-navpanel-selected);
  }

  &.collapsed .an-element__tool.arrow>* {
    transform: rotate(-90deg);
  }

  .an-element__counter {
    margin-left: .75rem;
    font-weight: 600;
    font-size: .75rem;
    color: var(--theme-content-color);
  }

  &__dropbox {
    height: auto;
  }

  &.disabled {
    cursor: not-allowed;

    .an-element__icon {
      opacity: .5;
    }

    .an-element__label {
      color: rgb(var(--theme-caption-color) / 40%);
    }
  }
}

.antiNav-divider {
  flex-shrink: 0;
  margin: .75rem 0;
  height: 1px;

  &.line {
    background-color: var(--theme-navpanel-divider);
  }

  &.short {
    margin: .25rem 1rem;
  }
}

.antiNav-space {
  flex-shrink: 0;
  height: .5rem;

  &.x2 {
    height: 1rem;
  }
}

.antiNav-footer-line {
  flex-shrink: 0;
  width: 100%;
  height: 1px;
  background-color: var(--theme-navpanel-divider);
}

.antiNav-footer-grower {
  flex-shrink: 10;
  flex-grow: 1;
  min-height: 0;
}

.antiNav-footer {
  display: flex;
  flex-direction: column;
  padding: .5rem 0 1.25rem;
}

/* Statusbar - Popup */

/* Basic */
.antiGrid {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  min-width: 0;
  min-height: 0;

  &-row {
    display: flex;
    align-items: center;
    min-width: 0;

    &__header {
      width: 15rem;
      padding-right: 1rem;
      color: var(--theme-caption-color);

      &.withDesciption {
        display: flex;
        flex-direction: column;
        min-width: 0;
        min-height: 0;

        span {
          font-size: .75rem;
          color: var(--theme-halfcontent-color);
        }
      }

      &.topAlign {
        align-self: flex-start;
        margin-top: .75rem;
      }
    }

    .padding {
      flex-grow: 1;
      padding: .75rem 0;
    }

    &:not(:last-child) {
      margin-bottom: .5rem;
    }

    &>*:not(.padding, .topAlign) {
      margin: .25rem 0;
    }
  }
}

/* Basic */
.antiTitle {

  .icon-wrapper,
  &.icon-wrapper,
  .title-wrapper,
  &.title-wrapper {
    display: flex;
    flex-wrap: nowrap;
    min-width: 0;
  }

  .title-wrapper,
  &.title-wrapper {
    flex-direction: column;
    flex-grow: 1;
  }

  .icon-wrapper,
  &.icon-wrapper {
    align-items: center;
  }

  .wrapped-icon,
  &.wrapped-icon {
    margin-right: .75rem;
    color: var(--theme-content-color);
  }

  .wrapped-title,
  &.wrapped-title {
    min-width: 0;
    font-weight: 500;
    font-size: 1rem;
    color: var(--theme-caption-color);

    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .wrapped-subtitle,
  &.wrapped-subtitle {
    min-width: 0;
    font-size: 0.75rem;
    color: var(--theme-dark-color);

    overflow: hidden;
    visibility: visible;
    display: -webkit-box;
    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    user-select: text;

    b {
      color: var(--theme-content-color);
    }
  }
}

.antiMention,
.antiMention:visited {
  display: inline;
  font-weight: normal;
  padding: 0 .25rem;
  color: var(--theme-link-color);
  background-color: var(--theme-mention-bg-color);
  text-decoration: none;
  border-radius: .25rem;
  cursor: pointer;
  user-select: text;
  font-size: var(--body-font-size);

  &.transparent {
    background-color: transparent;

    &:hover {
      background-color: transparent;
    }
  }

  &:hover {
    text-decoration: none !important;
    background-color: var(--theme-mention-focused-bg-color);
  }

  &.broken {
    color: var(--theme-broken-mention-color);
    background-color: var(--theme-broken-mention-bg-color);
    --theme-mention-focused-bg-color: var(--theme-broken-mention-focused-bg-color);

    span {
      color: var(--theme-broken-mention-color);
    }
  }

  >svg {
    display: inline-block;
    vertical-align: sub;
    margin-bottom: 1px;
    width: .875rem;
  }
}

.antiDivider {
  margin: .25rem 0;
  min-height: 1px;
  height: 1px;
  background-color: var(--theme-divider-color);

  &.dark {
    background-color: var(--theme-bg-accent-color);
  }

  &.noMargin {
    margin: 0;
  }

  &+& {
    display: none;
  }
}

.antiHSpacer {
  flex-shrink: 0;
  width: .25rem;

  &:not(.withMargins) {
    margin: 0 !important;
  }

  &.x1-5 {
    width: .375rem;
  }

  &.x2 {
    width: .5rem;
  }

  &.x3 {
    width: .75rem;
  }

  &.x4 {
    width: 1rem;
  }
}

.antiVSpacer {
  flex-shrink: 0;
  height: .25rem;

  &:not(.withMargins) {
    margin: 0 !important;
  }

  &.x0-5 {
    height: .125rem;
  }

  &.x1-5 {
    height: .375rem;
  }

  &.x2 {
    height: .5rem;
  }

  &.x3 {
    height: .75rem;
  }

  &.x4 {
    height: 1rem;
  }

  &.x7 {
    height: 1.75rem;
  }
}

.antiSection {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  min-height: 0;

  &-header {
    display: flex;
    align-items: center;
    height: 2.5rem;
    min-height: 2.5rem;
    border-bottom: 1px solid var(--theme-divider-color);

    &.high {
      padding-right: 1rem;
      height: 3.5rem;
      min-height: 3.5rem;
    }

    &.spaceBeforeContent {
      margin-bottom: .75rem;
    }

    &__icon {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: .5rem;
      height: 2rem;
      color: var(--theme-caption-color);
    }

    &__title {
      min-width: 0;
      font-size: 1rem;
      color: var(--caption-color);

      &:not(.short) {
        flex-grow: 1;
      }
    }

    &__header {
      display: flex;
      align-items: center;
      flex-grow: 1;
      margin: 0 .5rem 0 .75rem;
      padding: .25rem .75rem;
      height: 100%;
      min-width: 0;
      font-weight: 500;
      font-size: 1rem;
      color: var(--theme-caption-color);
      background: var(--header-bg-color);
      border-radius: .5rem .5rem 0 0;
    }

    &__counter {
      color: var(--theme-darker-color);
    }

    &__tag {
      display: flex;
      align-items: center;
      padding: .3125rem .5rem;
      min-width: 0;
      font-size: .875rem;
      background-color: var(--theme-button-default);
      color: var(--theme-halfcontent-color);
      border: 1px solid var(--theme-button-border);
      border-radius: .25rem;

      .tag-icon {
        margin-left: .5rem;
        width: 1rem;
        height: 1rem;
        color: var(--theme-content-color);

        &:hover {
          color: var(--theme-caption-color);
        }
      }

      &.highlight {
        color: var(--theme-content-color);
      }
    }

    &__tag+&__tag {
      margin-left: .375rem;
    }
  }

  .invisible {
    display: none;
  }

  &-empty {
    display: flex;
    min-width: 0;
    min-height: 0;

    &:not(.none-appearance) {
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      padding: 1rem;
      font-size: .75rem;
      color: var(--dark-color);
      border: 1px dashed var(--divider-color);
      border-radius: 0.75rem;
    }

    &.none-appearance {
      flex-direction: column;
    }

    &.solid {
      border-style: solid;
    }

    &.noBorder {
      border: none;
    }

    &.items {
      justify-content: start;
      padding: .75rem;
    }

    &.solid.attachments {
      background-color: var(--accent-bg-color);

      .item {
        border-style: solid;
      }
    }
  }
}

:is(.antiSection, .step-tb-6, .mt-6)+ :is(.antiSection, .step-tb-6) {
  margin-top: 1.5rem;
}

// Button on selected card in Kanban
.card-container.checked .button.inline.link-bordered {
  background-color: var(--highlight-select);
  border-color: var(--highlight-select-border);

  &:hover {
    background-color: var(--highlight-select-hover);
    border-color: var(--highlight-select-border);
  }
}

// Indented
.antiIndented {
  margin: .75rem;
  border-radius: .25rem;

  &:hover,
  &.focusable:focus-within {
    border-color: var(--theme-divider-color);
  }
}

// Emphasized
.antiEmphasized {
  padding: .75rem;
  background-color: var(--theme-comp-header-color);
  border: 1px solid var(--theme-popup-divider);
  border-radius: .25rem;
  transition-property: border, background-color;
  transition-duration: .15s;
  transition-timing-function: var(--timing-main);

  &:hover,
  &.focusable:focus-within {
    // background-color: var(--theme-bg-color);
    border-color: var(--theme-list-divider-color);
  }
}

// Accordion
.antiAccordion {
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 0;

  .description {
    // overflow: hidden;
    padding: 0.75rem;
    background-color: var(--theme-bg-color);
    border: 1px solid var(--theme-divider-color);
    // border-radius: .5rem;
    transition-property: background-color, height;
    transition-duration: .15s;
    transition-timing-function: var(--timing-main);

    .label {
      color: var(--theme-dark-color);
    }

    .caption {
      display: flex;
      align-items: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin: -.5rem;
      padding: .5rem .5rem .5rem 1rem;
      margin-bottom: 1rem;
      min-width: 0;

      font-weight: 500;
      font-size: 1rem;
      color: var(--theme-caption-color);
      background-color: var(--theme-comp-header-color);
      border: 1px solid transparent;
      border-radius: .125rem;
      transition: margin-bottom .15s var(--timing-main),
        border-radius .3s var(--timing-main),
        box-shadow .15s var(--timing-main);
      box-shadow: 0 0 .25rem .125rem #00000020;
      // cursor: pointer;
      z-index: 1;

      .value {
        overflow: auto;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        margin: 0 .5rem;
        max-height: 1.5rem;
        font-weight: 400;
        transition: opacity .15s var(--timing-main);

        &::-webkit-scrollbar:vertical {
          width: 0;
        }

        &::-webkit-scrollbar:horizontal {
          height: 0;
        }
      }

      .rotated-icon {
        transform-origin: center;
        transition: transform .15s var(--timing-main);

        &.opened {
          transform: rotate(0deg);
        }

        &.closed {
          transform: rotate(90deg);
        }
      }
    }

    &.opened {
      .caption .value {
        opacity: 0;
      }

      .expand-collapse .expand-collapse,
      .expand-collapse {
        visibility: visible;
        max-height: max-content;
      }
    }

    &.closed {
      .caption {
        margin-bottom: -.5rem;

        .value {
          opacity: 1;
        }

        &.hasAttachments {
          margin-bottom: 0;
        }
      }

      .expand-collapse .expand-collapse,
      .expand-collapse:not(.hasAttachments) {
        overflow: hidden;
        visibility: hidden;
        max-height: 0;
      }

      &:hover .caption.hasAttachments {
        margin-bottom: .5rem;
      }
    }

    &:first-child {
      border-top-left-radius: .75rem;
      border-top-right-radius: .75rem;
    }

    &:first-child .caption {
      border-top-left-radius: .65rem;
      border-top-right-radius: .65rem;
    }

    &:last-child {
      border-bottom-left-radius: .75rem;
      border-bottom-right-radius: .75rem;
    }

    &:last-child.closed .caption {
      border-bottom-left-radius: .65rem;
      border-bottom-right-radius: .65rem;
    }

    &:last-child:not(:first-child),
    &:not(:first-child):not(:last-child) {
      border-top: none;
    }

    &:hover,
    &:focus-within {
      background-color: var(--theme-bg-color);
    }

    // &:focus-within .caption { box-shadow: 0 0 2px 1px var(--primary-button-outline); }
    &:focus-within .caption {
      border-color: var(--primary-button-outline);
    }
  }
}

// Consecutive messages in Telegram (incomung/outcoming)
.message-row-bg[data-type="in"]+.message-row-bg[data-type="in"] .message-row .message-container {
  padding-top: 0;

  .message {
    border-radius: 0.125rem 0.75rem 0.75rem 0.125rem;
  }
}

.message-row-bg[data-type="out"]+.message-row-bg[data-type="out"] .message-row .message-container {
  padding-top: 0;

  .message.outcoming {
    border-radius: 0.75rem 0.125rem 0.125rem 0.75rem;
  }
}

.emoji-system {
  // Uses system default emoji font
}

.emoji-noto {
  .emoji {
    font-family: "Noto Color Emoji";
  }
}

.emoji {
  &.center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.emoji>img {
  display: inline-block;
  vertical-align: sub;
  height: 1.3em;
  width: auto;
  margin: 0 0.05em 0 0.1em;
  padding: 0;
  line-height: 1;
}

.emoji.noMargin>img {
  margin: 0;
}

.emoji.fitSize>img {
  height: 1em;
}

.editor-grid {
  display: grid;
  grid-template-columns: 1fr 3fr;
  grid-auto-rows: minmax(2rem, max-content);
  justify-content: start;
  align-items: center;
  row-gap: 0.5rem;
  margin: 0.25rem 2rem 0;
  width: calc(100% - 4rem);
  column-gap: 1rem;
  margin-top: 0.5rem;
  height: min-content;
}