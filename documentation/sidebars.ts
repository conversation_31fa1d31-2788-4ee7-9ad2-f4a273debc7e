import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */
const sidebars: SidebarsConfig = {
  // Main documentation sidebar
  docs: [
    'intro',
    'quick-reference',
    'best-practices',
    'glossary',
    {
      type: 'category',
      label: 'Getting Started',
      items: [
        'getting-started/installation',
        'getting-started/configuration',
        'getting-started/daily-workflow',
        'getting-started/first-workspace',
      ],
    },
    {
      type: 'category',
      label: 'User Guide',
      items: [
        'user-guide/workspaces',
        'user-guide/projects',
        'user-guide/documents',
        'user-guide/issues',
      ],
    },
    {
      type: 'category',
      label: 'Development',
      items: [
        'development/plugin-development',
        'development/plugin-recipes',
        'development/plugin-debugging',
        'development/ui-components',
        'development/querying-data',
        'development/testing-guide',
      ],
    },
    {
      type: 'category',
      label: 'Deployment',
      items: ['deployment/production-deployment'],
    },
  ],

  // Architecture sidebar
  architecture: [
    'architecture/overview',
    {
      type: 'category',
      label: 'Model Architecture',
      items: [
        'architecture/model/core-concepts',
        'architecture/model/workspaces',
        'architecture/model/spaces',
        'architecture/model/typed-spaces',
        'architecture/model/roles-permissions',
        'architecture/model/transactions',
        'architecture/model/mixins',
        'architecture/model/domains',
        'architecture/model/builder',
      ],
    },
    {
      type: 'category',
      label: 'System Architecture',
      items: [
        'architecture/plugin-system',
        'architecture/client-server',
        'architecture/migrations',
        'architecture/activity-notifications',
        'architecture/server-triggers',
        'architecture/search-indexing',
        'architecture/blob-storage',
        'architecture/collaborative-editing',
        'architecture/authentication',
        'architecture/ai-system',
        'architecture/services-pods',
      ],
    },
    {
      type: 'category',
      label: 'Technical Implementation',
      items: [
        'architecture/technical/storage',
        'architecture/technical/server',
        'architecture/technical/client',
      ],
    },
  ],
};

export default sidebars;
