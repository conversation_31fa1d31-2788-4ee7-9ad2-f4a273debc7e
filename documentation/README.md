# HULY Documentation

This directory contains the official documentation for HULY, built with [Docusaurus](https://docusaurus.io/).

## 🚀 Quick Start

### Installation

```bash
cd documentation
npm install
```

### Local Development

```bash
npm start
```

This command starts a local development server and opens up a browser window. Most changes are reflected live without having to restart the server.

The documentation will be available at: `http://localhost:3000`

### Build

```bash
npm run build
```

This command generates static content into the `build` directory and can be served using any static contents hosting service.

### Serve Locally

```bash
npm run serve
```

This command serves the built website locally.

## 📚 Documentation Structure

```
docs/
├── intro.md                          # Introduction and overview
├── getting-started/                  # Getting started guides
│   ├── installation.md
│   ├── configuration.md
│   └── first-workspace.md
├── user-guide/                       # End-user documentation
│   ├── workspaces.md
│   ├── projects.md
│   ├── documents.md
│   └── issues.md
└── architecture/                     # Technical architecture
    ├── overview.md
    ├── model/                        # Model architecture
    │   ├── core-concepts.md
    │   ├── workspaces.md
    │   ├── spaces.md
    │   ├── typed-spaces.md
    │   ├── roles-permissions.md
    │   ├── transactions.md
    │   ├── mixins.md
    │   ├── domains.md
    │   └── builder.md
    └── technical/                    # Technical implementation
        ├── storage.md
        ├── server.md
        └── client.md
```

## ✍️ Contributing to Documentation

### Adding a New Page

1. Create a new `.md` file in the appropriate directory under `docs/`
2. Add front matter if needed:
   ```markdown
   ---
   title: My Page Title
   description: Page description
   ---
   ```
3. Update `sidebars.ts` to include the new page in the navigation

### Updating the Sidebar

Edit `sidebars.ts` to modify the documentation structure:

```typescript
const sidebars: SidebarsConfig = {
  docs: [
    'intro',
    {
      type: 'category',
      label: 'My Category',
      items: ['my-category/page-1', 'my-category/page-2']
    }
  ]
}
```

### Writing Guidelines

- Use clear, concise language
- Include code examples where appropriate
- Add diagrams for complex concepts (use Mermaid or ASCII art)
- Link to related pages
- Keep paragraphs short and scannable

### Code Examples

Use syntax highlighting for code blocks:

````markdown
```typescript
const example: SomeType = {
  property: 'value'
}
```
````

### Diagrams

You can use ASCII art for simple diagrams:

```
┌──────────────┐
│   Client     │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│   Server     │
└──────────────┘
```

Or Mermaid for more complex diagrams:

````markdown
```mermaid
graph TD
    A[Client] --> B[Server]
    B --> C[Database]
```
````

## 🎨 Customization

### Config

The main configuration file is `docusaurus.config.ts`. Here you can modify:

- Site metadata (title, tagline, etc.)
- Navigation bar
- Footer
- Theme settings

### Styling

Custom styles can be added in `src/css/custom.css`.

### Components

React components are in `src/components/`. You can create new components for reusable documentation elements.

## 📖 Documentation Sections

### Getting Started

Guides for installing and setting up HULY for the first time.

### User Guide

End-user documentation for using HULY's features.

### Architecture

Technical documentation about HULY's architecture:

- **Model Architecture**: The data model and object system
- **Technical Architecture**: Server, client, and storage systems

## 🔗 Useful Links

- [Docusaurus Documentation](https://docusaurus.io/)
- [Markdown Guide](https://www.markdownguide.org/)
- [HULY GitHub Repository](https://github.com/hcengineering/platform)

## 📝 License

Copyright © 2025 Hardcore Engineering Inc.

This documentation is part of the HULY project, licensed under the Eclipse Public License, Version 2.0.
