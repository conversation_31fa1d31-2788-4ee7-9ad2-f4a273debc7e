# Daily Development Workflow

Learn the essential commands and workflows for daily HULY development.

## Starting Your Day

### 1. Pull Latest Changes

```bash
git pull origin main
```

### 2. Update Dependencies

After pulling changes, **always** update dependencies:

```bash
rush update
```

**Why?** Because:

- Package dependencies might have changed
- New packages might have been added
- Lock file might have updated

**When to run:**

- ✅ After `git pull`
- ✅ After switching branches
- ✅ After merging
- ✅ When you see "missing dependencies" errors

### 3. Rebuild Changed Packages

```bash
rush build
```

**Faster option** - only rebuild changed packages:

```bash
rush build --verbose
```

**Even faster** - rebuild only specific packages:

```bash
# Rebuild just the tracker plugin
cd plugins/tracker
rushx build

# Or from repo root
rush build --to @hcengineering/tracker
```

### 4. Start Development

```bash
# Start everything (server + client)
rushx dev

# Or separately:
rushx dev-server  # Terminal 1
rushx dev-client  # Terminal 2
```

## Common Commands

### Rush Commands

#### Update Dependencies

```bash
# Update all packages
rush update

# Force update (if lock file is corrupted)
rush update --full

# Update and recheck (verifies consistency)
rush update --recheck
```

#### Build

```bash
# Build all packages
rush build

# Build only changed packages
rush build --verbose

# Build specific package and its dependencies
rush build --to <package-name>

# Build from specific package onwards
rush build --from <package-name>

# Clean build (remove all build artifacts first)
rush clean && rush build
```

#### Validate

```bash
# Validate TypeScript types across entire workspace
rush validate
```

This checks TypeScript without building - faster for catching type errors.

#### Lint

```bash
# Run linter on all packages
rush lint

# Run linter on specific package
cd plugins/tracker
rushx lint
```

#### Clean

```bash
# Remove all build artifacts
rush clean

# Clean specific package
cd plugins/tracker
rushx clean
```

### Development Scripts

#### Start Development Server

```bash
# All-in-one (server + client)
rushx dev

# Server only
rushx dev-server

# Client only
rushx dev-client

# Production mode
rushx dev-production
```

#### Database Operations

```bash
# Create a workspace
rushx workspace-tool create-workspace \
  --name "Test Workspace" \
  --email <EMAIL> \
  --password test123

# Upgrade workspace to latest model
rushx workspace-tool upgrade \
  --workspace <workspace-id>

# Backup workspace
rushx workspace-tool backup \
  --workspace <workspace-id> \
  --output backup.tar.gz

# Restore workspace
rushx workspace-tool restore \
  --workspace <workspace-id> \
  --input backup.tar.gz
```

#### Testing

```bash
# Run all tests
rush test

# Run tests for specific package
cd plugins/tracker
rushx test

# Run tests in watch mode
rushx test:watch

# Run integration tests
cd tests
rushx test:integration
```

## After Git Pull Workflow

**Complete workflow after pulling changes:**

```bash
# 1. Pull latest
git pull origin main

# 2. Update dependencies (IMPORTANT!)
rush update

# 3. Rebuild (only if needed)
rush build

# 4. Restart services if needed
docker-compose restart  # If service configs changed

# 5. Upgrade workspace if model changed
rushx workspace-tool upgrade --workspace <workspace-id>

# 6. Start development
rushx dev
```

**Quick version:**

```bash
git pull && rush update && rush build && rushx dev
```

## When Things Break

### "Module not found" errors

```bash
# Solution: Update dependencies
rush update

# If still broken, clean and rebuild
rush clean
rush build
```

### Build errors after pull

```bash
# Clean everything and rebuild
rush clean
rush update --full
rush build
```

### TypeScript errors

```bash
# Validate types
rush validate

# If errors, might need to rebuild dependencies
rush build --to <failing-package>
```

### Database errors

```bash
# Reset database (WARNING: destroys data)
docker-compose down -v
docker-compose up -d

# Wait for PostgreSQL to be ready
sleep 10

# Recreate workspace
rushx workspace-tool create-workspace ...
```

### Docker issues

```bash
# Restart all services
docker-compose restart

# Stop and remove containers
docker-compose down

# Start fresh
docker-compose up -d

# View logs
docker-compose logs -f postgres
docker-compose logs -f minio
```

## Working on Specific Features

### Working on a Plugin

```bash
# 1. Navigate to plugin
cd plugins/tracker-resources

# 2. Start watch mode (auto-rebuild on changes)
rushx dev

# 3. In another terminal, run the dev server
cd ../..
rushx dev-server
```

**Watch mode rebuilds automatically when you save files!**

### Working on Models

```bash
# 1. Edit model
vim models/tracker/src/index.ts

# 2. Rebuild model
cd models/tracker
rushx build

# 3. Rebuild packages that depend on it
cd ../..
rush build --from @hcengineering/model-tracker

# 4. Upgrade workspace to new model
rushx workspace-tool upgrade
```

### Working on Server Code

```bash
# 1. Edit server code
vim server/server/src/server.ts

# 2. Rebuild server
cd server/server
rushx build

# 3. Restart server
cd ../..
rushx dev-server
```

## Package Scripts

Each package has its own scripts. Common ones:

```bash
# In any package directory:

# Build
rushx build

# Watch (auto-rebuild)
rushx dev

# Clean
rushx clean

# Lint
rushx lint

# Test
rushx test

# Type check
rushx validate
```

## Performance Tips

### Faster Builds

```bash
# Only build what changed
rush build --verbose

# Parallel builds (use all CPU cores)
rush build -p

# Build specific package
rush build --to @hcengineering/tracker

# Skip validation (faster, but risky)
rush build --no-validate
```

### Faster Dependency Updates

```bash
# Use pnpm store (first time setup)
rush update --purge

# Subsequent updates are faster with cached packages
```

### Development Server Tips

```bash
# Use production mode for better performance
rushx dev-production

# Disable source maps for faster builds
export NO_SOURCE_MAPS=true
rushx dev
```

## Essential Environment Variables

```bash
# Database
export DB_URL=postgresql://postgres:example@localhost:5432/huly

# Storage
export STORAGE_CONFIG=minio
export MINIO_ENDPOINT=localhost
export MINIO_ACCESS_KEY=minioadmin
export MINIO_SECRET_KEY=minioadmin

# Server
export SERVER_PORT=3333
export ACCOUNTS_URL=http://localhost:3000

# Development
export DEBUG=true
export ENABLE_CONSOLE_LOG=true

# Performance
export NODE_OPTIONS="--max-old-space-size=8192"
```

Save these in `.env` file in repo root.

## Troubleshooting Commands

### Check what changed

```bash
# See changed packages
rush list --verbose

# See which packages will rebuild
rush build --verbose
```

### Inspect dependencies

```bash
# Show dependency tree
rush list --verbose

# Check for circular dependencies
rush check
```

### View rush logs

```bash
# Last build log
cat common/temp/rush.log

# Package-specific build log
cat <package>/temp/build.log
```

## Git Workflow Integration

### Before Committing

```bash
# 1. Ensure everything builds
rush build

# 2. Run linter
rush lint

# 3. Run tests
rush test

# 4. Validate types
rush validate

# All-in-one
rush build && rush lint && rush test && rush validate
```

### After Pull Request Merge

```bash
# 1. Switch to main
git checkout main

# 2. Pull latest
git pull origin main

# 3. Update deps
rush update

# 4. Clean build
rush clean && rush build

# 5. Restart dev
rushx dev
```

### Switching Branches

```bash
# 1. Switch branch
git checkout feature/my-feature

# 2. Always update after switch
rush update

# 3. Build if needed
rush build
```

## Monorepo Structure

Understanding the structure helps with navigation:

```
platform/
├── common/           # Rush configuration
├── dev/              # Docker compose, dev tools
├── models/           # Data model definitions
├── packages/         # Core packages (platform, core, etc.)
├── plugins/          # Plugin packages (tracker, document, etc.)
├── server/           # Server packages
├── server-plugins/   # Server-side plugin logic
├── services/         # Microservices
├── tests/            # Integration tests
├── desktop/          # Desktop app
└── rush.json         # Rush configuration
```

## Best Practices

### ✅ Do's

- Always run `rush update` after `git pull`
- Use `rush build --verbose` to see what's building
- Clean build if weird errors: `rush clean && rush build`
- Use watch mode when developing: `rushx dev`
- Restart services after pulling service changes
- Check Docker logs if services misbehave

### ❌ Don'ts

- Don't run `npm install` directly (use `rush update`)
- Don't commit without running `rush build`
- Don't skip `rush update` after pull
- Don't modify lock files manually
- Don't run multiple build processes simultaneously
- Don't ignore TypeScript errors

## Summary

**Daily workflow:**

1. `git pull` - Get latest changes
2. `rush update` - Update dependencies
3. `rush build` - Rebuild if needed
4. `rushx dev` - Start development

**When stuck:**

1. `rush clean` - Clean build artifacts
2. `rush update --full` - Full dependency update
3. `rush build` - Fresh build
4. `docker-compose restart` - Restart services

**Essential commands:**

- `rush update` - Update dependencies
- `rush build` - Build all packages
- `rush validate` - Check TypeScript
- `rushx dev` - Start development
- `rush clean` - Clean artifacts

You're now equipped for daily HULY development! 💪
