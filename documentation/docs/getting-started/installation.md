# Installation

Complete guide to installing and setting up HULY for local development.

## Prerequisites

Before installing HUL<PERSON>, ensure you have the following installed:

### Required

- **Node.js** - v18.x or v20.x (v20.x recommended)

  ```bash
  node --version  # Should be v18.x or v20.x
  ```

- **npm** - v9.x or higher

  ```bash
  npm --version
  ```

- **Rush** - Microsoft's monorepo manager

  ```bash
  npm install -g @microsoft/rush
  ```

- **Docker** - For running services (PostgreSQL, MinIO, Elasticsearch)
  ```bash
  docker --version
  docker-compose --version
  ```

### Optional but Recommended

- **Git** - For version control
- **VSCode** - Recommended IDE with TypeScript support
- **PostgreSQL Client** - For database inspection (psql, pgAdmin, DBeaver)

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/hcengineering/platform.git
cd platform
```

Or if you have SSH configured:

```bash
<NAME_EMAIL>:hcengineering/platform.git
cd platform
```

### 2. Install Dependencies

HULY uses Rush for monorepo management:

```bash
# Install all dependencies for all packages
rush update
```

**What this does:**

- Installs dependencies for all packages in the monorepo
- Creates symlinks between local packages
- Generates `common/config/rush/pnpm-lock.yaml`
- Sets up the workspace

**First time?** This will take 5-10 minutes.

### 3. Build All Packages

```bash
# Build all packages in dependency order
rush build
```

**What this does:**

- Compiles TypeScript to JavaScript
- Builds all packages in the correct order (respecting dependencies)
- Generates type definitions
- Builds Svelte components

**First time?** This will take 10-15 minutes.

### 4. Start Development Services

HULY requires several services to run. Use Docker Compose:

```bash
# Start PostgreSQL, MinIO, and other services
cd dev/
docker-compose up -d
```

**Services started:**

- **PostgreSQL** (port 5432) - Main database
- **MinIO** (port 9000) - Object storage (S3-compatible)
- **Elasticsearch** (port 9200) - Full-text search
- **MongoDB** (optional, port 27017) - Alternative database

**Check services are running:**

```bash
docker-compose ps
```

### 5. Initialize Database

Create the initial workspace and database:

```bash
cd ../  # Back to repo root

# Set environment variables
export DB_URL=postgresql://postgres:example@localhost:5432/huly
export STORAGE_CONFIG=minio

# Initialize the database
rushx workspace-tool create-workspace \
  --name "My Workspace" \
  --email <EMAIL> \
  --password admin123
```

This creates:

- Database schema
- Initial workspace
- Admin user account
- Default data (space types, permissions, etc.)

### 6. Start Development Server

Start the HULY server and client:

```bash
# Terminal 1 - Start the server
rushx dev-server

# Terminal 2 - Start the client (in a new terminal)
rushx dev-client
```

**Or use the all-in-one command:**

```bash
rushx dev
```

### 7. Access HULY

Open your browser and navigate to:

```
http://localhost:8080
```

**Login with:**

- Email: `<EMAIL>`
- Password: `admin123`

## Quick Start Script

For convenience, here's a complete setup script:

```bash
#!/bin/bash

# Install Rush globally
npm install -g @microsoft/rush

# Clone repository
git clone https://github.com/hcengineering/platform.git
cd platform

# Install dependencies
rush update

# Build all packages
rush build

# Start services
cd dev/
docker-compose up -d
cd ../

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Set environment
export DB_URL=postgresql://postgres:example@localhost:5432/huly
export STORAGE_CONFIG=minio

# Create workspace (adjust email/password as needed)
rushx workspace-tool create-workspace \
  --name "Development" \
  --email <EMAIL> \
  --password dev123

# Start development
rushx dev
```

Save this as `setup.sh`, make it executable with `chmod +x setup.sh`, and run `./setup.sh`.

## Verification

After installation, verify everything works:

### Check Services

```bash
# PostgreSQL
psql postgresql://postgres:example@localhost:5432/huly -c "SELECT 1;"

# MinIO (S3)
curl http://localhost:9000/minio/health/live

# Elasticsearch
curl http://localhost:9200/_cluster/health
```

### Check HULY Server

```bash
# Server should respond
curl http://localhost:3333/api/v1/version
```

### Check HULY Client

```bash
# Client should load
curl http://localhost:8080
```

## Common Issues

### Port Already in Use

If ports are already in use, modify `dev/docker-compose.yml`:

```yaml
services:
  postgres:
    ports:
      - '5433:5432' # Use 5433 instead of 5432
```

Then update `DB_URL`:

```bash
export DB_URL=postgresql://postgres:example@localhost:5433/huly
```

### Build Failures

If build fails:

```bash
# Clean build artifacts
rush clean

# Rebuild
rush build
```

### Docker Not Starting

Ensure Docker daemon is running:

```bash
# Linux/Mac
sudo systemctl start docker

# Or restart Docker Desktop on Mac/Windows
```

### Permission Errors

On Linux, you may need to run Docker without sudo:

```bash
sudo usermod -aG docker $USER
newgrp docker
```

## Next Steps

- [Configuration Guide](configuration) - Configure environment variables
- [Creating Your First Workspace](first-workspace) - Workspace setup
- [Daily Workflow](daily-workflow) - Daily development tasks

## Platform-Specific Notes

### macOS

No special configuration needed. Just ensure:

- Xcode Command Line Tools are installed: `xcode-select --install`
- Docker Desktop is running

### Linux

Install additional dependencies:

```bash
# Ubuntu/Debian
sudo apt-get install build-essential python3

# Fedora/RHEL
sudo dnf install gcc-c++ make python3
```

### Windows

Use WSL2 (Windows Subsystem for Linux):

1. Install WSL2: https://docs.microsoft.com/en-us/windows/wsl/install
2. Install Ubuntu from Microsoft Store
3. Follow Linux installation steps inside WSL2
4. Use Docker Desktop with WSL2 backend

**Or use Git Bash/PowerShell:**

- Install Node.js from nodejs.org
- Install Docker Desktop
- Use Git Bash for commands

## Summary

Installation checklist:

- ✅ Install Node.js v18+ and npm
- ✅ Install Rush globally
- ✅ Clone repository
- ✅ Run `rush update` to install dependencies
- ✅ Run `rush build` to build packages
- ✅ Start Docker services with `docker-compose up -d`
- ✅ Create initial workspace
- ✅ Start dev server with `rushx dev`
- ✅ Access http://localhost:8080

You're now ready to develop with HULY! 🚀
