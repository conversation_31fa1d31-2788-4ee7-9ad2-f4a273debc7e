# Configuration

Complete guide to configuring HULY for development and production environments.

## Environment Variables

HULY uses environment variables for configuration. Create a `.env` file in the repository root:

```bash
# Create .env file
touch .env
```

### Core Configuration

```bash
# ============================================
# DATABASE
# ============================================

# PostgreSQL connection string
DB_URL=postgresql://postgres:example@localhost:5432/huly

# Alternative: MongoDB (if using MongoDB instead)
# MONGO_URL=mongodb://localhost:27017

# ============================================
# STORAGE
# ============================================

# Storage backend (minio, s3, or mongodb)
STORAGE_CONFIG=minio

# MinIO configuration (for local development)
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# S3 configuration (for production)
# STORAGE_CONFIG=s3
# S3_REGION=us-east-1
# S3_BUCKET=huly-storage
# AWS_ACCESS_KEY_ID=your-key
# AWS_SECRET_ACCESS_KEY=your-secret

# ============================================
# SERVER
# ============================================

# Server port
SERVER_PORT=3333

# Account service URL
ACCOUNTS_URL=http://localhost:3000

# Transactor URL (WebSocket)
TRANSACTOR_URL=ws://localhost:3333

# Upload size limit (in MB)
UPLOAD_SIZE_LIMIT=100

# ============================================
# ELASTICSEARCH (Full-text search)
# ============================================

ELASTIC_URL=http://localhost:9200

# Authentication (if enabled)
# ELASTIC_USERNAME=elastic
# ELASTIC_PASSWORD=changeme

# ============================================
# DEVELOPMENT
# ============================================

# Enable debug mode
DEBUG=true

# Enable console logging
ENABLE_CONSOLE_LOG=true

# Log level (error, warn, info, debug)
LOG_LEVEL=debug

# ============================================
# PERFORMANCE
# ============================================

# Node.js memory limit (in MB)
NODE_OPTIONS=--max-old-space-size=8192

# Worker threads
WORKER_THREADS=4

# ============================================
# SECURITY
# ============================================

# JWT secret (change in production!)
SECRET=your-secret-key-change-in-production

# Token expiration (in seconds)
TOKEN_EXPIRATION=86400

# ============================================
# BRANDING (Optional)
# ============================================

# Custom branding
BRANDING=huly

# Front URL (for emails)
FRONT_URL=http://localhost:8080

# ============================================
# EMAIL (Optional)
# ============================================

# SMTP configuration
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM=<EMAIL>

# ============================================
# INTEGRATIONS (Optional)
# ============================================

# GitHub integration
# GITHUB_CLIENT_ID=your-github-client-id
# GITHUB_CLIENT_SECRET=your-github-client-secret

# Gmail integration
# GMAIL_CLIENT_ID=your-gmail-client-id
# GMAIL_CLIENT_SECRET=your-gmail-client-secret

# Telegram bot
# TELEGRAM_BOT_TOKEN=your-telegram-bot-token
```

## Docker Services Configuration

Configure services in `dev/docker-compose.yml`:

### PostgreSQL

```yaml
postgres:
  image: postgres:15
  environment:
    POSTGRES_DB: huly
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: example
  ports:
    - '5432:5432'
  volumes:
    - postgres_data:/var/lib/postgresql/data
```

**Custom configuration:**

```yaml
postgres:
  environment:
    POSTGRES_DB: my_custom_db
    POSTGRES_USER: my_user
    POSTGRES_PASSWORD: my_secure_password
  ports:
    - '5433:5432' # Use port 5433 on host
```

Then update `.env`:

```bash
DB_URL=postgresql://my_user:my_secure_password@localhost:5433/my_custom_db
```

### MinIO (S3-compatible storage)

```yaml
minio:
  image: minio/minio
  command: server /data --console-address ":9001"
  environment:
    MINIO_ROOT_USER: minioadmin
    MINIO_ROOT_PASSWORD: minioadmin
  ports:
    - '9000:9000' # API
    - '9001:9001' # Console
  volumes:
    - minio_data:/data
```

**Access MinIO Console:**

- URL: http://localhost:9001
- Username: `minioadmin`
- Password: `minioadmin`

### Elasticsearch

```yaml
elasticsearch:
  image: elasticsearch:8.11.0
  environment:
    - discovery.type=single-node
    - xpack.security.enabled=false
    - 'ES_JAVA_OPTS=-Xms512m -Xmx512m'
  ports:
    - '9200:9200'
  volumes:
    - elastic_data:/usr/share/elasticsearch/data
```

**For production, enable security:**

```yaml
elasticsearch:
  environment:
    - xpack.security.enabled=true
    - ELASTIC_PASSWORD=your-strong-password
```

## Configuration Files

### rush.json

Rush configuration for the monorepo. **Don't modify unless you know what you're doing.**

Key sections:

```json
{
  "rushVersion": "5.117.0",
  "pnpmVersion": "8.15.4",
  "projects": [
    {
      "packageName": "@hcengineering/platform",
      "projectFolder": "packages/platform"
    }
    // ... more projects
  ]
}
```

### tsconfig.json

TypeScript configuration. Located in each package:

```json
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "outDir": "lib",
    "rootDir": "src"
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "lib"]
}
```

### package.json (per package)

Each package has its own `package.json`:

```json
{
  "name": "@hcengineering/tracker",
  "version": "0.6.0",
  "scripts": {
    "build": "compile",
    "dev": "compile --watch",
    "lint": "eslint src",
    "test": "jest"
  },
  "dependencies": {
    "@hcengineering/core": "^0.6.0",
    "@hcengineering/platform": "^0.6.0"
  }
}
```

## Development vs Production

### Development Configuration

```bash
# .env.development
DEBUG=true
ENABLE_CONSOLE_LOG=true
LOG_LEVEL=debug
NODE_ENV=development

# Use local services
DB_URL=postgresql://postgres:example@localhost:5432/huly
STORAGE_CONFIG=minio
MINIO_ENDPOINT=localhost
```

### Production Configuration

```bash
# .env.production
DEBUG=false
ENABLE_CONSOLE_LOG=false
LOG_LEVEL=warn
NODE_ENV=production

# Use production services
DB_URL=postgresql://user:<EMAIL>:5432/huly
STORAGE_CONFIG=s3
S3_REGION=us-east-1
S3_BUCKET=huly-prod-storage
AWS_ACCESS_KEY_ID=PROD_KEY
AWS_SECRET_ACCESS_KEY=PROD_SECRET

# Security
SECRET=very-long-random-secret-change-this
TOKEN_EXPIRATION=3600

# Performance
NODE_OPTIONS=--max-old-space-size=16384
WORKER_THREADS=16
```

## Region Configuration

For multi-region deployment:

```bash
# Region 1 (US East)
export REGION=us-east
export DB_URL=postgresql://user:<EMAIL>/huly
export S3_REGION=us-east-1

# Region 2 (EU West)
export REGION=eu-west
export DB_URL=postgresql://user:<EMAIL>/huly
export S3_REGION=eu-west-1
```

## Workspace Configuration

Configure individual workspaces:

```bash
# Create workspace with specific settings
rushx workspace-tool create-workspace \
  --name "My Company" \
  --region "us-east" \
  --branding "custom-brand" \
  --email <EMAIL> \
  --password secure-password

# Allow guest access
rushx workspace-tool update-workspace \
  --workspace <workspace-id> \
  --allow-guest true

# Set workspace region
rushx workspace-tool update-workspace \
  --workspace <workspace-id> \
  --region "eu-west"
```

## Logging Configuration

### Console Logging

```bash
# Enable all logs
export ENABLE_CONSOLE_LOG=true
export LOG_LEVEL=debug

# Specific subsystems
export DEBUG=huly:*           # All HULY logs
export DEBUG=huly:server:*    # Server logs only
export DEBUG=huly:storage:*   # Storage logs only
```

### File Logging

Configure in code or via environment:

```bash
# Log to file
export LOG_FILE=/var/log/huly/server.log

# Log rotation
export LOG_MAX_SIZE=100M
export LOG_MAX_FILES=10
```

## Performance Tuning

### Node.js Options

```bash
# Increase memory (for large workspaces)
export NODE_OPTIONS="--max-old-space-size=16384"

# Enable garbage collection logging
export NODE_OPTIONS="--max-old-space-size=8192 --trace-gc"

# Optimize for production
export NODE_OPTIONS="--max-old-space-size=16384 --max-semi-space-size=128"
```

### Database Connection Pool

```bash
# PostgreSQL
export DB_POOL_MIN=2
export DB_POOL_MAX=10
export DB_POOL_IDLE_TIMEOUT=30000
```

### Worker Threads

```bash
# Number of worker threads for parallel processing
export WORKER_THREADS=8  # Set to number of CPU cores
```

## Security Configuration

### JWT Tokens

```bash
# Secret for signing tokens (CHANGE IN PRODUCTION!)
export SECRET=$(openssl rand -base64 32)

# Token expiration
export TOKEN_EXPIRATION=86400  # 24 hours in seconds

# Refresh token expiration
export REFRESH_TOKEN_EXPIRATION=604800  # 7 days
```

### CORS

```bash
# Allowed origins (comma-separated)
export CORS_ORIGINS=http://localhost:8080,https://app.huly.io

# Allow credentials
export CORS_CREDENTIALS=true
```

### Rate Limiting

```bash
# Max requests per minute per user
export RATE_LIMIT_PER_MINUTE=60

# Max requests per hour per user
export RATE_LIMIT_PER_HOUR=1000
```

## Integration Configuration

### GitHub

```bash
export GITHUB_CLIENT_ID=your_client_id
export GITHUB_CLIENT_SECRET=your_client_secret
export GITHUB_CALLBACK_URL=http://localhost:8080/auth/github/callback
```

### Gmail

```bash
export GMAIL_CLIENT_ID=your_client_id
export GMAIL_CLIENT_SECRET=your_client_secret
export GMAIL_REDIRECT_URI=http://localhost:8080/auth/gmail/callback
```

### Telegram

```bash
export TELEGRAM_BOT_TOKEN=your_bot_token
export TELEGRAM_WEBHOOK_URL=https://your-domain.com/api/telegram/webhook
```

## Testing Configuration

```bash
# Test database (separate from development)
export TEST_DB_URL=postgresql://postgres:example@localhost:5432/huly_test

# Disable external services in tests
export SKIP_EXTERNAL_SERVICES=true

# Test timeout
export TEST_TIMEOUT=30000  # 30 seconds
```

## Configuration Loading Order

HULY loads configuration in this order (later overrides earlier):

1. Default values (hardcoded)
2. `config.json` (if exists)
3. `.env` file
4. Environment variables
5. Command-line arguments

Example:

```bash
# .env sets DB_URL=postgresql://localhost/huly
# Command line overrides it:
DB_URL=postgresql://production-db/huly rushx dev-server
```

## Validation

Validate your configuration:

```bash
# Check required variables are set
rushx config-validate

# Print current configuration (sanitized)
rushx config-print
```

## Best Practices

### ✅ Do's

- Use `.env` for local development
- Use environment variables in production
- Keep secrets out of version control
- Use strong random values for SECRET
- Set appropriate memory limits
- Configure log levels per environment
- Use separate databases for dev/test/prod

### ❌ Don'ts

- Don't commit `.env` to git (it's in `.gitignore`)
- Don't use default passwords in production
- Don't share the same database between environments
- Don't use `DEBUG=true` in production
- Don't hardcode configuration in code
- Don't expose MinIO/PostgreSQL ports publicly

## Summary

**Essential variables:**

```bash
DB_URL=postgresql://postgres:example@localhost:5432/huly
STORAGE_CONFIG=minio
SERVER_PORT=3333
DEBUG=true
```

**File locations:**

- `.env` - Environment variables (root of repo)
- `dev/docker-compose.yml` - Docker services
- `rush.json` - Monorepo configuration
- `tsconfig.json` - TypeScript configuration (per package)

**Next steps:**

- [Daily Workflow](daily-workflow) - Development commands
- [Creating Your First Workspace](first-workspace) - Workspace setup

Your HULY instance is now properly configured! ⚙️
