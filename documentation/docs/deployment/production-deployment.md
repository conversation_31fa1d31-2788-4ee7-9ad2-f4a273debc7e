# Production Deployment

Complete guide to deploying HULY in production environments.

## Overview

Production deployment requires:

- **Database** - PostgreSQL (primary) or MongoDB
- **Object Storage** - AWS S3 or S3-compatible (MinIO)
- **Search** - Elasticsearch or OpenSearch
- **Collaborator** - For real-time editing (optional)
- **Load Balancer** - For high availability
- **CDN** - For static assets
- **Monitoring** - Logs and metrics

## Architecture

### Single Server (Small Teams)

```
┌──────────────────────────────────────┐
│        Single Server (4GB+ RAM)      │
│                                      │
│  ┌─────────┐  ┌─────────┐          │
│  │ Server  │  │ Front   │          │
│  └─────────┘  └─────────┘          │
│                                      │
│  ┌─────────┐  ┌─────────┐          │
│  │Postgres │  │MinIO/S3 │          │
│  └─────────┘  └─────────┘          │
│                                      │
│  ┌─────────────────────┐            │
│  │   Elasticsearch     │            │
│  └─────────────────────┘            │
└──────────────────────────────────────┘
```

### Multi-Server (Large Teams)

```
               ┌──────────────┐
               │Load Balancer │
               └──────┬───────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
        ▼             ▼             ▼
┌──────────┐   ┌──────────┐  ┌──────────┐
│ Server 1 │   │ Server 2 │  │ Server 3 │
└─────┬────┘   └─────┬────┘  └─────┬────┘
      │              │             │
      └──────────────┼─────────────┘
                     │
        ┌────────────┼────────────┐
        │            │            │
        ▼            ▼            ▼
  ┌──────────┐ ┌─────────┐ ┌──────────┐
  │PostgreSQL│ │  MinIO  │ │  Elastic │
  │ Cluster  │ │ Cluster │ │ Cluster  │
  └──────────┘ └─────────┘ └──────────┘
```

## Prerequisites

### Server Requirements

**Minimum (< 50 users):**

- 4 GB RAM
- 2 CPU cores
- 50 GB SSD storage
- 100 Mbps network

**Recommended (50-200 users):**

- 16 GB RAM
- 8 CPU cores
- 500 GB SSD storage
- 1 Gbps network

**Large (200+ users):**

- 32+ GB RAM per server
- 16+ CPU cores per server
- 1+ TB SSD storage
- Multiple servers
- Load balancer

### Software Requirements

- **OS**: Ubuntu 22.04 LTS, RHEL 8+, or Debian 11+
- **Node.js**: v20.x
- **PostgreSQL**: 15+
- **Elasticsearch**: 8.x
- **Docker**: Optional but recommended

## Installation Steps

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Install build tools
sudo apt install -y build-essential python3

# Install Rush
sudo npm install -g @microsoft/rush

# Create huly user
sudo useradd -r -m -d /opt/huly -s /bin/bash huly
sudo su - huly
```

### 2. Clone and Build

```bash
# Clone repository
cd /opt/huly
git clone https://github.com/hcengineering/platform.git
cd platform

# Install dependencies
rush update

# Build for production
NODE_ENV=production rush build
```

### 3. Database Setup

```bash
# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Create database
sudo -u postgres psql -c "CREATE DATABASE huly;"
sudo -u postgres psql -c "CREATE USER hulyuser WITH PASSWORD 'secure-password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE huly TO hulyuser;"

# Test connection
psql -h localhost -U hulyuser -d huly -c "SELECT 1;"
```

### 4. Object Storage Setup

**Option A: AWS S3**

```bash
# Configure AWS credentials
aws configure

# Create bucket
aws s3 mb s3://huly-production --region us-east-1

# Set bucket policy (public read for files)
aws s3api put-bucket-policy --bucket huly-production --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::huly-production/*"
    }
  ]
}'
```

**Option B: MinIO**

```bash
# Install MinIO
wget https://dl.min.io/server/minio/release/linux-amd64/minio
chmod +x minio
sudo mv minio /usr/local/bin/

# Create data directory
sudo mkdir -p /var/lib/minio/data
sudo chown huly:huly /var/lib/minio/data

# Start MinIO
minio server /var/lib/minio/data --console-address ":9001"
```

### 5. Elasticsearch Setup

```bash
# Install Elasticsearch
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.11.0-amd64.deb
sudo dpkg -i elasticsearch-8.11.0-amd64.deb

# Configure
sudo vim /etc/elasticsearch/elasticsearch.yml

# Set:
# cluster.name: huly-cluster
# network.host: 0.0.0.0
# discovery.type: single-node

# Start
sudo systemctl enable elasticsearch
sudo systemctl start elasticsearch

# Test
curl http://localhost:9200
```

## Configuration

### Production Environment Variables

```bash
# /opt/huly/platform/.env.production

# Database
DB_URL=postgresql://hulyuser:secure-password@localhost:5432/huly

# Storage (S3)
STORAGE_CONFIG=s3
S3_REGION=us-east-1
S3_BUCKET=huly-production
AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE
AWS_SECRET_ACCESS_KEY=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY

# Or MinIO
# STORAGE_CONFIG=minio
# MINIO_ENDPOINT=minio.example.com
# MINIO_ACCESS_KEY=minioadmin
# MINIO_SECRET_KEY=secure-password

# Elasticsearch
ELASTIC_URL=http://localhost:9200

# Server
SERVER_PORT=3333
ACCOUNTS_URL=https://accounts.huly.io
FRONT_URL=https://app.huly.io

# Security
SECRET=$(openssl rand -base64 64)
TOKEN_EXPIRATION=86400

# Performance
NODE_OPTIONS=--max-old-space-size=8192
WORKER_THREADS=8

# Production mode
NODE_ENV=production
DEBUG=false
LOG_LEVEL=warn
```

## Systemd Services

### Server Service

```bash
# /etc/systemd/system/huly-server.service
[Unit]
Description=HULY Server
After=network.target postgresql.service

[Service]
Type=simple
User=huly
WorkingDirectory=/opt/huly/platform
Environment="NODE_ENV=production"
EnvironmentFile=/opt/huly/platform/.env.production
ExecStart=/usr/bin/node server/server/lib/index.js
Restart=on-failure
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### Front Service

```bash
# /etc/systemd/system/huly-front.service
[Unit]
Description=HULY Front Server
After=network.target

[Service]
Type=simple
User=huly
WorkingDirectory=/opt/huly/platform
Environment="NODE_ENV=production"
EnvironmentFile=/opt/huly/platform/.env.production
ExecStart=/usr/bin/node server/front/lib/index.js
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Enable Services

```bash
sudo systemctl daemon-reload
sudo systemctl enable huly-server huly-front
sudo systemctl start huly-server huly-front

# Check status
sudo systemctl status huly-server
sudo systemctl status huly-front

# View logs
sudo journalctl -u huly-server -f
sudo journalctl -u huly-front -f
```

## Nginx Reverse Proxy

```nginx
# /etc/nginx/sites-available/huly

# Upstream servers
upstream huly_server {
    server localhost:3333;
}

upstream huly_front {
    server localhost:8080;
}

# HTTP redirect to HTTPS
server {
    listen 80;
    server_name app.huly.io;
    return 301 https://$server_name$request_uri;
}

# HTTPS
server {
    listen 443 ssl http2;
    server_name app.huly.io;

    # SSL certificates (Let's Encrypt)
    ssl_certificate /etc/letsencrypt/live/app.huly.io/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/app.huly.io/privkey.pem;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Frontend
    location / {
        proxy_pass http://huly_front;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket (Server)
    location /api/ {
        proxy_pass http://huly_server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        # WebSocket timeout
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }

    # File uploads
    client_max_body_size 100M;
}
```

## SSL Certificates

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d app.huly.io -d accounts.huly.io

# Auto-renewal
sudo certbot renew --dry-run

# Add to crontab
sudo crontab -e
# Add: 0 0 * * * certbot renew --quiet
```

## High Availability

### Load Balancing

```nginx
# Multiple server instances
upstream huly_servers {
    least_conn;
    server server1.internal:3333;
    server server2.internal:3333;
    server server3.internal:3333;
}

server {
    location /api/ {
        proxy_pass http://huly_servers;
        # ... other settings
    }
}
```

### PostgreSQL Replication

```bash
# Primary server
# /etc/postgresql/15/main/postgresql.conf
wal_level = replica
max_wal_senders = 3
wal_keep_size = 64

# Replica server
# /etc/postgresql/15/main/postgresql.conf
hot_standby = on

# Create replication slot on primary
sudo -u postgres psql
SELECT * FROM pg_create_physical_replication_slot('replica_1');
```

## Monitoring

### Health Checks

```typescript
// server/server/src/health.ts

app.get('/health', (req, res) => {
  const health = {
    status: 'ok',
    timestamp: Date.now(),
    services: {
      database: 'ok',
      storage: 'ok',
      elasticsearch: 'ok'
    }
  }

  res.json(health)
})
```

### Prometheus Metrics

```typescript
import { register, Counter, Histogram } from 'prom-client'

// Define metrics
const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code']
})

const txProcessed = new Counter({
  name: 'huly_transactions_processed_total',
  help: 'Total number of transactions processed',
  labelNames: ['workspace']
})

// Expose metrics endpoint
app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType)
  res.end(register.metrics())
})
```

### Logging

```bash
# Configure logging in production
export LOG_LEVEL=warn
export LOG_FORMAT=json
export LOG_FILE=/var/log/huly/server.log

# Rotate logs
sudo vim /etc/logrotate.d/huly

/var/log/huly/*.log {
    daily
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 huly huly
    sharedscripts
    postrotate
        systemctl reload huly-server
    endscript
}
```

## Backup Strategy

### Database Backup

```bash
# Automated backup script
#!/bin/bash
# /opt/huly/scripts/backup-db.sh

BACKUP_DIR=/var/backups/huly/postgres
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Dump database
pg_dump -h localhost -U hulyuser huly | gzip > $BACKUP_DIR/huly_$DATE.sql.gz

# Keep only last 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

# Upload to S3
aws s3 cp $BACKUP_DIR/huly_$DATE.sql.gz s3://huly-backups/postgres/
```

### Blob Backup

```bash
# Sync blobs to backup bucket
aws s3 sync s3://huly-production s3://huly-backups/blobs/ \
  --storage-class GLACIER
```

### Automated Backups

```bash
# Add to crontab
crontab -e

# Daily database backup at 2 AM
0 2 * * * /opt/huly/scripts/backup-db.sh

# Weekly blob backup on Sunday at 3 AM
0 3 * * 0 /opt/huly/scripts/backup-blobs.sh
```

## Performance Tuning

### PostgreSQL

```sql
-- /etc/postgresql/15/main/postgresql.conf

# Memory
shared_buffers = 4GB
effective_cache_size = 12GB
work_mem = 64MB
maintenance_work_mem = 1GB

# Connections
max_connections = 200

# Write-ahead log
wal_buffers = 16MB
checkpoint_completion_target = 0.9

# Query tuning
random_page_cost = 1.1
effective_io_concurrency = 200
```

### Node.js

```bash
# /opt/huly/platform/.env.production

# Increase memory limit
NODE_OPTIONS=--max-old-space-size=16384

# Worker threads
WORKER_THREADS=16

# Connection pools
DB_POOL_MIN=10
DB_POOL_MAX=50
```

### Elasticsearch

```yaml
# /etc/elasticsearch/elasticsearch.yml

# Memory
bootstrap.memory_lock: true

# JVM heap (50% of RAM, max 32GB)
# /etc/elasticsearch/jvm.options
-Xms8g
-Xmx8g

# Shards
number_of_shards: 3
number_of_replicas: 1
```

## Security

### Firewall

```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# Don't expose internal ports
# PostgreSQL (5432), Elasticsearch (9200), MinIO (9000)
```

### SSL/TLS

```bash
# Strong SSL configuration in Nginx
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
ssl_prefer_server_ciphers on;
ssl_session_cache shared:SSL:10m;
```

### Database Security

```sql
-- Restrict PostgreSQL access
-- /etc/postgresql/15/main/pg_hba.conf

# Local connections
local   all   postgres   peer
local   all   hulyuser   md5

# Remote connections (if needed)
host    huly  hulyuser   10.0.0.0/8   md5

# Deny all others
host    all   all        0.0.0.0/0    reject
```

## Maintenance

### Updates

```bash
# Pull latest code
cd /opt/huly/platform
git pull origin main

# Update dependencies
rush update

# Build
rush build

# Run migrations
rushx workspace-tool upgrade-all

# Restart services
sudo systemctl restart huly-server huly-front
```

### Database Maintenance

```bash
# Vacuum (reclaim space)
sudo -u postgres vacuumdb --all --analyze

# Reindex
sudo -u postgres reindexdb huly

# Check database size
sudo -u postgres psql -c "
  SELECT pg_database.datname,
         pg_size_pretty(pg_database_size(pg_database.datname))
  FROM pg_database
  WHERE datname = 'huly';
"
```

## Monitoring & Alerts

### System Monitoring

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Monitor processes
htop

# Monitor disk I/O
iotop

# Monitor network
nethogs
```

### Application Monitoring

Use tools like:

- **Prometheus** - Metrics collection
- **Grafana** - Visualization
- **Loki** - Log aggregation
- **Sentry** - Error tracking

## Scaling

### Vertical Scaling

Increase server resources:

- More RAM (16GB → 32GB → 64GB)
- More CPU cores (4 → 8 → 16)
- Faster storage (SSD → NVMe)

### Horizontal Scaling

Add more servers:

```
1. Deploy multiple server instances
2. Add load balancer (nginx, HAProxy, AWS ALB)
3. Use shared PostgreSQL
4. Use shared storage (S3)
5. Use shared Elasticsearch
```

## Disaster Recovery

### Recovery Plan

1. **Database**: Restore from latest backup
2. **Blobs**: Restore from S3 backup/versioning
3. **Elasticsearch**: Rebuild from database

### Recovery Steps

```bash
# 1. Restore database
gunzip < /var/backups/huly/postgres/huly_20240115.sql.gz | \
  psql -h localhost -U hulyuser huly

# 2. Restore blobs
aws s3 sync s3://huly-backups/blobs/ s3://huly-production/

# 3. Reindex Elasticsearch
rushx workspace-tool reindex-all

# 4. Restart services
sudo systemctl restart huly-server huly-front
```

## Best Practices

### ✅ Do's

- Use HTTPS everywhere
- Regular backups (daily DB, weekly blobs)
- Monitor resource usage
- Set up alerts for errors
- Use separate database for production
- Keep software updated
- Use strong passwords and secrets
- Implement rate limiting
- Enable logging
- Test disaster recovery plan

### ❌ Don'ts

- Don't use default passwords
- Don't expose internal ports
- Don't skip SSL certificates
- Don't ignore security updates
- Don't run as root
- Don't skip backups
- Don't put secrets in code
- Don't use single server for critical workloads

## Summary

Production deployment checklist:

- ✅ Server setup (Ubuntu/RHEL)
- ✅ PostgreSQL database
- ✅ S3/MinIO object storage
- ✅ Elasticsearch search
- ✅ Nginx reverse proxy
- ✅ SSL certificates
- ✅ Systemd services
- ✅ Monitoring and logging
- ✅ Automated backups
- ✅ Security hardening

Deploy HULY to production confidently! 🚀
