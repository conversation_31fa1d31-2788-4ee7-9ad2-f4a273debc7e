# Best Practices & Performance

Comprehensive guide to best practices, performance optimization, and code quality for HULY development.

## Development Best Practices

### Code Organization

```
✅ Good Structure:
plugins/my-feature/
├── src/
│   ├── index.ts          # Clean exports
│   ├── types.ts          # Type definitions
│   └── utils.ts          # Shared utilities

✅ Good: Small, focused modules
❌ Bad: One giant file with everything
```

### TypeScript

```typescript
// ✅ Good: Explicit types
async function createTask(data: Data<Task>): Promise<Ref<Task>> {
  // ...
}

// ❌ Bad: Any types
async function createTask(data: any): Promise<any> {
  // ...
}

// ✅ Good: Type guards
function isTask(doc: Doc): doc is Task {
  return doc._class === myFeature.class.Task
}

// ✅ Good: Const assertions
const PRIORITIES = ['low', 'medium', 'high'] as const
type Priority = (typeof PRIORITIES)[number]
```

### Error Handling

```typescript
// ✅ Good: Specific error types
class TaskValidationError extends Error {
  constructor(message: string, public field: string) {
    super(message)
    this.name = 'TaskValidationError'
  }
}

async function createTask(data: Data<Task>): Promise<Ref<Task>> {
  if (!data.title || data.title.length < 3) {
    throw new TaskValidationError('Title too short', 'title')
  }

  try {
    return await client.createDoc(myFeature.class.Task, data.space, data)
  } catch (err) {
    console.error('Failed to create task:', err)
    Analytics.handleError(err)
    throw err // Re-throw for caller to handle
  }
}

// ❌ Bad: Silent failures
async function createTask(data: Data<Task>): Promise<Ref<Task> | undefined> {
  try {
    return await client.createDoc(myFeature.class.Task, data.space, data)
  } catch (err) {
    console.log(err) // Just log and return undefined?!
    return undefined
  }
}
```

## Performance Optimization

### Query Optimization

```typescript
// ❌ Bad: N+1 query problem
const tasks = await client.findAll(myFeature.class.Task, { space })
for (const task of tasks) {
  task.assignee = await client.findOne(contact.class.Employee, {
    _id: task.assignee // Separate query for each!
  })
}

// ✅ Good: Use lookup
const tasks = await client.findAll(
  myFeature.class.Task,
  { space },
  {
    lookup: {
      assignee: contact.class.Employee // Single query!
    }
  }
)

// ❌ Bad: Load all fields
const tasks = await client.findAll(myFeature.class.Task, { space })

// ✅ Good: Projection (only needed fields)
const tasks = await client.findAll(
  myFeature.class.Task,
  { space },
  {
    projection: {
      _id: 1,
      title: 1,
      completed: 1
    }
  }
)

// ❌ Bad: Load unlimited results
const tasks = await client.findAll(myFeature.class.Task, { space })

// ✅ Good: Pagination
const tasks = await client.findAll(
  myFeature.class.Task,
  { space },
  {
    limit: 50,
    skip: page * 50
  }
)
```

### Component Optimization

```svelte
<script lang="ts">
  import { onMount, onDestroy } from 'svelte'

  // ✅ Good: Cleanup subscriptions
  let unsubscribe: () => void

  onMount(() => {
    unsubscribe = client.subscribe(
      myFeature.class.Task,
      { space },
      handleUpdate
    )
  })

  onDestroy(() => {
    unsubscribe?.()  // Don't forget!
  })

  // ✅ Good: Memoize expensive computations
  $: sortedTasks = tasks.then(list =>
    list.sort((a, b) => a.priority - b.priority)
  )

  // ❌ Bad: Compute on every render
  {#await tasks then list}
    {#each list.sort((a, b) => a.priority - b.priority) as task}
      <!-- Sorts on every render! -->
    {/each}
  {/await}
</script>
```

### Virtual Scrolling

```svelte
<script lang="ts">
  import { VirtualList } from '@hcengineering/ui'

  export let tasks: Task[]  // 10,000 items!
</script>

<!-- ✅ Good: Only renders visible items -->
<VirtualList
  items={tasks}
  itemHeight={60}
  let:item={task}
>
  <TaskView {task} />
</VirtualList>

<!-- ❌ Bad: Renders all 10,000 items -->
{#each tasks as task}
  <TaskView {task} />
{/each}
```

### Lazy Loading

```svelte
<script lang="ts">
  import { IntersectionObserver } from '@hcengineering/ui'

  let visible = false

  // ✅ Good: Only load when scrolled into view
</script>

<IntersectionObserver on:intersect={() => visible = true}>
  {#if visible}
    <ExpensiveComponent />
  {:else}
    <div class="placeholder">...</div>
  {/if}
</IntersectionObserver>
```

## Memory Management

### Avoiding Memory Leaks

```svelte
<script lang="ts">
  import { onDestroy } from 'svelte'

  // ✅ Good: Clean up timers
  const interval = setInterval(refresh, 5000)
  onDestroy(() => clearInterval(interval))

  // ✅ Good: Clean up event listeners
  function handleClick(e) { /*...*/ }
  element.addEventListener('click', handleClick)
  onDestroy(() => element.removeEventListener('click', handleClick))

  // ✅ Good: Clean up subscriptions
  const unsubscribe = store.subscribe(value => {/*...*/})
  onDestroy(unsubscribe)

  // ✅ Good: Clean up object URLs
  const url = URL.createObjectURL(blob)
  onDestroy(() => URL.revokeObjectURL(url))
</script>
```

### Efficient Data Structures

```typescript
// ✅ Good: Use Map for lookups
const taskMap = new Map(tasks.map((t) => [t._id, t]))
const task = taskMap.get(taskId) // O(1)

// ❌ Bad: Use array find
const task = tasks.find((t) => t._id === taskId) // O(n)

// ✅ Good: Use Set for membership
const memberSet = new Set(space.members)
const isMember = memberSet.has(userUuid) // O(1)

// ❌ Bad: Use array includes
const isMember = space.members.includes(userUuid) // O(n)
```

## Security Best Practices

### Input Validation

```typescript
// ✅ Good: Validate all inputs
function validateTask(data: Data<Task>): void {
  if (!data.title || data.title.length < 3) {
    throw new Error('Title must be at least 3 characters')
  }

  if (data.title.length > 200) {
    throw new Error('Title too long')
  }

  if (data.dueDate && data.dueDate < Date.now()) {
    throw new Error('Due date must be in future')
  }
}

// ❌ Bad: No validation
async function createTask(data: any) {
  return await client.createDoc(myFeature.class.Task, space, data)
}
```

### XSS Prevention

```svelte
<script lang="ts">
  // ✅ Good: Svelte escapes by default
  <div>{task.title}</div>

  // ❌ Dangerous: Unescaped HTML
  <div>{@html task.userInput}</div>

  // ✅ Safe: Use sanitizer if you need HTML
  import DOMPurify from 'dompurify'
  $: sanitized = DOMPurify.sanitize(task.description)
</script>

<div>{@html sanitized}</div>
```

### Permission Checks

```typescript
// ✅ Good: Check permissions before actions
async function deleteTask(task: Task): Promise<void> {
  const canDelete = await checkPermission(client, myFeature.permission.DeleteTask, task.space)

  if (!canDelete) {
    throw new Error('Permission denied')
  }

  await client.remove(task)
}

// ❌ Bad: No permission check
async function deleteTask(task: Task): Promise<void> {
  await client.remove(task) // Server will reject, but better to check first
}
```

## Code Quality

### Naming Conventions

```typescript
// ✅ Good: Clear, descriptive names
async function createTaskWithNotification(data: Data<Task>): Promise<Ref<Task>>
const isPending = !task.completed
const TASK_STATUSES = ['todo', 'in-progress', 'done'] as const

// ❌ Bad: Unclear abbreviations
async function crTskWNtf(d: any): Promise<any>
const p = !t.c
const TS = ['t', 'ip', 'd']
```

### Function Size

```typescript
// ✅ Good: Small, focused functions
async function createTask(data: Data<Task>): Promise<Ref<Task>> {
  validateTaskData(data)
  const taskId = await saveTask(data)
  await sendNotifications(taskId)
  return taskId
}

// ❌ Bad: 200-line function doing everything
async function createTask(data: any) {
  // Validation (50 lines)
  // Saving (50 lines)
  // Notifications (50 lines)
  // Analytics (50 lines)
}
```

### Comments

```typescript
// ✅ Good: Explain WHY, not WHAT
// Calculate priority score using custom algorithm
// Higher score = more urgent (based on due date + priority level)
const score = calculatePriorityScore(task)

// ❌ Bad: Obvious comments
// Set completed to true
task.completed = true

// ✅ Good: Document complex logic
/**
 * Assigns task to team member with least workload.
 *
 * Algorithm:
 * 1. Get all team members
 * 2. Count pending tasks for each
 * 3. Assign to member with fewest tasks
 * 4. Break ties by join date (newest first)
 */
async function autoAssignTask(task: Task): Promise<void>

// ❌ Bad: No comments on complex code
async function autoAssignTask(task: Task): Promise<void> {
  // 50 lines of complex logic with no explanation
}
```

## Testing Best Practices

### Test Coverage

```typescript
// ✅ Good: Test edge cases
describe('calculateTotal', () => {
  it('should handle empty array', () => {
    expect(calculateTotal([])).toBe(0)
  })

  it('should handle negative numbers', () => {
    expect(calculateTotal([{ price: -10, qty: 2 }])).toBe(-20)
  })

  it('should handle zero quantities', () => {
    expect(calculateTotal([{ price: 10, qty: 0 }])).toBe(0)
  })
})

// ❌ Bad: Only test happy path
describe('calculateTotal', () => {
  it('should calculate total', () => {
    expect(calculateTotal([{ price: 10, qty: 2 }])).toBe(20)
  })
})
```

### Test Isolation

```typescript
// ✅ Good: Each test is independent
describe('Task operations', () => {
  beforeEach(async () => {
    // Fresh data for each test
    testSpace = await createTestSpace()
  })

  afterEach(async () => {
    // Clean up after each test
    await deleteTestSpace(testSpace)
  })

  it('test 1', async () => {
    // Uses fresh testSpace
  })

  it('test 2', async () => {
    // Uses fresh testSpace, independent of test 1
  })
})

// ❌ Bad: Tests depend on each other
describe('Task operations', () => {
  let task

  it('should create task', async () => {
    task = await createTask() // Sets global variable
  })

  it('should update task', async () => {
    await update(task) // Depends on previous test!
  })
})
```

## Monitoring & Observability

### Structured Logging

```typescript
// ✅ Good: Structured logs
ctx.info('Task created', {
  taskId: task._id,
  title: task.title,
  assignee: task.assignee,
  space: task.space,
  duration: Date.now() - startTime
})

// ❌ Bad: Unstructured logs
console.log('Created task ' + task._id + ' with title ' + task.title)
```

### Performance Metrics

```typescript
// ✅ Good: Track performance
await ctx.with('create-task', {}, async (ctx) => {
  const task = await client.createDoc(...)

  ctx.info('Task created', {
    duration: ctx.elapsed,
    taskId: task._id
  })

  return task
})

// Track custom metrics
Analytics.handleTiming({
  category: 'task',
  variable: 'creation_time',
  time: duration
})
```

## Database Best Practices

### Indexing Strategy

```typescript
// ✅ Good: Index frequently queried fields
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  @Prop(TypeRef(contact.class.Employee), contact.string.Assignee)
  @Index(IndexKind.Indexed) // Often queried
  assignee?: Ref<Employee>

  @Prop(TypeBoolean(), myFeature.string.Completed)
  @Index(IndexKind.Indexed) // Often filtered
  completed!: boolean

  @Prop(TypeString(), myFeature.string.InternalNotes)
  notes?: string // Not indexed - rarely queried
}
```

### Efficient Updates

```typescript
// ✅ Good: Update only changed fields
await client.update(task, {
  completed: true
})

// ❌ Bad: Update entire document
await client.update(task, {
  title: task.title,
  description: task.description,
  completed: true,
  assignee: task.assignee
  // ... all fields
})

// ✅ Good: Atomic operations
await client.update(task, {
  $inc: { viewCount: 1 }
})

// ❌ Bad: Read-modify-write (race condition!)
const task = await client.findOne(myFeature.class.Task, { _id })
await client.update(task, {
  viewCount: task.viewCount + 1
})
```

## UI/UX Best Practices

### Loading States

```svelte
<!-- ✅ Good: Always show loading state -->
{#await data}
  <Spinner />
  <span>Loading tasks...</span>
{:then tasks}
  <TaskList {tasks} />
{:catch error}
  <ErrorMessage {error} />
{/await}

<!-- ❌ Bad: No loading state -->
{#await data then tasks}
  <TaskList {tasks} />
{/await}
```

### Optimistic Updates

```svelte
<script lang="ts">
  // ✅ Good: Update UI immediately
  async function toggleComplete(task: Task) {
    // Update local state immediately
    task.completed = !task.completed

    // Sync to server
    try {
      await client.update(task, { completed: task.completed })
    } catch (err) {
      // Revert on error
      task.completed = !task.completed
      showError('Failed to update task')
    }
  }
</script>
```

### Debouncing

```svelte
<script lang="ts">
  import { debounce } from '@hcengineering/core'

  let searchQuery = ''

  // ✅ Good: Debounce search
  const debouncedSearch = debounce((query: string) => {
    performSearch(query)
  }, 300)

  $: debouncedSearch(searchQuery)
</script>

<EditBox bind:value={searchQuery} placeholder="Search..." />
```

## Security Best Practices

### Sanitize User Input

```typescript
// ✅ Good: Sanitize
function sanitizeTitle(title: string): string {
  return title
    .trim()
    .replace(/<script>/gi, '')
    .substring(0, 200)
}

// ✅ Good: Validate
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
```

### Rate Limiting

```typescript
// ✅ Good: Implement rate limiting
const rateLimiter = new Map<string, number[]>()

function checkRateLimit(userId: string, limit: number = 10): boolean {
  const now = Date.now()
  const userRequests = rateLimiter.get(userId) || []

  // Remove requests older than 1 minute
  const recentRequests = userRequests.filter((time) => now - time < 60000)

  if (recentRequests.length >= limit) {
    return false // Rate limit exceeded
  }

  recentRequests.push(now)
  rateLimiter.set(userId, recentRequests)
  return true
}
```

## Documentation

### Code Documentation

````typescript
/**
 * Creates a new task and notifies the assignee.
 *
 * @param space - Space to create task in
 * @param data - Task data
 * @returns Reference to created task
 * @throws {TaskValidationError} If data is invalid
 * @throws {PermissionError} If user lacks permission
 *
 * @example
 * ```ts
 * const taskId = await createTask(spaceId, {
 *   title: 'Fix bug',
 *   assignee: employeeId
 * })
 * ```
 */
export async function createTask(space: Ref<Space>, data: Data<Task>): Promise<Ref<Task>> {
  // Implementation
}
````

### Component Documentation

````svelte
<!--
  TaskView - Displays a task with editing capabilities

  @component

  Props:
  - task: Task - The task to display
  - readonly: boolean - Whether task is editable

  Events:
  - update - Fired when task is updated
  - delete - Fired when task is deleted

  Example:
  ```svelte
  <TaskView
    task={myTask}
    readonly={false}
    on:update={handleUpdate}
  />
````

-->

<script lang="ts">
  export let task: Task
  export let readonly: boolean = false
</script>

````

## Git Workflow

### Commit Messages

```bash
# ✅ Good: Descriptive commits
git commit -m "feat(tracker): Add task priority field

- Added priority field to Task model
- Updated UI to show priority indicator
- Added migration for existing tasks"

# ❌ Bad: Vague commits
git commit -m "fix stuff"
git commit -m "updates"
````

### Branch Naming

```bash
# ✅ Good: Descriptive branch names
git checkout -b feature/task-priority
git checkout -b bugfix/login-error
git checkout -b refactor/query-optimization

# ❌ Bad: Unclear branches
git checkout -b new-feature
git checkout -b fix
```

## Performance Checklist

### Before Committing

- [ ] Run `rush validate` (no type errors)
- [ ] Run `rush lint` (no lint errors)
- [ ] Run `rush test` (all tests pass)
- [ ] Check bundle size hasn't increased significantly
- [ ] Test with realistic data (not just 10 items)
- [ ] Profile performance (no obvious bottlenecks)
- [ ] Check for memory leaks
- [ ] Test on slow network (throttle in DevTools)

### Before Deploying

- [ ] Run all tests in CI
- [ ] Check test coverage > 80%
- [ ] Review security (no credentials in code)
- [ ] Check all migrations work
- [ ] Load test with realistic traffic
- [ ] Update documentation
- [ ] Review error handling
- [ ] Check logging is appropriate

## Common Anti-Patterns

### Anti-Pattern: God Component

```svelte
<!-- ❌ Bad: One component does everything -->
<script lang="ts">
  // 1000 lines of logic
  // Handles: display, editing, deletion, permissions, etc.
</script>

<!-- ✅ Good: Split into focused components -->
<TaskView {task} />           <!-- Display -->
<TaskEditor {task} />         <!-- Editing -->
<TaskActions {task} />        <!-- Actions -->
```

### Anti-Pattern: Prop Drilling

```svelte
<!-- ❌ Bad: Pass props through many layers -->
<GrandParent user={user}>
  <Parent user={user}>
    <Child user={user}>
      <DeepChild user={user} />
    </Child>
  </Parent>
</GrandParent>

<!-- ✅ Good: Use context -->
<script lang="ts">
  import { setContext } from 'svelte'
  setContext('user', user)
</script>

<!-- Deep child -->
<script lang="ts">
  import { getContext } from 'svelte'
  const user = getContext('user')
</script>
```

### Anti-Pattern: Tight Coupling

```typescript
// ❌ Bad: Tightly coupled to implementation
function createTask(taskData: any) {
  const task = new MongoDBTask(taskData)
  task.save()
}

// ✅ Good: Depend on abstractions
function createTask(data: Data<Task>): Promise<Ref<Task>> {
  return client.createDoc(myFeature.class.Task, data.space, data)
}
```

## Summary

Best practices summary:

- ✅ Write clean, typed code
- ✅ Optimize queries (projection, lookup, indexes)
- ✅ Optimize UI (virtual scrolling, lazy loading)
- ✅ Handle errors properly
- ✅ Clean up resources (subscriptions, timers)
- ✅ Validate all inputs
- ✅ Check permissions
- ✅ Write tests
- ✅ Document code
- ✅ Monitor performance

Follow these practices for maintainable, performant HULY plugins! 🏆
