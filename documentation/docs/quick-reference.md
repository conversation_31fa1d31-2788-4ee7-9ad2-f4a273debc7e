# Quick Reference

Essential commands, patterns, and code snippets for HULY development.

## Essential Commands

### After Git Pull

```bash
git pull
rush update    # ALWAYS run this!
rush build
rushx dev
```

### Daily Development

```bash
# Start development
rushx dev              # Server + Client
rushx dev-server       # Server only
rushx dev-client       # Client only

# Build
rush build             # All packages
rush build --to PKG    # Specific package
rush build -p          # Parallel build

# Update deps
rush update            # After pull, branch switch
rush update --full     # Force full update

# Validate
rush validate          # Type check
rush lint              # Lint code
rush test              # Run tests

# Clean
rush clean             # Remove build artifacts
```

### Package-Specific

```bash
cd plugins/my-plugin
rushx build            # Build
rushx dev              # Watch mode
rushx test             # Test
rushx lint             # Lint
rushx clean            # Clean
```

## Code Snippets

### Client Setup

```typescript
import { getClient } from '@hcengineering/presentation'
import { getCurrentAccount } from '@hcengineering/core'

const client = getClient()
const me = getCurrentAccount()
```

### Create Document

```typescript
const docId = await client.createDoc(myPlugin.class.MyDoc, spaceId, {
  title: 'Title',
  description: 'Description'
})
```

### Update Document

```typescript
await client.update(doc, {
  title: 'New Title',
  completed: true
})

// Partial update
await client.update(doc, {
  $set: { field: 'value' }
})

// Array operations
await client.update(doc, {
  $push: { tags: 'new-tag' },
  $pull: { tags: 'old-tag' }
})
```

### Delete Document

```typescript
await client.remove(doc)
```

### Query Documents

```typescript
// Find all
const docs = await client.findAll(
  myPlugin.class.MyDoc,
  {
    space: spaceId,
    completed: false
  },
  {
    sort: { modifiedOn: -1 },
    limit: 50
  }
)

// Find one
const doc = await client.findOne(myPlugin.class.MyDoc, {
  _id: docId
})

// With lookup
const docs = await client.findAll(
  myPlugin.class.MyDoc,
  {},
  {
    lookup: {
      assignee: contact.class.Employee
    }
  }
)

console.log(docs[0].$lookup.assignee.name)
```

### Reactive Query (Svelte)

```svelte
<script lang="ts">
  const client = getClient()

  // Auto-updates on changes!
  $: docs = client.findAll(myPlugin.class.MyDoc, { space })
</script>

{#await docs then list}
  {#each list as doc}
    <DocView {doc} />
  {/each}
{/await}
```

## Plugin Structure

### Minimal Plugin

```
plugins/my-feature/src/index.ts          # Types & plugin def
plugins/my-feature-assets/lang/en.json   # Translations
plugins/my-feature-resources/src/        # UI & logic
models/my-feature/src/index.ts           # Data model
```

### Plugin Definition

```typescript
export default plugin('my-feature', {
  class: {
    MyDoc: '' as Ref<Class<MyDoc>>
  },
  string: {
    MyFeature: '' as IntlString
  },
  component: {
    MyView: '' as AnyComponent
  }
})
```

### Model Definition

```typescript
@Model(myPlugin.class.MyDoc, core.class.Doc, DOMAIN_MY_PLUGIN)
export class TMyDoc extends TDoc implements MyDoc {
  @Prop(TypeString(), myPlugin.string.Title)
  @Index(IndexKind.FullText)
  title!: string
}
```

### Resources Export

```typescript
export default async (): Promise<Resources> => ({
  component: { MyView },
  actionImpl: { MyAction },
  function: { MyFunction }
})
```

## Query Operators

```typescript
// Comparison
{
  field: value
} // Equals
{
  field: {
    $ne: value
  }
} // Not equals
{
  field: {
    $gt: value
  }
} // Greater than
{
  field: {
    $gte: value
  }
} // Greater or equal
{
  field: {
    $lt: value
  }
} // Less than
{
  field: {
    $lte: value
  }
} // Less or equal

// Array
{
  field: {
    $in: [v1, v2]
  }
} // In array
{
  field: {
    $nin: [v1, v2]
  }
} // Not in array
{
  field: {
    $all: [v1, v2]
  }
} // Contains all
{
  field: {
    $size: 3
  }
} // Array size

// String
{
  field: {
    $like: '%text%'
  }
} // Contains
{
  field: {
    $regex: '^pattern$'
  }
} // Regex

// Existence
{
  field: {
    $exists: true
  }
} // Field exists
{
  field: {
    $exists: false
  }
} // Field missing

// Logical
{
  $or: [{ f1: v1 }, { f2: v2 }]
} // OR
```

## UI Components

```svelte
<script lang="ts">
  import { Button, EditBox, CheckBox, Label, Icon } from '@hcengineering/ui'
</script>

<Button label="Click me" on:click={handler} />
<EditBox bind:value placeholder="Enter text..." />
<CheckBox bind:checked label="Option" />
<Label label={myPlugin.string.Title} />
<Icon icon={myPlugin.icon.MyIcon} size="medium" />
```

## Common Patterns

### Find or Create

```typescript
let doc = await client.findOne(MyDoc, { name })
if (!doc) {
  const id = await client.createDoc(MyDoc, space, { name })
  doc = await client.findOne(MyDoc, { _id: id })
}
```

### Batch Load

```typescript
const docs = await client.findAll(
  MyDoc,
  {
    _id: { $in: docIds }
  },
  {
    lookup: { ref1: Class1, ref2: Class2 }
  }
)
```

### Permission Check

```typescript
const canDo = await checkPermission(client, myPlugin.permission.DoSomething, spaceId)

if (!canDo) {
  throw new Error('Permission denied')
}
```

## Theme Variables

```css
/* Colors */
--theme-content-color           /* Primary text */
--theme-bg-color               /* Primary background */
--theme-accent-color           /* Accent color */
--theme-error-color            /* Error color */
--theme-divider-color          /* Border color */

/* Spacing */
--spacing-1                    /* 0.25rem */
--spacing-2                    /* 0.5rem */
--spacing-4                    /* 1rem */

/* Other */
--border-radius-1              /* 0.25rem */
--theme-shadow                 /* Box shadow */
```

## Debugging

### Console Debugging

```typescript
// Enable logging
localStorage.setItem('platform.logging.enabled', 'true')

// View all plugins
import { getPlugins } from '@hcengineering/platform'
console.log(getPlugins())

// Get resource
import { getResource } from '@hcengineering/platform'
const MyComponent = await getResource(myPlugin.component.MyView)
```

### Quick Tests

```javascript
// In browser console:
const client = (await import('@hcengineering/presentation')).getClient()

// Create test doc
await client.createDoc('my-plugin:class:MyDoc', 'space-id', {
  title: 'Test'
})

// Query
await client.findAll('my-plugin:class:MyDoc', {})
```

## Environment Variables

```bash
# Database
DB_URL=postgresql://postgres:example@localhost:5432/huly

# Storage
STORAGE_CONFIG=minio
MINIO_ENDPOINT=localhost

# Server
SERVER_PORT=3333
DEBUG=true

# Performance
NODE_OPTIONS=--max-old-space-size=8192
```

## Troubleshooting

| Problem              | Solution                                         |
| -------------------- | ------------------------------------------------ |
| Module not found     | `rush update && rush build`                      |
| Type errors          | `rush validate` then `rush build`                |
| Build fails          | `rush clean && rush update --full && rush build` |
| Services not running | `cd dev && docker-compose up -d`                 |
| Port in use          | Change port in `docker-compose.yml`              |
| Permission denied    | Check space membership and roles                 |
| Changes not saving   | Check browser console for errors                 |
| Plugin not loading   | Check registration and build                     |

## Links to Full Documentation

- 🚀 [Installation Guide](getting-started/installation)
- 🔄 [Daily Workflow](getting-started/daily-workflow)
- 🔌 [Plugin Development](development/plugin-development)
- 📚 [Plugin Recipes](development/plugin-recipes)
- 🐛 [Plugin Debugging](development/plugin-debugging)
- 🎨 [UI Components](development/ui-components)
- 🔍 [Querying Data](development/querying-data)

---

**Bookmark this page for quick reference!** 📌
