# Glossary

Definitions of key terms used in HULY documentation and codebase.

## A

**Account**
: A user account in HULY, identified by an `AccountUuid`. Can belong to multiple workspaces with different roles.

**AccountRole**
: Workspace-level role (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Guest, User, Maintainer, Owner, Admin) that determines workspace-wide permissions.

**AccountUuid**
: Globally unique identifier for a user account across all workspaces.

**Action**
: A user-initiated operation (e.g., create, edit, delete) defined in the model and implemented by plugins.

**Activity**
: Tracked changes to documents, displayed in timelines and feeds.

**ActivityMessage**
: Document that represents a change event (create, update, delete) for audit and activity feeds.

**Adapter**
: Component that connects HULY to a specific storage backend (PostgreSQL, MongoDB, MinIO, S3).

**AttachedDoc**
: Document that belongs to another document (parent-child relationship). Examples: comments, subtasks, attachments.

**Attribute**
: Property of a class, defined with name, type, and metadata (indexing, visibility).

## B

**Builder**
: Pattern for defining HULY's data model using TypeScript decorators.

**Binary Protocol**
: Optimized binary serialization format for client-server communication (30-50% smaller than JSON).

## C

**Class**
: Defines the structure and behavior of objects. Classes are themselves documents in HULY.

**Client**
: The HULY client library that connects to the server and provides the API for querying and modifying data.

**ClientConnection**
: WebSocket connection between client and server.

**Collection**
: Set of attached documents. Example: an issue's collection of comments.

**CollectionSize**
: Type representing the count of items in a collection.

**Collaborator**
: User who should be notified about changes to a document (creator, assignee, commenter).

**Component**
: Svelte UI component provided by a plugin.

**Compression**
: Optional gzip compression of WebSocket messages to reduce bandwidth.

## D

**Data**
: Type representing document data without Doc metadata fields (`_id`, `space`, `modifiedOn`, etc.).

**Doc**
: Base class for all persistent documents. Has `_id`, `space`, `modifiedOn`, `modifiedBy`, etc.

**DocUpdateMessage**
: Activity message tracking changes to a document's attributes.

**Domain**
: Logical storage partition. Each class belongs to a domain (e.g., `DOMAIN_TRACKER`, `DOMAIN_SPACE`).

**DocumentQuery**
: Query object for finding documents, supports operators like `$gt`, `$in`, `$like`.

## E

**Event Sourcing**
: Architectural pattern where all changes are stored as a sequence of transactions that can be replayed.

## F

**FindAll**
: Client method to query multiple documents.

**FindOne**
: Client method to query a single document.

**FindOptions**
: Options for queries: `sort`, `limit`, `skip`, `projection`, `lookup`.

## H

**Hierarchy**
: System that manages class inheritance and relationships.

## I

**Index**
: Database index for fast queries. Can be `FullText` or `Indexed`.

**IntlString**
: Internationalized string identifier (e.g., `myPlugin.string.Title`).

## L

**Lookup**
: Query option that automatically loads referenced documents (like SQL JOIN).

## M

**Mixin**
: Special class that adds optional attributes to existing classes at runtime.

**Model**
: The data schema - all class and attribute definitions.

**ModelDb**
: In-memory cache of the model on client side.

## N

**Notification**
: Message sent to users about events (task assigned, comment added, etc.).

**NotificationType**
: Definition of when and how to send notifications.

## O

**Obj**
: Root of all objects. Has `_class` reference to its type.

**ObjectId**
: Alias for document ID (`Ref<Doc>`).

## P

**Permission**
: Atomic access control unit (e.g., `CreateTask`, `UpdateSpace`).

**PersonId**
: Social ID for a person (email, GitHub ID, etc.).

**PersonUuid**
: Globally unique identifier for a person (same as AccountUuid when account exists).

**Pipeline**
: Server component that processes transactions and applies them to storage.

**Plugin**
: Module that extends HULY with new functionality.

**PluginConfiguration**
: Document defining plugin metadata and enablement.

**Projection**
: Query option to select specific fields (optimizes performance).

## Q

**Query**
: Request to find documents matching criteria.

## R

**Ref**
: Type-safe reference to a document: `Ref<Task>`.

**Resource**
: Anything provided by a plugin: component, function, action, etc.

**Role**
: Space-level role that grants specific permissions (Manager, Developer, QA).

**RPC**
: Remote Procedure Call - method invocation between client and server.

**Rush**
: Microsoft's monorepo manager, used by HULY for managing packages.

## S

**Space**
: Container for documents. Every document must belong to a space.

**SpaceType**
: Configuration for a typed space, including roles and permissions.

**SpaceTypeDescriptor**
: Blueprint defining what kind of typed space (Project Type, Teamspace Type, etc.).

**SystemSpace**
: Special space used by the system (Model, Tx, Configuration).

## T

**Transactor**
: Server component that processes transactions.

**Transaction (Tx)**
: Represents a change to data (create, update, delete, mixin).

**TxCreateDoc**
: Transaction that creates a document.

**TxUpdateDoc**
: Transaction that updates a document.

**TxRemoveDoc**
: Transaction that deletes a document.

**TxMixin**
: Transaction that applies a mixin to a document.

**TypedSpace**
: Space with a custom type that has roles and permissions.

## U

**Uuid**
: Universally Unique Identifier.

## V

**Viewlet**
: Configuration for how to display documents (table, board, list).

## W

**Workspace**
: Top-level organizational unit - isolated tenant with own data, users, and configuration.

**WorkspaceUuid**
: Unique identifier for a workspace.

**WebSocket**
: Protocol used for real-time bidirectional communication between client and server.

## Symbol Reference

```typescript
// Common symbols in code
Ref<T> // Reference to document of type T
Data<T> // T without Doc metadata fields
Mixin<T> // Mixin type
Class<T> // Class type
Collection<T> // Collection of attached documents
CollectionSize<T> // Number of items in collection
Timestamp // Unix timestamp (milliseconds)
Markup // HTML/Markdown string
MarkupBlobRef // Reference to collaborative document blob
IntlString // Internationalized string
Asset // Icon/image asset reference
AnyComponent // Svelte component type
Resource<T> // Plugin resource type
```

## Decorator Reference

```typescript
@Model(id, extends, domain)     // Define class
@Mixin(id, extends)             // Define mixin
@UX(label, icon, ...)           // UI metadata
@Prop(type, label)              // Define property
@Index(kind)                    // Add index
@Hidden()                       // Hide from UI
@ReadOnly()                     // Make read-only
@Collection(class)              // Define collection
```

## Method Reference

### Client Methods

```typescript
client.createDoc(class, space, data)
client.update(doc, operations)
client.updateDoc(class, space, id, operations)
client.remove(doc)
client.removeDoc(class, space, id)

client.findAll(class, query, options)
client.findOne(class, query, options)
client.searchFulltext(query, options)

client.createMixin(id, class, space, mixin, data)
client.updateMixin(id, class, space, mixin, data)

client.addCollection(class, space, attachedTo, attachedToClass, collection, data)
```

### Hierarchy Methods

```typescript
hierarchy.getClass(ref)
hierarchy.isDerived(child, parent)
hierarchy.getAllAttributes(class)
hierarchy.getAttribute(class, key)
hierarchy.hasMixin(doc, mixin)
hierarchy.as(doc, mixin)
hierarchy.findDomain(class)
```

## Quick Links

- [Full Architecture Overview](architecture/overview)
- [Core Concepts](architecture/model/core-concepts)
- [Plugin Development](development/plugin-development)
- [Quick Reference](quick-reference)

---

**Tip:** Use Ctrl+F / Cmd+F to search this page for terms you need to understand!
