# Welcome to HULY Documentation

Welcome to the comprehensive documentation for **HULY** - an all-in-one platform for teams.

## What is HULY?

HULY is a powerful, flexible platform that combines project management, document collaboration, and team communication into a single, cohesive system. Built on a sophisticated model architecture, HULY provides:

- 🎯 **Project Management** - Track issues, tasks, and milestones
- 📝 **Document Collaboration** - Create and manage documents with team members
- 💬 **Team Communication** - Integrated messaging and chat
- 🔐 **Fine-grained Permissions** - Control access at workspace and space levels
- 🔌 **Plugin Architecture** - Extend functionality with custom plugins
- ⚡ **Real-time Sync** - Changes propagate instantly to all users

## Documentation Structure

This documentation is organized into several sections:

### Getting Started

Learn how to install, configure, and develop with HULY:

- [Installation Guide](getting-started/installation) - Complete setup guide
- [Configuration](getting-started/configuration) - Environment variables & services
- [Daily Workflow](getting-started/daily-workflow) - Essential commands & workflows
- [Creating Your First Workspace](getting-started/first-workspace) - Workspace setup

### User Guide

Understand how to use HULY's features effectively:

- [Managing Workspaces](user-guide/workspaces)
- [Working with Projects](user-guide/projects)
- [Collaborating on Documents](user-guide/documents)
- [Tracking Issues](user-guide/issues)

### Development

Complete guides for plugin and feature development:

- [Plugin Development](development/plugin-development) - Step-by-step plugin creation
- [Plugin Recipes](development/plugin-recipes) - Common patterns and solutions
- [Plugin Debugging](development/plugin-debugging) - Debug and troubleshoot
- [UI Components](development/ui-components) - Build beautiful UIs with Svelte
- [Querying Data](development/querying-data) - Master the Client API
- [Testing Guide](development/testing-guide) - Unit, integration, and E2E tests

### Best Practices

- [Best Practices & Performance](best-practices) - Code quality, optimization, security

### Deployment

- [Production Deployment](deployment/production-deployment) - Deploy to production

### Architecture

Deep dive into HULY's technical architecture:

#### Model Architecture

Understand HULY's data model and object system:

- [Core Concepts](architecture/model/core-concepts) - Obj, Doc, AttachedDoc
- [Workspaces](architecture/model/workspaces) - Tenant isolation
- [Spaces](architecture/model/spaces) - Container system
- [Typed Spaces](architecture/model/typed-spaces) - Customizable spaces
- [Roles & Permissions](architecture/model/roles-permissions) - Access control
- [Transactions](architecture/model/transactions) - Event sourcing
- [Mixins](architecture/model/mixins) - Dynamic type extensions
- [Domains](architecture/model/domains) - Storage organization
- [Builder](architecture/model/builder) - Model definition

#### System Architecture

Learn how HULY's core systems work:

- [Plugin System](architecture/plugin-system) - Extensibility & modularity
- [Client-Server Communication](architecture/client-server) - WebSocket protocol
- [Migration System](architecture/migrations) - Schema evolution
- [Activity & Notifications](architecture/activity-notifications) - Change tracking
- [Server Triggers](architecture/server-triggers) - Automated workflows
- [Search & Indexing](architecture/search-indexing) - Full-text search
- [Blob Storage](architecture/blob-storage) - File handling
- [Collaborative Editing](architecture/collaborative-editing) - Real-time collaboration
- [Authentication](architecture/authentication) - Security & auth
- [AI System](architecture/ai-system) - AI bot and features
- [Services & Pods](architecture/services-pods) - Microservices architecture

#### Technical Implementation

Implementation details:

- [Storage Architecture](architecture/technical/storage) - Multi-adapter storage
- [Server Architecture](architecture/technical/server) - Server components
- [Client Architecture](architecture/technical/client) - Client implementation

## Quick Links

### For Users

- 📚 [User Guide](user-guide/workspaces) - Start using HULY
- 🚀 [Getting Started](getting-started/installation) - Install and configure

### For Developers

- 🚀 [Daily Workflow](getting-started/daily-workflow) - Essential commands after git pull
- 🔌 [Plugin Development](development/plugin-development) - Create your first plugin
- 📚 [Plugin Recipes](development/plugin-recipes) - Common patterns & solutions
- 🎨 [UI Components](development/ui-components) - Build UIs with Svelte
- 🔍 [Querying Data](development/querying-data) - Master the Client API
- ✅ [Testing Guide](development/testing-guide) - Test your code
- 🏆 [Best Practices](best-practices) - Code quality & performance
- 🏗️ [Architecture Overview](architecture/overview) - System architecture
- 💾 [Model Architecture](architecture/model/core-concepts) - Data model deep dive

### For Contributors

- 📖 [Migration System](architecture/migrations) - Database migrations
- 🔔 [Activity & Notifications](architecture/activity-notifications) - Notification system
- ⚡ [Server Triggers](architecture/server-triggers) - Automated workflows
- 🔍 [Search & Indexing](architecture/search-indexing) - Full-text search system
- 🤖 [AI System](architecture/ai-system) - AI bot and OpenAI integration
- 🏗️ [Services & Pods](architecture/services-pods) - Microservices architecture
- 🚀 [Production Deployment](deployment/production-deployment) - Deploy to production
- 💻 [GitHub Repository](https://github.com/hcengineering/platform) - Source code

## Contributing

HULY is open source! Contributions are welcome. Check out the GitHub repository for contribution guidelines.

## License

HULY is licensed under the Eclipse Public License, Version 2.0.

---

Ready to get started? Head over to the [Installation Guide](getting-started/installation) or dive into the [Architecture Overview](architecture/overview).
