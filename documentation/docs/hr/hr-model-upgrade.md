---
id: hr-model-upgrade
title: HR model upgrade (BambooHR-style)
slug: /hr/model-upgrade
---

This note documents the HR model and UI improvements implemented in this branch, what’s still missing, and recommended next steps.

## Summary

We upgraded the HR domain to be more BambooHR-like:

- Richer Employee (Staff) profile: manager, employment status, FTE, office, hire/termination dates, internal employee ID, work email, and assigned time‑off policies.
- Robust Time‑Off domain: Policy, Balance, and Transaction entities for accruals, carryovers, adjustments, and a full audit trail.
- Enhanced Requests: store computed requested days and half‑day flags to support partial days accurately.
- Persistence improvements: request creation now stores requestedDays and half‑day placeholders.
- Utilities: business day calculation helpers to compute requested days excluding weekends (and ready to exclude holidays per department).

Build has been validated across pods after deduplicating conflicting string/class mappings in the server plugin.

## Files changed (high level)

Server model:

- models/matrics-hr/src/index.ts
  - Staff (TStaff) extended with BambooHR-like attributes
  - Request (TRequest) extended with `requestedDays`, `halfDayStart`, `halfDayEnd`
  - New classes: `TTimeOffPolicy`, `TTimeOffBalance`, `TTimeOffTransaction`
  - All new classes registered in `createModel(...)`
- models/matrics-hr/src/plugin.ts
  - Pruned overlapping strings/class ids to avoid `mergeIds` collisions with resources plugin

Shared HR types (used by clients):

- plugins/matrics-hr/src/index.ts
  - Added interfaces: `TimeOffPolicy`, `TimeOffBalance`, `TimeOffTransaction`
  - Extended `Staff` and `Request` interfaces accordingly
  - Added class mappings for new entities

UI resources:

- plugins/matrics-hr-resources/src/plugin.ts
  - Added Intl string keys for new fields/entities (labels in UI)
- plugins/matrics-hr-resources/src/components/CreateRequest.svelte
  - Persist `requestedDays` and half‑day placeholders on save
- plugins/matrics-hr/src/utils.ts
  - `tzDateKey`, `businessDaysBetween`, `computeRequestedDays` helper functions

Sidebar:

- `plugins/matrics-hr-resources/src/components/sidebar/Sidebar.svelte` already included “My Profile” and “My Time Off.” No functional changes needed; navigation dispatch is correct. (One cosmetic svelte‑check type message on icon remains.)

## What’s missing (gaps)

### Time‑Off Policy management UI

- Create/Read/Update forms and presenter for `TimeOffPolicy`
- Validation (e.g., accrualMethod/Period combos, carryover caps)

### Balances and Transactions UI

- “My Time Off” page showing balances (current/pending) and a ledger of `TimeOffTransaction`
- Admin view to adjust balances (adjustment transactions) and inspect history

### Half‑day support in Request UI

- Controls to mark `halfDayStart`/`halfDayEnd`
- Use `computeRequestedDays()` to calculate fractional days precisely

### Holiday-aware day calculations

- Feed `businessDaysBetween` with a department/office holiday set
- Use `PublicHoliday` docs to build a holiday map per department

### Accrual/carryover workflows

- Scheduled jobs or workflows that:
  - Accrue balances per policy (monthly/anniversary/fixed date)
  - Apply carryover and expirations
  - Write resulting `TimeOffTransaction` entries and update `TimeOffBalance`

### Policy enforcement

- On Request creation: check policy rules (waiting period, half‑day allowed, negative balance allowed, required approvals)
- Approval routing (auto-select approver, escalations)

### Data migration/backfill

- Seed `TimeOffBalance` and an initial `TimeOffTransaction` from existing `ptoBalance`, `vacationBalance`, `sickBalance`
- Link to default `TimeOffPolicy` per leave type

### Permissions & visibility

- Ensure employees only see their own balances/transactions, managers see their team, admins see all

### Notifications & webhooks

- Hook `Request` transitions (submitted/approved/rejected) to notifications and possible external integrations

### Reporting & exports

- Simple reports: usage per period, balance changes, policy violations, upcoming expirations

### Tests

- Unit tests for day calculation, policy checks, and request validation
- Integration tests to ensure workflows update balances correctly

## Next steps (recommended order)

### 1. UI for policies and balances

- Build minimal screens:
  - Policy list + editor for `TimeOffPolicy`
  - My Time Off page: list balances and transactions

### 0.5. Centralized settings (done)

- People & Culture now appears under Settings → Workspace Settings as a dedicated category.
- Inside it, HR managers will find Offices, Policies, and Workflows. Access is gated in UI for department managers and workspace admins.
- Components added: PeopleCultureSettings and PeopleCultureSettingsNavigation (reusing OfficesList, PolicyList, WorkflowList).

### 2. Request UI half-day support

- Add `halfDayStart`/`halfDayEnd` toggles and switch to `computeRequestedDays()` for saving

### 3. Holiday support

- Derive a holiday set from `PublicHoliday` for the employee’s department and feed it to `computeRequestedDays()`

### 4. Policy enforcement in request creation

- Validate negative balances (disallow unless policy allows)
- Enforce waiting periods and half-day allowance
- Set approver if required by policy

### 5. Accrual/carryover jobs

- Implement scheduled accrual strategies based on policy
- Write `TimeOffTransaction` entries and update `TimeOffBalance`

### 6. Migration

- Script: read `ptoBalance`/`vacationBalance`/`sickBalance` -> create balances + initial transactions
- Assign default `TimeOffPolicy` for existing leave types

### 7. Testing

- Unit tests for utils and policy rules
- E2E tests for request submission/approval and balance debits

## Try it

-- Bundle the repository (validated after changes):

```bash
rush bundle
```

-- Optional: Svelte type check for the HR resources plugin

```bash
pushd plugins/matrics-hr-resources && npm run -s svelte-check && popd
```

## Notes

- I avoided `mergeIds` collisions by pruning duplicate string/class mappings from the server plugin (`models/matrics-hr/src/plugin.ts`). UI strings live in resources plugin.
- The system is ready for your planned “workflows that add days on a specific date” via `TimeOffTransaction` entries and updates to `TimeOffBalance` according to `TimeOffPolicy`.
