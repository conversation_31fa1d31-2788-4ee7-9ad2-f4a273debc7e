# UI Components Guide

Complete guide to building UI components for HULY plugins using Svelte.

## Overview

HULY's UI is built with:

- **Svelte** - Component framework
- **TypeScript** - Type safety
- **Custom UI Kit** - `@hcengineering/ui` package
- **Theme System** - CSS variables for styling

## Using Built-in Components

### Import from UI Package

```typescript
import { Button, CheckBox, EditBox, Label, Icon, showPopup, Modal } from '@hcengineering/ui'
```

### Common Components

#### Button

```svelte
<script lang="ts">
  import { Button } from '@hcengineering/ui'

  function handleClick() {
    console.log('Clicked!')
  }
</script>

<Button
  label={myFeature.string.CreateTask}
  icon={myFeature.icon.Task}
  on:click={handleClick}
/>

<!-- Variants -->
<Button label="Primary" kind="primary" />
<Button label="Secondary" kind="secondary" />
<Button label="Dangerous" kind="dangerous" />
<Button label="Ghost" kind="ghost" />

<!-- Sizes -->
<Button label="Small" size="small" />
<Button label="Medium" size="medium" />
<Button label="Large" size="large" />

<!-- States -->
<Button label="Disabled" disabled={true} />
<Button label="Loading" loading={true} />
```

#### EditBox (Input)

```svelte
<script lang="ts">
  import { EditBox } from '@hcengineering/ui'

  let value: string = ''
</script>

<!-- Single line -->
<EditBox
  bind:value
  placeholder="Enter task title..."
  on:change={(e) => console.log('Changed:', e.detail)}
/>

<!-- Multiline -->
<EditBox
  bind:value
  placeholder="Enter description..."
  multiline
  rows={5}
/>

<!-- With validation -->
<EditBox
  bind:value
  placeholder="Email..."
  format="email"
  required
  on:validate={(e) => {
    if (!e.detail.includes('@')) {
      return 'Invalid email'
    }
  }}
/>
```

#### CheckBox

```svelte
<script lang="ts">
  import { CheckBox } from '@hcengineering/ui'

  let completed: boolean = false
</script>

<CheckBox
  bind:checked={completed}
  on:value={(e) => console.log('New value:', e.detail)}
/>

<CheckBox
  checked={completed}
  label="Mark as completed"
  disabled={false}
/>
```

#### Label

```svelte
<script lang="ts">
  import { Label } from '@hcengineering/ui'
  import myFeature from '@hcengineering/my-awesome-feature'
</script>

<Label label={myFeature.string.TaskTitle} />

<!-- With parameters -->
<Label
  label={myFeature.string.TaskCount}
  params={{ count: tasks.length }}
/>
```

#### Icon

```svelte
<script lang="ts">
  import { Icon } from '@hcengineering/ui'
  import myFeature from '@hcengineering/my-awesome-feature'
</script>

<Icon icon={myFeature.icon.Task} size="small" />
<Icon icon={myFeature.icon.Task} size="medium" />
<Icon icon={myFeature.icon.Task} size="large" />

<!-- With color -->
<Icon
  icon={myFeature.icon.Task}
  fill="var(--theme-accent-color)"
/>
```

### Modal Dialogs

```svelte
<!-- CreateTaskModal.svelte -->
<script lang="ts">
  import { EditBox, Button, createEventDispatcher } from '@hcengineering/ui'

  export let space: Ref<Space>

  let title: string = ''
  let description: string = ''

  const dispatch = createEventDispatcher()

  async function create() {
    const client = getClient()
    await client.createDoc(myFeature.class.Task, space, {
      title,
      description,
      completed: false
    })

    dispatch('close')
  }
</script>

<div class="modal">
  <div class="modal-header">
    <Label label={myFeature.string.CreateTask} />
  </div>

  <div class="modal-body">
    <EditBox
      bind:value={title}
      placeholder="Task title..."
      focus
    />

    <EditBox
      bind:value={description}
      placeholder="Description..."
      multiline
      rows={5}
    />
  </div>

  <div class="modal-footer">
    <Button
      label={core.string.Cancel}
      kind="ghost"
      on:click={() => dispatch('close')}
    />
    <Button
      label={core.string.Create}
      kind="primary"
      disabled={!title}
      on:click={create}
    />
  </div>
</div>

<style>
  .modal {
    display: flex;
    flex-direction: column;
    width: 500px;
    max-width: 90vw;
  }

  .modal-body {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid var(--theme-divider-color);
  }
</style>
```

**Show modal:**

```typescript
import { showPopup } from '@hcengineering/ui'
import CreateTaskModal from './CreateTaskModal.svelte'

// Show modal
showPopup(CreateTaskModal, { space: currentSpace._id })

// Show modal and wait for result
const result = await showPopup(CreateTaskModal, { space: currentSpace._id })
console.log('Modal result:', result)
```

## Data Binding

### Reactive Queries

```svelte
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'
  import type { Task } from '@hcengineering/my-awesome-feature'

  export let space: Ref<Space>

  const client = getClient()

  // Reactive query - auto-updates when data changes!
  $: tasks = client.findAll(myFeature.class.Task, {
    space,
    completed: false
  }, {
    sort: { modifiedOn: -1 }
  })

  // Derived reactive values
  $: pendingCount = tasks.then(t => t.length)
</script>

{#await tasks}
  <div class="loading">Loading tasks...</div>
{:then taskList}
  <div class="task-list">
    {#each taskList as task}
      <TaskView {task} />
    {/each}
  </div>
{:catch error}
  <div class="error">Error: {error.message}</div>
{/await}
```

### Reactive Updates

```svelte
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { getClient } from '@hcengineering/presentation'

  export let task: Task

  const client = getClient()
  const dispatch = createEventDispatcher()

  // Update document
  async function updateTitle(newTitle: string) {
    await client.update(task, { title: newTitle })
    // Task prop will auto-update via transaction broadcast!
  }

  // Watch for changes
  $: {
    console.log('Task changed:', task)
    dispatch('taskChanged', task)
  }
</script>
```

## Layout Components

### Flex Layouts

```svelte
<div class="flex-row">
  <div class="flex-item">Left</div>
  <div class="flex-item">Right</div>
</div>

<div class="flex-col">
  <div class="flex-item">Top</div>
  <div class="flex-item">Bottom</div>
</div>

<style>
  .flex-row {
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .flex-item {
    flex: 1;
  }
</style>
```

### Grid Layouts

```svelte
<div class="grid">
  <div class="grid-item">1</div>
  <div class="grid-item">2</div>
  <div class="grid-item">3</div>
  <div class="grid-item">4</div>
</div>

<style>
  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }
</style>
```

### Scrollable Areas

```svelte
<div class="scrollable">
  {#each items as item}
    <div class="item">{item}</div>
  {/each}
</div>

<style>
  .scrollable {
    overflow-y: auto;
    max-height: 400px;
  }
</style>
```

## Theming

### Using Theme Variables

```svelte
<style>
  .my-component {
    /* Colors */
    color: var(--theme-content-color);
    background: var(--theme-bg-color);
    border-color: var(--theme-divider-color);

    /* Accents */
    --accent: var(--theme-accent-color);

    /* Dark colors */
    --dark-bg: var(--theme-bg-dark-color);

    /* Spacing */
    padding: var(--spacing-1);  /* 0.25rem */
    margin: var(--spacing-2);   /* 0.5rem */
    gap: var(--spacing-4);      /* 1rem */

    /* Border radius */
    border-radius: var(--border-radius-1);  /* 0.25rem */

    /* Shadows */
    box-shadow: var(--theme-shadow);

    /* Fonts */
    font-size: var(--theme-font-size-1);  /* 0.75rem */
    font-family: var(--theme-font-family);
  }

  /* Dark mode support (automatic!) */
  .my-component:global(.dark) {
    /* Automatically uses dark theme variables */
  }
</style>
```

### Common Theme Variables

```css
/* Text colors */
--theme-content-color           /* Primary text */
--theme-content-trans-color     /* Secondary text */
--theme-content-accent-color    /* Accent text */

/* Background colors */
--theme-bg-color               /* Primary background */
--theme-bg-accent-color        /* Accent background */
--theme-bg-dark-color          /* Dark background */

/* Interactive elements */
--theme-button-default         /* Default button */
--theme-button-hovered        /* Hovered button */
--theme-button-pressed        /* Pressed button */

/* Status colors */
--theme-error-color           /* Errors */
--theme-warning-color         /* Warnings */
--theme-success-color         /* Success */
--theme-info-color            /* Info */

/* Borders */
--theme-divider-color         /* Dividers */
--theme-border-color          /* Borders */
```

## Component Patterns

### Loading States

```svelte
<script lang="ts">
  let loading = false

  async function loadData() {
    loading = true
    try {
      const data = await fetchData()
      // Use data
    } finally {
      loading = false
    }
  }
</script>

{#if loading}
  <div class="loading">
    <Spinner />
    <span>Loading...</span>
  </div>
{:else}
  <div class="content">
    <!-- Your content -->
  </div>
{/if}
```

### Empty States

```svelte
{#if tasks.length === 0}
  <div class="empty-state">
    <Icon icon={myFeature.icon.Task} size="large" />
    <Label label={myFeature.string.NoTasksYet} />
    <Button
      label={myFeature.string.CreateFirstTask}
      on:click={createTask}
    />
  </div>
{:else}
  {#each tasks as task}
    <TaskView {task} />
  {/each}
{/if}
```

### Error States

```svelte
<script lang="ts">
  let error: Error | null = null

  async function loadData() {
    try {
      const data = await fetchData()
    } catch (err) {
      error = err as Error
    }
  }
</script>

{#if error}
  <div class="error-state">
    <Icon icon={core.icon.Error} fill="var(--theme-error-color)" />
    <span>Error: {error.message}</span>
    <Button label="Retry" on:click={loadData} />
  </div>
{/if}
```

## Advanced Patterns

### Virtual Scrolling

For large lists:

```svelte
<script lang="ts">
  import { VirtualList } from '@hcengineering/ui'
  import type { Task } from '@hcengineering/my-awesome-feature'

  export let tasks: Task[]
</script>

<VirtualList
  items={tasks}
  itemHeight={60}
  let:item={task}
>
  <TaskView {task} />
</VirtualList>
```

### Drag and Drop

```svelte
<script lang="ts">
  import { dndZone } from '@hcengineering/ui'

  let tasks: Task[] = []

  function handleSort(e: CustomEvent) {
    tasks = e.detail.items
    // Save new order
    saveSortOrder(tasks)
  }
</script>

<div use:dndZone={{ items: tasks }} on:consider={handleSort} on:finalize={handleSort}>
  {#each tasks as task (task._id)}
    <div class="draggable-task">
      <TaskView {task} />
    </div>
  {/each}
</div>
```

### Context Menus

```svelte
<script lang="ts">
  import { showPopup } from '@hcengineering/ui'
  import ContextMenu from './ContextMenu.svelte'

  function showMenu(event: MouseEvent, task: Task) {
    event.preventDefault()

    showPopup(ContextMenu, {
      task,
      x: event.clientX,
      y: event.clientY
    })
  }
</script>

<div class="task" on:contextmenu={(e) => showMenu(e, task)}>
  <!-- Task content -->
</div>

<!-- ContextMenu.svelte -->
<script lang="ts">
  import { Menu, MenuItem } from '@hcengineering/ui'

  export let task: Task
  export let x: number
  export let y: number

  const items = [
    {
      label: myFeature.string.Edit,
      icon: core.icon.Edit,
      action: () => editTask(task)
    },
    {
      label: myFeature.string.Delete,
      icon: core.icon.Delete,
      action: () => deleteTask(task)
    }
  ]
</script>

<Menu {items} position={{ x, y }} />
```

### Tooltips

```svelte
<script lang="ts">
  import { Tooltip } from '@hcengineering/ui'
</script>

<div class="tooltip-trigger" use:tooltip={{ label: myFeature.string.ClickToEdit }}>
  <Icon icon={core.icon.Edit} />
</div>

<!-- Or with component -->
<Tooltip label="Click to edit">
  <Button icon={core.icon.Edit} kind="ghost" />
</Tooltip>
```

## Responsive Design

### Mobile-Friendly Components

```svelte
<script lang="ts">
  import { deviceInfo } from '@hcengineering/ui'

  $: isMobile = $deviceInfo.isMobile
</script>

{#if isMobile}
  <!-- Mobile layout -->
  <div class="mobile-view">
    <TaskListMobile tasks={tasks} />
  </div>
{:else}
  <!-- Desktop layout -->
  <div class="desktop-view">
    <TaskListDesktop tasks={tasks} />
  </div>
{/if}

<style>
  /* Responsive breakpoints */
  @media (max-width: 768px) {
    .desktop-view {
      display: none;
    }
  }

  @media (min-width: 769px) {
    .mobile-view {
      display: none;
    }
  }
</style>
```

## Forms

### Form Component

```svelte
<!-- TaskForm.svelte -->
<script lang="ts">
  import { EditBox, DatePicker, EmployeeBox, Button } from '@hcengineering/ui'
  import type { Data } from '@hcengineering/core'
  import type { Task } from '@hcengineering/my-awesome-feature'

  export let task: Data<Task> = {
    title: '',
    description: '',
    completed: false
  }

  export let onSubmit: (task: Data<Task>) => Promise<void>

  let loading = false
  let errors: Record<string, string> = {}

  function validate(): boolean {
    errors = {}

    if (!task.title) {
      errors.title = 'Title is required'
      return false
    }

    if (task.title.length < 3) {
      errors.title = 'Title must be at least 3 characters'
      return false
    }

    return true
  }

  async function submit() {
    if (!validate()) return

    loading = true
    try {
      await onSubmit(task)
    } catch (err) {
      errors.general = err.message
    } finally {
      loading = false
    }
  }
</script>

<form on:submit|preventDefault={submit}>
  <div class="form-field">
    <Label label={myFeature.string.TaskTitle} />
    <EditBox
      bind:value={task.title}
      placeholder="Enter title..."
      error={errors.title}
    />
  </div>

  <div class="form-field">
    <Label label={myFeature.string.TaskDescription} />
    <EditBox
      bind:value={task.description}
      placeholder="Enter description..."
      multiline
      rows={5}
    />
  </div>

  <div class="form-field">
    <Label label={myFeature.string.DueDate} />
    <DatePicker bind:value={task.dueDate} />
  </div>

  <div class="form-field">
    <Label label={myFeature.string.Assignee} />
    <EmployeeBox bind:value={task.assignee} />
  </div>

  {#if errors.general}
    <div class="error">{errors.general}</div>
  {/if}

  <div class="form-actions">
    <Button
      type="submit"
      label={core.string.Save}
      kind="primary"
      disabled={loading}
      {loading}
    />
  </div>
</form>

<style>
  form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .form-field {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .error {
    color: var(--theme-error-color);
    font-size: 0.875rem;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
  }
</style>
```

## Accessibility

### Keyboard Navigation

```svelte
<script lang="ts">
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' && event.ctrlKey) {
      createTask()
      event.preventDefault()
    }

    if (event.key === 'Escape') {
      closeModal()
      event.preventDefault()
    }
  }
</script>

<div on:keydown={handleKeydown} tabindex="0">
  <!-- Content -->
</div>
```

### ARIA Labels

```svelte
<button
  aria-label="Complete task"
  aria-pressed={task.completed}
  on:click={toggleComplete}
>
  <Icon icon={myFeature.icon.Task} />
</button>

<input
  type="text"
  aria-label="Task title"
  aria-required="true"
  aria-invalid={!!errors.title}
  bind:value={task.title}
/>
```

### Focus Management

```svelte
<script lang="ts">
  import { tick } from 'svelte'

  let inputElement: HTMLInputElement

  async function focusInput() {
    await tick()  // Wait for DOM update
    inputElement?.focus()
  }
</script>

<input bind:this={inputElement} />
```

## Performance Optimization

### Memoization

```svelte
<script lang="ts">
  import { derived } from 'svelte/store'

  export let tasks: Task[]

  // Memoize expensive computation
  $: completedTasks = derived(
    [tasks],
    ([$tasks]) => $tasks.filter(t => t.completed)
  )

  // Only recomputes when tasks change
  $: completionRate = $completedTasks.length / tasks.length
</script>
```

### Virtual Scrolling

Use virtual scrolling for large lists (1000+ items):

```svelte
<script lang="ts">
  import { VirtualList } from '@hcengineering/ui'

  export let tasks: Task[]  // Can be 10,000 items!
</script>

<VirtualList
  items={tasks}
  itemHeight={60}
  let:item={task}
>
  <TaskView {task} />
</VirtualList>
```

### Lazy Loading

```svelte
<script lang="ts">
  let visible = false

  function onIntersect() {
    visible = true
  }
</script>

<div use:intersect on:intersect={onIntersect}>
  {#if visible}
    <ExpensiveComponent />
  {:else}
    <div class="placeholder">Loading...</div>
  {/if}
</div>
```

## Testing UI Components

### Component Tests

```typescript
import { render, fireEvent } from '@testing-library/svelte'
import TaskView from '../TaskView.svelte'

describe('TaskView', () => {
  it('should render task', () => {
    const task = {
      _id: 'task-1',
      title: 'Test Task',
      completed: false
    }

    const { getByText } = render(TaskView, { props: { task } })

    expect(getByText('Test Task')).toBeInTheDocument()
  })

  it('should toggle completion', async () => {
    const task = { ...testTask, completed: false }

    const { getByRole } = render(TaskView, { props: { task } })
    const checkbox = getByRole('checkbox')

    await fireEvent.click(checkbox)

    expect(task.completed).toBe(true)
  })
})
```

## Best Practices

### ✅ Do's

- Use reactive statements (`$:`) for derived values
- Bind to form inputs for two-way data binding
- Use theme variables for all colors
- Add loading and error states
- Make components accessible (ARIA labels)
- Use virtual scrolling for large lists
- Optimize expensive computations
- Test components in isolation

### ❌ Don'ts

- Don't hardcode colors (use theme variables)
- Don't query inside render loop
- Don't forget to handle loading/error states
- Don't ignore accessibility
- Don't create components for every tiny piece
- Don't inline large styles (use external CSS)
- Don't forget to clean up subscriptions

## Summary

HULY UI components:

- ✅ Built with Svelte
- ✅ Reactive and performant
- ✅ Themed with CSS variables
- ✅ Accessible
- ✅ Mobile-friendly
- ✅ Reusable UI kit

Key concepts:

- Use built-in components from `@hcengineering/ui`
- Reactive queries with `client.findAll`
- Theme variables for styling
- Modal dialogs with `showPopup`
- Virtual scrolling for performance
- Accessibility with ARIA

Build beautiful, performant UIs for your plugins! 🎨
