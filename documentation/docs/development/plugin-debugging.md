# Plugin Debugging & Troubleshooting

Complete guide to debugging HULY plugins and solving common issues.

## Development Tools

### Browser DevTools

Essential for client-side debugging:

#### Console

```javascript
// Enable verbose logging
localStorage.setItem('platform.logging.enabled', 'true')

// Filter logs
localStorage.setItem('platform.logging.filter', 'my-awesome-feature')

// View all transactions
client.subscribe((tx) => {
  console.log('Transaction:', tx)
})
```

#### Network Tab

Monitor WebSocket communication:

1. Open DevTools → Network tab
2. Filter by WS (WebSocket)
3. Click on connection
4. View Messages tab
5. See all RPC calls and transactions

#### React/Svelte DevTools

- Install Svelte DevTools extension
- Inspect component hierarchy
- View component props and state
- Monitor reactivity

### VSCode Debugging

#### Client Debugging

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Debug Client",
      "url": "http://localhost:8080",
      "webRoot": "${workspaceFolder}/plugins/my-awesome-feature-resources/src",
      "sourceMapPathOverrides": {
        "webpack:///./*": "${webRoot}/*"
      }
    }
  ]
}
```

Set breakpoints in `.svelte` and `.ts` files!

#### Server Debugging

```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Server",
  "program": "${workspaceFolder}/server/server/lib/index.js",
  "env": {
    "DB_URL": "postgresql://postgres:example@localhost:5432/huly",
    "DEBUG": "*"
  },
  "sourceMaps": true,
  "outFiles": ["${workspaceFolder}/server/**/*.js"]
}
```

## Logging Strategies

### Client-Side Logging

```typescript
import { Analytics } from '@hcengineering/analytics'

// Info logging
console.log('[MyPlugin] Task created:', task)

// Warning
console.warn('[MyPlugin] Potential issue:', data)

// Error with stack trace
console.error('[MyPlugin] Failed to create task:', error)

// Analytics
Analytics.handleEvent({
  event: 'task_action',
  properties: { action: 'create', success: true }
})
```

### Server-Side Logging

```typescript
export default async () => ({
  trigger: {
    OnTaskCreate: async (txes, control) => {
      // Use control.ctx for structured logging
      control.ctx.info('Task created', {
        count: txes.length,
        plugin: 'my-awesome-feature'
      })

      control.ctx.warn('Large batch', {
        count: txes.length
      })

      control.ctx.error('Failed to process', {
        error: err.message
      })

      // Measure performance
      await control.ctx.with('process-tasks', {}, async (ctx) => {
        // Your code
        ctx.info('Processing complete', { processed: count })
      })
    }
  }
})
```

## Common Issues

### Issue: Plugin Not Loading

**Symptoms:**

- Component not rendering
- "Resource not found" errors
- Blank screen

**Causes & Solutions:**

1. **Plugin not registered**

   ```typescript
   // Check: Is plugin in locations?
   import { addLocation } from '@hcengineering/platform'
   addLocation(myFeatureId, () => import('@hcengineering/my-awesome-feature-resources'))
   ```

2. **Resource not exported**

   ```typescript
   // Check: Is resource in default export?
   export default async (): Promise<Resources> => ({
     component: {
       TaskView // ← Must be here!
     }
   })
   ```

3. **Build not updated**

   ```bash
   # Rebuild plugin
   cd plugins/my-awesome-feature-resources
   rushx build
   ```

4. **Model not applied**
   ```bash
   # Upgrade workspace
   rushx workspace-tool upgrade
   ```

### Issue: Type Errors

**Symptoms:**

- TypeScript compilation errors
- "Cannot find name" errors

**Solutions:**

1. **Run rush validate**

   ```bash
   rush validate
   ```

2. **Rebuild dependencies**

   ```bash
   rush build --to @hcengineering/my-awesome-feature
   ```

3. **Check imports**

   ```typescript
   // ❌ Wrong
   import { Task } from './types'

   // ✅ Correct
   import { Task } from '@hcengineering/my-awesome-feature'
   ```

### Issue: Data Not Saving

**Symptoms:**

- Updates don't persist
- Documents disappear after refresh

**Debug steps:**

1. **Check transaction is sent**

   ```typescript
   client.subscribe((tx) => {
     console.log('TX:', tx)
   })
   ```

2. **Check for errors in console**

   - Look for "Permission denied"
   - Look for validation errors

3. **Check space is correct**

   ```typescript
   // ❌ Wrong space
   await client.createDoc(myFeature.class.Task, undefined, {...})

   // ✅ Correct space
   await client.createDoc(myFeature.class.Task, currentSpace._id, {...})
   ```

4. **Check model is loaded**
   ```typescript
   const hierarchy = client.getHierarchy()
   const taskClass = hierarchy.getClass(myFeature.class.Task)
   console.log('Task class:', taskClass)
   ```

### Issue: Server Trigger Not Firing

**Symptoms:**

- Server-side code not executing
- Notifications not sent

**Debug steps:**

1. **Check trigger is registered**

   ```typescript
   // In models/server-my-awesome-feature/src/index.ts
   builder.createDoc(serverCore.class.Trigger, core.space.Model, {
     trigger: serverMyFeature.trigger.OnTaskCreate,
     txMatch: {
       objectClass: myFeature.class.Task,
       _class: core.class.TxCreateDoc
     }
   })
   ```

2. **Check txMatch pattern**

   ```typescript
   // Trigger only fires if transaction matches pattern
   txMatch: {
     objectClass: myFeature.class.Task,  // Must match exactly
     _class: core.class.TxCreateDoc      // Create vs Update vs Remove
   }
   ```

3. **Add logging**

   ```typescript
   async function OnTaskCreate(txes, control) {
     control.ctx.info('TRIGGER FIRED', { count: txes.length })
     // ... rest of code
   }
   ```

4. **Check server logs**
   ```bash
   # View server output
   docker-compose logs -f server
   ```

### Issue: Permissions Not Working

**Symptoms:**

- Users can't access features
- "Forbidden" errors

**Debug steps:**

1. **Check user is space member**

   ```typescript
   const space = await client.findOne(core.class.Space, { _id: spaceId })
   console.log('Members:', space.members)
   console.log('Current user:', getCurrentAccount().uuid)
   ```

2. **Check role assignments**

   ```typescript
   const hierarchy = client.getHierarchy()
   const spaceType = await client.findOne(core.class.SpaceType, {
     _id: space.type
   })

   const mixin = spaceType.targetClass
   const asMixin = hierarchy.as(space, mixin)

   console.log('Role assignments:', asMixin)
   ```

3. **Check permission definition**
   ```typescript
   const permission = await client.findOne(core.class.Permission, {
     _id: myFeature.permission.CreateTask
   })
   console.log('Permission:', permission)
   ```

## Debugging Techniques

### Trace Execution

```typescript
function createTask(data: TaskData): Promise<Ref<Task>> {
  console.trace('[createTask] Called from:')
  console.log('[createTask] Data:', data)

  const client = getClient()

  return client
    .createDoc(myFeature.class.Task, data.space, data)
    .then((id) => {
      console.log('[createTask] Created:', id)
      return id
    })
    .catch((err) => {
      console.error('[createTask] Failed:', err)
      throw err
    })
}
```

### Inspect Model

```typescript
// Get class info
const hierarchy = client.getHierarchy()
const taskClass = hierarchy.getClass(myFeature.class.Task)
console.log('Task class:', taskClass)

// Get all attributes
const attrs = hierarchy.getAllAttributes(myFeature.class.Task)
console.log('Attributes:', Array.from(attrs.entries()))

// Check inheritance
console.log('Extends:', hierarchy.getClass(taskClass.extends))
console.log('Is Doc?', hierarchy.isDerived(myFeature.class.Task, core.class.Doc))
```

### Monitor Performance

```typescript
// Measure operation time
console.time('create-task')
await client.createDoc(myFeature.class.Task, space, data)
console.timeEnd('create-task')

// Profile rendering
import { tick } from 'svelte'

console.time('render')
await tick() // Wait for render
console.timeEnd('render')
```

### Network Debugging

```typescript
// Monitor RPC calls
const originalSend = client.send
client.send = function (...args) {
  console.log('[RPC] Sending:', args)
  return originalSend.apply(this, args)
}

// Monitor responses
connection.onResponse = (response) => {
  console.log('[RPC] Response:', response)
}
```

## Error Handling

### Graceful Error Handling

```typescript
async function createTask(data: TaskData): Promise<Ref<Task> | undefined> {
  try {
    return await client.createDoc(myFeature.class.Task, data.space, data)
  } catch (err: any) {
    // Log error
    console.error('Failed to create task:', err)
    Analytics.handleError(err)

    // Show user-friendly message
    await showPopup(notification.component.Notification, {
      message: myFeature.string.FailedToCreateTask,
      type: 'error'
    })

    // Return undefined instead of throwing
    return undefined
  }
}
```

### Transaction Rollback

```typescript
// Use TxApplyIf for atomic operations
const tx: TxApplyIf = {
  _class: core.class.TxApplyIf,
  space: core.space.Tx,

  // Check condition
  match: [
    {
      _class: myFeature.class.Task,
      query: { _id: taskId, completed: false }
    }
  ],

  // If condition true, apply these
  txes: [
    client.txFactory.createTxUpdateDoc(myFeature.class.Task, task.space, taskId, {
      completed: true
    })
  ]
}

const result = await client.tx(tx)
if (!result.success) {
  console.error('Task already completed or not found')
}
```

## Performance Debugging

### Find Slow Queries

```typescript
// Wrap queries with timing
async function findTasks(filter: DocumentQuery<Task>) {
  console.time('findTasks')
  const tasks = await client.findAll(myFeature.class.Task, filter)
  console.timeEnd('findTasks')
  return tasks
}

// Use query options to optimize
const tasks = await client.findAll(
  myFeature.class.Task,
  { space: spaceId },
  {
    limit: 50, // Don't load thousands
    projection: {
      // Only load needed fields
      _id: 1,
      title: 1,
      completed: 1
    }
  }
)
```

### Find Memory Leaks

```typescript
// Monitor component lifecycle
<script lang="ts">
  import { onMount, onDestroy } from 'svelte'

  onMount(() => {
    console.log('[TaskView] Mounted')
  })

  onDestroy(() => {
    console.log('[TaskView] Destroyed')
    // Clean up subscriptions!
    unsubscribe()
  })
</script>
```

### Profile Rendering

```typescript
// Use Svelte profiler
<script lang="ts">
  $: {
    console.time('render-task')
    // Reactive code
    console.timeEnd('render-task')
  }
</script>
```

## Best Practices for Debugging

### ✅ Do's

- Use structured logging with context
- Add debug logs liberally during development
- Remove verbose logs before committing
- Use browser breakpoints for step-through debugging
- Monitor network tab for RPC issues
- Check server logs for backend issues
- Use TypeScript strict mode
- Write tests for complex logic

### ❌ Don'ts

- Don't commit console.log statements
- Don't ignore TypeScript errors
- Don't debug without source maps
- Don't test only in development mode
- Don't skip error handling
- Don't assume data exists (always check)

## Debugging Checklist

When something doesn't work:

- [ ] Check browser console for errors
- [ ] Check Network tab for failed requests
- [ ] Check server logs
- [ ] Verify plugin is loaded: `getPlugins()`
- [ ] Verify resource exists: `await getResource(id)`
- [ ] Check model is applied: `rushx workspace-tool upgrade`
- [ ] Verify permissions
- [ ] Check space membership
- [ ] Rebuild plugin: `rush build --to <plugin>`
- [ ] Clear browser cache and reload

## Common Error Messages

### "Resource not found"

```
Error: Resource 'my-awesome-feature:component:TaskView' not found
```

**Fix:**

1. Check resource is exported in `index.ts`
2. Rebuild plugin: `rushx build`
3. Reload page

### "Permission denied"

```
Error: Permission denied for operation
```

**Fix:**

1. Check user has required permission
2. Check user is space member
3. Check role assignments in space

### "Cannot find module"

```
Error: Cannot find module '@hcengineering/my-awesome-feature'
```

**Fix:**

1. Run `rush update`
2. Run `rush build --to @hcengineering/my-awesome-feature`
3. Check package name in `package.json`

### "Invalid transaction"

```
Error: Invalid transaction: missing required field 'title'
```

**Fix:**

1. Check all required fields are provided
2. Check field types match model
3. Verify model is up to date

## Advanced Debugging

### Debug Transaction Processing

```typescript
// Hook into transaction processor
import { TxProcessor } from '@hcengineering/core'

const originalApply = TxProcessor.applyUpdate
TxProcessor.applyUpdate = function (doc, tx) {
  console.log('[TX] Applying to:', doc._id, tx)
  return originalApply(doc, tx)
}
```

### Debug Plugin Loading

```typescript
import { loadPlugin } from '@hcengineering/platform'

// Override load to add logging
const originalLoad = loadPlugin
loadPlugin = function (id) {
  console.log('[Plugin] Loading:', id)
  const start = Date.now()

  return originalLoad(id).then((resources) => {
    console.log('[Plugin] Loaded:', id, 'in', Date.now() - start, 'ms')
    return resources
  })
}
```

### Debug Query Performance

```typescript
// Wrap findAll with timing
const originalFindAll = client.findAll
client.findAll = async function (clazz, query, options) {
  const start = Date.now()
  const result = await originalFindAll.call(this, clazz, query, options)
  const time = Date.now() - start

  if (time > 1000) {
    console.warn('[Slow Query]', time, 'ms', { clazz, query, count: result.length })
  }

  return result
}
```

## Testing During Development

### Quick Manual Tests

```typescript
// Open browser console and run:

// Get client
const client = await import('@hcengineering/presentation').then((m) => m.getClient())

// Create test task
const taskId = await client.createDoc('my-awesome-feature:class:Task', 'space-id', {
  title: 'Debug Test Task',
  description: 'Testing from console',
  completed: false
})

// Find it
const task = await client.findOne('my-awesome-feature:class:Task', { _id: taskId })
console.log('Task:', task)

// Update it
await client.update(taskId, { completed: true })

// Delete it
await client.remove(taskId)
```

### Test Permissions

```typescript
// Check if user has permission
const canCreate = await checkPermission(client, 'my-awesome-feature:permission:CreateTask', spaceId)
console.log('Can create tasks:', canCreate)

// Get user's roles
const space = await client.findOne(core.class.TypedSpace, { _id: spaceId })
const spaceType = await client.findOne(core.class.SpaceType, { _id: space.type })
const hierarchy = client.getHierarchy()
const asMixin = hierarchy.as(space, spaceType.targetClass)

console.log(
  'My roles:',
  Object.entries(asMixin).filter(([key, value]) => key.startsWith('role:') && value.includes(getCurrentAccount().uuid))
)
```

## Summary

Debugging tools:

- ✅ Browser DevTools (Console, Network, Elements)
- ✅ VSCode debugger (breakpoints, step-through)
- ✅ Logging (client and server)
- ✅ Analytics and monitoring
- ✅ Manual testing via console

Common issues:

- ✅ Plugin not loading → Check registration and build
- ✅ Type errors → Run rush validate
- ✅ Data not saving → Check transactions and permissions
- ✅ Server trigger not firing → Check trigger registration
- ✅ Permissions → Check roles and membership

Debug strategies:

- ✅ Structured logging
- ✅ Performance monitoring
- ✅ Transaction tracing
- ✅ Model inspection
- ✅ Query optimization

With these tools and techniques, you can debug any plugin issue! 🐛🔍
