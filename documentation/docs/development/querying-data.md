# Querying Data

Complete guide to querying data in HULY using the Client API.

## Client API Overview

The HULY client provides several methods for querying data:

```typescript
interface Client {
  // Find all documents matching query
  findAll<T extends Doc>(
    _class: Ref<Class<T>>,
    query: DocumentQuery<T>,
    options?: FindOptions<T>
  ): Promise<FindResult<T>>

  // Find single document
  findOne<T extends Doc>(
    _class: Ref<Class<T>>,
    query: DocumentQuery<T>,
    options?: FindOptions<T>
  ): Promise<T | undefined>

  // Full-text search
  searchFulltext(query: SearchQuery, options: SearchOptions): Promise<SearchResult>

  // Subscribe to query results (reactive)
  subscribe<T extends Doc>(_class: Ref<Class<T>>, query: DocumentQuery<T>, callback: (docs: T[]) => void): () => void
}
```

## Basic Queries

### Find All Documents

```typescript
import { getClient } from '@hcengineering/presentation'

const client = getClient()

// Get all tasks
const allTasks = await client.findAll(myFeature.class.Task, {})

// Get tasks in specific space
const spaceTasks = await client.findAll(myFeature.class.Task, {
  space: spaceId
})

// Get completed tasks
const completedTasks = await client.findAll(myFeature.class.Task, {
  completed: true
})

// Get tasks with assignee
const assignedTasks = await client.findAll(myFeature.class.Task, {
  assignee: { $exists: true }
})
```

### Find One Document

```typescript
// Find by ID
const task = await client.findOne(myFeature.class.Task, {
  _id: taskId
})

// Find first matching
const firstPending = await client.findOne(
  myFeature.class.Task,
  {
    completed: false
  },
  {
    sort: { createdOn: 1 }
  }
)

// Returns undefined if not found
if (!task) {
  console.log('Task not found')
}
```

## Query Operators

### Comparison Operators

```typescript
// Equals
{
  priority: 'high'
}
{
  assignee: employeeId
}

// Not equals
{
  priority: {
    $ne: 'low'
  }
}
{
  assignee: {
    $ne: null
  }
}

// Greater than / Less than
{
  dueDate: {
    $gt: Date.now()
  }
}
{
  estimatedHours: {
    $gte: 8
  }
}
{
  actualHours: {
    $lt: 40
  }
}
{
  actualHours: {
    $lte: 40
  }
}

// In array
{
  status: {
    $in: ['todo', 'in-progress']
  }
}
{
  priority: {
    $in: priorities
  }
}

// Not in array
{
  status: {
    $nin: ['completed', 'cancelled']
  }
}
```

### Logical Operators

```typescript
// AND (implicit)
{
  completed: false,
  priority: 'high'
}

// OR
{
  $or: [
    { priority: 'urgent' },
    { dueDate: { $lt: Date.now() } }
  ]
}

// AND + OR
{
  completed: false,
  $or: [
    { assignee: currentUser },
    { createdBy: currentUser }
  ]
}

// NOT
{
  completed: { $ne: true }
}
```

### Array Operators

```typescript
// Array contains value
{ tags: 'important' }

// Array contains all values
{ tags: { $all: ['important', 'urgent'] } }

// Array size
{ tags: { $size: 3 } }

// Array exists and not empty
{ tags: { $exists: true, $ne: [] } }
```

### String Operators

```typescript
// Case-insensitive contains
{
  title: {
    $like: '%bug%'
  }
}

// Starts with
{
  title: {
    $like: 'Fix%'
  }
}

// Ends with
{
  title: {
    $like: '%issue'
  }
}

// Regex
{
  title: {
    $regex: '^BUG-[0-9]+$'
  }
}
```

### Existence Operators

```typescript
// Field exists
{ description: { $exists: true } }

// Field does not exist or is null
{ description: { $exists: false } }

// Field exists and is not null
{ description: { $exists: true, $ne: null } }
```

## Query Options

### Sorting

```typescript
import { SortingOrder } from '@hcengineering/core'

// Sort ascending
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    sort: { title: SortingOrder.Ascending }
  }
)

// Sort descending
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    sort: { modifiedOn: SortingOrder.Descending }
  }
)

// Multiple sort fields
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    sort: {
      priority: SortingOrder.Descending,
      dueDate: SortingOrder.Ascending
    }
  }
)
```

### Limit and Skip (Pagination)

```typescript
// Get first 10 tasks
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    limit: 10
  }
)

// Get next 10 tasks (page 2)
const moreTasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    limit: 10,
    skip: 10
  }
)

// Pagination helper
async function getPage(page: number, pageSize: number = 20) {
  return await client.findAll(
    myFeature.class.Task,
    {},
    {
      limit: pageSize,
      skip: page * pageSize,
      sort: { createdOn: SortingOrder.Descending }
    }
  )
}
```

### Projection (Select Specific Fields)

```typescript
// Only load specific fields (performance optimization)
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    projection: {
      _id: 1,
      title: 1,
      completed: 1
      // description, assignee, etc. are NOT loaded
    }
  }
)

// Exclude specific fields
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    projection: {
      description: 0 // Don't load description
    }
  }
)
```

### Lookup (Join References)

```typescript
// Automatically load referenced documents
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    lookup: {
      assignee: contact.class.Employee, // Load employee data
      status: myFeature.class.TaskStatus // Load status data
    }
  }
)

// Access looked-up data
for (const task of tasks) {
  const employee = task.$lookup.assignee // Employee object, not just Ref!
  console.log('Assigned to:', employee.name)

  const status = task.$lookup.status
  console.log('Status:', status.name)
}

// Multi-level lookup
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    lookup: {
      assignee: contact.class.Employee,
      _lookup: {
        assignee: {
          // Look up employee's manager
          manager: contact.class.Employee
        }
      }
    }
  }
)

console.log('Manager:', task.$lookup.assignee.$lookup.manager.name)
```

## Advanced Queries

### Nested Field Queries

```typescript
// Query on looked-up fields
const tasks = await client.findAll(
  myFeature.class.Task,
  {
    '$lookup.assignee.department': 'Engineering'
  },
  {
    lookup: {
      assignee: contact.class.Employee
    }
  }
)
```

### Array Field Queries

```typescript
// Tasks with specific tag
const tasks = await client.findAll(myFeature.class.Task, {
  tags: 'urgent'
})

// Tasks with any of these tags
const tasks = await client.findAll(myFeature.class.Task, {
  tags: { $in: ['urgent', 'important'] }
})

// Tasks with all these tags
const tasks = await client.findAll(myFeature.class.Task, {
  tags: { $all: ['urgent', 'important'] }
})
```

### Date Range Queries

```typescript
// Tasks due this week
const weekStart = new Date()
weekStart.setHours(0, 0, 0, 0)
weekStart.setDate(weekStart.getDate() - weekStart.getDay())

const weekEnd = new Date(weekStart)
weekEnd.setDate(weekEnd.getDate() + 7)

const thisWeekTasks = await client.findAll(myFeature.class.Task, {
  dueDate: {
    $gte: weekStart.getTime(),
    $lt: weekEnd.getTime()
  }
})

// Tasks created in last 30 days
const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000
const recentTasks = await client.findAll(myFeature.class.Task, {
  createdOn: { $gte: thirtyDaysAgo }
})
```

### Complex Queries

```typescript
// Tasks that are:
// - Not completed
// - Either high priority OR overdue
// - Assigned to current user OR created by current user
const me = getCurrentAccount()

const myUrgentTasks = await client.findAll(myFeature.class.Task, {
  completed: false,
  $or: [{ priority: 'high' }, { dueDate: { $lt: Date.now() } }],
  $or: [{ assignee: me.uuid }, { createdBy: me.uuid }]
})
```

## Reactive Queries (Svelte)

### Auto-Updating Queries

```svelte
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'

  export let space: Ref<Space>
  export let showCompleted: boolean = false

  const client = getClient()

  // Query automatically updates when:
  // - space changes
  // - showCompleted changes
  // - tasks are created/updated/deleted
  $: tasks = client.findAll(myFeature.class.Task, {
    space,
    ...(showCompleted ? {} : { completed: false })
  })
</script>

{#await tasks then taskList}
  {#each taskList as task}
    <TaskView {task} />
  {/each}
{/await}
```

### Derived Reactive Queries

```svelte
<script lang="ts">
  const client = getClient()

  $: allTasks = client.findAll(myFeature.class.Task, { space })

  // Derived values automatically recompute
  $: pendingCount = allTasks.then(tasks =>
    tasks.filter(t => !t.completed).length
  )

  $: completionRate = allTasks.then(tasks => {
    const completed = tasks.filter(t => t.completed).length
    return tasks.length > 0 ? completed / tasks.length : 0
  })
</script>

<div>
  {#await pendingCount then count}
    Pending: {count}
  {/await}

  {#await completionRate then rate}
    Completion: {(rate * 100).toFixed(1)}%
  {/await}
</div>
```

## Full-Text Search

### Basic Search

```typescript
// Search across all task fields
const results = await client.searchFulltext({
  query: 'bug fix',
  classes: [myFeature.class.Task],
  options: {
    limit: 20
  }
})

// Results include score and highlights
for (const result of results.docs) {
  console.log('Match:', result.doc.title, 'Score:', result.score)
}
```

### Advanced Search

```typescript
// Search with filters
const results = await client.searchFulltext({
  query: 'performance',
  classes: [myFeature.class.Task],
  filter: {
    completed: false,
    priority: 'high'
  },
  options: {
    limit: 50,
    fields: ['title', 'description'] // Only search these fields
  }
})
```

## Aggregations

### Count

```typescript
// Get count without loading all documents
const count = await client.findAll(
  myFeature.class.Task,
  {
    completed: false
  },
  {
    projection: { _id: 1 },
    limit: 0 // Don't return docs, just count
  }
)

console.log('Pending tasks:', count.total)
```

### Group By

```typescript
// Group tasks by status
const tasks = await client.findAll(
  myFeature.class.Task,
  { space },
  {
    lookup: { status: myFeature.class.TaskStatus }
  }
)

const byStatus = tasks.reduce((acc, task) => {
  const status = task.$lookup.status.name
  acc[status] = (acc[status] || 0) + 1
  return acc
}, {} as Record<string, number>)

console.log('By status:', byStatus)
// { "To Do": 5, "In Progress": 3, "Done": 12 }
```

### Sum/Average

```typescript
// Calculate total hours
const tasks = await client.findAll(myFeature.class.Task, { space })

const totalHours = tasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0)

const avgHours = totalHours / tasks.length
```

## Query Patterns

### Find or Create

```typescript
async function findOrCreateTask(title: string, space: Ref<Space>): Promise<Task> {
  // Try to find existing
  let task = await client.findOne(myFeature.class.Task, {
    space,
    title
  })

  // Create if not found
  if (!task) {
    const taskId = await client.createDoc(myFeature.class.Task, space, { title, description: '', completed: false })

    task = await client.findOne(myFeature.class.Task, { _id: taskId })
  }

  return task!
}
```

### Batch Loading

```typescript
// Load multiple documents by ID efficiently
async function loadTasks(taskIds: Ref<Task>[]): Promise<Task[]> {
  return await client.findAll(myFeature.class.Task, {
    _id: { $in: taskIds }
  })
}

// Load with lookup in single query
async function loadTasksWithAssignees(taskIds: Ref<Task>[]): Promise<Task[]> {
  return await client.findAll(
    myFeature.class.Task,
    {
      _id: { $in: taskIds }
    },
    {
      lookup: {
        assignee: contact.class.Employee
      }
    }
  )
}
```

### Hierarchical Queries

```typescript
// Get task and all subtasks
async function getTaskTree(taskId: Ref<Task>): Promise<Task[]> {
  const root = await client.findOne(myFeature.class.Task, { _id: taskId })
  if (!root) return []

  const children = await client.findAll(
    myFeature.class.Task,
    {
      parent: taskId
    },
    {
      sort: { rank: 1 }
    }
  )

  // Recursively get children's children
  const allDescendants = [root]
  for (const child of children) {
    const subtree = await getTaskTree(child._id)
    allDescendants.push(...subtree)
  }

  return allDescendants
}
```

### Time-based Queries

```typescript
// Tasks modified today
const todayStart = new Date()
todayStart.setHours(0, 0, 0, 0)

const todaysTasks = await client.findAll(myFeature.class.Task, {
  modifiedOn: { $gte: todayStart.getTime() }
})

// Tasks created this month
const monthStart = new Date()
monthStart.setDate(1)
monthStart.setHours(0, 0, 0, 0)

const thisMonthTasks = await client.findAll(myFeature.class.Task, {
  createdOn: { $gte: monthStart.getTime() }
})

// Tasks due in next 7 days
const nextWeek = Date.now() + 7 * 24 * 60 * 60 * 1000

const upcomingTasks = await client.findAll(myFeature.class.Task, {
  completed: false,
  dueDate: {
    $gte: Date.now(),
    $lte: nextWeek
  }
})
```

## Performance Optimization

### Projection (Load Only Needed Fields)

```typescript
// ❌ Bad - loads everything
const tasks = await client.findAll(myFeature.class.Task, {})

// ✅ Good - only loads needed fields
const tasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    projection: {
      _id: 1,
      title: 1,
      completed: 1
    }
  }
)

// 50% smaller payload, faster query, less memory
```

### Limit Results

```typescript
// ❌ Bad - loads all 10,000 tasks
const tasks = await client.findAll(myFeature.class.Task, { space })

// ✅ Good - only loads what you show
const tasks = await client.findAll(
  myFeature.class.Task,
  { space },
  {
    limit: 50
  }
)
```

### Use Indexes

```typescript
// ✅ Indexed field - fast query
const tasks = await client.findAll(myFeature.class.Task, {
  completed: false  // Indexed field
})

// ❌ Non-indexed field - slow query
const tasks = await client.findAll(myFeature.class.Task, {
  customField: 'value'  // Not indexed
})

// Add index in model:
@Prop(TypeString(), myFeature.string.CustomField)
@Index(IndexKind.Indexed)  // ← Add this
customField!: string
```

### Batch Queries

```typescript
// ❌ Bad - N queries (N = tasks.length)
for (const task of tasks) {
  task.assignee = await client.findOne(contact.class.Employee, {
    _id: task.assignee
  })
}

// ✅ Good - 1 query with lookup
const tasks = await client.findAll(
  myFeature.class.Task,
  { space },
  {
    lookup: {
      assignee: contact.class.Employee
    }
  }
)
```

## Reactive Queries in Svelte

### Basic Reactive Query

```svelte
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'

  export let space: Ref<Space>

  const client = getClient()

  // Automatically updates when tasks change!
  $: tasks = client.findAll(myFeature.class.Task, { space })
</script>

{#await tasks then taskList}
  <div>
    Found {taskList.length} tasks
    {#each taskList as task}
      <TaskView {task} />
    {/each}
  </div>
{/await}
```

### Reactive with Filters

```svelte
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'
  import { CheckBox } from '@hcengineering/ui'

  export let space: Ref<Space>

  let showCompleted = false
  let searchQuery = ''

  const client = getClient()

  // Query updates when showCompleted or searchQuery change
  $: tasks = client.findAll(myFeature.class.Task, {
    space,
    ...(showCompleted ? {} : { completed: false }),
    ...(searchQuery ? { title: { $like: `%${searchQuery}%` } } : {})
  })
</script>

<div>
  <EditBox
    bind:value={searchQuery}
    placeholder="Search tasks..."
  />

  <CheckBox
    bind:checked={showCompleted}
    label="Show completed"
  />

  {#await tasks then taskList}
    {#each taskList as task}
      <TaskView {task} />
    {/each}
  {/await}
</div>
```

### Derived Reactive Values

```svelte
<script lang="ts">
  const client = getClient()

  $: tasks = client.findAll(myFeature.class.Task, { space })

  // Derived values auto-update
  $: stats = tasks.then(list => ({
    total: list.length,
    completed: list.filter(t => t.completed).length,
    pending: list.filter(t => !t.completed).length,
    overdue: list.filter(t =>
      !t.completed &&
      t.dueDate &&
      t.dueDate < Date.now()
    ).length
  }))
</script>

{#await stats then data}
  <div class="stats">
    <div>Total: {data.total}</div>
    <div>Completed: {data.completed}</div>
    <div>Pending: {data.pending}</div>
    <div>Overdue: {data.overdue}</div>
  </div>
{/await}
```

## Query Subscriptions

### Manual Subscription

```typescript
// Subscribe to query results
const unsubscribe = client.subscribe(myFeature.class.Task, { space: spaceId, completed: false }, (tasks) => {
  console.log('Tasks updated:', tasks)
  updateUI(tasks)
})

// Unsubscribe when done
onDestroy(() => {
  unsubscribe()
})
```

### Reactive Store Subscription

```svelte
<script lang="ts">
  import { writable } from 'svelte/store'
  import { onDestroy } from 'svelte'

  const tasks = writable<Task[]>([])

  const unsubscribe = client.subscribe(
    myFeature.class.Task,
    { space },
    (result) => {
      tasks.set(result)
    }
  )

  onDestroy(unsubscribe)
</script>

<!-- Use store with $ prefix -->
{#each $tasks as task}
  <TaskView {task} />
{/each}
```

## Query Best Practices

### ✅ Do's

- Use projection to load only needed fields
- Add indexes for frequently queried fields
- Use limit to prevent loading too much data
- Use lookup instead of separate queries
- Use reactive queries in Svelte components
- Cache query results when appropriate
- Unsubscribe from queries when component unmounts

### ❌ Don'ts

- Don't query in loops (use $in or lookup)
- Don't load all documents without limit
- Don't query non-indexed fields for large datasets
- Don't forget to sort for pagination
- Don't query in reactive statements that run frequently
- Don't forget error handling
- Don't mutate query results directly

## Query Debugging

### Log Queries

```typescript
const originalFindAll = client.findAll

client.findAll = async function (clazz, query, options) {
  console.log('[Query]', { clazz, query, options })
  const start = Date.now()

  const result = await originalFindAll.call(this, clazz, query, options)

  console.log('[Query Result]', {
    count: result.length,
    time: Date.now() - start
  })

  return result
}
```

### Explain Query Performance

```typescript
// Check if query uses index
const tasks = await client.findAll(myFeature.class.Task, {
  assignee: employeeId  // Should use index
})

// Check query time
console.time('query')
await client.findAll(...)
console.timeEnd('query')

// If slow (>100ms for simple queries):
// 1. Check if fields are indexed
// 2. Reduce result count with limit
// 3. Use projection to load fewer fields
```

## Summary

Query methods:

- ✅ `findAll` - Find multiple documents
- ✅ `findOne` - Find single document
- ✅ `searchFulltext` - Full-text search
- ✅ `subscribe` - Reactive queries

Query operators:

- ✅ Comparison: `$gt`, `$lt`, `$gte`, `$lte`, `$ne`
- ✅ Array: `$in`, `$nin`, `$all`, `$size`
- ✅ Logic: `$or`, `$and`
- ✅ String: `$like`, `$regex`
- ✅ Existence: `$exists`

Query options:

- ✅ `sort` - Order results
- ✅ `limit` - Limit count
- ✅ `skip` - Pagination offset
- ✅ `projection` - Select fields
- ✅ `lookup` - Join references

Performance:

- ✅ Use projection for large documents
- ✅ Use limit for large result sets
- ✅ Use indexes for query fields
- ✅ Use lookup instead of separate queries
- ✅ Use reactive queries in Svelte

Master querying for efficient, performant plugins! 🔍
