# Plugin Development Guide

Step-by-step guide to developing plugins for HULY, from concept to deployment.

## Before You Start

### Understanding Plugins

A HULY plugin is a set of packages that work together:

```
Your Feature
├── Type definitions  → plugins/my-feature/
├── Translations     → plugins/my-feature-assets/
├── UI & Logic       → plugins/my-feature-resources/
├── Data Model       → models/my-feature/
└── Server Logic     → server-plugins/my-feature-resources/
```

**Why this structure?**

- **Separation of concerns** - Types, UI, model, and server are independent
- **Lazy loading** - UI code only loads when needed
- **Type safety** - Types are shared across all packages
- **Hot reloading** - Edit UI without rebuilding everything

### Prerequisites

- Familiarity with TypeScript
- Understanding of React/Svelte (for UI)
- Basic knowledge of HULY's model system
- Rush monorepo experience (optional)

## Creating Your First Plugin

### Step 1: Generate Plugin Structure

```bash
# Generate complete plugin structure
cd /path/to/huly
npm run gen:plugin -- --name my-awesome-feature --with-server

# This creates:
# - plugins/my-awesome-feature/
# - plugins/my-awesome-feature-assets/
# - plugins/my-awesome-feature-resources/
# - models/my-awesome-feature/
# - server-plugins/my-awesome-feature-resources/
```

### Step 2: Install Dependencies

```bash
# Update dependencies for new packages
rush update

# Rebuild to generate types
rush build
```

### Step 3: Define Your Plugin

Edit `plugins/my-awesome-feature/src/index.ts`:

```typescript
import { type Ref, type Class, type Doc, plugin, Plugin } from '@hcengineering/platform'
import { type IntlString, type Asset } from '@hcengineering/platform'
import { type AnyComponent } from '@hcengineering/ui'

/**
 * Plugin ID - must be unique
 */
export const myAwesomeFeatureId = 'my-awesome-feature' as Plugin

/**
 * Your domain models
 */
export interface Task extends Doc {
  title: string
  description: string
  completed: boolean
  dueDate?: Date
  assignee?: Ref<Employee>
}

export interface TaskList extends TypedSpace {
  icon?: string
  color?: number
}

/**
 * Plugin definition
 * This declares all resources without implementing them
 */
export default plugin(myAwesomeFeatureId, {
  // Classes (data model)
  class: {
    Task: '' as Ref<Class<Task>>,
    TaskList: '' as Ref<Class<TaskList>>
  },

  // UI Components
  component: {
    TaskView: '' as AnyComponent,
    TaskList: '' as AnyComponent,
    CreateTask: '' as AnyComponent
  },

  // Strings (for i18n)
  string: {
    MyAwesomeFeature: '' as IntlString,
    Tasks: '' as IntlString,
    CreateTask: '' as IntlString,
    TaskTitle: '' as IntlString,
    TaskDescription: '' as IntlString,
    DueDate: '' as IntlString
  },

  // Icons
  icon: {
    Task: '' as Asset,
    TaskList: '' as Asset
  },

  // Action handlers
  actionImpl: {
    CreateTask: '' as ViewAction,
    CompleteTask: '' as ViewAction
  },

  // Functions (utilities)
  function: {
    TaskTitleProvider: '' as Resource<(doc: Task) => string>,
    CanDeleteTask: '' as Resource<(doc: Task) => Promise<boolean>>
  },

  // Spaces
  space: {
    DefaultTaskList: '' as Ref<TaskList>
  }
})
```

### Step 4: Add Translations

Edit `plugins/my-awesome-feature-assets/lang/en.json`:

```json
{
  "MyAwesomeFeature": "My Awesome Feature",
  "Tasks": "Tasks",
  "CreateTask": "Create Task",
  "TaskTitle": "Title",
  "TaskDescription": "Description",
  "DueDate": "Due Date",
  "CompletedTasks": "Completed Tasks",
  "PendingTasks": "Pending Tasks"
}
```

### Step 5: Define Data Model

Edit `models/my-awesome-feature/src/index.ts`:

```typescript
import {
  Builder,
  Model,
  Prop,
  TypeString,
  TypeBoolean,
  TypeDate,
  TypeRef,
  Index,
  IndexKind,
  UX
} from '@hcengineering/model'
import core, { DOMAIN_MODEL, TDoc, TTypedSpace } from '@hcengineering/model-core'
import contact, { TEmployee } from '@hcengineering/model-contact'
import myFeature, { Task, TaskList } from '@hcengineering/my-awesome-feature'

// Define storage domain
export const DOMAIN_MY_FEATURE = 'my-awesome-feature' as Domain

/**
 * Task model definition
 */
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
@UX(myFeature.string.Tasks, myFeature.icon.Task, undefined, 'title')
export class TTask extends TDoc implements Task {
  @Prop(TypeString(), myFeature.string.TaskTitle)
  @Index(IndexKind.FullText)
  title!: string

  @Prop(TypeString(), myFeature.string.TaskDescription)
  @Index(IndexKind.FullText)
  description!: string

  @Prop(TypeBoolean(), myFeature.string.Completed)
  @Index(IndexKind.Indexed)
  completed!: boolean

  @Prop(TypeDate(), myFeature.string.DueDate)
  @Index(IndexKind.Indexed)
  dueDate?: Date

  @Prop(TypeRef(contact.class.Employee), myFeature.string.Assignee)
  @Index(IndexKind.Indexed)
  assignee?: Ref<Employee>
}

/**
 * TaskList space definition
 */
@Model(myFeature.class.TaskList, core.class.TypedSpace, DOMAIN_MODEL)
@UX(myFeature.string.TaskList, myFeature.icon.TaskList, undefined, 'name')
export class TTaskList extends TTypedSpace implements TaskList {
  @Prop(TypeString(), myFeature.string.Icon)
  icon?: string

  @Prop(TypeNumber(), myFeature.string.Color)
  color?: number
}

/**
 * Model builder
 */
export function createModel(builder: Builder): void {
  // Create model classes
  builder.createModel(TTask, TTaskList)

  // Create default space type
  builder.createDoc(
    core.class.SpaceTypeDescriptor,
    core.space.Model,
    {
      name: myFeature.string.TaskList,
      description: myFeature.string.TaskListDescription,
      icon: myFeature.icon.TaskList,
      baseClass: myFeature.class.TaskList,
      availablePermissions: [
        core.permission.UpdateSpace,
        core.permission.ArchiveSpace,
        myFeature.permission.CreateTask,
        myFeature.permission.CompleteTask
      ]
    },
    myFeature.descriptors.TaskList
  )

  // Create default space type instance
  builder.createDoc(
    core.class.SpaceType,
    core.space.Model,
    {
      name: 'Default Task List',
      descriptor: myFeature.descriptors.TaskList,
      roles: 0,
      targetClass: myFeature.class.TaskList
    },
    myFeature.spaceType.DefaultTaskList
  )

  // Create permissions
  builder.createDoc(
    core.class.Permission,
    core.space.Model,
    {
      label: myFeature.string.CreateTask,
      description: myFeature.string.CreateTaskPermission,
      scope: 'space'
    },
    myFeature.permission.CreateTask
  )

  // Create default roles
  builder.addCollection(
    core.class.Role,
    core.space.Model,
    myFeature.spaceType.DefaultTaskList,
    core.class.SpaceType,
    'roles',
    {
      name: 'Task Manager',
      permissions: [
        core.permission.UpdateSpace,
        myFeature.permission.CreateTask,
        myFeature.permission.CompleteTask,
        myFeature.permission.DeleteTask
      ]
    },
    myFeature.role.TaskManager
  )

  builder.addCollection(
    core.class.Role,
    core.space.Model,
    myFeature.spaceType.DefaultTaskList,
    core.class.SpaceType,
    'roles',
    {
      name: 'Task User',
      permissions: [myFeature.permission.CreateTask, myFeature.permission.CompleteTask]
    },
    myFeature.role.TaskUser
  )
}
```

### Step 6: Implement UI Components

Edit `plugins/my-awesome-feature-resources/src/components/TaskView.svelte`:

```svelte
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'
  import { Task } from '@hcengineering/my-awesome-feature'
  import { Button, CheckBox, EditBox } from '@hcengineering/ui'
  import contact from '@hcengineering/contact'
  import { EmployeePresenter } from '@hcengineering/contact-resources'

  export let task: Task

  const client = getClient()

  async function updateTask(update: Partial<Task>) {
    await client.update(task, update)
  }

  async function toggleComplete() {
    await updateTask({ completed: !task.completed })
  }
</script>

<div class="task-view">
  <div class="task-header">
    <CheckBox
      checked={task.completed}
      on:value={toggleComplete}
    />

    <EditBox
      value={task.title}
      on:change={(e) => updateTask({ title: e.detail })}
      placeholder="Task title..."
    />
  </div>

  <div class="task-details">
    <EditBox
      value={task.description}
      on:change={(e) => updateTask({ description: e.detail })}
      placeholder="Task description..."
      multiline
    />

    {#if task.assignee}
      <div class="assignee">
        <span>Assigned to:</span>
        <EmployeePresenter value={task.assignee} />
      </div>
    {/if}

    {#if task.dueDate}
      <div class="due-date">
        Due: {new Date(task.dueDate).toLocaleDateString()}
      </div>
    {/if}
  </div>
</div>

<style>
  .task-view {
    padding: 1rem;
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.5rem;
  }

  .task-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .task-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .assignee, .due-date {
    font-size: 0.875rem;
    color: var(--theme-content-color);
  }
</style>
```

### Step 7: Export Resources

Edit `plugins/my-awesome-feature-resources/src/index.ts`:

```typescript
import { type Resources } from '@hcengineering/platform'
import { type Client } from '@hcengineering/core'
import { type Task } from '@hcengineering/my-awesome-feature'

// Import components
import TaskView from './components/TaskView.svelte'
import TaskList from './components/TaskList.svelte'
import CreateTask from './components/CreateTask.svelte'

// Action implementations
async function CreateTaskAction(doc: any, event: any, props: any): Promise<void> {
  const client = getClient()
  await client.createDoc(myFeature.class.Task, props.space, {
    title: props.title,
    description: props.description || '',
    completed: false,
    dueDate: props.dueDate,
    assignee: props.assignee
  })
}

async function CompleteTaskAction(doc: Task): Promise<void> {
  const client = getClient()
  await client.update(doc, { completed: true })
}

// Function implementations
function TaskTitleProvider(doc: Task): string {
  return doc.title || 'Untitled Task'
}

async function CanDeleteTask(doc: Task): Promise<boolean> {
  // Only allow deleting completed tasks
  return doc.completed
}

/**
 * Export all plugin resources
 */
export default async (): Promise<Resources> => ({
  component: {
    TaskView,
    TaskList,
    CreateTask
  },

  actionImpl: {
    CreateTask: CreateTaskAction,
    CompleteTask: CompleteTaskAction
  },

  function: {
    TaskTitleProvider,
    CanDeleteTask
  }
})
```

### Step 8: Add Server Logic (Optional)

Edit `server-plugins/my-awesome-feature-resources/src/index.ts`:

```typescript
import { type TriggerControl } from '@hcengineering/server-core'
import { type TxCreateDoc, type Tx } from '@hcengineering/core'
import { type Task } from '@hcengineering/my-awesome-feature'
import notification from '@hcengineering/notification'

/**
 * Trigger when task is created
 */
async function OnTaskCreate(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    // Send notification to assignee
    if (task.assignee) {
      const notificationTx = control.txFactory.createTxCreateDoc(notification.class.InboxNotification, task.space, {
        user: task.assignee,
        header: myFeature.string.TaskAssigned,
        message: myFeature.string.TaskAssignedBody,
        objectId: task._id,
        objectClass: task._class,
        intlParams: {
          title: task.title
        },
        isViewed: false,
        archived: false
      })
      result.push(notificationTx)
    }
  }

  return result
}

/**
 * Export server resources
 */
export default async () => ({
  trigger: {
    OnTaskCreate
  },

  function: {
    // Server-side functions
  }
})
```

### Step 9: Register Plugin

Edit `models/all/src/index.ts`:

```typescript
import myAwesomeFeatureModel from '@hcengineering/model-my-awesome-feature'
import { myAwesomeFeatureId } from '@hcengineering/my-awesome-feature'

const builders: BuilderConfig[] = [
  // ... existing plugins

  [
    myAwesomeFeatureModel,
    myAwesomeFeatureId,
    {
      label: myFeature.string.MyAwesomeFeature,
      description: myFeature.string.PluginDescription,
      icon: myFeature.icon.TaskList,
      enabled: true,
      beta: false
    }
  ]
]
```

### Step 10: Build and Test

```bash
# Build your new plugin
rush build --to @hcengineering/my-awesome-feature-resources

# Start development server
rushx dev

# Access http://localhost:8080 and test your plugin!
```

## Advanced Topics

### Plugin Communication

Plugins can communicate with each other:

```typescript
// In my-awesome-feature-resources
import tracker from '@hcengineering/tracker'

// Use types from tracker plugin
export interface TaskIssue extends AttachedDoc<Issue> {
  task: Ref<Task>
}

// Call functions from other plugins
const issueTitle = await getResource(tracker.function.IssueTitleProvider)
const title = await issueTitle(issue)
```

### Custom Queries

Implement custom query functions:

```typescript
// In resources
export default async (): Promise<Resources> => ({
  function: {
    GetCompletedTasks: async (client: Client, space: Ref<Space>) => {
      return await client.findAll(
        myFeature.class.Task,
        {
          space,
          completed: true
        },
        {
          sort: { modifiedOn: -1 }
        }
      )
    }
  }
})
```

### Custom Presenters

Create custom presenters for your data:

```svelte
<!-- TaskPresenter.svelte -->
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'
  import { Task } from '@hcengineering/my-awesome-feature'
  import type { Ref } from '@hcengineering/core'

  export let value: Ref<Task>

  const client = getClient()

  $: task = client.findOne(myFeature.class.Task, { _id: value })
</script>

{#await task then task}
  {#if task}
    <div class="task-presenter">
      <span class:completed={task.completed}>
        {task.title}
      </span>
    </div>
  {/if}
{/await}
```

## Testing Your Plugin

### Unit Tests

Create `plugins/my-awesome-feature-resources/__tests__/task.test.ts`:

```typescript
import { describe, it, expect } from '@jest/globals'
import { TaskTitleProvider, CanDeleteTask } from '../src/index'

describe('Task utilities', () => {
  it('should provide task title', () => {
    const task = {
      title: 'My Task',
      completed: false
    } as Task

    expect(TaskTitleProvider(task)).toBe('My Task')
  })

  it('should allow deleting completed tasks', async () => {
    const task = {
      title: 'My Task',
      completed: true
    } as Task

    expect(await CanDeleteTask(task)).toBe(true)
  })

  it('should not allow deleting pending tasks', async () => {
    const task = {
      title: 'My Task',
      completed: false
    } as Task

    expect(await CanDeleteTask(task)).toBe(false)
  })
})
```

### Integration Tests

Test with actual HULY client:

```typescript
import { createClient } from '@hcengineering/server-client'
import myFeature from '@hcengineering/my-awesome-feature'

describe('Task creation', () => {
  let client: Client

  beforeAll(async () => {
    client = await createClient(/* ... */)
  })

  it('should create task', async () => {
    const taskId = await client.createDoc(myFeature.class.Task, 'space-1', {
      title: 'Test Task',
      description: 'Test Description',
      completed: false
    })

    const task = await client.findOne(myFeature.class.Task, {
      _id: taskId
    })

    expect(task).toBeDefined()
    expect(task?.title).toBe('Test Task')
  })
})
```

## Debugging

### Debug Client Code

```typescript
// Add console logs
console.log('Task created:', task)

// Use debugger
debugger

// Enable verbose logging
import { setMetadata } from '@hcengineering/platform'
setMetadata(client.metadata.EnableLogging, true)
```

### Debug Server Code

```typescript
// In server plugin
export default async () => ({
  trigger: {
    OnTaskCreate: async (txes, control) => {
      control.ctx.info('Task created', { count: txes.length })
      // ... rest of code
    }
  }
})
```

## Common Patterns

### Pattern: Attachments

Allow attaching files to your documents:

```typescript
import attachment from '@hcengineering/attachment'

@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  // ... other props

  @Prop(Collection(attachment.class.Attachment), attachment.string.Attachments)
  attachments?: number
}
```

### Pattern: Comments

Add commenting to your documents:

```typescript
import chunter from '@hcengineering/chunter'

@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  // ... other props

  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
  comments?: number
}
```

### Pattern: Activity Tracking

Track changes to your documents:

```typescript
import activity from '@hcengineering/activity'

// Activity is automatically tracked for all Doc subclasses!
// View activity:
const activities = await client.findAll(activity.class.DocUpdateMessage, { objectId: task._id })
```

## Next Steps

- [Plugin System Architecture](../architecture/plugin-system) - Deep dive
- [Model Architecture](../architecture/model/core-concepts) - Data model
- [Client-Server Communication](../architecture/client-server) - How plugins communicate

Your plugin is now ready for development! 🚀
