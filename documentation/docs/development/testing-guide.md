# Testing Guide

Comprehensive guide to testing HULY plugins and features.

## Testing Strategy

HULY uses multiple testing layers:

```
┌─────────────────────────────────────┐
│         E2E Tests                    │ (Playwright)
│  Full user workflows                 │
└─────────────────────────────────────┘
                  │
┌─────────────────────────────────────┐
│      Integration Tests               │ (Jest + Client)
│  Test with real HULY client          │
└─────────────────────────────────────┘
                  │
┌─────────────────────────────────────┐
│        Unit Tests                    │ (Jest)
│  Test individual functions           │
└─────────────────────────────────────┘
```

## Unit Tests

Test individual functions and utilities:

### Setup

```typescript
// __tests__/utils.test.ts
import { describe, it, expect } from '@jest/globals'
import { calculateTotal, formatDate } from '../utils'

describe('Utils', () => {
  it('should calculate total', () => {
    const items = [
      { price: 10, quantity: 2 },
      { price: 5, quantity: 3 }
    ]

    expect(calculateTotal(items)).toBe(35)
  })

  it('should format date', () => {
    const date = new Date('2024-01-15')
    expect(formatDate(date)).toBe('Jan 15, 2024')
  })
})
```

### Testing Pure Functions

```typescript
// src/utils.ts
export function getTaskPriority(task: Task): number {
  if (task.priority === 'urgent') return 4
  if (task.priority === 'high') return 3
  if (task.priority === 'medium') return 2
  return 1
}

// __tests__/utils.test.ts
describe('getTaskPriority', () => {
  it('should return 4 for urgent', () => {
    const task = { priority: 'urgent' } as Task
    expect(getTaskPriority(task)).toBe(4)
  })

  it('should return 1 for low', () => {
    const task = { priority: 'low' } as Task
    expect(getTaskPriority(task)).toBe(1)
  })
})
```

## Integration Tests

Test with HULY client:

### Setup Test Client

```typescript
import { createClient } from '@hcengineering/server-client'
import { createTestWorkspace } from '@hcengineering/test-utils'

describe('Task Creation', () => {
  let client: Client
  let workspace: WorkspaceIds
  let space: Ref<Space>

  beforeAll(async () => {
    // Create test workspace
    workspace = await createTestWorkspace('test-workspace')

    // Create client
    client = await createClient('ws://localhost:3333', generateTestToken(workspace.uuid))

    // Create test space
    space = await client.createDoc(myFeature.class.TaskList, core.space.Space, {
      name: 'Test Space',
      description: 'Test space',
      private: false,
      members: [testAccount]
    })
  })

  afterAll(async () => {
    await client.close()
    await deleteTestWorkspace(workspace)
  })

  it('should create task', async () => {
    const taskId = await client.createDoc(myFeature.class.Task, space, {
      title: 'Test Task',
      description: 'Test Description',
      completed: false
    })

    const task = await client.findOne(myFeature.class.Task, {
      _id: taskId
    })

    expect(task).toBeDefined()
    expect(task?.title).toBe('Test Task')
    expect(task?.completed).toBe(false)
  })

  it('should update task', async () => {
    const taskId = await createTestTask(client, space)

    await client.update(taskId, {
      title: 'Updated Title',
      completed: true
    })

    const updated = await client.findOne(myFeature.class.Task, {
      _id: taskId
    })

    expect(updated?.title).toBe('Updated Title')
    expect(updated?.completed).toBe(true)
  })

  it('should delete task', async () => {
    const taskId = await createTestTask(client, space)

    await client.remove(taskId)

    const deleted = await client.findOne(myFeature.class.Task, {
      _id: taskId
    })

    expect(deleted).toBeUndefined()
  })
})
```

### Testing Permissions

```typescript
describe('Task Permissions', () => {
  it('should allow user to create task', async () => {
    const userClient = await createClientWithRole(AccountRole.User)

    const taskId = await userClient.createDoc(myFeature.class.Task, space, { title: 'Test' })

    expect(taskId).toBeDefined()
  })

  it('should prevent guest from deleting task', async () => {
    const guestClient = await createClientWithRole(AccountRole.Guest)
    const task = await createTestTask(userClient, space)

    await expect(guestClient.remove(task._id)).rejects.toThrow('Permission denied')
  })
})
```

### Testing Triggers

```typescript
import { createTriggerControl } from '@hcengineering/test-utils'

describe('OnTaskCreate Trigger', () => {
  it('should send notification when task assigned', async () => {
    const control = createTriggerControl()

    const tx: TxCreateDoc<Task> = {
      _class: core.class.TxCreateDoc,
      objectClass: myFeature.class.Task,
      objectId: 'task-1' as Ref<Task>,
      objectSpace: 'space-1' as Ref<Space>,
      attributes: {
        title: 'Test Task',
        assignee: 'user-123' as AccountUuid,
        completed: false
      }
    }

    const result = await OnTaskCreate([tx], control)

    // Should generate notification
    expect(result.length).toBe(1)
    expect(result[0]._class).toBe(notification.class.InboxNotification)
    expect(result[0].attributes.user).toBe('user-123')
  })
})
```

## Component Tests

Test Svelte components:

### Setup

```bash
# Install testing library
npm install --save-dev @testing-library/svelte @testing-library/jest-dom
```

### Test Component Rendering

```typescript
import { render, screen } from '@testing-library/svelte'
import TaskView from '../TaskView.svelte'

describe('TaskView', () => {
  it('should render task title', () => {
    const task = {
      _id: 'task-1',
      title: 'Test Task',
      completed: false
    } as Task

    render(TaskView, { props: { task } })

    expect(screen.getByText('Test Task')).toBeInTheDocument()
  })

  it('should show completed state', () => {
    const task = {
      _id: 'task-1',
      title: 'Completed Task',
      completed: true
    } as Task

    render(TaskView, { props: { task } })

    const checkbox = screen.getByRole('checkbox')
    expect(checkbox).toBeChecked()
  })
})
```

### Test User Interactions

```typescript
import { render, fireEvent } from '@testing-library/svelte'

describe('TaskView Interactions', () => {
  it('should toggle completion on click', async () => {
    const task = {
      _id: 'task-1',
      title: 'Test Task',
      completed: false
    } as Task

    const { component } = render(TaskView, { props: { task } })

    const checkbox = screen.getByRole('checkbox')
    await fireEvent.click(checkbox)

    // Check component emitted event
    expect(component.$capture_state().task.completed).toBe(true)
  })

  it('should call update on title change', async () => {
    const task = {
      _id: 'task-1',
      title: 'Test Task',
      completed: false
    } as Task

    const onUpdate = jest.fn()
    render(TaskView, {
      props: { task },
      on: { update: onUpdate }
    })

    const input = screen.getByPlaceholderText('Task title')
    await fireEvent.input(input, { target: { value: 'New Title' } })

    expect(onUpdate).toHaveBeenCalled()
  })
})
```

## E2E Tests

Test complete user workflows with Playwright:

### Setup

```typescript
// tests/task-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Task Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login
    await page.goto('http://localhost:8080')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'test123')
    await page.click('button[type="submit"]')

    // Wait for dashboard
    await page.waitForSelector('.dashboard')
  })

  test('should create task', async ({ page }) => {
    // Click create button
    await page.click('button:has-text("Create Task")')

    // Fill form
    await page.fill('[name="title"]', 'New Test Task')
    await page.fill('[name="description"]', 'Description')

    // Submit
    await page.click('button:has-text("Create")')

    // Verify task appears
    await expect(page.locator('text=New Test Task')).toBeVisible()
  })

  test('should complete task', async ({ page }) => {
    // Find task
    const task = page.locator('.task-item').first()

    // Click checkbox
    await task.locator('input[type="checkbox"]').click()

    // Verify completed
    await expect(task).toHaveClass(/completed/)
  })

  test('should search tasks', async ({ page }) => {
    // Type in search
    await page.fill('[placeholder="Search tasks"]', 'bug')

    // Wait for results
    await page.waitForSelector('.search-results')

    // Verify results
    const results = page.locator('.search-result')
    await expect(results).toHaveCount(3)
  })
})
```

### Visual Regression Tests

```typescript
import { test, expect } from '@playwright/test'

test('task view matches screenshot', async ({ page }) => {
  await page.goto('http://localhost:8080/tasks/task-123')

  await page.waitForLoadState('networkidle')

  // Compare screenshot
  await expect(page).toHaveScreenshot('task-view.png')
})
```

## Test Data Management

### Create Test Data

```typescript
// test-utils/fixtures.ts

export async function createTestTask(
  client: Client,
  space: Ref<Space>,
  overrides?: Partial<Data<Task>>
): Promise<Task> {
  const defaults: Data<Task> = {
    title: 'Test Task',
    description: 'Test Description',
    completed: false,
    ...overrides
  }

  const taskId = await client.createDoc(myFeature.class.Task, space, defaults)

  return await client.findOne(myFeature.class.Task, { _id: taskId })
}

export async function createTestSpace(client: Client): Promise<Ref<Space>> {
  return await client.createDoc(myFeature.class.TaskList, core.space.Space, {
    name: 'Test Space',
    description: 'Test space',
    private: false,
    members: [getCurrentAccount().uuid]
  })
}
```

### Cleanup Test Data

```typescript
afterEach(async () => {
  // Delete all test tasks
  const tasks = await client.findAll(myFeature.class.Task, {
    space: testSpace
  })

  for (const task of tasks) {
    await client.remove(task)
  }
})

afterAll(async () => {
  // Delete test space
  await client.remove(testSpace)

  // Close client
  await client.close()
})
```

## Mocking

### Mock Client

```typescript
const mockClient = {
  findAll: jest.fn().mockResolvedValue([]),
  findOne: jest.fn().mockResolvedValue(null),
  createDoc: jest.fn().mockResolvedValue('doc-123'),
  update: jest.fn().mockResolvedValue({}),
  remove: jest.fn().mockResolvedValue({})
}

// Use in tests
it('should call findAll', async () => {
  mockClient.findAll.mockResolvedValue([testTask])

  const component = render(TaskList, {
    props: { client: mockClient }
  })

  expect(mockClient.findAll).toHaveBeenCalledWith(myFeature.class.Task, { space: testSpace })
})
```

### Mock Stores

```typescript
import { writable } from 'svelte/store'

const mockThemeStore = writable({
  language: 'en',
  dark: false
})

setContext('theme', mockThemeStore)
```

## Performance Tests

### Measure Query Performance

```typescript
describe('Query Performance', () => {
  it('should load 1000 tasks in < 100ms', async () => {
    // Create 1000 test tasks
    const tasks = await createManyTestTasks(1000)

    const start = Date.now()

    const result = await client.findAll(myFeature.class.Task, {
      space: testSpace
    })

    const duration = Date.now() - start

    expect(result.length).toBe(1000)
    expect(duration).toBeLessThan(100)
  })

  it('should use index for filtered query', async () => {
    const start = Date.now()

    // This should be fast (indexed field)
    await client.findAll(myFeature.class.Task, {
      completed: false
    })

    const duration = Date.now() - start
    expect(duration).toBeLessThan(50)
  })
})
```

### Load Testing

```typescript
import { test } from '@playwright/test'

test('should handle 100 concurrent users', async () => {
  const browsers = []

  // Create 100 browser contexts
  for (let i = 0; i < 100; i++) {
    const browser = await chromium.launch()
    const context = await browser.newContext()
    const page = await context.newPage()

    browsers.push({ browser, page })
  }

  // All users create tasks simultaneously
  await Promise.all(
    browsers.map(async ({ page }) => {
      await page.goto('http://localhost:8080')
      await page.click('button:has-text("Create Task")')
      await page.fill('[name="title"]', 'Load Test Task')
      await page.click('button:has-text("Create")')
    })
  )

  // Cleanup
  for (const { browser } of browsers) {
    await browser.close()
  }
})
```

## Snapshot Tests

Test component output:

```typescript
import { render } from '@testing-library/svelte'
import TaskView from '../TaskView.svelte'

describe('TaskView Snapshots', () => {
  it('should match snapshot', () => {
    const task = {
      _id: 'task-1',
      title: 'Test Task',
      description: 'Description',
      completed: false
    } as Task

    const { container } = render(TaskView, { props: { task } })

    expect(container).toMatchSnapshot()
  })
})
```

## Test Coverage

### Generate Coverage Report

```bash
# Run tests with coverage
rushx test --coverage

# View coverage report
open coverage/lcov-report/index.html
```

### Coverage Thresholds

```json
// jest.config.js
module.exports = {
  coverageThresholds: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
}
```

## Testing Reactive Queries

```typescript
import { render, waitFor } from '@testing-library/svelte'
import { writable } from 'svelte/store'

describe('Reactive Task List', () => {
  it('should update when tasks change', async () => {
    const tasksStore = writable([testTask1])

    const mockClient = {
      findAll: () => tasksStore
    }

    const { getByText, rerender } = render(TaskList, {
      props: { client: mockClient, space: testSpace }
    })

    // Initially shows 1 task
    expect(getByText('Test Task 1')).toBeInTheDocument()

    // Add another task
    tasksStore.set([testTask1, testTask2])

    // Wait for update
    await waitFor(() => {
      expect(getByText('Test Task 2')).toBeInTheDocument()
    })
  })
})
```

## Testing Async Operations

```typescript
describe('Async Operations', () => {
  it('should handle loading state', async () => {
    let resolveLoad
    const loadPromise = new Promise((resolve) => {
      resolveLoad = resolve
    })

    mockClient.findAll.mockReturnValue(loadPromise)

    const { getByText } = render(TaskList, {
      props: { client: mockClient, space: testSpace }
    })

    // Should show loading
    expect(getByText('Loading...')).toBeInTheDocument()

    // Resolve load
    resolveLoad([testTask])

    // Wait for task to appear
    await waitFor(() => {
      expect(getByText('Test Task')).toBeInTheDocument()
    })
  })

  it('should handle error state', async () => {
    mockClient.findAll.mockRejectedValue(new Error('Network error'))

    const { getByText } = render(TaskList, {
      props: { client: mockClient, space: testSpace }
    })

    await waitFor(() => {
      expect(getByText(/error/i)).toBeInTheDocument()
    })
  })
})
```

## Best Practices

### ✅ Do's

- Write tests for new features
- Test edge cases and error conditions
- Use descriptive test names
- Clean up test data
- Mock external dependencies
- Test async operations
- Use fixtures for test data
- Run tests before committing
- Maintain test coverage >80%
- Test both happy and sad paths

### ❌ Don'ts

- Don't skip tests
- Don't test implementation details
- Don't write flaky tests
- Don't leave test data in database
- Don't hardcode test data
- Don't ignore failing tests
- Don't test third-party libraries
- Don't forget to test error handling

## Continuous Integration

### GitHub Actions Example

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: example
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install Rush
        run: npm install -g @microsoft/rush

      - name: Install dependencies
        run: rush update

      - name: Build
        run: rush build

      - name: Run tests
        run: rush test
        env:
          DB_URL: postgresql://postgres:example@localhost:5432/test

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          files: ./coverage/lcov.info
```

## Summary

HULY testing provides:

- ✅ Unit tests for pure functions
- ✅ Integration tests with real client
- ✅ Component tests for UI
- ✅ E2E tests for workflows
- ✅ Performance tests
- ✅ Coverage reports
- ✅ CI/CD integration

Build confidence with comprehensive testing! ✅
