# Plugin Recipes

Common patterns and recipes for HULY plugin development.

## Recipe: Adding a Custom Field to Existing Class

Add new fields to existing classes using mixins:

```typescript
// 1. Define the mixin interface in your plugin
export interface TaskExtension extends Issue {
  estimatedHours?: number
  actualHours?: number
  taskCategory?: string
}

// 2. Create mixin in model
@Mixin(myFeature.mixin.TaskExtension, tracker.class.Issue)
export class TTaskExtension extends TIssue implements TaskExtension {
  @Prop(TypeNumber(), myFeature.string.EstimatedHours)
  estimatedHours?: number

  @Prop(TypeNumber(), myFeature.string.ActualHours)
  actualHours?: number

  @Prop(TypeString(), myFeature.string.TaskCategory)
  taskCategory?: string
}

// 3. Use in code
const client = getClient()
const issue = await client.findOne(tracker.class.Issue, { _id: issueId })

// Apply mixin
await client.createMixin(issue._id, tracker.class.Issue, issue.space, myFeature.mixin.TaskExtension, {
  estimatedHours: 8,
  taskCategory: 'development'
})

// Read mixin data
const hierarchy = client.getHierarchy()
if (hierarchy.hasMixin(issue, myFeature.mixin.TaskExtension)) {
  const extended = hierarchy.as(issue, myFeature.mixin.TaskExtension)
  console.log('Estimated hours:', extended.estimatedHours)
}
```

## Recipe: Custom Status Flow

Create custom statuses for your documents:

```typescript
// 1. Define status class
export interface TaskStatus extends Status {
  category: 'open' | 'in-progress' | 'completed' | 'cancelled'
}

// 2. Model definition
@Model(myFeature.class.TaskStatus, core.class.Status, DOMAIN_MODEL)
export class TTaskStatus extends TStatus implements TaskStatus {
  @Prop(TypeString(), myFeature.string.Category)
  @Index(IndexKind.Indexed)
  category!: 'open' | 'in-progress' | 'completed' | 'cancelled'
}

// 3. Create default statuses in model
export function createModel(builder: Builder): void {
  builder.createDoc(
    myFeature.class.TaskStatus,
    core.space.Model,
    {
      name: 'To Do',
      category: 'open',
      color: 9
    },
    myFeature.status.ToDo
  )

  builder.createDoc(
    myFeature.class.TaskStatus,
    core.space.Model,
    {
      name: 'In Progress',
      category: 'in-progress',
      color: 10
    },
    myFeature.status.InProgress
  )

  builder.createDoc(
    myFeature.class.TaskStatus,
    core.space.Model,
    {
      name: 'Done',
      category: 'completed',
      color: 11
    },
    myFeature.status.Done
  )
}

// 4. Use in document
export interface Task extends Doc {
  status: Ref<TaskStatus>
}

// 5. Query by status category
const activeTasks = await client.findAll(
  myFeature.class.Task,
  {},
  {
    lookup: {
      status: myFeature.class.TaskStatus
    }
  }
)

const openTasks = activeTasks.filter((t) => t.$lookup.status.category === 'open')
```

## Recipe: Custom Views and Filters

Add custom views to the UI:

```typescript
// 1. Define view configuration
builder.createDoc(
  view.class.Viewlet,
  core.space.Model,
  {
    attachTo: myFeature.class.Task,
    descriptor: view.viewlet.Table,
    config: [
      '', // Checkbox column
      '$lookup.assignee', // Assignee
      'title', // Title
      'status', // Status
      'dueDate', // Due date
      'modifiedOn' // Last modified
    ],
    configOptions: {
      hiddenKeys: ['description'],
      sortable: true
    }
  },
  myFeature.viewlet.TaskTable
)

// 2. Define filters
builder.createDoc(view.class.FilteredView, core.space.Model, {
  name: 'My Tasks',
  location: 'table',
  filterClass: myFeature.class.Task,
  filters: [
    {
      key: 'assignee',
      label: myFeature.string.AssignedToMe,
      filter: { assignee: getCurrentAccount().uuid }
    },
    {
      key: 'completed',
      label: myFeature.string.OnlyPending,
      filter: { completed: false }
    }
  ]
})
```

## Recipe: Search and Autocomplete

Add search and autocomplete for your documents:

```typescript
// 1. Mark fields as searchable with @Index
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  @Prop(TypeString(), myFeature.string.TaskTitle)
  @Index(IndexKind.FullText) // ← Enables full-text search
  title!: string

  @Prop(TypeString(), myFeature.string.TaskDescription)
  @Index(IndexKind.FullText) // ← Searchable
  description!: string
}

// 2. Implement completion function
export default async (): Promise<Resources> => ({
  completion: {
    TaskCompletion: async (client: Client, query: string, filter?: DocumentQuery<Task>) => {
      // Search tasks by title
      const tasks = await client.findAll(
        myFeature.class.Task,
        {
          title: { $like: `%${query}%` },
          ...filter
        },
        { limit: 10 }
      )

      return tasks.map((task) => ({
        id: task._id,
        label: task.title,
        description: task.description,
        icon: task.completed ? myFeature.icon.TaskCompleted : myFeature.icon.Task
      }))
    }
  }
})

// 3. Full-text search
const results = await client.searchFulltext({
  query: 'bug fix',
  classes: [myFeature.class.Task],
  options: { limit: 20 }
})
```

## Recipe: Custom Actions

Add context menu actions for your documents:

```typescript
// 1. Define action in model
builder.createDoc(view.class.Action, core.space.Model, {
  label: myFeature.string.CompleteTask,
  icon: myFeature.icon.TaskComplete,
  action: myFeature.actionImpl.CompleteTask,
  input: 'focus',
  category: myFeature.category.Task,
  target: myFeature.class.Task,
  context: {
    mode: ['context', 'browser'],
    group: 'edit'
  },
  visibilityTester: myFeature.function.CanCompleteTask
})

// 2. Implement visibility checker
export default async (): Promise<Resources> => ({
  function: {
    CanCompleteTask: async (doc: Task) => {
      // Only show for incomplete tasks
      return !doc.completed
    }
  },

  actionImpl: {
    CompleteTask: async (doc: Task) => {
      const client = getClient()
      await client.update(doc, {
        completed: true,
        completedOn: Date.now()
      })

      // Show notification
      await showPopup(notification.component.Notification, {
        message: 'Task completed!',
        type: 'success'
      })
    }
  }
})
```

## Recipe: Bulk Operations

Implement bulk operations on multiple documents:

```typescript
builder.createDoc(view.class.Action, core.space.Model, {
  label: myFeature.string.BulkComplete,
  icon: myFeature.icon.TaskComplete,
  action: myFeature.actionImpl.BulkCompleteTask,
  input: 'selection',
  category: myFeature.category.Task,
  target: myFeature.class.Task,
  context: {
    mode: ['browser'],
    group: 'tools'
  }
})

// Implementation
export default async (): Promise<Resources> => ({
  actionImpl: {
    BulkCompleteTask: async (docs: Task[]) => {
      const client = getClient()

      // Update all tasks
      for (const task of docs) {
        await client.update(task, { completed: true })
      }

      await showPopup(notification.component.Notification, {
        message: `${docs.length} tasks completed`,
        type: 'success'
      })
    }
  }
})
```

## Recipe: Relations Between Documents

Create relationships between different document types:

```typescript
// 1. Define relation class
export interface TaskIssueRelation extends AttachedDoc<Task> {
  issue: Ref<Issue>
  relationType: 'blocks' | 'related' | 'duplicate'
}

// 2. Model definition
@Model(myFeature.class.TaskIssueRelation, core.class.AttachedDoc, DOMAIN_MY_FEATURE)
export class TTaskIssueRelation extends TAttachedDoc implements TaskIssueRelation {
  @Prop(TypeRef(tracker.class.Issue), tracker.string.Issue)
  @Index(IndexKind.Indexed)
  issue!: Ref<Issue>

  @Prop(TypeString(), myFeature.string.RelationType)
  relationType!: 'blocks' | 'related' | 'duplicate'
}

// 3. Add to task model
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  // ... other props

  @Prop(Collection(myFeature.class.TaskIssueRelation), myFeature.string.RelatedIssues)
  relatedIssues?: number
}

// 4. Use in code
// Create relation
await client.addCollection(
  myFeature.class.TaskIssueRelation,
  task.space,
  task._id,
  myFeature.class.Task,
  'relatedIssues',
  {
    issue: issueId,
    relationType: 'blocks'
  }
)

// Query relations
const relations = await client.findAll(myFeature.class.TaskIssueRelation, { attachedTo: task._id })
```

## Recipe: Custom Space Types

Create specialized space types:

```typescript
// 1. Define space type descriptor
export const taskSpaceTypes = [
  {
    _id: myFeature.descriptors.PersonalTaskList,
    name: myFeature.string.PersonalTaskList,
    description: myFeature.string.PersonalTaskListDescription,
    icon: myFeature.icon.PersonalTasks,
    baseClass: myFeature.class.TaskList,
    availablePermissions: [myFeature.permission.CreateTask, myFeature.permission.CompleteTask]
  },
  {
    _id: myFeature.descriptors.TeamTaskList,
    name: myFeature.string.TeamTaskList,
    description: myFeature.string.TeamTaskListDescription,
    icon: myFeature.icon.TeamTasks,
    baseClass: myFeature.class.TaskList,
    availablePermissions: [
      core.permission.UpdateSpace,
      myFeature.permission.CreateTask,
      myFeature.permission.CompleteTask,
      myFeature.permission.AssignTask
    ]
  }
]

// 2. Create in model
export function createModel(builder: Builder): void {
  for (const descriptor of taskSpaceTypes) {
    builder.createDoc(core.class.SpaceTypeDescriptor, core.space.Model, descriptor, descriptor._id)
  }
}
```

## Recipe: Notifications for Your Plugin

Add notifications when things happen:

```typescript
// 1. Define notification types
builder.createDoc(
  notification.class.NotificationGroup,
  core.space.Model,
  {
    label: myFeature.string.Tasks,
    icon: myFeature.icon.Task
  },
  myFeature.ids.TaskNotificationGroup
)

builder.createDoc(
  notification.class.NotificationType,
  core.space.Model,
  {
    label: myFeature.string.TaskAssigned,
    group: myFeature.ids.TaskNotificationGroup,
    txClasses: [core.class.TxUpdateDoc],
    objectClass: myFeature.class.Task,
    defaultEnabled: true,
    templates: {
      textTemplate: '{sender} assigned {doc} to you',
      htmlTemplate: '<b>{sender}</b> assigned {doc} to you',
      subjectTemplate: 'Task assigned: {doc}'
    }
  },
  myFeature.ids.TaskAssignedNotification
)

// 2. Server trigger to send notification
async function OnTaskAssigned(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    // Only if assignee changed
    if (!tx.operations.assignee) continue

    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })

    if (!task) continue

    // Create notification
    const notificationTx = control.txFactory.createTxCreateDoc(notification.class.InboxNotification, task.space, {
      user: tx.operations.assignee,
      header: myFeature.string.TaskAssigned,
      message: myFeature.string.TaskAssignedBody,
      objectId: task._id,
      objectClass: task._class,
      intlParams: {
        sender: control.getSenderName(tx.modifiedBy),
        doc: task.title
      },
      isViewed: false,
      archived: false
    })

    result.push(notificationTx)
  }

  return result
}
```

## Recipe: Integrations with External Services

Integrate with external APIs:

```typescript
// 1. Define integration config
export interface GitHubIntegration extends Doc {
  repository: string
  accessToken: string
  syncEnabled: boolean
}

// 2. Server function to sync
export default async () => ({
  function: {
    SyncGitHubIssues: async (integration: GitHubIntegration) => {
      const octokit = new Octokit({ auth: integration.accessToken })

      const { data: issues } = await octokit.rest.issues.listForRepo({
        owner: integration.repository.split('/')[0],
        repo: integration.repository.split('/')[1]
      })

      const client = getClient()

      for (const ghIssue of issues) {
        // Find or create task
        let task = await client.findOne(myFeature.class.Task, {
          githubId: ghIssue.id
        })

        if (!task) {
          await client.createDoc(myFeature.class.Task, integration.space, {
            title: ghIssue.title,
            description: ghIssue.body || '',
            githubId: ghIssue.id,
            githubUrl: ghIssue.html_url,
            completed: ghIssue.state === 'closed'
          })
        } else {
          await client.update(task, {
            title: ghIssue.title,
            description: ghIssue.body || '',
            completed: ghIssue.state === 'closed'
          })
        }
      }
    }
  }
})
```

## Recipe: Custom Kanban Board

Create a kanban board for your documents:

```typescript
// 1. Define board configuration
builder.createDoc(
  view.class.Viewlet,
  core.space.Model,
  {
    attachTo: myFeature.class.Task,
    descriptor: view.viewlet.Board,
    config: [
      { key: 'status', label: myFeature.string.Status },
      { key: '', label: myFeature.string.NoStatus }
    ],
    configOptions: {
      groupBy: ['status', 'assignee'],
      orderBy: [
        ['status', SortingOrder.Ascending],
        ['rank', SortingOrder.Ascending]
      ]
    }
  },
  myFeature.viewlet.TaskBoard
)

// 2. Component for board card
<!-- TaskCard.svelte -->
<script lang="ts">
  import type { Task } from '@hcengineering/my-awesome-feature'

  export let task: Task
</script>

<div class="card">
  <div class="title">{task.title}</div>
  {#if task.dueDate}
    <div class="due-date">
      Due: {new Date(task.dueDate).toLocaleDateString()}
    </div>
  {/if}
</div>
```

## Recipe: Hierarchical Documents

Create parent-child document hierarchies:

```typescript
// 1. Define with parent reference
export interface Task extends Doc {
  title: string
  parent?: Ref<Task>  // Parent task
  rank: string  // For ordering
}

// 2. Model definition
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  @Prop(TypeString(), myFeature.string.TaskTitle)
  title!: string

  @Prop(TypeRef(myFeature.class.Task), myFeature.string.ParentTask)
  @Index(IndexKind.Indexed)
  parent?: Ref<Task>

  @Prop(TypeString(), myFeature.string.Rank)
  @Index(IndexKind.Indexed)
  rank!: string
}

// 3. Query hierarchy
async function getTaskHierarchy(taskId: Ref<Task>): Promise<Task[]> {
  const client = getClient()
  const task = await client.findOne(myFeature.class.Task, { _id: taskId })

  if (!task) return []

  // Get all subtasks
  const subtasks = await client.findAll(myFeature.class.Task, {
    parent: taskId
  }, {
    sort: { rank: 1 }
  })

  // Recursively get sub-subtasks
  const allSubtasks = []
  for (const subtask of subtasks) {
    allSubtasks.push(subtask)
    const children = await getTaskHierarchy(subtask._id)
    allSubtasks.push(...children)
  }

  return allSubtasks
}

// 4. Tree component
<!-- TaskTree.svelte -->
<script lang="ts">
  import { getClient } from '@hcengineering/presentation'
  import type { Task } from '@hcengineering/my-awesome-feature'

  export let task: Task
  export let level: number = 0

  const client = getClient()

  $: subtasks = client.findAll(myFeature.class.Task, {
    parent: task._id
  }, {
    sort: { rank: 1 }
  })
</script>

<div class="task-item" style="padding-left: {level * 1.5}rem">
  <TaskView {task} />

  {#await subtasks then subs}
    {#each subs as subtask}
      <svelte:self task={subtask} level={level + 1} />
    {/each}
  {/await}
</div>
```

## Recipe: Time Tracking

Add time tracking to documents:

```typescript
// 1. Define time entry
export interface TimeEntry extends AttachedDoc<Task> {
  date: Date
  hours: number
  description: string
  employee: Ref<Employee>
}

// 2. Model
@Model(myFeature.class.TimeEntry, core.class.AttachedDoc, DOMAIN_MY_FEATURE)
export class TTimeEntry extends TAttachedDoc implements TimeEntry {
  @Prop(TypeDate(), myFeature.string.Date)
  @Index(IndexKind.Indexed)
  date!: Date

  @Prop(TypeNumber(), myFeature.string.Hours)
  hours!: number

  @Prop(TypeString(), myFeature.string.Description)
  description!: string

  @Prop(TypeRef(contact.class.Employee), contact.string.Employee)
  @Index(IndexKind.Indexed)
  employee!: Ref<Employee>
}

// 3. Add to task
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  // ... other props

  @Prop(Collection(myFeature.class.TimeEntry), myFeature.string.TimeEntries)
  timeEntries?: number
}

// 4. Calculate total time
async function getTotalTime(task: Task): Promise<number> {
  const client = getClient()
  const entries = await client.findAll(myFeature.class.TimeEntry, {
    attachedTo: task._id
  })

  return entries.reduce((sum, entry) => sum + entry.hours, 0)
}
```

## Recipe: Document Templates

Create templates for quick document creation:

```typescript
// 1. Define template mixin
export interface TaskTemplate extends Task {
  isTemplate: boolean
  category: string
}

@Mixin(myFeature.mixin.TaskTemplate, myFeature.class.Task)
export class TTaskTemplate extends TTask implements TaskTemplate {
  @Prop(TypeBoolean(), myFeature.string.IsTemplate)
  isTemplate!: boolean

  @Prop(TypeString(), myFeature.string.Category)
  category!: string
}

// 2. Create template
await client.createMixin(task._id, myFeature.class.Task, task.space, myFeature.mixin.TaskTemplate, {
  isTemplate: true,
  category: 'development'
})

// 3. Create from template
async function createFromTemplate(template: Task, space: Ref<Space>, overrides: Partial<Task>): Promise<Ref<Task>> {
  const client = getClient()

  // Copy template data
  const taskData: Data<Task> = {
    title: overrides.title || template.title,
    description: overrides.description || template.description,
    completed: false
    // ... copy other fields
  }

  return await client.createDoc(myFeature.class.Task, space, taskData)
}
```

## Recipe: Export/Import

Add export and import functionality:

```typescript
// 1. Export to JSON
async function exportTasks(space: Ref<Space>): Promise<string> {
  const client = getClient()
  const tasks = await client.findAll(myFeature.class.Task, { space })

  const exportData = {
    version: '1.0',
    exportDate: new Date().toISOString(),
    tasks: tasks.map((task) => ({
      title: task.title,
      description: task.description,
      completed: task.completed,
      dueDate: task.dueDate
    }))
  }

  return JSON.stringify(exportData, null, 2)
}

// 2. Import from JSON
async function importTasks(space: Ref<Space>, json: string): Promise<number> {
  const client = getClient()
  const data = JSON.parse(json)

  if (data.version !== '1.0') {
    throw new Error('Unsupported import version')
  }

  let count = 0
  for (const taskData of data.tasks) {
    await client.createDoc(myFeature.class.Task, space, {
      title: taskData.title,
      description: taskData.description,
      completed: taskData.completed,
      dueDate: taskData.dueDate ? new Date(taskData.dueDate) : undefined
    })
    count++
  }

  return count
}
```

## Recipe: Custom Presenters

Create custom ways to display your data:

```typescript
// 1. Register presenter
builder.mixin(
  myFeature.class.Task,
  core.class.Class,
  view.mixin.ObjectPresenter,
  {
    presenter: myFeature.component.TaskPresenter
  }
)

// 2. Create presenter component
<!-- TaskPresenter.svelte -->
<script lang="ts">
  import type { Task } from '@hcengineering/my-awesome-feature'
  import { Icon } from '@hcengineering/ui'
  import myFeature from '@hcengineering/my-awesome-feature'

  export let value: Task
  export let inline: boolean = false
  export let disabled: boolean = false
</script>

<div class="task-presenter" class:inline class:disabled>
  <Icon icon={value.completed ? myFeature.icon.TaskCompleted : myFeature.icon.Task} size="small" />
  <span class:completed={value.completed}>
    {value.title}
  </span>
  {#if value.dueDate}
    <span class="due-date">
      {new Date(value.dueDate).toLocaleDateString()}
    </span>
  {/if}
</div>

<style>
  .task-presenter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .completed {
    text-decoration: line-through;
    opacity: 0.6;
  }

  .due-date {
    font-size: 0.75rem;
    color: var(--theme-content-trans-color);
  }
</style>
```

## Recipe: Statistics and Dashboards

Create dashboard widgets:

```typescript
// 1. Define aggregation functions
export default async (): Promise<Resources> => ({
  function: {
    GetTaskStats: async (space: Ref<Space>) => {
      const client = getClient()
      const tasks = await client.findAll(myFeature.class.Task, { space })

      return {
        total: tasks.length,
        completed: tasks.filter(t => t.completed).length,
        pending: tasks.filter(t => !t.completed).length,
        overdue: tasks.filter(t =>
          !t.completed &&
          t.dueDate &&
          new Date(t.dueDate) < new Date()
        ).length
      }
    }
  }
})

// 2. Dashboard widget component
<!-- TaskStatsWidget.svelte -->
<script lang="ts">
  import { getResource } from '@hcengineering/platform'
  import myFeature from '@hcengineering/my-awesome-feature'

  export let space: Ref<Space>

  const getStats = getResource(myFeature.function.GetTaskStats)

  $: stats = getStats.then(fn => fn(space))
</script>

{#await stats then data}
  <div class="stats-widget">
    <div class="stat">
      <span class="label">Total</span>
      <span class="value">{data.total}</span>
    </div>
    <div class="stat">
      <span class="label">Completed</span>
      <span class="value">{data.completed}</span>
    </div>
    <div class="stat">
      <span class="label">Pending</span>
      <span class="value">{data.pending}</span>
    </div>
    <div class="stat overdue">
      <span class="label">Overdue</span>
      <span class="value">{data.overdue}</span>
    </div>
  </div>
{/await}
```

## Recipe: Collaborative Editing

Add real-time collaborative editing:

```typescript
// 1. Use collaborative doc type
import { TypeCollaborativeDoc } from '@hcengineering/model'

@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  @Prop(TypeCollaborativeDoc(), myFeature.string.Description)
  description!: MarkupBlobRef | null

  // Lock tracking
  @Prop(TypeAccountUuid(), myFeature.string.LockedBy)
  @Hidden()
  lockedBy?: AccountUuid
}

// 2. Lock/unlock in UI
async function lockTask(task: Task): Promise<void> {
  const client = getClient()
  const me = getCurrentAccount()

  if (task.lockedBy && task.lockedBy !== me.uuid) {
    throw new Error('Task is locked by another user')
  }

  await client.update(task, { lockedBy: me.uuid })
}

async function unlockTask(task: Task): Promise<void> {
  const client = getClient()
  await client.update(task, { lockedBy: undefined })
}
```

## Recipe: Webhooks

Add webhook support for external notifications:

```typescript
// 1. Define webhook config
export interface Webhook extends Doc {
  url: string
  events: string[]
  secret: string
  enabled: boolean
}

// 2. Server trigger to call webhooks
async function OnTaskUpdate(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const webhooks = await control.findAll(myFeature.class.Webhook, {
    enabled: true,
    events: { $in: ['task.updated'] }
  })

  for (const tx of txes) {
    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })

    if (!task) continue

    // Call each webhook
    for (const webhook of webhooks) {
      try {
        await fetch(webhook.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Signature': createSignature(webhook.secret, task)
          },
          body: JSON.stringify({
            event: 'task.updated',
            task: {
              id: task._id,
              title: task.title,
              completed: task.completed
            },
            changes: tx.operations
          })
        })
      } catch (err) {
        control.ctx.error('Webhook failed', { webhook: webhook.url, err })
      }
    }
  }

  return []
}
```

## Recipe: Custom Permissions

Define granular permissions:

```typescript
// 1. Define permissions
builder.createDoc(
  core.class.Permission,
  core.space.Model,
  {
    label: myFeature.string.CreateTask,
    description: myFeature.string.CreateTaskPermissionDescription,
    scope: 'space'
  },
  myFeature.permission.CreateTask
)

builder.createDoc(
  core.class.Permission,
  core.space.Model,
  {
    label: myFeature.string.CompleteTask,
    scope: 'space'
  },
  myFeature.permission.CompleteTask
)

builder.createDoc(
  core.class.Permission,
  core.space.Model,
  {
    label: myFeature.string.DeleteTask,
    scope: 'space'
  },
  myFeature.permission.DeleteTask
)

builder.createDoc(
  core.class.Permission,
  core.space.Model,
  {
    label: myFeature.string.AssignTask,
    scope: 'space'
  },
  myFeature.permission.AssignTask
)

// 2. Use in actions
async function completeTaskAction(task: Task): Promise<void> {
  const client = getClient()

  // Check permission
  const canComplete = await checkPermission(client, myFeature.permission.CompleteTask, task.space)

  if (!canComplete) {
    throw new Error('You do not have permission to complete tasks')
  }

  await client.update(task, { completed: true })
}
```

## Recipe: Analytics and Metrics

Track plugin usage:

```typescript
import { Analytics } from '@hcengineering/analytics'

// Track events
Analytics.handleEvent({
  event: 'task_created',
  properties: {
    plugin: 'my-awesome-feature',
    hasAssignee: task.assignee !== undefined,
    hasDueDate: task.dueDate !== undefined
  }
})

// Track performance
Analytics.handleTiming({
  category: 'task',
  variable: 'creation_time',
  time: creationTime
})

// Track errors
try {
  await createTask(data)
} catch (err) {
  Analytics.handleError(err)
  throw err
}
```

## Recipe: Plugin Settings

Add user-configurable settings:

```typescript
// 1. Define settings
export interface TaskSettings extends Preference {
  defaultDueDate?: number  // Days from now
  autoAssign: boolean
  notifyOnOverdue: boolean
}

// 2. Create default settings
builder.createDoc(
  preference.class.Preference,
  core.space.Model,
  {
    attachedTo: myFeature.id,
    key: 'task-settings',
    value: {
      defaultDueDate: 7,
      autoAssign: false,
      notifyOnOverdue: true
    }
  }
)

// 3. Use settings
async function getTaskSettings(): Promise<TaskSettings> {
  const client = getClient()
  const me = getCurrentAccount()

  const setting = await client.findOne(preference.class.Preference, {
    attachedTo: myFeature.id,
    key: 'task-settings',
    user: me.uuid
  })

  return setting?.value || getDefaultSettings()
}

// 4. Settings UI component
<!-- TaskSettings.svelte -->
<script lang="ts">
  import { CheckBox, NumberBox } from '@hcengineering/ui'
  import { getClient } from '@hcengineering/presentation'

  let settings = await getTaskSettings()

  async function saveSettings() {
    const client = getClient()
    await client.update(settingsDoc, { value: settings })
  }
</script>

<div class="settings">
  <NumberBox
    label="Default due date (days)"
    bind:value={settings.defaultDueDate}
    on:change={saveSettings}
  />

  <CheckBox
    label="Auto-assign to me"
    bind:checked={settings.autoAssign}
    on:value={saveSettings}
  />

  <CheckBox
    label="Notify on overdue"
    bind:checked={settings.notifyOnOverdue}
    on:value={saveSettings}
  />
</div>
```

## Testing Recipes

### Test Plugin Resources

```typescript
import { describe, it, expect, beforeAll } from '@jest/globals'
import { createTestClient } from '@hcengineering/testing'

describe('My Awesome Feature', () => {
  let client: Client
  let space: Ref<Space>

  beforeAll(async () => {
    client = await createTestClient()
    space = await createTestSpace(client)
  })

  it('should create task', async () => {
    const taskId = await client.createDoc(myFeature.class.Task, space, {
      title: 'Test Task',
      description: 'Description',
      completed: false
    })

    const task = await client.findOne(myFeature.class.Task, { _id: taskId })
    expect(task).toBeDefined()
    expect(task?.title).toBe('Test Task')
  })

  it('should complete task', async () => {
    const task = await createTestTask(client, space)

    await client.update(task, { completed: true })

    const updated = await client.findOne(myFeature.class.Task, { _id: task._id })
    expect(updated?.completed).toBe(true)
  })
})
```

## Summary

These recipes cover:

- ✅ Custom fields with mixins
- ✅ Status flows
- ✅ Custom views and filters
- ✅ Search and autocomplete
- ✅ Custom actions
- ✅ Bulk operations
- ✅ Document relations
- ✅ Custom space types
- ✅ Notifications
- ✅ External integrations
- ✅ Kanban boards
- ✅ Hierarchical documents
- ✅ Time tracking
- ✅ Document templates
- ✅ Export/import
- ✅ Custom presenters
- ✅ Analytics
- ✅ Settings
- ✅ Testing

Use these as starting points for your own plugin features!
