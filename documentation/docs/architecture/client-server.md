# Client-Server Communication

HULY uses a WebSocket-based protocol for real-time bidirectional communication between clients and the server.

## Overview

The communication system enables:

- **Real-time Updates** - Changes propagate instantly to all connected clients
- **Bidirectional RPC** - Both client and server can call methods on each other
- **Binary Protocol** - Efficient binary serialization (optional)
- **Compression** - Optional compression for bandwidth optimization
- **Reconnection** - Automatic reconnection with state recovery

## Architecture

```
┌──────────────┐           WebSocket            ┌──────────────┐
│              │◄──────────────────────────────►│              │
│   Client     │                                 │   Server     │
│  (Browser)   │          RPC Messages           │ (Transactor) │
│              │◄──────────────────────────────►│              │
└──────────────┘                                 └──────────────┘
       │                                                 │
       │                                                 │
       ▼                                                 ▼
┌──────────────┐                                 ┌──────────────┐
│  Local       │                                 │  Pipeline    │
│  Model Cache │                                 │  + Storage   │
└──────────────┘                                 └──────────────┘
```

## Connection Flow

### 1. Initial Connection

```typescript
const client = await createClient(
  'ws://localhost:3333', // Transactor URL
  token // Auth token
)
```

### 2. WebSocket Handshake

```typescript
// Client sends:
{
  method: 'hello',
  params: [],
  id: -1,
  binary: true,      // Use binary protocol?
  compression: true  // Use compression?
}

// Server responds:
{
  id: -1,
  result: 'hello',
  binary: true,
  compression: true
}
```

### 3. Load Model

After connection, the client loads the data model:

```typescript
const model = await connection.loadModel(lastModelHash)
```

**Model Loading:**

- Client sends last known model hash
- Server checks if model has changed
- If changed, server sends model transactions
- Client rebuilds local model from transactions

### 4. Find All (Initial Data Load)

```typescript
// Client requests initial data
const issues = await client.findAll(tracker.class.Issue, { space: projectId })
```

Server returns matching documents.

### 5. Transaction Subscription

Client subscribes to transaction updates:

```typescript
connection.subscribe((tx: Tx) => {
  // Handle incoming transaction
  model.tx(tx) // Update local model
  hierarchy.tx(tx) // Update hierarchy

  // Notify UI of changes
  notify(tx)
})
```

## Message Protocol

### Request Format

```typescript
interface Request {
  method: string // Method name
  params: any[] // Method parameters
  id: number // Request ID for matching responses
  binary?: boolean // Use binary protocol
  compression?: boolean // Use compression
}
```

### Response Format

```typescript
interface Response {
  id: number // Matches request ID
  result?: any // Method result
  error?: string // Error message if failed
  chunk?: {
    // For chunked responses
    index: number
    final: boolean
  }
}
```

### Transaction Broadcast

When server processes a transaction, it broadcasts to all connected clients:

```typescript
{
  method: '#tx',
  params: [tx],  // The transaction
  id: -1         // Broadcast (no response expected)
}
```

## RPC Methods

### Client → Server

**findAll** - Query documents

```typescript
{
  method: 'findAll',
  params: [
    'tracker:class:Issue',         // Class
    { space: 'project-1' },        // Query
    { sort: { modifiedOn: -1 } }   // Options
  ]
}
```

**tx** - Send transaction

```typescript
{
  method: 'tx',
  params: [{
    _class: 'core:class:TxCreateDoc',
    objectClass: 'tracker:class:Issue',
    attributes: {
      title: 'New Issue',
      status: 'todo'
    }
  }]
}
```

**searchFulltext** - Full-text search

```typescript
{
  method: 'searchFulltext',
  params: [
    { query: 'search terms' },
    { limit: 20 }
  ]
}
```

**loadModel** - Load data model

```typescript
{
  method: 'loadModel',
  params: [lastHash]  // Last known model hash
}
```

**closeWorkspace** - Close connection

```typescript
{
  method: 'closeWorkspace',
  params: []
}
```

### Server → Client

**#tx** - Broadcast transaction

```typescript
{
  method: '#tx',
  params: [transaction]
}
```

**ping** - Keep-alive ping

```typescript
'ping' // Simple string message
```

Client responds with `"pong"`.

## Binary Protocol

For efficiency, HULY can use a binary protocol:

### Advantages

- **Smaller Payloads** - 30-50% size reduction
- **Faster Parsing** - No JSON parsing overhead
- **Type Preservation** - Better handling of binary data

### Format

Binary messages use a custom serialization format:

- Magic bytes for protocol version
- Message type identifier
- Length-prefixed strings
- Optimized number encoding
- Buffer concatenation for binary data

### Example

```typescript
// JSON Protocol
{
  _id: "issue-123",
  title: "Fix bug",
  priority: "high"
}
// Size: ~70 bytes

// Binary Protocol
// Size: ~45 bytes (35% reduction)
```

## Compression

Optional gzip compression can be enabled:

```typescript
const client = await createClient(url, token, {
  useProtocolCompression: true
})
```

**Benefits:**

- Reduces bandwidth usage
- Especially effective for large responses
- Trades CPU for bandwidth

**Trade-offs:**

- Increased CPU usage
- Small latency overhead
- Most beneficial for large payloads

## Connection State Management

### States

```typescript
enum ConnectionState {
  Connecting,
  Connected,
  Disconnected,
  Closed
}
```

### Reconnection Strategy

```typescript
class Connection {
  private delay = 0 // Reconnection delay

  private scheduleOpen(ctx: MeasureContext, force: boolean) {
    if (this.closed) return

    // Exponential backoff (up to 3 seconds)
    const timeout = this.delay * 1000
    if (this.delay < 3) {
      this.delay += 1
    }

    setTimeout(() => {
      this.openConnection(ctx)
    }, timeout)
  }
}
```

**Reconnection Flow:**

1. Connection lost
2. Wait (exponential backoff: 0s, 1s, 2s, 3s, 3s, ...)
3. Attempt reconnection
4. If successful, reset delay
5. Resubscribe to transactions
6. Fetch any missed transactions
7. Update UI

### Request Retry

Failed requests are automatically retried on reconnection:

```typescript
private sendRequest(data: {
  method: string
  params: any[]
  retry?: () => Promise<boolean>
  allowReconnect?: boolean
}): Promise<any> {
  // If connection lost, store request
  if (!this.connected && data.allowReconnect) {
    // Will retry when reconnected
    return this.retryQueue.add(data)
  }

  // Send immediately
  return this.doSendRequest(data)
}
```

## Ping/Pong Keep-Alive

Server sends periodic pings to detect disconnections:

```typescript
// Server sends every 10 seconds
ws.send('ping')

// Client responds
ws.on('message', (msg) => {
  if (msg === 'ping') {
    ws.send('pong')
    return
  }
})
```

If pong not received within timeout, connection is considered dead.

## Transaction Streaming

Transactions are streamed in real-time:

### Server Side

```typescript
// Transaction processed
await pipeline.tx(ctx, tx)

// Broadcast to all connected clients in workspace
for (const session of sessions.getWorkspaceSessions(workspaceId)) {
  session.send({
    method: '#tx',
    params: [tx]
  })
}
```

### Client Side

```typescript
connection.subscribe((tx: Tx) => {
  switch (tx._class) {
    case 'core:class:TxCreateDoc':
      handleCreate(tx)
      break
    case 'core:class:TxUpdateDoc':
      handleUpdate(tx)
      break
    case 'core:class:TxRemoveDoc':
      handleRemove(tx)
      break
  }
})
```

## Session Management

### Server Session

```typescript
interface Session {
  sessionId: string
  workspaceId: WorkspaceUuid
  user: PersonId
  socket: ConnectionSocket
  context: MeasureContext

  send(msg: Response): void
  close(): void
}
```

### Session Lifecycle

```
┌──────────────┐
│ Client       │
│ Connects     │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│ Authenticate │ (verify token)
└──────┬───────┘
       │
       ▼
┌──────────────┐
│ Create       │
│ Session      │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│ Active       │ (handle requests)
│ Session      │
└──────┬───────┘
       │
       ▼
┌──────────────┐
│ Close        │
│ Session      │
└──────────────┘
```

### Session Timeout

Sessions timeout after period of inactivity:

```typescript
// Remove session after 1 second of no activity
setTimeout(() => {
  if (!session.hasActivity()) {
    sessions.close(session.id)
  }
}, 1000)
```

## Performance Optimizations

### 1. Request Batching

Multiple requests can be batched:

```typescript
// Instead of 3 separate requests
await client.findAll(class1, {})
await client.findAll(class2, {})
await client.findAll(class3, {})

// Batch them
await client.findAllBatch([
  [class1, {}],
  [class2, {}],
  [class3, {}]
])
```

### 2. Backpressure Handling

Server monitors WebSocket buffer:

```typescript
if (ws.bufferedAmount > backpressureSize) {
  // Pause sending until buffer drains
  await waitForBufferDrain()
}
```

### 3. Chunked Responses

Large responses are chunked:

```typescript
// Server sends in chunks
{
  id: 123,
  result: [...data],
  chunk: { index: 0, final: false }
}
{
  id: 123,
  result: [...moreData],
  chunk: { index: 1, final: false }
}
{
  id: 123,
  result: [...lastData],
  chunk: { index: 2, final: true }
}

// Client reassembles
const fullResult = chunks.flatMap(c => c.result)
```

## Security

### Authentication

```typescript
// Token includes workspace and user info
const token = generateToken({
  workspace: workspaceId,
  user: userId,
  email: userEmail
})

// Server validates on connection
const { workspace, user } = verifyToken(token)
```

### Permission Checks

Server validates all operations:

```typescript
// Check if user can execute transaction
if (!canExecute(user, tx)) {
  throw new Error('Permission denied')
}
```

### Rate Limiting

Prevent abuse with rate limiting:

```typescript
if (session.requestsPerSecond > rateLimit) {
  session.close()
  throw new Error('Rate limit exceeded')
}
```

## Debugging

### Enable Logging

```typescript
setMetadata(client.metadata.EnableLogging, true)
```

### Monitor Messages

```typescript
ws.addEventListener('message', (event) => {
  console.log('Received:', event.data)
})

ws.addEventListener('send', (data) => {
  console.log('Sent:', data)
})
```

### Connection Events

```typescript
client.on('connected', () => {
  console.log('Connected to server')
})

client.on('disconnected', () => {
  console.log('Disconnected from server')
})

client.on('transaction', (tx) => {
  console.log('Received transaction:', tx)
})
```

## Best Practices

### 1. Handle Disconnections Gracefully

```typescript
// ✅ Good
client.on('disconnected', () => {
  showReconnectingIndicator()
})

client.on('connected', () => {
  hideReconnectingIndicator()
  refreshData()
})
```

### 2. Use Subscriptions for Real-time Updates

```typescript
// ✅ Good - Subscribe to changes
client.subscribe((tx) => {
  if (isRelevant(tx)) {
    updateUI(tx)
  }
})

// ❌ Bad - Poll for changes
setInterval(() => {
  const data = await client.findAll(...)
  updateUI(data)
}, 1000)
```

### 3. Minimize Round Trips

```typescript
// ✅ Good - Single request with lookup
const issues = await client.findAll(
  tracker.class.Issue,
  { space: projectId },
  { lookup: { assignee: contact.class.Employee } }
)

// ❌ Bad - Multiple round trips
const issues = await client.findAll(tracker.class.Issue, ...)
for (const issue of issues) {
  issue.assignee = await client.findOne(
    contact.class.Employee,
    { _id: issue.assignee }
  )
}
```

## Summary

HULY's client-server communication provides:

- ✅ Real-time bidirectional communication
- ✅ Efficient binary protocol
- ✅ Automatic reconnection
- ✅ Transaction streaming
- ✅ Compression support
- ✅ Request batching
- ✅ Type-safe RPC

The WebSocket-based protocol enables seamless real-time collaboration!
