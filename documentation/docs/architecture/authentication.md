# Authentication & Security

Complete guide to HULY's authentication system, account management, and security features.

## Overview

HULY's auth system provides:

- **Email/Password** - Traditional login
- **OAuth** - Google, GitHub, OpenID Connect
- **OTP (One-Time Password)** - Passwordless login
- **JWT Tokens** - Secure session management
- **Guest Access** - Read-only and limited access
- **Multi-Workspace** - Single account, multiple workspaces

## Architecture

```
┌──────────────┐
│   Client     │
└──────┬───────┘
       │
       │ Login Request
       ▼
┌──────────────┐
│   Account    │
│   Service    │
└──────┬───────┘
       │
       ├─────────────────┐
       │                 │
       ▼                 ▼
┌──────────────┐  ┌──────────────┐
│  Verify      │  │  OAuth       │
│  Credentials │  │  Provider    │
└──────┬───────┘  └──────┬───────┘
       │                 │
       └────────┬────────┘
                ▼
       ┌────────────────┐
       │  Generate JWT  │
       │  Token         │
       └────────┬───────┘
                │
                ▼
       ┌────────────────┐
       │  Return to     │
       │  Client        │
       └────────────────┘
```

## Account Model

### Person

```typescript
interface Person {
  uuid: PersonUuid // Global unique ID
  firstName: string
  lastName: string
  name: string // Full name
}
```

### Account

```typescript
interface Account {
  uuid: AccountUuid // Same as PersonUuid when account exists
  role: AccountRole // Workspace-level role
  primarySocialId: PersonId // Primary login method
  socialIds: PersonId[] // All login methods
  hash: Buffer // Password hash (bcrypt)
  salt: Buffer // Password salt
}
```

### Social ID

Multiple login methods per person:

```typescript
interface SocialId {
  _id: PersonId // Unique ID for this social login
  personUuid: PersonUuid // Which person
  type: SocialIdType // Email, GitHub, Google, etc.
  value: string // <EMAIL>, github-id, etc.
  verifiedOn?: Timestamp // When verified (email confirmation)
}

enum SocialIdType {
  EMAIL = 'EMAIL',
  GITHUB = 'GITHUB',
  GOOGLE = 'GOOGLE',
  OPENID = 'OPENID',
  LINKEDIN = 'LINKEDIN'
}
```

## Authentication Methods

### Email/Password Login

```typescript
// Client-side
async function login(email: string, password: string): Promise<void> {
  const response = await fetch(`${ACCOUNTS_URL}/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  })

  const data = await response.json()

  if (data.token) {
    // Store token
    localStorage.setItem('huly.token', data.token)

    // Connect to workspace
    const client = await createClient(TRANSACTOR_URL, data.token)
  } else {
    throw new Error('Login failed')
  }
}

// Server-side validation
async function loginHandler(email: string, password: string) {
  // 1. Find social ID by email
  const socialId = await db.socialId.findOne({
    type: SocialIdType.EMAIL,
    value: email
  })

  if (!socialId) {
    throw new Error('Account not found')
  }

  // 2. Get account
  const account = await db.account.findOne({
    uuid: socialId.personUuid
  })

  // 3. Verify password
  if (!verifyPassword(password, account.hash, account.salt)) {
    throw new Error('Invalid password')
  }

  // 4. Generate token
  const token = generateToken(account.uuid, undefined)

  return { token, account }
}
```

### OAuth Login (GitHub Example)

```typescript
// 1. Redirect to GitHub
function loginWithGitHub() {
  const clientId = GITHUB_CLIENT_ID
  const redirectUri = `${ACCOUNTS_URL}/auth/github/callback`
  const state = encodeState({ branding, workspace })

  window.location.href = `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}`
}

// 2. GitHub redirects back to callback
// 3. Server exchanges code for access token
async function githubCallback(code: string, state: string) {
  // Exchange code for token
  const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
    method: 'POST',
    body: JSON.stringify({
      client_id: GITHUB_CLIENT_ID,
      client_secret: GITHUB_CLIENT_SECRET,
      code
    })
  })

  const { access_token } = await tokenResponse.json()

  // Get user info
  const userResponse = await fetch('https://api.github.com/user', {
    headers: { Authorization: `Bearer ${access_token}` }
  })

  const githubUser = await userResponse.json()

  // Find or create account
  const account = await loginOrSignUpWithProvider(db, githubUser.email, githubUser.name, githubUser.name, {
    type: SocialIdType.GITHUB,
    value: githubUser.id
  })

  // Generate HULY token
  const token = generateToken(account.uuid, undefined)

  return { token, account }
}
```

### OTP (One-Time Password) Login

Passwordless authentication:

```typescript
// 1. Request OTP
async function requestOTP(email: string): Promise<void> {
  await fetch(`${ACCOUNTS_URL}/otp-request`, {
    method: 'POST',
    body: JSON.stringify({ email })
  })

  // Server sends 6-digit code to email
}

// 2. Validate OTP
async function validateOTP(email: string, code: string): Promise<string> {
  const response = await fetch(`${ACCOUNTS_URL}/otp-validate`, {
    method: 'POST',
    body: JSON.stringify({ email, code })
  })

  const data = await response.json()
  return data.token
}

// Server-side OTP generation
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Store OTP with expiration
await db.otp.insertOne({
  socialId: emailSocialId._id,
  code: otpCode,
  expiresOn: Date.now() + 10 * 60 * 1000 // 10 minutes
})
```

## JWT Tokens

### Token Structure

```typescript
interface TokenPayload {
  account: AccountUuid // User account
  workspace?: WorkspaceUuid // Current workspace
  email: string // User email
  extra?: Record<string, string> // Additional data
  nbf?: number // Not before (timestamp)
  exp?: number // Expiration (timestamp)
}
```

### Token Generation

```typescript
import jwt from 'jsonwebtoken'

function generateToken(account: AccountUuid, workspace?: WorkspaceUuid, extra?: Record<string, string>): string {
  const payload: TokenPayload = {
    account,
    workspace,
    email: account.email,
    extra,
    nbf: Date.now() / 1000,
    exp: Date.now() / 1000 + 24 * 60 * 60 // 24 hours
  }

  return jwt.sign(payload, SECRET, { algorithm: 'HS256' })
}
```

### Token Validation

```typescript
function verifyToken(token: string): TokenPayload {
  try {
    const payload = jwt.verify(token, SECRET) as TokenPayload

    // Check expiration
    if (payload.exp && payload.exp < Date.now() / 1000) {
      throw new Error('Token expired')
    }

    // Check not before
    if (payload.nbf && payload.nbf > Date.now() / 1000) {
      throw new Error('Token not yet active')
    }

    return payload
  } catch (err) {
    throw new Error('Invalid token')
  }
}
```

### Token Refresh

```typescript
// Client refreshes token before expiration
setInterval(async () => {
  const response = await fetch(`${ACCOUNTS_URL}/refresh-token`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${currentToken}`
    }
  })

  const data = await response.json()

  if (data.token) {
    localStorage.setItem('huly.token', data.token)
    currentToken = data.token
  }
}, 20 * 60 * 1000) // Refresh every 20 minutes
```

## Account Roles

### Role Hierarchy

```typescript
enum AccountRole {
  ReadOnlyGuest = 'READONLYGUEST', // Power: 5
  DocGuest = 'DocGuest', // Power: 10
  Guest = 'GUEST', // Power: 20
  User = 'USER', // Power: 30
  Maintainer = 'MAINTAINER', // Power: 40
  Owner = 'OWNER', // Power: 50
  Admin = 'ADMIN' // Power: 100
}
```

### Role Checking

```typescript
function hasAccountRole(account: Account, targetRole: AccountRole): boolean {
  const rolePower = {
    [AccountRole.ReadOnlyGuest]: 5,
    [AccountRole.DocGuest]: 10,
    [AccountRole.Guest]: 20,
    [AccountRole.User]: 30,
    [AccountRole.Maintainer]: 40,
    [AccountRole.Owner]: 50,
    [AccountRole.Admin]: 100
  }

  return rolePower[account.role] >= rolePower[targetRole]
}

// Usage
if (!hasAccountRole(account, AccountRole.Maintainer)) {
  throw new Error('Insufficient permissions')
}
```

## Guest Access

### Read-Only Guest

```typescript
// Special guest account UUID
const readOnlyGuestUuid = 'guest:readonly' as AccountUuid

// Check if user is read-only guest
function isReadOnlyGuest(account: Account): boolean {
  return account.uuid === readOnlyGuestUuid
}

// Restrict write operations
if (isReadOnlyGuest(account)) {
  throw new Error('Read-only access')
}
```

### Guest Sign-Up

Allow guests to self-register:

```typescript
// Check workspace allows guest sign-up
const workspace = await db.workspace.findOne({ uuid: workspaceUuid })

if (!workspace.allowGuestSignUp) {
  throw new Error('Guest sign-up not allowed')
}

// Create guest account
const guestAccount = await createAccount(
  db,
  personUuid,
  false // Not confirmed
)

// Assign guest role
await db.assignWorkspace(guestAccount.uuid, workspaceUuid, AccountRole.Guest)
```

## Multi-Workspace Authentication

### Workspace Token

Token includes workspace:

```typescript
const token = generateToken(
  accountUuid,
  workspaceUuid // ← Workspace in token
)

// Decode to get workspace
const { account, workspace } = verifyToken(token)
```

### Switch Workspace

```typescript
async function switchWorkspace(workspaceId: WorkspaceUuid): Promise<void> {
  // Get new token for workspace
  const response = await fetch(`${ACCOUNTS_URL}/select-workspace`, {
    method: 'POST',
    headers: { Authorization: `Bearer ${currentToken}` },
    body: JSON.stringify({ workspace: workspaceId })
  })

  const data = await response.json()

  // Disconnect from current workspace
  await currentClient.close()

  // Connect to new workspace
  const newClient = await createClient(TRANSACTOR_URL, data.token)

  // Update UI
  window.location.reload()
}
```

## Security Best Practices

### Password Security

```typescript
import bcrypt from 'bcryptjs'

// Hash password with salt
async function hashPassword(password: string): Promise<{ hash: Buffer; salt: Buffer }> {
  const salt = await bcrypt.genSalt(10)
  const hash = await bcrypt.hash(password, salt)

  return {
    hash: Buffer.from(hash),
    salt: Buffer.from(salt)
  }
}

// Verify password
function verifyPassword(password: string, hash: Buffer, salt: Buffer): boolean {
  return bcrypt.compareSync(password, hash.toString())
}
```

### Token Storage

```typescript
// ❌ Bad - XSS vulnerable
localStorage.setItem('token', token)

// ✅ Better - HttpOnly cookie (if possible)
// Set by server:
res.setHeader('Set-Cookie', `token=${token}; HttpOnly; Secure; SameSite=Strict`)

// ✅ Good - localStorage with XSS protection
// Sanitize all user input
// Use Content Security Policy
localStorage.setItem('huly.token', token)
```

### Rate Limiting

Prevent brute-force attacks:

```typescript
const loginAttempts = new Map<string, number>()

async function checkRateLimit(email: string): Promise<void> {
  const attempts = loginAttempts.get(email) || 0

  if (attempts >= 5) {
    throw new Error('Too many login attempts. Try again later.')
  }

  loginAttempts.set(email, attempts + 1)

  // Reset after 15 minutes
  setTimeout(() => {
    loginAttempts.delete(email)
  }, 15 * 60 * 1000)
}
```

### CORS Configuration

```typescript
const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = ['http://localhost:8080', 'https://app.huly.io', 'https://*.huly.io']

    if (!origin || allowedOrigins.some((allowed) => origin.match(new RegExp(allowed.replace('*', '.*'))))) {
      callback(null, true)
    } else {
      callback(new Error('Not allowed by CORS'))
    }
  },
  credentials: true
}

app.use(cors(corsOptions))
```

## Account Management

### User Registration

```typescript
async function signUp(email: string, password: string, firstName: string, lastName: string): Promise<LoginInfo> {
  // 1. Check if email exists
  const existing = await db.socialId.findOne({
    type: SocialIdType.EMAIL,
    value: email
  })

  if (existing) {
    throw new Error('Email already registered')
  }

  // 2. Create person
  const personUuid = await db.person.insertOne({
    firstName,
    lastName,
    name: `${firstName} ${lastName}`
  })

  // 3. Create social ID
  const socialId = await db.socialId.insertOne({
    personUuid,
    type: SocialIdType.EMAIL,
    value: email,
    verifiedOn: null // Not verified yet
  })

  // 4. Create account with password
  const { hash, salt } = await hashPassword(password)
  await db.account.insertOne({
    uuid: personUuid as AccountUuid,
    role: AccountRole.User,
    primarySocialId: socialId._id,
    socialIds: [socialId._id],
    hash,
    salt
  })

  // 5. Send verification email
  await sendVerificationEmail(email, socialId._id)

  return {
    account: personUuid as AccountUuid,
    socialId: socialId._id
  }
}
```

### Email Verification

```typescript
// Send verification email
async function sendVerificationEmail(email: string, socialId: PersonId): Promise<void> {
  // Generate OTP
  const code = generateOTP()

  await db.otp.insertOne({
    socialId,
    code,
    expiresOn: Date.now() + 24 * 60 * 60 * 1000 // 24 hours
  })

  // Send email
  await sendEmail(
    email,
    'Verify your email',
    `
    Your verification code: ${code}
    
    Click here to verify:
    ${FRONT_URL}/verify?email=${email}&code=${code}
  `
  )
}

// Verify email
async function verifyEmail(email: string, code: string): Promise<void> {
  const socialId = await db.socialId.findOne({
    type: SocialIdType.EMAIL,
    value: email
  })

  if (!socialId) {
    throw new Error('Email not found')
  }

  // Check OTP
  const otp = await db.otp.findOne({
    socialId: socialId._id,
    code
  })

  if (!otp || otp.expiresOn < Date.now()) {
    throw new Error('Invalid or expired code')
  }

  // Mark as verified
  await db.socialId.update({ _id: socialId._id }, { verifiedOn: Date.now() })

  // Delete OTP
  await db.otp.deleteOne({ _id: otp._id })
}
```

### Password Reset

```typescript
// 1. Request reset
async function requestPasswordReset(email: string): Promise<void> {
  const socialId = await db.socialId.findOne({
    type: SocialIdType.EMAIL,
    value: email
  })

  if (!socialId) {
    // Don't reveal if email exists
    return
  }

  // Generate reset code
  const code = generateOTP()

  await db.otp.insertOne({
    socialId: socialId._id,
    code,
    expiresOn: Date.now() + 1 * 60 * 60 * 1000 // 1 hour
  })

  await sendEmail(
    email,
    'Reset your password',
    `
    Reset code: ${code}
    
    Click here: ${FRONT_URL}/reset-password?email=${email}
  `
  )
}

// 2. Reset password with code
async function resetPassword(email: string, code: string, newPassword: string): Promise<void> {
  const socialId = await db.socialId.findOne({
    type: SocialIdType.EMAIL,
    value: email
  })

  if (!socialId) {
    throw new Error('Invalid email')
  }

  // Verify OTP
  const otp = await db.otp.findOne({
    socialId: socialId._id,
    code
  })

  if (!otp || otp.expiresOn < Date.now()) {
    throw new Error('Invalid or expired code')
  }

  // Update password
  const { hash, salt } = await hashPassword(newPassword)
  await db.account.update({ uuid: socialId.personUuid as AccountUuid }, { hash, salt })

  // Delete OTP
  await db.otp.deleteOne({ _id: otp._id })
}
```

## Session Management

### Server-Side Sessions

```typescript
interface Session {
  sessionId: string
  accountUuid: AccountUuid
  workspaceUuid: WorkspaceUuid
  createdAt: Timestamp
  lastActivity: Timestamp
  ipAddress: string
  userAgent: string
}

// Create session on login
async function createSession(account: AccountUuid, workspace: WorkspaceUuid, req: Request): Promise<Session> {
  const session = {
    sessionId: generateId(),
    accountUuid: account,
    workspaceUuid: workspace,
    createdAt: Date.now(),
    lastActivity: Date.now(),
    ipAddress: req.ip,
    userAgent: req.headers['user-agent']
  }

  await db.session.insertOne(session)

  return session
}

// Update activity
async function updateSessionActivity(sessionId: string): Promise<void> {
  await db.session.update({ sessionId }, { lastActivity: Date.now() })
}

// Clean up old sessions
async function cleanupSessions(): Promise<void> {
  const threshold = Date.now() - 7 * 24 * 60 * 60 * 1000 // 7 days

  await db.session.deleteMany({
    lastActivity: { $lt: threshold }
  })
}
```

## Security Checklist

### ✅ Do's

- Hash passwords with bcrypt (10+ rounds)
- Use HTTPS in production
- Set secure JWT secret (long random string)
- Implement rate limiting
- Validate all inputs
- Use HttpOnly cookies when possible
- Implement CSRF protection
- Log authentication events
- Enable 2FA for admin accounts
- Regularly rotate secrets
- Use strong password requirements
- Implement account lockout

### ❌ Don'ts

- Don't store passwords in plain text
- Don't use weak JWT secrets
- Don't expose user enumeration
- Don't skip email verification
- Don't log passwords
- Don't use MD5/SHA1 for passwords
- Don't trust client-side validation
- Don't allow unlimited login attempts
- Don't store tokens in URLs
- Don't reuse tokens across workspaces

## Summary

HULY's authentication provides:

- ✅ Multiple authentication methods
- ✅ Secure password hashing
- ✅ JWT token-based sessions
- ✅ OAuth integration
- ✅ OTP/passwordless login
- ✅ Guest access
- ✅ Multi-workspace support
- ✅ Email verification
- ✅ Password reset
- ✅ Rate limiting

Secure and flexible authentication for all use cases! 🔐
