# Architecture Overview

HULY is built on a sophisticated, flexible architecture that enables powerful collaboration features while maintaining security and performance.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                         Workspace                           │
│  (Isolated tenant with its own data and configuration)      │
│                                                              │
│  ┌────────────┐  ┌────────────┐  ┌────────────┐            │
│  │   Space    │  │   Space    │  │   Space    │  ...       │
│  │ (Project)  │  │ (Teamspace)│  │ (Channel)  │            │
│  │            │  │            │  │            │            │
│  │  ┌──────┐  │  │  ┌──────┐  │  │  ┌──────┐  │            │
│  │  │ Doc  │  │  │  │ Doc  │  │  │  │ Doc  │  │            │
│  │  └──────┘  │  │  └──────┘  │  │  └──────┘  │            │
│  │  ┌──────┐  │  │  ┌──────┐  │  │  ┌──────┐  │            │
│  │  │ Doc  │  │  │  │ Doc  │  │  │  │ Msg  │  │            │
│  │  └──────┘  │  │  └──────┘  │  │  └──────┘  │            │
│  └────────────┘  └────────────┘  └────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## Core Principles

### 1. Everything is an Object

HULY follows a strict object hierarchy:

- **Obj** - The root of all objects
- **Doc** - Persistable documents
- **Space** - Container for documents
- **TypedSpace** - Spaces with custom types and roles

### 2. Event Sourcing

All changes in HULY are represented as **Transactions**:

- Every modification creates a transaction
- Transactions are persisted and can be replayed
- Enables real-time collaboration and audit trails

### 3. Fine-Grained Permissions

Two-level permission system:

- **Account Roles** - Workspace-level permissions (Owner, Maintainer, User, Guest)
- **Space Roles** - Space-level permissions (Manager, Developer, QA, etc.)

### 4. Domain-Based Storage

Data is organized into **Domains** for optimized storage:

- Transactions → PostgreSQL (ACID compliance)
- Documents → PostgreSQL/MongoDB
- Blobs → S3 (object storage)
- Transient data → In-memory

### 5. Plugin Architecture

HULY is extensible through plugins that can:

- Define new document types
- Add custom UI components
- Extend the data model with new classes
- Add new permissions and roles

## Key Components

### Workspaces

- Isolated tenants with separate data
- Own configuration and branding
- Regional deployment support

### Spaces

- Organizational containers for documents
- Can be private or public
- Have members and owners
- Support custom types via TypedSpaces

### Documents

- Everything is a document (issues, projects, messages, etc.)
- Every document belongs to a space
- Documents can be attached to other documents
- Rich metadata and versioning support

### Transactions

- Atomic units of change
- Support create, update, delete, and mixin operations
- Broadcasted to all connected clients
- Stored for audit and replay

## Next Steps

Dive deeper into specific aspects:

- [Core Concepts](model/core-concepts) - Understand the fundamental building blocks
- [Workspaces](model/workspaces) - Learn about tenant isolation
- [Spaces](model/spaces) - Explore the container system
- [Roles & Permissions](model/roles-permissions) - Master access control
- [Transactions](model/transactions) - Understand how changes work

## Architecture Diagrams

### Component Architecture

```
┌──────────────┐
│   Client     │
│  (Browser)   │
└──────┬───────┘
       │
       │ WebSocket
       │
┌──────▼───────┐      ┌──────────────┐
│  Transactor  │◄────►│  PostgreSQL  │
│   (Server)   │      │   (Tx Log)   │
└──────┬───────┘      └──────────────┘
       │
       │
┌──────▼───────┐      ┌──────────────┐
│   Storage    │◄────►│  PostgreSQL  │
│   Adapter    │      │  (Documents) │
└──────┬───────┘      └──────────────┘
       │
       │              ┌──────────────┐
       └─────────────►│      S3      │
                      │   (Blobs)    │
                      └──────────────┘
```

### Data Flow

```
User Action
    │
    ▼
Create Transaction (TxCreateDoc, TxUpdateDoc, etc.)
    │
    ▼
Validate Permissions
    │
    ▼
Apply to Storage
    │
    ▼
Broadcast to Connected Clients
    │
    ▼
Update Local State
```

This architecture enables HULY to be scalable, secure, and extensible while maintaining real-time collaboration capabilities.
