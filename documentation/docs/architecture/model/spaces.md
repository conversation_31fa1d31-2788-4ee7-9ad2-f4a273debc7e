# Spaces

**Spaces** are the fundamental organizational containers in HULY. They group related documents together and control access through membership.

## What is a Space?

A space is like a folder, project, team, or channel - it's a container that holds related documents and controls who can access them.

```typescript
interface Space extends Doc {
  name: string // Display name
  description: string // Description
  private: boolean // Is this space private?
  members: AccountUuid[] // Who can access this space
  archived: boolean // Is this space archived?
  owners?: AccountUuid[] // Who owns/manages this space
  autoJoin?: boolean // Auto-add new users?
}
```

## Key Characteristics

### 1. Every Document Lives in a Space

This is a fundamental rule in HULY:

```typescript
// ✅ Valid - document has a space
const issue = {
  _id: 'issue-1',
  _class: 'tracker:class:Issue',
  space: 'project-alpha', // Lives in Project Alpha space
  title: 'Fix bug'
}

// ❌ Invalid - no orphaned documents!
const orphan = {
  _id: 'doc-1',
  _class: 'document:class:Document',
  space: undefined // Error!
}
```

**Why this rule?**

- Ensures all documents have access control
- Provides organizational structure
- Enables permission inheritance
- Supports queries like "show me all issues in this project"

### 2. Membership Controls Access

Only members can access a space's documents:

```typescript
const projectSpace = {
  _id: 'project-1',
  name: 'Mobile App',
  members: ['user-1', 'user-2', 'user-3'],
  private: true
}

// user-1 can see all issues in project-1 ✅
// user-4 cannot see any issues in project-1 ❌
```

### 3. Spaces Can Be Public or Private

```typescript
// Private space - only members can access
const privateProject = {
  name: 'Secret Project',
  private: true,
  members: ['user-1', 'user-2']
}

// Public space - all workspace users can access
const publicChannel = {
  name: 'General Discussion',
  private: false,
  members: [] // Everyone can access
}
```

### 4. Spaces Can Be Archived

```typescript
// Active space
const activeProject = {
  name: 'Current Sprint',
  archived: false
}

// Archived space - read-only
const oldProject = {
  name: 'Completed Project',
  archived: true // No new documents, but data preserved
}
```

## Space Types

HULY has several types of spaces:

### 1. Base Space

The simplest form - just a container with members.

### 2. SystemSpace

Special spaces used by the system:

```typescript
interface SystemSpace extends Space {}
```

Examples:

- `core.space.Model` - Stores class definitions
- `core.space.Tx` - Stores transactions
- `core.space.Configuration` - Stores configuration

### 3. TypedSpace

Spaces with custom types, roles, and permissions:

```typescript
interface TypedSpace extends Space {
  type: Ref<SpaceType> // Reference to a space type definition
}
```

See [Typed Spaces](typed-spaces) for details.

## Space Storage

Spaces are stored in the `DOMAIN_SPACE` domain:

```typescript
const DOMAIN_SPACE = 'space' as Domain
```

This means all spaces (regardless of their type) are stored together, separate from other documents.

## Space Operations

### Creating a Space

```typescript
const projectSpace = await client.createDoc(
  tracker.class.Project,
  core.space.Space, // Parent space
  {
    name: 'New Project',
    description: 'Project description',
    private: false,
    members: [currentUser],
    owners: [currentUser],
    identifier: 'PROJ',
    defaultIssueStatus: 'status:todo'
  }
)
```

### Adding Members

```typescript
// Add a user to space members
await client.update(spaceId, {
  $push: { members: newUserUuid }
})

// Add multiple users
await client.update(spaceId, {
  $push: { members: { $each: [user1, user2, user3] } }
})
```

### Removing Members

```typescript
// Remove a user from space
await client.update(spaceId, {
  $pull: { members: userToRemove }
})
```

### Archiving a Space

```typescript
// Archive (soft delete)
await client.update(spaceId, {
  archived: true
})

// Unarchive
await client.update(spaceId, {
  archived: false
})
```

## Space Membership

### Owners vs Members

```typescript
const space = {
  owners: ['user-1', 'user-2'], // Can manage space settings
  members: ['user-1', 'user-2', 'user-3', 'user-4'] // Can access space
}
```

**Owners** can:

- Change space settings
- Add/remove members
- Archive the space
- Delete the space

**Members** can:

- Access documents in the space
- Create documents (if they have permission)
- View other members

### Auto-Join

Spaces can automatically add new workspace users:

```typescript
const publicSpace = {
  name: 'General',
  autoJoin: true, // All new users automatically become members
  members: [...existingUsers]
}
```

When a new user joins the workspace, they're automatically added to all `autoJoin` spaces.

## Space Queries

### Finding Spaces

```typescript
// Get all spaces user is a member of
const mySpaces = await client.findAll(core.class.Space, {
  members: currentUser.uuid
})

// Get all active (non-archived) spaces
const activeSpaces = await client.findAll(core.class.Space, {
  archived: false
})

// Get all private spaces
const privateSpaces = await client.findAll(core.class.Space, {
  private: true,
  members: currentUser.uuid
})
```

### Finding Documents in a Space

```typescript
// Get all issues in a project
const issues = await client.findAll(tracker.class.Issue, {
  space: projectId
})

// Get all documents in a teamspace
const docs = await client.findAll(document.class.Document, {
  space: teamspaceId
})
```

## Space Examples

### Project Space (Tracker)

```typescript
const softwareProject = {
  _id: 'project-1',
  _class: 'tracker:class:Project',
  name: 'Mobile App Rewrite',
  description: 'Rewriting our mobile app',
  identifier: 'MOBILE',
  private: false,
  members: [dev1, dev2, dev3, pm1, qa1],
  owners: [pm1],
  defaultIssueStatus: 'status:backlog'
}

// Issues in this space
const bug = {
  _class: 'tracker:class:Issue',
  space: 'project-1', // Belongs to Mobile App project
  title: 'Login broken',
  status: 'status:todo'
}
```

### Teamspace (Documents)

```typescript
const docsSpace = {
  _id: 'teamspace-1',
  _class: 'document:class:Teamspace',
  name: 'Engineering Docs',
  description: 'Technical documentation',
  private: false,
  members: [allEngineers],
  owners: [techLead]
}

// Documents in this space
const apiDoc = {
  _class: 'document:class:Document',
  space: 'teamspace-1', // Belongs to Engineering Docs
  title: 'API Documentation',
  content: 'API reference...'
}
```

### Channel (Chat)

```typescript
const chatChannel = {
  _id: 'channel-1',
  _class: 'chunter:class:Channel',
  name: 'General',
  description: 'General discussion',
  private: false,
  members: [everyone],
  autoJoin: true // Everyone joins automatically
}

// Messages in this channel
const message = {
  _class: 'chunter:class:Message',
  space: 'channel-1', // Belongs to General channel
  content: 'Hello everyone!'
}
```

## Best Practices

### 1. Meaningful Names

```
✅ Good: "Mobile App Rewrite", "Q1 Planning", "Customer Support"
❌ Bad: "Project 1", "Space A", "Untitled"
```

### 2. Appropriate Privacy

```
✅ Good: Private for confidential projects, public for general discussion
❌ Bad: Everything private, or everything public
```

### 3. Regular Cleanup

```typescript
// Archive completed projects
const completedProjects = await findCompletedProjects()
for (const project of completedProjects) {
  await client.update(project._id, { archived: true })
}
```

### 4. Clear Ownership

```typescript
// ✅ Good: Clear owners for each space
const space = {
  owners: [projectManager, techLead],
  members: [allTeamMembers]
}

// ❌ Bad: No owners, or everyone is an owner
const badSpace = {
  owners: [], // Who manages this?
  members: [everyone]
}
```

## Permissions

Space access is determined by:

1. **Workspace Role** - Must be at least a User
2. **Space Membership** - Must be in `members` array (for private spaces)
3. **Space Type Roles** - For TypedSpaces, specific permissions per role

See [Roles & Permissions](roles-permissions) for details.

## Summary

**Spaces are:**

- ✅ Containers for documents
- ✅ Access control boundaries
- ✅ Organizational units
- ✅ Required for all documents

**Key concepts:**

- Every document must have a space
- Membership controls access
- Spaces can be public or private
- Spaces can be archived
- Owners manage the space

**Space types:**

- Base `Space` - Simple container
- `SystemSpace` - System spaces
- `TypedSpace` - Customizable with roles (Projects, Teamspaces, etc.)

Next: Learn about [Typed Spaces](typed-spaces) - spaces with custom roles and permissions.
