# Roles and Permissions

HULY has a sophisticated two-level permission system that provides fine-grained access control.

## Two Permission Levels

### 1. Account Roles (Workspace-Level)

Control what you can do in the workspace:

```typescript
enum AccountRole {
  ReadOnlyGuest = 'READONLYGUEST',
  DocGuest = 'DocGuest',
  Guest = 'GUEST',
  User = 'USER',
  Maintainer = 'MAINTAINER',
  Owner = 'OWNER',
  Admin = 'ADMIN'
}
```

**See:** [Workspaces](workspaces#account-roles-workspace-level) for details.

### 2. Space Roles (Space-Level)

Control what you can do within a specific space:

- Manager
- Developer
- QA
- QARA (Quality Assurance/Regulatory Affairs)
- Qualified User

## How Permissions Work

For comprehensive permission system documentation, see the [Model Architecture overview](../overview.md) and related pages.

## Next Steps

- [Transactions](transactions) - How changes are validated
- [Mixins](mixins) - Extending objects with role data
