# The Builder Pattern

The **Builder** is how HULY defines its data model using decorators and generates transactions to create the schema.

## Model Decorators

HULY uses TypeScript decorators to define models:

```typescript
@Model(core.class.Space, core.class.Doc, DOMAIN_SPACE)
@UX(core.string.Space, undefined, undefined, 'name')
export class TSpace extends TDoc implements Space {
  @Prop(TypeString(), core.string.Name)
  @Index(IndexKind.FullText)
  name!: string

  @Prop(TypeString(), core.string.Description)
  @Index(IndexKind.FullText)
  description!: string

  // ... more properties
}
```

## How It Works

The Builder:

1. Processes decorator metadata
2. Generates transactions to create classes and attributes
3. Applies transactions to build the model
4. Creates the hierarchy and indexes

## Model as Transactions

The beautiful thing: **The model itself is just transactions!**

This means:

- Models can be versioned
- Models can be migrated
- Models can be inspected at runtime
- Models can be extended by plugins

For comprehensive Builder documentation, see the [Model Architecture overview](../overview.md) and related pages.
