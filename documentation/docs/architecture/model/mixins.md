# Mixins

**Mixins** allow you to add attributes to objects at runtime without changing their base class.

## What is a Mixin?

```typescript
type Mixin<T extends Doc> = Class<T>
```

A mixin is a special kind of class that extends an existing class, adding optional attributes.

## Why Use Mixins?

1. **Flexibility** - Add attributes without changing base classes
2. **Optional Features** - Not all instances need all attributes
3. **Plugin Architecture** - Plugins can add their own mixins
4. **Type Safety** - Still type-checked by TypeScript

## Example Use Cases

- Role assignments (only TypedSpaces need this)
- Template data (only template documents need this)
- Integration metadata (only integrated objects need this)
- Custom attributes for specific space types

For comprehensive mixin documentation, see the [Model Architecture overview](../overview.md) and related pages.

## Next Steps

- [Domains](domains) - Storage organization
- [Builder](builder) - Creating models
