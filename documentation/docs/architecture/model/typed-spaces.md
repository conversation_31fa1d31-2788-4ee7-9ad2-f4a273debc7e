# Typed Spaces

**TypedSpaces** are spaces with customizable types that support custom roles and permissions. They're the foundation for projects, teamspaces, and other specialized spaces.

## Overview

```typescript
interface TypedSpace extends Space {
  type: Ref<SpaceType> // Reference to a space type definition
}
```

A TypedSpace adds one crucial feature to a base Space: a reference to a **SpaceType** that defines:

- Custom roles
- Custom permissions
- Custom attributes (via mixins)
- Custom behavior

## The TypedSpace System

The TypedSpace system consists of three main components:

### 1. SpaceTypeDescriptor - The Blueprint

```typescript
interface SpaceTypeDescriptor extends Doc {
  name: IntlString // Display name
  description: IntlString // Description
  icon: Asset // Icon
  baseClass: Ref<Class<Space>> // Which Space class this is for
  availablePermissions: Ref<Permission>[] // What permissions are available
  system?: boolean // Is this a system type?
}
```

**Purpose:** Defines what KIND of typed space this is.

**Examples:**

- "Project Space Type" for tracker projects
- "Document Space Type" for QMS documents
- "Channel Type" for chat channels

### 2. SpaceType - The Configuration

```typescript
interface SpaceType extends Doc {
  name: string // Instance name (e.g., "Software Project")
  shortDescription?: string // Short description
  descriptor: Ref<SpaceTypeDescriptor> // Which kind of space type
  members?: AccountUuid[] // Auto-add these members to new spaces
  autoJoin?: boolean // Auto-join all users
  targetClass: Ref<Class<Space>> // Mixin class for custom attributes
  roles: CollectionSize<Role> // Collection of roles for this type
}
```

**Purpose:** An INSTANCE of a descriptor with specific configuration.

**Examples:**

- "Software Development" (SpaceType for projects)
- "QMS Documents" (SpaceType for controlled documents)
- "Team Chat" (SpaceType for channels)

### 3. Role - Permission Sets

```typescript
interface Role extends AttachedDoc<SpaceType, 'roles'> {
  name: string // Role name (e.g., "Manager", "Developer")
  permissions: Ref<Permission>[] // What can this role do?
}
```

**Purpose:** Defines what users with this role can do in spaces of this type.

## Complete Example

For more detailed documentation, see the [Model Architecture overview](../overview.md) and related pages.

## Next Steps

- [Roles & Permissions](roles-permissions) - Understanding the permission system
- [Transactions](transactions) - How changes are made
