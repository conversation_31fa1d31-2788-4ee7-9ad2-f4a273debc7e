# Workspaces

A **Workspace** is the highest-level organizational unit in HULY - a complete, isolated instance for an organization or team.

## What is a Workspace?

Think of a workspace as a tenant in a multi-tenant system. Each workspace:

- Has its own isolated data
- Has its own configuration
- Has its own set of users
- Can be deployed in different regions
- Can have custom branding

```typescript
interface Workspace {
  uuid: WorkspaceUuid // Unique identifier (modern)
  dataId?: WorkspaceDataId // Old identifier (legacy - DB name, bucket, etc)
  name: string // Display name
  url: string // Workspace URL slug
  allowReadOnlyGuest: boolean // Can read-only guests access?
  allowGuestSignUp: boolean // Can guests self-register?
  branding?: string // Custom branding identifier
  location?: Location // Physical location
  region?: string // Transactor group/region
  createdBy?: PersonUuid // Creator UUID
  billingAccount?: PersonUuid // Billing owner UUID
  createdOn?: Timestamp // Creation timestamp
}
```

## Key Characteristics

### 1. Complete Isolation

Workspaces are completely isolated from each other:

```
Workspace A              Workspace B
┌────────────────┐      ┌────────────────┐
│ Users: [...]   │      │ Users: [...]   │
│ Spaces: [...]  │      │ Spaces: [...]  │
│ Docs: [...]    │      │ Docs: [...]    │
│ Config: {...}  │      │ Config: {...}  │
└────────────────┘      └────────────────┘
     No data sharing →  ← Completely separate
```

- No data is shared between workspaces
- Users in workspace A cannot see workspace B's data
- Each workspace has its own database schema/tables

### 2. Regional Deployment

Workspaces can be deployed in different regions for compliance and performance:

```typescript
const workspace = {
  uuid: 'ws-123',
  name: 'ACME Corp',
  region: 'eu-west', // Europe
  location: 'eu'
}
```

This enables:

- GDPR compliance (EU data stays in EU)
- Low latency (users connect to nearby servers)
- Data sovereignty requirements

### 3. Custom Branding

Each workspace can have its own branding:

```typescript
const workspace = {
  uuid: 'ws-123',
  name: 'ACME Corp',
  branding: 'acme-theme',
  url: 'acme' // acme.huly.io
}
```

### 4. Guest Access Control

Workspaces control who can access them:

```typescript
const workspace = {
  allowReadOnlyGuest: true, // Read-only guests can view
  allowGuestSignUp: false // Guests cannot self-register
}
```

## Workspace Lifecycle

### 1. Creation

```typescript
// Create a new workspace
const workspace = await createWorkspace({
  name: 'My Company',
  url: 'mycompany',
  region: 'us-east',
  createdBy: currentUser.uuid
})
```

When a workspace is created:

1. A new database instance is provisioned
2. The model is initialized
3. Default spaces and data are created
4. The creator becomes the workspace owner

### 2. Configuration

Workspaces can be configured with:

- User roles and permissions
- Integrations (GitHub, Jira, etc.)
- Branding and themes
- Notification settings
- Security policies

### 3. Membership

Users can be members of multiple workspaces:

```typescript
interface WorkspaceMembership {
  accountUuid: AccountUuid
  workspaceUuid: WorkspaceUuid
  role: AccountRole // Owner, Maintainer, User, Guest
}
```

A user's role in a workspace determines their workspace-level permissions.

## Account Roles (Workspace-Level)

Workspaces have a hierarchy of account roles:

```typescript
enum AccountRole {
  ReadOnlyGuest = 'READONLYGUEST', // Can only read, no write
  DocGuest = 'DocGuest', // Guest with doc access
  Guest = 'GUEST', // Regular guest
  User = 'USER', // Regular user
  Maintainer = 'MAINTAINER', // Can manage workspace
  Owner = 'OWNER', // Workspace owner
  Admin = 'ADMIN' // System admin
}
```

**Power Hierarchy:**

```
ReadOnlyGuest (5) < DocGuest (10) < Guest (20) < User (30) <
Maintainer (40) < Owner (50) < Admin (100)
```

**What each role can do:**

| Action           | ReadOnlyGuest | Guest | User | Maintainer | Owner | Admin |
| ---------------- | ------------- | ----- | ---- | ---------- | ----- | ----- |
| Read data        | ✅            | ✅    | ✅   | ✅         | ✅    | ✅    |
| Create docs      | ❌            | ✅    | ✅   | ✅         | ✅    | ✅    |
| Invite users     | ❌            | ❌    | ❌   | ✅         | ✅    | ✅    |
| Manage workspace | ❌            | ❌    | ❌   | ✅         | ✅    | ✅    |
| Change billing   | ❌            | ❌    | ❌   | ❌         | ✅    | ✅    |
| Delete workspace | ❌            | ❌    | ❌   | ❌         | ✅    | ✅    |

## Workspace Structure

Inside a workspace, you have:

```
Workspace
├── Spaces (Projects, Teamspaces, Channels, etc.)
│   ├── Project Alpha
│   │   ├── Issue #1
│   │   ├── Issue #2
│   │   └── ...
│   ├── Documentation
│   │   ├── Getting Started
│   │   ├── User Guide
│   │   └── ...
│   └── General Chat
│       ├── Message 1
│       ├── Message 2
│       └── ...
├── People (Employees, Contacts)
├── Configuration
└── Integrations
```

## Multi-Workspace Users

Users can belong to multiple workspaces:

```typescript
const user = {
  uuid: 'user-123',
  name: 'John Doe',
  workspaces: [
    { workspace: 'ws-personal', role: AccountRole.Owner },
    { workspace: 'ws-company', role: AccountRole.User },
    { workspace: 'ws-client', role: AccountRole.Guest }
  ]
}
```

When switching workspaces:

1. Client disconnects from current workspace
2. Client connects to new workspace
3. All data is loaded from the new workspace
4. User sees only the new workspace's data

## Workspace Operations

### Updating Workspace Settings

```typescript
// Change guest access
await updateWorkspace(workspaceId, {
  allowReadOnlyGuest: false
})

// Update branding
await updateWorkspace(workspaceId, {
  branding: 'new-theme'
})
```

### Managing Members

```typescript
// Add a user to workspace
await assignWorkspace(userUuid, workspaceUuid, AccountRole.User)

// Change user's role
await updateWorkspaceRole(userUuid, workspaceUuid, AccountRole.Maintainer)

// Remove user from workspace
await unassignWorkspace(userUuid, workspaceUuid)
```

### Archiving and Deletion

```typescript
// Archive workspace (soft delete)
await archiveWorkspace(workspaceUuid)

// Permanently delete workspace
await deleteWorkspace(workspaceUuid)
```

## Workspace Identification

Workspaces can be identified by:

1. **UUID** - Modern unique identifier

   ```
   ws-a1b2c3d4-e5f6-4789-0123-456789abcdef
   ```

2. **URL** - User-friendly slug

   ```
   https://mycompany.huly.io
   ```

3. **DataId** - Legacy identifier (database name, etc.)
   ```
   workspace_123abc
   ```

## Best Practices

### 1. One Workspace Per Organization

```
✅ Good: One workspace for entire company
❌ Bad: Separate workspace for each department
```

Use spaces within a workspace to organize departments/teams.

### 2. Meaningful URLs

```
✅ Good: acme-corp, engineering-team
❌ Bad: ws123, myworkspace
```

### 3. Proper Role Assignment

```
✅ Good: Most users are User, few Maintainers, one Owner
❌ Bad: Everyone is Owner
```

Follow principle of least privilege.

### 4. Regular Guest Cleanup

```
// Periodically review and remove inactive guests
const inactiveGuests = await getInactiveGuests(30 /* days */)
for (const guest of inactiveGuests) {
  await unassignWorkspace(guest.uuid, workspaceUuid)
}
```

## Summary

**Workspaces are:**

- ✅ Isolated tenants with separate data
- ✅ Deployed in specific regions
- ✅ Customizable with branding
- ✅ Controlled by account roles
- ✅ The highest organizational level in HULY

**Key concepts:**

- Complete data isolation
- Regional deployment
- Account role hierarchy
- Guest access control
- Multi-workspace support

Next: Learn about [Spaces](spaces) - the organizational containers within a workspace.
