# Core Concepts

This document explains the fundamental building blocks of HULY's model architecture.

## The Object Hierarchy

Everything in HULY follows a strict inheritance hierarchy, starting from the most basic object type and building up to complex domain-specific documents.

### 1. Obj - The Root

```typescript
interface Obj {
  _class: Ref<Class<this>> // Reference to the object's type/class
}
```

**Obj** is the root of all objects in HULY. Every single thing in the system knows its own type through the `_class` reference.

This is fundamental for:

- **Type checking** - Runtime type validation
- **Polymorphism** - Treating objects generically while preserving type information
- **Dynamic dispatch** - Calling the right methods based on actual type

### 2. Doc - Persistable Documents

```typescript
interface Doc<S extends Space = Space> extends Obj {
  _id: Ref<this> // Unique identifier
  space: Ref<S> // Which space this doc belongs to
  modifiedOn: Timestamp // Last modification time
  modifiedBy: PersonId // Who modified it last
  createdBy?: PersonId // Who created it
  createdOn?: Timestamp // When it was created
}
```

**Doc** is the base class for ALL persistent data in HULY.

**Key characteristics:**

- Every doc lives in a **Space** (no orphaned documents allowed)
- Every doc tracks creation and modification metadata automatically
- Every doc has a globally unique ID

**Examples of Docs:**

- Issues
- Projects
- Documents
- Messages
- Employees
- Spaces themselves (yes, spaces are documents too!)

### 3. AttachedDoc - Child Documents

```typescript
interface AttachedDoc<Parent extends Doc = Doc, Collection extends string = string, S extends Space = Space>
  extends Doc<S> {
  attachedTo: Ref<Parent> // Parent document
  attachedToClass: Ref<Class<Parent>> // Parent's class
  collection: Collection // Collection name in parent
}
```

**AttachedDoc** represents documents that are logically contained within another document. They form parent-child relationships.

**Examples:**

- Comments on an issue (attached to Issue)
- Subtasks within a task (attached to Task)
- Attachments on a document (attached to Document)
- Roles within a space type (attached to SpaceType)

**Why the separate class?**

- Enforces parent-child relationship
- Enables cascading operations (delete parent → delete children)
- Supports collection queries (get all comments for this issue)

## Type System

HULY has a sophisticated type system that supports:

### Classes

Classes define the structure and behavior of objects:

```typescript
interface Class<T extends Obj> extends Doc {
  kind: ClassifierKind // CLASS, MIXIN, or INTERFACE
  extends?: Ref<Class<Obj>> // Parent class
  domain?: Domain // Storage domain
}
```

Classes are themselves documents! This means:

- The schema is defined as data
- Schema can be versioned and migrated
- Runtime introspection is possible

### Attributes

Every property on a class is an Attribute:

```typescript
interface Attribute<T extends PropertyType> extends Doc {
  name: string // Property name
  type: Type<T> // Property type
  index?: IndexKind // Indexing strategy
  hidden?: boolean // UI visibility
}
```

Attributes define:

- Property name and type
- Whether it should be indexed
- UI presentation hints
- Validation rules

### References

Objects reference each other using typed references:

```typescript
type Ref<T extends Doc> = string & { __ref: T }
```

This provides type safety - you can't accidentally assign a reference to an Issue where a reference to a Project is expected.

## The Class Hierarchy in Practice

Here's how it all fits together:

```
Obj (root of everything)
 │
 └─ Doc (persistable documents)
     ├─ Space (organizational containers)
     │   ├─ SystemSpace (system spaces)
     │   └─ TypedSpace (customizable spaces)
     │       ├─ Project (tracker projects)
     │       ├─ Teamspace (document spaces)
     │       └─ Channel (chat channels)
     │
     ├─ AttachedDoc (child documents)
     │   ├─ Comment (attached to issues/docs)
     │   ├─ Task (attached to projects)
     │   └─ Role (attached to space types)
     │
     ├─ Employee (people in the system)
     ├─ Issue (tracker issues)
     ├─ Document (documents)
     └─ Class (type definitions)
```

## Key Principles

### 1. Everything Has a Type

```typescript
const issue: Issue = {
  _class: 'tracker:class:Issue', // This IS an Issue
  _id: 'issue-123',
  space: 'project-alpha',
  title: 'Fix bug'
  // ... more properties
}
```

### 2. Everything Lives in a Space

```typescript
// ✅ Valid - document has a space
{
  _id: 'doc-1',
  _class: 'document:class:Document',
  space: 'teamspace-1',
  title: 'My Document'
}

// ❌ Invalid - no orphaned documents!
{
  _id: 'doc-2',
  _class: 'document:class:Document',
  space: undefined,  // Error!
  title: 'Orphaned Document'
}
```

### 3. Metadata is Automatic

When you create a document, HULY automatically fills in:

- `createdBy` - Who created it
- `createdOn` - When it was created
- `modifiedBy` - Who last modified it
- `modifiedOn` - When it was last modified

## Type Safety Example

Here's how the type system prevents errors:

```typescript
// Type-safe document creation
const client: TxOperations = getClient()

// ✅ Correct - all required fields provided
await client.createDoc(tracker.class.Issue, 'project-1', {
  title: 'Fix login',
  status: 'todo',
  priority: 'high'
})

// ❌ TypeScript error - missing required field 'title'
await client.createDoc(tracker.class.Issue, 'project-1', {
  status: 'todo',
  priority: 'high'
})

// ❌ TypeScript error - wrong space type
// Issue belongs in a Project, not a Teamspace
await client.createDoc(tracker.class.Issue, teamspace._id, {
  title: 'Wrong space type'
})
```

## Next Steps

Now that you understand the core concepts, learn about:

- [Workspaces](workspaces) - Tenant isolation and organization
- [Spaces](spaces) - The container system
- [Typed Spaces](typed-spaces) - Customizable spaces with roles
- [Transactions](transactions) - How changes are made

## Summary

The core concepts of HULY's model:

1. **Obj** - Everything has a `_class` reference
2. **Doc** - Persistent documents with metadata
3. **AttachedDoc** - Parent-child relationships
4. **Classes** - Define object structure
5. **Attributes** - Define properties
6. **Refs** - Type-safe references

These building blocks enable HULY to be:

- ✅ Type-safe (compile-time and runtime)
- ✅ Flexible (extensible through plugins)
- ✅ Introspectable (schema is data)
- ✅ Organized (everything lives in a space)
