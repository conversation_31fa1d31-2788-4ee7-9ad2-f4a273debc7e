# Domains

**Domains** are logical storage partitions in HULY. Each class belongs to a domain, which determines how and where its data is stored.

## What are Domains?

```typescript
type Domain = string
```

A domain is simply a string identifier that groups related data together.

## Common Domains

```typescript
const DOMAIN_TX = 'tx' // Transactions
const DOMAIN_MODEL = 'model' // Class definitions, types, etc.
const DOMAIN_SPACE = 'space' // Spaces
const DOMAIN_BLOB = 'blob' // Binary blobs (files)
const DOMAIN_TRANSIENT = 'transient' // Temporary, non-persisted data
```

## Why Domains?

1. **Storage Optimization** - Different domains can use different storage backends
2. **Query Optimization** - Can create domain-specific indexes
3. **Data Locality** - Related data stays together
4. **Migration Management** - Can migrate domains independently

For comprehensive domain documentation, see the [Model Architecture overview](../overview.md) and related pages.

## Next Steps

- [Builder](builder) - Creating models
