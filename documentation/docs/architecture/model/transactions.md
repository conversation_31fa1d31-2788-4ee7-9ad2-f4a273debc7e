# Transactions

HULY uses **Event Sourcing** - all changes are represented as transactions that are persisted and can be replayed.

## What are Transactions?

Every modification in HULY creates a transaction:

```typescript
interface Tx extends Doc {
  objectSpace: Ref<Space> // Where the transaction operates
  meta?: Record<string, string | number | boolean> // Non-persisted metadata
}
```

## Transaction Types

### TxCreateDoc - Creating Documents

```typescript
interface TxCreateDoc<T extends Doc> extends TxCUD<T> {
  attributes: Data<T> // The initial data for the document
}
```

### TxUpdateDoc - Updating Documents

```typescript
interface TxUpdateDoc<T extends Doc> extends TxCUD<T> {
  operations: DocumentUpdate<T> // What to change
}
```

### TxRemoveDoc - Deleting Documents

### TxMixin - Applying Mixins

```typescript
interface TxMixin<D extends Doc, M extends D> extends TxCUD<D> {
  mixin: Ref<Mixin<M>> // Which mixin to apply
  attributes: MixinUpdate<D, M> // Mixin data
}
```

## How Transactions Work

For comprehensive transaction documentation, see the [Model Architecture overview](../overview.md) and related pages.

## Next Steps

- [Mixins](mixins) - Dynamic type extensions
- [Domains](domains) - Storage organization
