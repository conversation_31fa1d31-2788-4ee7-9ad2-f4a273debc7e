# Activity & Notifications

HULY includes a comprehensive activity tracking and notification system that keeps users informed of changes and updates across the platform.

## Overview

The system provides:

- **Activity Tracking** - Track all changes to documents
- **Notifications** - Inform users of relevant changes
- **Collaboration** - Track collaborators on documents
- **Multiple Channels** - In-app, push, email, sound notifications
- **Smart Filtering** - Only notify relevant users
- **Real-time Updates** - Instant notification delivery

## Architecture

```
┌─────────────────┐
│  Transaction    │
│  (TxCUD)        │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Activity       │
│  Trigger        │ (ActivityMessagesHandler)
└────────┬────────┘
         │
         ├──────────────────┐
         │                  │
         ▼                  ▼
┌─────────────────┐  ┌─────────────────┐
│  Activity       │  │  Notification   │
│  Message        │  │  Context        │
└─────────────────┘  └────────┬────────┘
                              │
                              ▼
                     ┌─────────────────┐
                     │  Inbox          │
                     │  Notification   │
                     └────────┬────────┘
                              │
                    ┌─────────┴─────────┐
                    │                   │
                    ▼                   ▼
           ┌─────────────────┐  ┌─────────────────┐
           │  Push           │  │  Browser        │
           │  Notification   │  │  Notification   │
           └─────────────────┘  └─────────────────┘
```

## Activity Messages

Activity messages track changes to documents:

```typescript
interface ActivityMessage extends Doc {
  attachedTo: Ref<Doc> // Which document
  attachedToClass: Ref<Class<Doc>> // Document class
  createdBy: PersonId // Who made the change
  createdOn: Timestamp // When
  repliedPersons?: PersonId[] // Who replied
}
```

### Doc Update Messages

Track document updates:

```typescript
interface DocUpdateMessage extends ActivityMessage {
  objectId: Ref<Doc> // Same as attachedTo
  objectClass: Ref<Class<Doc>> // Document class
  action: 'create' | 'update' | 'remove'
  attributeUpdates?: AttributeUpdate[] // What changed
  txId: Ref<TxCUD<Doc>> // Original transaction
}

interface AttributeUpdate {
  attrKey: string // Attribute name
  attrClass: Ref<Class<Doc>> // Attribute class
  set?: any[] // New values
  removed?: any[] // Removed values
  added?: any[] // Added values
  prevValue?: any // Previous value
  isMixin?: boolean // Is this a mixin attribute?
}
```

**Example:**

```typescript
// User updates issue status
await client.update(issue._id, {
  status: 'in-progress'
})

// Generates DocUpdateMessage:
{
  _class: 'activity:class:DocUpdateMessage',
  attachedTo: issue._id,
  objectId: issue._id,
  objectClass: 'tracker:class:Issue',
  action: 'update',
  attributeUpdates: [{
    attrKey: 'status',
    prevValue: 'todo',
    set: ['in-progress']
  }],
  createdBy: currentUser,
  createdOn: Date.now()
}
```

## Generating Activity

Activity is generated automatically by triggers:

```typescript
// Server trigger
async function ActivityMessagesHandler(txes: TxCUD<Doc>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    // Generate activity messages
    const activityTxes = await generateDocUpdateMessages(ctx, tx, control)

    result.push(...activityTxes)
  }

  // Apply activity messages
  await control.apply(ctx, result)

  return []
}
```

### Activity Generation Rules

Activity is generated for:

- ✅ Document creation
- ✅ Document updates (with changed attributes)
- ✅ Document removal
- ✅ Collection updates (added/removed items)
- ❌ System documents (transactions, configurations)
- ❌ Activity messages themselves (prevent recursion)

## Notifications

### Notification Types

```typescript
enum NotificationType {
  // Defined per feature
  IssueCreated = 'tracker:ids:IssueCreated',
  IssueUpdated = 'tracker:ids:IssueUpdated',
  IssueCommented = 'tracker:ids:IssueCommented',
  MessagePosted = 'chunter:ids:MessagePosted',
  DocumentShared = 'document:ids:DocumentShared'
}
```

**Defining a notification type:**

```typescript
builder.createDoc(
  notification.class.NotificationType,
  core.space.Model,
  {
    hidden: false,
    generated: false,
    label: tracker.string.IssueAssigned,
    group: tracker.ids.TrackerNotificationGroup,
    txClasses: [core.class.TxUpdateDoc],
    objectClass: tracker.class.Issue,
    defaultEnabled: true,
    templates: {
      textTemplate: '{sender} assigned {doc} to you',
      htmlTemplate: '<p><b>{sender}</b> assigned {doc} to you</p>',
      subjectTemplate: 'Issue assigned: {doc}'
    }
  },
  tracker.ids.IssueAssignedNotification
)
```

### Notification Context

Tracks notification state per user per document:

```typescript
interface DocNotifyContext extends Doc {
  user: AccountUuid // Which user
  objectId: Ref<Doc> // Which document
  objectClass: Ref<Class<Doc>> // Document class
  objectSpace: Ref<Space> // Document space
  isPinned: boolean // Pinned in inbox
  hidden: boolean // Hidden from inbox
  lastUpdateTimestamp?: Timestamp // Last update
}
```

**Purpose:**

- Track which documents user is following
- Store notification preferences per document
- Group related notifications

### Inbox Notifications

The actual notifications shown to users:

```typescript
interface InboxNotification extends Doc {
  user: AccountUuid // Recipient
  objectId: Ref<Doc> // Related document
  objectClass: Ref<Class<Doc>> // Document class
  docNotifyContext: Ref<DocNotifyContext> // Context
  header: IntlString // Notification title
  message: IntlString // Notification body
  intlParams: any // Template parameters
  isViewed: boolean // Has user seen it?
  archived: boolean // Archived?
  createdOn: Timestamp // When created
}
```

**Example:**

```typescript
{
  _class: 'notification:class:CommonInboxNotification',
  user: 'user-123',
  objectId: 'issue-456',
  objectClass: 'tracker:class:Issue',
  header: tracker.string.IssueAssigned,
  message: tracker.string.IssueAssignedBody,
  intlParams: {
    sender: 'John Doe',
    doc: 'Fix login bug',
    assignee: 'You'
  },
  isViewed: false,
  archived: false,
  createdOn: Date.now()
}
```

## Notification Flow

### 1. Transaction Occurs

```typescript
// User assigns issue
await client.update(issue._id, {
  assignee: newAssignee
})
```

### 2. Activity Trigger Fires

```typescript
// Generate activity message
const activityMsg = createDocUpdateMessage(tx)

// Identify collaborators
const collaborators = await getCollaborators(issue)

// Create notification contexts
for (const user of collaborators) {
  await createNotifyContext(user, issue)
}
```

### 3. Create Notifications

```typescript
// Create inbox notification
await createInboxNotification({
  user: newAssignee,
  objectId: issue._id,
  type: tracker.ids.IssueAssignedNotification,
  header: tracker.string.IssueAssigned,
  message: tracker.string.IssueAssignedBody,
  intlParams: {
    sender: currentUser.name,
    doc: issue.title
  }
})
```

### 4. Deliver Notifications

```typescript
// Send via enabled channels
for (const provider of enabledProviders) {
  switch (provider) {
    case 'inbox':
      // Already created above
      break
    case 'push':
      await sendPushNotification(user, notification)
      break
    case 'email':
      await sendEmailNotification(user, notification)
      break
    case 'sound':
      await playSoundNotification(user, notification)
      break
  }
}
```

## Collaborators

Collaborators are users who should be notified about changes:

```typescript
interface ClassCollaborators extends Doc {
  attachedTo: Ref<Class<Doc>> // Which class
  fields: string[] // Which fields contain collaborators
}
```

**Example:**

```typescript
// Issues track collaborators from these fields:
builder.createDoc<ClassCollaborators<Issue>>(core.class.ClassCollaborators, core.space.Model, {
  attachedTo: tracker.class.Issue,
  fields: [
    'createdBy', // Issue creator
    'assignee', // Assigned person
    'repliedPersons' // People who commented
  ]
})
```

**Finding Collaborators:**

```typescript
async function getCollaborators(doc: Doc, control: TriggerControl): Promise<AccountUuid[]> {
  const collaboratorDef = await control.findOne(core.class.ClassCollaborators, { attachedTo: doc._class })

  const collaborators = new Set<AccountUuid>()

  for (const field of collaboratorDef.fields) {
    const value = doc[field]
    if (Array.isArray(value)) {
      value.forEach((v) => collaborators.add(v))
    } else if (value) {
      collaborators.add(value)
    }
  }

  return Array.from(collaborators)
}
```

## Notification Providers

### Built-in Providers

```typescript
enum NotificationProvider {
  Inbox = 'notification:provider:Inbox',
  Push = 'notification:provider:Push',
  Email = 'notification:provider:Email',
  Sound = 'notification:provider:Sound'
}
```

### Provider Defaults

Configure which notifications are enabled by default:

```typescript
builder.createDoc(notification.class.NotificationProviderDefaults, core.space.Model, {
  provider: notification.providers.InboxNotificationProvider,
  ignoredTypes: [],
  enabledTypes: [tracker.ids.IssueAssignedNotification, tracker.ids.IssueCommentedNotification]
})
```

### User Preferences

Users can customize notification settings:

```typescript
interface NotificationSetting extends Preference {
  type: Ref<NotificationType> // Which notification type
  enabled: boolean // Is it enabled?
  providers: Ref<NotificationProvider>[] // Which providers
}
```

## Notification Templates

Templates define notification content:

```typescript
interface NotificationTemplate {
  textTemplate: string // Plain text: '{sender} commented on {doc}'
  htmlTemplate: string // HTML: '<b>{sender}</b> commented on {doc}'
  subjectTemplate: string // Subject: 'New comment on {doc}'
}
```

**Template Variables:**

- `{sender}` - Person who triggered the notification
- `{doc}` - Document title/name
- `{assignee}` - Assigned person name
- Custom variables from `intlParams`

**Example:**

```typescript
const notification = {
  textTemplate: '{sender} assigned {doc} to {assignee}',
  intlParams: {
    sender: 'John Doe',
    doc: 'Fix login bug',
    assignee: 'Jane Smith'
  }
}

// Rendered: "John Doe assigned Fix login bug to Jane Smith"
```

## Real-time Delivery

Notifications are delivered in real-time via WebSocket:

```typescript
// Server sends notification transaction
connection.send({
  method: '#tx',
  params: [
    {
      _class: 'core:class:TxCreateDoc',
      objectClass: 'notification:class:InboxNotification',
      attributes: notificationData
    }
  ]
})

// Client receives and displays
client.subscribe((tx) => {
  if (isNotification(tx)) {
    showNotificationPopup(tx)
    updateInboxCount()
  }
})
```

## Notification Actions

Users can interact with notifications:

### Mark as Read

```typescript
await client.update(notification._id, {
  isViewed: true
})
```

### Archive

```typescript
await client.update(notification._id, {
  archived: true
})
```

### Pin

```typescript
await client.update(context._id, {
  isPinned: true
})
```

### Mute

```typescript
await client.update(context._id, {
  hidden: true
})
```

## Activity Presentation

Activity messages are displayed in document timelines:

```svelte
<script>
  import { getClient } from '@hcengineering/presentation'

  export let doc: Doc

  const client = getClient()

  // Query activity for this document
  $: activity = client.findAll(
    activity.class.DocUpdateMessage,
    { attachedTo: doc._id },
    { sort: { createdOn: -1 } }
  )
</script>

{#each $activity as msg}
  <ActivityMessage message={msg} />
{/each}
```

## Best Practices

### 1. Don't Over-Notify

```typescript
// ✅ Good - only notify on important changes
if (isImportantChange(tx)) {
  await createNotification(...)
}

// ❌ Bad - notify on every change
await createNotification(...)
```

### 2. Batch Related Notifications

```typescript
// ✅ Good - group related changes
{
  header: 'Multiple updates to {doc}',
  message: '{sender} made {count} changes',
  intlParams: { count: 5 }
}

// ❌ Bad - 5 separate notifications
```

### 3. Provide Context

```typescript
// ✅ Good - include relevant details
{
  message: '{sender} changed status from {oldStatus} to {newStatus}',
  intlParams: {
    oldStatus: 'todo',
    newStatus: 'in-progress'
  }
}

// ❌ Bad - vague message
{
  message: '{sender} updated {doc}'
}
```

### 4. Respect User Preferences

```typescript
// Check if user wants this notification
const setting = await getUserNotificationSetting(user, notificationType)

if (!setting.enabled) {
  return // Don't send
}
```

## Summary

HULY's activity and notification system provides:

- ✅ Automatic activity tracking
- ✅ Smart collaboration detection
- ✅ Multi-channel notifications
- ✅ User preferences
- ✅ Real-time delivery
- ✅ Template-based messages
- ✅ Context tracking

The system keeps users informed while avoiding notification fatigue!
