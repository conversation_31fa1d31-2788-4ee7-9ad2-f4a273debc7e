# Services & Pods Architecture

HULY uses a microservices architecture with specialized services ("pods") for different features. This guide explains how services work and what pods are.

## Overview

HULY architecture includes:

- **Core Server** - Main transaction processing (TypeScript)
- **Pods** - Specialized services for specific features (TypeScript)
- **Microservices** - Independent services (Go)
- **Communication** - Service-to-service via HTTP/gRPC

## What are Pods?

**Pods** are specialized TypeScript services that handle specific functionality:

```
Core HULY Platform
├── Server (pods/server)          # Main transactor
├── Front (pods/front)            # Frontend server
├── Account (pods/account)        # Account management
├── Collaborator (pods/collaborator)  # Real-time editing
├── Workspace (pods/workspace)    # Workspace operations
└── ... more pods
```

### Pods vs Microservices

**Pods** (TypeScript):

- Part of main HULY codebase
- Share common libraries
- Built with Rush
- Use HULY's type system

**Microservices** (Go):

- Independent services
- Own repositories
- Built separately
- Communicate via APIs

## Pod Architecture

### Typical Pod Structure

```
pods/my-pod/
├── src/
│   ├── index.ts           # Entry point
│   └── server.ts          # Express server
├── config/
│   └── rig.json           # Rush configuration
├── Dockerfile             # Container image
├── package.json           # Dependencies
├── tsconfig.json          # TypeScript config
└── lib/                   # Compiled output
```

### Pod Types

**1. Server Pod** (`pods/server`)

- Main transaction processor
- WebSocket connections
- Transaction validation
- Real-time broadcasting

**2. Front Pod** (`pods/front`)

- Serves static frontend
- File upload/download
- API gateway
- Routing

**3. Account Pod** (`pods/account`)

- User authentication
- Account management
- Workspace membership
- OAuth providers

**4. Collaborator Pod** (`pods/collaborator`)

- Real-time document editing
- Yjs/Hocuspocus server
- Collaborative synchronization
- Cursor tracking

**5. Workspace Pod** (`pods/workspace`)

- Workspace creation
- Workspace upgrades
- Model migrations
- Backup/restore

## Services Architecture

### Available Services

Located in `services/` directory:

**AI Services:**

- `ai-bot` - AI chat assistant
- `love-agent` - AI agent for video calls

**Integration Services:**

- `github` - GitHub integration
- `gmail` - Gmail integration
- `telegram` - Telegram integration
- `calendar` - Calendar integration

**Processing Services:**

- `rekoni` - Document processing (PDF, Word, Excel)
- `preview` - Document preview generation
- `print` - PDF generation
- `sign` - Document signing

**Data Services:**

- `datalake` - Data lake storage
- `analytics-collector` - Analytics collection
- `backup` - Backup service
- `export` - Data export

**Communication Services:**

- `notification` - Push notifications
- `mail` - Email sending
- `telegram-bot` - Telegram bot

**Utility Services:**

- `process` - Process automation
- `worker` - Background job processing
- `stats` - Statistics aggregation

### Service Communication

Services communicate via:

**1. HTTP REST APIs**

```typescript
// Service A calls Service B
const response = await fetch(`${SERVICE_B_URL}/api/v1/endpoint`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`
  },
  body: JSON.stringify(data)
})

const result = await response.json()
```

**2. Platform Client**

```typescript
// Service connects to main platform
import { createClient } from '@hcengineering/server-client'

const client = await createClient(TRANSACTOR_URL, serviceToken)

// Use HULY client API
const issues = await client.findAll(tracker.class.Issue, {})
```

**3. Event Queues**

```typescript
// Publish event
await queue.publish({
  type: 'document.processed',
  workspace: workspaceId,
  documentId: doc._id,
  status: 'complete'
})

// Subscribe to events
queue.subscribe('document.processed', async (event) => {
  console.log('Document processed:', event.documentId)
  // Handle event...
})
```

## Service Examples

### AI Bot Service

```
services/ai-bot/pod-ai-bot/
├── src/
│   ├── start.ts              # Entry point
│   ├── server/
│   │   └── server.ts         # Express server
│   ├── controller.ts         # AI control logic
│   ├── workspace/
│   │   └── workspaceClient.ts  # HULY client wrapper
│   ├── utils/
│   │   ├── openai.ts         # OpenAI integration
│   │   └── account.ts        # Account utilities
│   └── storage.ts            # History storage
├── config/
│   └── rig.json
├── Dockerfile
└── package.json
```

**Features:**

- Connects to OpenAI API
- Maintains conversation history
- Executes tools (search, create tasks, etc.)
- Posts responses back to HULY

### GitHub Integration Service

```
services/github/pod-github/
├── src/
│   ├── start.ts              # Entry point
│   ├── server.ts             # HTTP server
│   ├── sync/
│   │   ├── issues.ts         # Sync GitHub issues
│   │   ├── pullRequests.ts   # Sync PRs
│   │   └── commits.ts        # Sync commits
│   ├── webhooks/
│   │   └── handler.ts        # GitHub webhook handler
│   └── client.ts             # GitHub API client
├── Dockerfile
└── package.json
```

**Features:**

- Syncs GitHub issues to HULY
- Handles GitHub webhooks
- Two-way synchronization
- OAuth authentication

### Rekoni Service (Document Processing)

```
services/rekoni/
├── src/
│   ├── index.ts              # Entry point
│   ├── server.ts             # HTTP server
│   ├── processors/
│   │   ├── pdf.ts            # PDF processing
│   │   ├── word.ts           # Word documents
│   │   ├── excel.ts          # Excel spreadsheets
│   │   └── image.ts          # Image processing
│   └── utils/
│       └── extract.ts        # Text extraction
├── Dockerfile
└── package.json
```

**Features:**

- Extracts text from PDFs
- Processes Word/Excel files
- Generates thumbnails
- Extracts metadata

## Creating a New Service

### 1. Create Service Directory

```bash
mkdir -p services/my-service/pod-my-service
cd services/my-service/pod-my-service
```

### 2. Initialize Package

```bash
# Create package.json
rushx init

# Install dependencies
rush update
```

### 3. Implement Service

```typescript
// src/start.ts
import express from 'express'
import { createClient } from '@hcengineering/server-client'

const app = express()
app.use(express.json())

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok' })
})

// API endpoint
app.post('/api/v1/process', async (req, res) => {
  try {
    const result = await processData(req.body)
    res.json({ success: true, result })
  } catch (err) {
    res.status(500).json({ success: false, error: err.message })
  }
})

// Start server
const PORT = process.env.PORT || 4000
app.listen(PORT, () => {
  console.log(`Service running on port ${PORT}`)
})
```

### 4. Add Dockerfile

```dockerfile
FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci --production

# Copy source
COPY lib/ ./lib/

# Expose port
EXPOSE 4000

# Start service
CMD ["node", "lib/index.js"]
```

### 5. Configure in docker-compose

```yaml
# dev/docker-compose.yml
services:
  my-service:
    build:
      context: ../services/my-service/pod-my-service
      dockerfile: Dockerfile
    ports:
      - '4000:4000'
    environment:
      - TRANSACTOR_URL=ws://server:3333
      - ACCOUNTS_URL=http://account:3000
      - PORT=4000
    depends_on:
      - server
      - postgres
```

## Go Microservices

### Microservice Structure

```
go/go-microservices/webhook-microservice/
├── cmd/
│   └── server/
│       └── main.go           # Entry point
├── internal/
│   ├── domain/               # Domain models
│   ├── app/                  # Application logic
│   ├── infra/                # Infrastructure (DB, etc.)
│   └── api/                  # HTTP handlers
├── configs/
│   ├── config.yaml           # Configuration
│   └── dapr/                 # Dapr config
├── migrations/               # SQL migrations
├── Dockerfile
└── go.mod
```

### Clean Architecture Layers

**1. Domain Layer** (`internal/domain/`)

```go
// Domain entities
package domain

type Webhook struct {
    ID        string
    URL       string
    Events    []string
    Secret    string
    Active    bool
    CreatedAt time.Time
}

// Domain interfaces
type WebhookRepository interface {
    Create(ctx context.Context, webhook *Webhook) error
    FindByID(ctx context.Context, id string) (*Webhook, error)
    Update(ctx context.Context, webhook *Webhook) error
}
```

**2. Application Layer** (`internal/app/`)

```go
// Use cases
package app

type WebhookService struct {
    repo domain.WebhookRepository
}

func (s *WebhookService) CreateWebhook(
    ctx context.Context,
    input CreateWebhookInput
) (*Webhook, error) {
    // Validation
    if err := validateWebhookInput(input); err != nil {
        return nil, err
    }

    // Business logic
    webhook := &domain.Webhook{
        ID: generateID(),
        URL: input.URL,
        Events: input.Events,
        Secret: generateSecret(),
        Active: true,
        CreatedAt: time.Now(),
    }

    // Persist
    if err := s.repo.Create(ctx, webhook); err != nil {
        return nil, err
    }

    return webhook, nil
}
```

**3. Infrastructure Layer** (`internal/infra/`)

```go
// Database implementation
package infra

type PostgresWebhookRepository struct {
    db *sql.DB
}

func (r *PostgresWebhookRepository) Create(
    ctx context.Context,
    webhook *domain.Webhook
) error {
    query := `
        INSERT INTO webhooks (id, url, events, secret, active, created_at)
        VALUES ($1, $2, $3, $4, $5, $6)
    `

    _, err := r.db.ExecContext(
        ctx,
        query,
        webhook.ID,
        webhook.URL,
        pq.Array(webhook.Events),
        webhook.Secret,
        webhook.Active,
        webhook.Created At
    )

    return err
}
```

**4. API Layer** (`internal/api/`)

```go
// HTTP handlers
package api

func CreateWebhookHandler(service *app.WebhookService) gin.HandlerFunc {
    return func(c *gin.Context) {
        var input CreateWebhookInput

        if err := c.ShouldBindJSON(&input); err != nil {
            c.JSON(400, gin.H{"error": err.Error()})
            return
        }

        webhook, err := service.CreateWebhook(c.Request.Context(), input)
        if err != nil {
            c.JSON(500, gin.H{"error": err.Error()})
            return
        }

        c.JSON(201, webhook)
    }
}
```

## Dapr Integration

Go microservices use Dapr for service mesh capabilities:

### State Management

```go
import "github.com/dapr/go-sdk/client"

// Save state
func SaveState(ctx context.Context, key string, value interface{}) error {
    client, err := dapr.NewClient()
    if err != nil {
        return err
    }
    defer client.Close()

    return client.SaveState(ctx, "statestore", key, value, nil)
}

// Get state
func GetState(ctx context.Context, key string) ([]byte, error) {
    client, err := dapr.NewClient()
    if err != nil {
        return nil, err
    }
    defer client.Close()

    item, err := client.GetState(ctx, "statestore", key, nil)
    if err != nil {
        return nil, err
    }

    return item.Value, nil
}
```

### Pub/Sub

```go
// Subscribe to events
func Subscribe(topic string, handler func(event Event)) error {
    s := daprd.NewService(":6000")

    sub := &common.Subscription{
        PubsubName: "pubsub",
        Topic:      topic,
        Route:      "/events",
    }

    err := s.AddTopicEventHandler(sub, func(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
        var event Event
        json.Unmarshal(e.RawData, &event)
        handler(event)
        return false, nil
    })

    if err != nil {
        return err
    }

    return s.Start()
}

// Publish event
func Publish(ctx context.Context, topic string, data interface{}) error {
    client, err := dapr.NewClient()
    if err != nil {
        return err
    }
    defer client.Close()

    return client.PublishEvent(ctx, "pubsub", topic, data)
}
```

### Service Invocation

```go
// Call another service via Dapr
func CallService(ctx context.Context, serviceID string, method string, data interface{}) ([]byte, error) {
    client, err := dapr.NewClient()
    if err != nil {
        return nil, err
    }
    defer client.Close()

    content := &dapr.DataContent{
        ContentType: "application/json",
        Data:        data,
    }

    resp, err := client.InvokeMethodWithContent(
        ctx,
        serviceID,
        method,
        "post",
        content
    )

    if err != nil {
        return nil, err
    }

    return resp, nil
}
```

## Common Pods

### Server Pod (`pods/server`)

**Purpose**: Main transaction processing

**Responsibilities:**

- WebSocket connections from clients
- Transaction validation
- Permission checking
- Real-time broadcasting
- Database operations

**Key files:**

```typescript
// src/server.ts
export function startServer(ctx: MeasureContext, config: ServerConfig): () => Promise<void> {
  const wss = new WebSocket.Server({ port: 3333 })

  wss.on('connection', (ws, request) => {
    handleConnection(ws, request)
  })

  return async () => {
    wss.close()
  }
}
```

### Front Pod (`pods/front`)

**Purpose**: Frontend serving and file handling

**Responsibilities:**

- Serve static frontend assets
- File upload endpoint
- File download endpoint
- Image resizing
- API routing

**Key endpoints:**

```typescript
// File upload
app.post('/files', async (req, res) => {
  const file = req.files?.file
  const { uuid, etag } = await storageUpload(ctx, storageAdapter, workspaceId, file)
  res.json({ id: uuid, metadata: { etag, size: file.size } })
})

// File download
app.get('/files/:id', async (req, res) => {
  const blob = await storageAdapter.get(ctx, workspaceId, req.params.id)
  blob.pipe(res)
})
```

### Account Pod (`pods/account`)

**Purpose**: Authentication and account management

**Responsibilities:**

- Login/logout
- User registration
- OAuth authentication
- Workspace membership
- Token generation

**Key operations:**

```typescript
// Login
app.post('/login', async (req, res) => {
  const { email, password } = req.body
  const result = await login(ctx, db, email, password)
  res.json({ token: result.token })
})

// OAuth callback
app.get('/auth/github/callback', async (req, res) => {
  const { code } = req.query
  const token = await handleGitHubAuth(code)
  res.redirect(`${FRONT_URL}?token=${token}`)
})
```

### Collaborator Pod (`pods/collaborator`)

**Purpose**: Real-time document collaboration

**Responsibilities:**

- Yjs document synchronization
- Cursor tracking
- Conflict resolution
- Document locking
- Persistence to storage

**Architecture:**

```typescript
import { Server } from '@hocuspocus/server'

const server = Server.configure({
  port: 3002,

  async onLoadDocument({ documentName }) {
    // Load document from storage
    const doc = await storage.load(documentName)
    return doc
  },

  async onStoreDocument({ documentName, state }) {
    // Save document to storage
    await storage.save(documentName, state)
  },

  async onChange({ documentName, context }) {
    // Track collaborators
    await trackCollaborators(documentName, context.connectionId)
  }
})

server.listen()
```

### Workspace Pod (`pods/workspace`)

**Purpose**: Workspace operations

**Responsibilities:**

- Create workspaces
- Upgrade model
- Run migrations
- Backup/restore
- Import/export

**CLI tool:**

```bash
# Create workspace
node lib/index.js create-workspace \
  --name "My Workspace" \
  --email <EMAIL>

# Upgrade workspace
node lib/index.js upgrade \
  --workspace ws-123

# Backup workspace
node lib/index.js backup \
  --workspace ws-123 \
  --output backup.tar.gz
```

## Service Development

### Creating a TypeScript Service

```typescript
// services/my-service/pod-my-service/src/start.ts

import express from 'express'
import { createClient } from '@hcengineering/server-client'

interface Config {
  port: number
  transactorUrl: string
  accountsUrl: string
}

export async function start(config: Config): Promise<void> {
  const app = express()
  app.use(express.json())

  // Connect to HULY
  const token = generateServiceToken()
  const client = await createClient(config.transactorUrl, token)

  // API endpoints
  app.post('/api/v1/process', async (req, res) => {
    try {
      const result = await processData(client, req.body)
      res.json({ success: true, result })
    } catch (err) {
      res.status(500).json({ success: false, error: err.message })
    }
  })

  // Health check
  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      service: 'my-service',
      timestamp: Date.now()
    })
  })

  // Start server
  app.listen(config.port, () => {
    console.log(`Service running on port ${config.port}`)
  })
}

// Entry point
const config: Config = {
  port: Number(process.env.PORT) || 4000,
  transactorUrl: process.env.TRANSACTOR_URL || 'ws://localhost:3333',
  accountsUrl: process.env.ACCOUNTS_URL || 'http://localhost:3000'
}

start(config).catch((err) => {
  console.error('Failed to start service:', err)
  process.exit(1)
})
```

### Creating a Go Microservice

Use the template:

```bash
cd go/go-microservices
./create-microservice.sh my-microservice
```

This creates a complete microservice with:

- Clean architecture structure
- ClickHouse, MongoDB, Redis integration
- Dapr configuration
- Health checks
- Prometheus metrics
- OpenTelemetry tracing

## Deployment

### Docker Compose (Development)

```yaml
# dev/docker-compose.yml
services:
  server:
    build: ../pods/server
    ports:
      - '3333:3333'
    environment:
      - DB_URL=*******************************************/huly
    depends_on:
      - postgres

  account:
    build: ../pods/account
    ports:
      - '3000:3000'
    environment:
      - DB_URL=*******************************************/huly
    depends_on:
      - postgres

  ai-bot:
    build: ../services/ai-bot/pod-ai-bot
    ports:
      - '4010:4010'
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - TRANSACTOR_URL=ws://server:3333
    depends_on:
      - server
```

### Kubernetes (Production)

```yaml
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: huly-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: huly-server
  template:
    metadata:
      labels:
        app: huly-server
    spec:
      containers:
        - name: server
          image: huly/server:latest
          ports:
            - containerPort: 3333
          env:
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: huly-secrets
                  key: db-url
          resources:
            limits:
              memory: '8Gi'
              cpu: '4'
            requests:
              memory: '4Gi'
              cpu: '2'

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: huly-server
spec:
  selector:
    app: huly-server
  ports:
    - port: 3333
      targetPort: 3333
  type: ClusterIP
```

## Service Discovery

### Environment Variables

```bash
# Service URLs
export SERVER_URL=http://server:3333
export ACCOUNT_URL=http://account:3000
export AI_BOT_URL=http://ai-bot:4010
export REKONI_URL=http://rekoni:4004
export GITHUB_URL=http://github:4011
```

### Service Registry

```typescript
const services = {
  server: process.env.SERVER_URL,
  account: process.env.ACCOUNT_URL,
  aiBot: process.env.AI_BOT_URL,
  rekoni: process.env.REKONI_URL,
  github: process.env.GITHUB_URL
}

export function getServiceUrl(serviceName: keyof typeof services): string {
  const url = services[serviceName]
  if (!url) {
    throw new Error(`Service ${serviceName} not configured`)
  }
  return url
}
```

## Monitoring Services

### Health Checks

```typescript
// Implement health check
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    service: 'my-service',
    timestamp: Date.now(),
    checks: {
      database: await checkDatabase(),
      storage: await checkStorage(),
      external: await checkExternalAPI()
    }
  }

  const allHealthy = Object.values(health.checks).every((c) => c === 'ok')
  res.status(allHealthy ? 200 : 503).json(health)
})
```

### Metrics

```typescript
import { register, Counter, Histogram } from 'prom-client'

// Define metrics
const requestCounter = new Counter({
  name: 'service_requests_total',
  help: 'Total requests',
  labelNames: ['method', 'endpoint', 'status']
})

const requestDuration = new Histogram({
  name: 'service_request_duration_seconds',
  help: 'Request duration',
  labelNames: ['method', 'endpoint']
})

// Expose metrics
app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType)
  res.end(register.metrics())
})
```

## Best Practices

### ✅ Do's

- Keep services focused (single responsibility)
- Use health checks
- Implement graceful shutdown
- Log structured data
- Monitor metrics
- Handle failures gracefully
- Use service discovery
- Version APIs
- Document endpoints

### ❌ Don'ts

- Don't create monolithic services
- Don't skip health checks
- Don't ignore errors
- Don't hardcode service URLs
- Don't skip authentication
- Don't forget timeouts
- Don't block the event loop

## Summary

HULY's service architecture:

- ✅ **Pods** - TypeScript services for core features
- ✅ **Microservices** - Go services for specific capabilities
- ✅ **Clean Architecture** - Layered design
- ✅ **Service Communication** - HTTP, WebSocket, events
- ✅ **Dapr Integration** - Service mesh capabilities
- ✅ **Scalable** - Deploy independently

Build distributed, scalable services! 🚀
