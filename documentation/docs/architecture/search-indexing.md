# Search & Indexing

HULY uses Elasticsearch/OpenSearch for powerful full-text search capabilities across all documents.

## Overview

The search system provides:

- **Full-Text Search** - Search across all document content
- **Multi-Field Search** - Search multiple fields simultaneously
- **Ranking** - Results sorted by relevance
- **Highlighting** - Show matching text snippets
- **Real-time Indexing** - Documents indexed as they're created/updated
- **Attachment Search** - Search inside PDF, DOC, and other files

## Architecture

```
┌────────────────┐
│  Transaction   │
│  (Create/Update)│
└────────┬───────┘
         │
         ▼
┌────────────────┐
│   Indexer      │ (Watches transactions)
│   Pipeline     │
└────────┬───────┘
         │
         ├─────────────────────┐
         │                     │
         ▼                     ▼
┌────────────────┐    ┌────────────────┐
│  Extract Text  │    │  Extract       │
│  Content       │    │  Attachments   │
└────────┬───────┘    └────────┬───────┘
         │                     │
         └──────────┬──────────┘
                    ▼
         ┌────────────────┐
         │  Elasticsearch │
         │  Index         │
         └────────┬───────┘
                  │
                  ▼
         ┌────────────────┐
         │  Search Query  │
         │  Returns       │
         │  Results       │
         └────────────────┘
```

## Indexable Attributes

Mark fields as searchable using `@Index` decorator:

```typescript
@Model(myFeature.class.Task, core.class.Doc, DOMAIN_MY_FEATURE)
export class TTask extends TDoc implements Task {
  @Prop(TypeString(), myFeature.string.TaskTitle)
  @Index(IndexKind.FullText) // ← Makes this field searchable
  title!: string

  @Prop(TypeString(), myFeature.string.TaskDescription)
  @Index(IndexKind.FullText) // ← Also searchable
  description!: string

  @Prop(TypeString(), myFeature.string.InternalNotes)
  notes!: string // ← NOT searchable (no @Index)
}
```

**Index Types:**

- `IndexKind.FullText` - Full-text search with stemming and scoring
- `IndexKind.Indexed` - Exact match (for filters, not search)

## How Indexing Works

### Automatic Indexing

Documents are automatically indexed when created or updated:

```
User Action → Transaction → Indexer Pipeline → Extract Content → Elasticsearch
```

**Example flow:**

```typescript
// 1. User creates task
await client.createDoc(myFeature.class.Task, space, {
  title: 'Fix login bug',
  description: 'Users cannot log in with Google OAuth'
})

// 2. Transaction broadcast to indexer
// 3. Indexer extracts searchable content:
{
  id: 'task-123',
  _class: 'my-feature:class:Task',
  space: 'project-1',
  fulltextSummary: 'Fix login bug Users cannot log in with Google OAuth',
  title: 'Fix login bug',
  description: 'Users cannot log in with Google OAuth',
  workspaceId: 'ws-123'
}

// 4. Indexed in Elasticsearch
// 5. Now searchable!
```

### Batch Indexing

For performance, documents are indexed in batches:

```typescript
class ElasticPushQueue {
  indexedDocs: IndexedDoc[] = []

  async push(doc: IndexedDoc): Promise<void> {
    this.indexedDocs.push(doc)

    // Push when batch reaches 25 docs
    if (this.indexedDocs.length > 25) {
      await this.pushToIndex()
    }
  }

  async pushToIndex(): Promise<void> {
    const docs = [...this.indexedDocs]
    this.indexedDocs = []

    // Bulk index to Elasticsearch
    await this.fulltextAdapter.updateMany(ctx, workspaceId, docs)
  }
}
```

## Searching

### Basic Search

```typescript
const results = await client.searchFulltext({
  query: 'login bug',
  classes: [myFeature.class.Task],
  options: {
    limit: 20
  }
})

// Results include docs and scores
for (const result of results.docs) {
  console.log('Title:', result.doc.title)
  console.log('Score:', result.score) // Relevance score
}
```

### Multi-Class Search

```typescript
// Search across multiple document types
const results = await client.searchFulltext({
  query: 'authentication',
  classes: [tracker.class.Issue, document.class.Document, chunter.class.Message],
  options: {
    limit: 50
  }
})
```

### Search with Filters

```typescript
// Search + filter by document properties
const results = await client.searchFulltext({
  query: 'bug',
  classes: [tracker.class.Issue],
  filter: {
    status: 'open',
    priority: 'high',
    space: projectId
  },
  options: {
    limit: 20
  }
})
```

### Field-Specific Search

```typescript
// Search only in title field
const results = await client.searchFulltext({
  query: 'login',
  classes: [myFeature.class.Task],
  options: {
    fields: ['title'] // Only search title, not description
  }
})
```

## Search Query Syntax

Elasticsearch supports advanced query syntax:

```typescript
// Phrase search
query: '"exact phrase"'

// AND operator
query: 'login AND bug'

// OR operator
query: 'login OR authentication'

// NOT operator
query: 'bug NOT documentation'

// Wildcard
query: 'auth*' // Matches authentication, authorize, etc.

// Fuzzy search (typo tolerance)
query: 'autentication~' // Finds "authentication"

// Proximity search
query: '"login bug"~3' // Words within 3 words of each other

// Field-specific
query: 'title:login AND description:oauth'
```

## Attachment Search

Files (PDF, DOC, etc.) are also searchable:

```typescript
// 1. Upload file - content is extracted automatically
const attachment = await uploadFile(pdfFile)

// 2. Attach to document
await client.addCollection(attachment.class.Attachment, task.space, task._id, task._class, 'attachments', {
  file: attachment._id,
  name: pdfFile.name
})

// 3. Search finds content inside PDF!
const results = await client.searchFulltext({
  query: 'quarterly report', // Searches PDF content
  classes: [myFeature.class.Task]
})
```

**Supported formats:**

- PDF
- Microsoft Word (.doc, .docx)
- Microsoft Excel (.xls, .xlsx)
- Microsoft PowerPoint (.ppt, .pptx)
- Text files (.txt, .md)
- HTML

## Indexer Pipeline

### How It Works

```typescript
// 1. Watch for transactions
for (const tx of transactions) {
  if (tx._class === core.class.TxCreateDoc) {
    // Index new document
    await indexDocument(doc)
  } else if (tx._class === core.class.TxUpdateDoc) {
    // Update index
    await updateIndex(doc)
  } else if (tx._class === core.class.TxRemoveDoc) {
    // Remove from index
    await removeFromIndex(doc._id)
  }
}

// 2. Extract searchable content
function getContent(doc: Doc): string {
  const attributes = getFullTextIndexableAttributes(doc._class)
  let content = ''

  for (const attr of attributes) {
    if (attr.index === IndexKind.FullText) {
      content += doc[attr.name] + ' '
    }
  }

  return content
}

// 3. Create indexed document
const indexedDoc = {
  id: doc._id,
  _class: doc._class,
  space: doc.space,
  modifiedOn: doc.modifiedOn,
  modifiedBy: doc.modifiedBy,
  fulltextSummary: content, // All searchable text
  // Individual fields for field-specific search
  title: doc.title,
  description: doc.description
}

// 4. Push to Elasticsearch
await elasticsearch.index({
  index: 'workspace-123',
  id: doc._id,
  body: indexedDoc
})
```

## Search Performance

### Optimization Strategies

**1. Limit result count**

```typescript
// Don't load too many results
const results = await client.searchFulltext({
  query: 'bug',
  classes: [tracker.class.Issue],
  options: {
    limit: 20 // Only first 20 results
  }
})
```

**2. Filter before searching**

```typescript
// Combine filter with search (faster)
const results = await client.searchFulltext({
  query: 'authentication',
  classes: [tracker.class.Issue],
  filter: {
    space: currentProject, // Only search current project
    status: { $in: ['open', 'in-progress'] }
  }
})
```

**3. Search specific fields**

```typescript
// Don't search all fields if not needed
const results = await client.searchFulltext({
  query: 'login',
  classes: [tracker.class.Issue],
  options: {
    fields: ['title'] // Only search title (faster)
  }
})
```

## Re-indexing

### Full Re-index

Re-index entire workspace (slow, for maintenance):

```bash
# Using workspace tool
rushx workspace-tool reindex \
  --workspace <workspace-id> \
  --classes "tracker:class:Issue,document:class:Document"
```

### Partial Re-index

Re-index specific document types:

```typescript
// Server-side re-index
async function reindexClass(_class: Ref<Class<Doc>>): Promise<void> {
  const docs = await storage.findAll(_class, {})

  for (const doc of docs) {
    await fulltextAdapter.index(ctx, workspaceId, createIndexedDoc(doc))
  }
}
```

## Search UI Components

### Search Input

```svelte
<script lang="ts">
  import { SearchEdit } from '@hcengineering/ui'
  import { getClient } from '@hcengineering/presentation'

  let query: string = ''
  let results: SearchResult[] = []

  const client = getClient()

  async function search() {
    if (!query) {
      results = []
      return
    }

    const searchResult = await client.searchFulltext({
      query,
      classes: [myFeature.class.Task],
      options: { limit: 10 }
    })

    results = searchResult.docs
  }

  // Debounce search
  $: {
    const timer = setTimeout(search, 300)
    return () => clearTimeout(timer)
  }
</script>

<SearchEdit bind:value={query} placeholder="Search tasks..." />

<div class="results">
  {#each results as result}
    <div class="result">
      <TaskPresenter value={result.doc} />
      <span class="score">Score: {result.score.toFixed(2)}</span>
    </div>
  {/each}
</div>
```

### Search with Highlighting

```typescript
// Get search highlights
const results = await client.searchFulltext({
  query: 'authentication bug',
  classes: [tracker.class.Issue],
  options: {
    highlight: true // Enable highlighting
  }
})

// results include highlight info
for (const result of results.docs) {
  console.log('Match:', result.highlight)
  // "Users cannot <mark>authenticate</mark> - <mark>bug</mark> in OAuth"
}
```

## Advanced Search Features

### Boosting

Give more weight to specific fields:

```typescript
// Title matches score higher than description matches
const results = await client.searchFulltext({
  query: 'login',
  classes: [tracker.class.Issue],
  options: {
    scoring: [
      { key: 'title', boost: 3.0 },
      { key: 'description', boost: 1.0 }
    ]
  }
})
```

### Fuzzy Matching

Handle typos automatically:

```typescript
// Finds "authentication" even with typo
const results = await client.searchFulltext({
  query: 'autentication', // Typo
  classes: [tracker.class.Issue],
  options: {
    fuzzy: true // Enable fuzzy matching
  }
})
```

### Aggregations

Get statistics about search results:

```typescript
const results = await client.searchFulltext({
  query: 'bug',
  classes: [tracker.class.Issue],
  options: {
    aggregations: {
      by_status: {
        terms: { field: 'status' }
      },
      by_priority: {
        terms: { field: 'priority' }
      }
    }
  }
})

// Result includes aggregations
console.log('By status:', results.aggregations.by_status)
// { "open": 45, "in-progress": 12, "done": 103 }
```

## Elasticsearch Configuration

### Index Settings

Elasticsearch index is configured per workspace:

```json
{
  "settings": {
    "number_of_shards": 1,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "default": {
          "type": "standard",
          "stopwords": "_english_"
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "workspaceId": { "type": "keyword" },
      "_class": { "type": "keyword" },
      "space": { "type": "keyword" },
      "fulltextSummary": {
        "type": "text",
        "analyzer": "standard"
      },
      "title": {
        "type": "text",
        "analyzer": "standard"
      },
      "description": {
        "type": "text",
        "analyzer": "standard"
      },
      "data": {
        "type": "text",
        "analyzer": "standard"
      },
      "modifiedOn": { "type": "date" }
    }
  }
}
```

### Attachment Pipeline

Process attachments to extract text:

```json
{
  "description": "Extract attachment content",
  "processors": [
    {
      "attachment": {
        "field": "data",
        "target_field": "attachment",
        "indexed_chars": -1
      }
    }
  ]
}
```

## Search Best Practices

### ✅ Do's

- Mark important fields as `@Index(IndexKind.FullText)`
- Use filters to narrow search scope
- Limit results to reasonable count (10-50)
- Use field-specific search when possible
- Enable fuzzy matching for user input
- Cache common searches
- Show loading state during search

### ❌ Don'ts

- Don't search all fields if not needed
- Don't index sensitive data
- Don't search without limits
- Don't ignore search errors
- Don't forget to debounce search input
- Don't index huge text fields (>1MB)

## Troubleshooting

### Search returns no results

**Check:**

1. Is field marked with `@Index(IndexKind.FullText)`?
2. Is document indexed? Check Elasticsearch:
   ```bash
   curl http://localhost:9200/workspace-123/_search?q=_id:doc-123
   ```
3. Is indexer running?
4. Check indexer logs for errors

### Slow search

**Optimize:**

1. Add filters to narrow scope
2. Reduce limit
3. Search specific fields
4. Add more Elasticsearch nodes
5. Increase Elasticsearch memory

### Indexer falling behind

**Solutions:**

1. Increase indexer workers
2. Increase batch size
3. Add more Elasticsearch capacity
4. Reduce indexed content size

## Monitoring

### Check Index Size

```bash
# Get index stats
curl http://localhost:9200/workspace-123/_stats

# Check document count
curl http://localhost:9200/workspace-123/_count
```

### Monitor Indexing Performance

```typescript
// Server logs show indexing metrics
ctx.info('Indexed documents', {
  count: 25,
  time: 150, // milliseconds
  workspace: workspaceId
})
```

## Summary

HULY's search system provides:

- ✅ Full-text search with Elasticsearch
- ✅ Real-time indexing
- ✅ Attachment content search
- ✅ Multi-field search
- ✅ Relevance ranking
- ✅ Fuzzy matching
- ✅ Aggregations
- ✅ Performance optimization

Make your documents discoverable with powerful search! 🔍
