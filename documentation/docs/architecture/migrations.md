# Migration System

HULY includes a robust migration system for evolving the database schema and data over time without breaking existing workspaces.

## Overview

The migration system handles:

- **Schema Evolution** - Add/modify/remove classes and attributes
- **Data Transformation** - Transform existing data to new formats
- **Workspace Upgrades** - Upgrade workspaces to newer model versions
- **Safe Migrations** - Concurrent-safe migrations with locking
- **Rollback Support** - Track migration state for rollbacks

## Migration Types

### 1. Model Migrations

Model migrations modify the data schema (classes, attributes, etc.):

```typescript
export interface Migrations {
  state: string // Migration identifier
  mode?: MigrateMode // When to run (fast, recovery, normal)
  func: (client: MigrationClient, mode: MigrateMode) => Promise<void>
}
```

**Example:**

```typescript
export const trackerMigration: Migrations[] = [
  {
    state: 'add-priority-field',
    func: async (client, mode) => {
      // Add priority attribute to all issues
      await client.update(DOMAIN_TRACKER, { _class: tracker.class.Issue }, { $set: { priority: 'normal' } })
    }
  }
]
```

### 2. Upgrade Operations

Upgrade operations transform data when model changes:

```typescript
export interface UpgradeOperations {
  state: string
  func: (client: MigrationUpgradeClient, mode: MigrateMode) => Promise<void>
}
```

**Example:**

```typescript
export const trackerUpgrade: UpgradeOperations[] = [
  {
    state: 'migrate-status-to-ref',
    func: async (client, mode) => {
      // Convert string status to Status reference
      const issues = await client.traverse(DOMAIN_TRACKER, { _class: tracker.class.Issue })

      for (const issue of issues) {
        if (typeof issue.status === 'string') {
          const statusRef = await findStatus(issue.status)
          await client.update(DOMAIN_TRACKER, { _id: issue._id }, { status: statusRef })
        }
      }
    }
  }
]
```

### 3. Database Migrations

Low-level database schema migrations (PostgreSQL, MongoDB, etc.):

```typescript
async migrate(name: string, ddl: string): Promise<void> {
  // Check if migration already applied
  const existing = await this.getMigration(name)
  if (existing?.applied_at) {
    return  // Already applied
  }

  // Apply migration
  await this.execute(ddl)

  // Record as applied
  await this.recordMigration(name, ddl)
}
```

## Migration Structure

Migrations are organized by plugin:

```
models/
├── all/
│   └── src/
│       └── migration.ts         # Master migration list
├── tracker/
│   └── src/
│       └── migration.ts         # Tracker migrations
├── document/
│   └── src/
│       └── migration.ts         # Document migrations
└── core/
    └── src/
        └── migration.ts         # Core migrations
```

### Master Migration List

```typescript
// models/all/src/migration.ts
export const migrateOperations: [string, MigrateOperation][] = [
  ['core', coreOperation],
  ['activity', activityOperation],
  ['tracker', trackerOperation],
  ['document', documentOperation]
  // ... more plugins
]
```

### Plugin Migration

```typescript
// models/tracker/src/migration.ts
export const trackerOperation: MigrateOperation = {
  async migrate(client: MigrationClient, mode: MigrateMode): Promise<void> {
    await tryMigrate(mode, client, trackerId, [
      // List of migrations
      {
        state: 'add-identifier-index',
        func: async (client) => {
          await client.createIndex(DOMAIN_TRACKER, { identifier: 1 })
        }
      }
    ])
  },

  async upgrade(
    state: Map<string, Set<string>>,
    client: () => Promise<MigrationUpgradeClient>,
    mode: MigrateMode
  ): Promise<void> {
    await tryUpgrade(mode, state, client, trackerId, [
      // List of upgrade operations
      {
        state: 'migrate-old-issues',
        func: async (client) => {
          // Transform old data
        }
      }
    ])
  }
}
```

## Migration Execution

### Workspace Upgrade Flow

```
┌─────────────────┐
│ New Model       │
│ Available       │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Compare Model   │
│ Versions        │
└────────┬────────┘
         │
    ┌────▼─────┐
    │ Changed? │
    └────┬─────┘
         │ Yes
         ▼
┌─────────────────┐
│ Load Pending    │
│ Transactions    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Apply Model     │
│ Transactions    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Run Migrations  │
│ (if needed)     │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Run Upgrade     │
│ Operations      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Update Model    │
│ Version         │
└─────────────────┘
```

### Migration State Tracking

Migrations are tracked to prevent duplicate execution:

```typescript
interface MigrationState extends Doc {
  plugin: string // Plugin ID
  state: string // Migration identifier
  _class: 'core:class:MigrationState'
  _id: Ref<MigrationState>
  modifiedBy: PersonId
  modifiedOn: Timestamp
}
```

**Check if migration applied:**

```typescript
const states = await client.findAll(core.class.MigrationState, { plugin: trackerId })

const appliedStates = new Set(states.map((s) => s.state))

if (!appliedStates.has('add-priority-field')) {
  // Run migration
  await addPriorityField(client)

  // Record as applied
  await client.create(DOMAIN_MIGRATION, {
    _class: core.class.MigrationState,
    plugin: trackerId,
    state: 'add-priority-field',
    modifiedBy: core.account.System,
    modifiedOn: Date.now()
  })
}
```

## Migration Modes

```typescript
enum MigrateMode {
  Normal = 'normal', // Standard migration
  Fast = 'fast', // Skip slow operations
  Recovery = 'recovery' // Recovery mode
}
```

**Mode Usage:**

- `Normal` - Full migration with all operations
- `Fast` - Skip expensive operations (for development)
- `Recovery` - Minimal operations for recovery scenarios

**Example:**

```typescript
{
  state: 'rebuild-full-text-index',
  mode: MigrateMode.Normal,  // Only run in normal mode
  func: async (client) => {
    // Expensive operation - rebuild search index
    await rebuildSearchIndex(client)
  }
}
```

## Migration Client API

### MigrationClient

```typescript
interface MigrationClient {
  // Find documents
  findAll<T extends Doc>(domain: Domain, query: DocumentQuery<T>): Promise<T[]>

  // Create document
  create<T extends Doc>(domain: Domain, doc: MigrationState): Promise<void>

  // Update documents
  update<T extends Doc>(domain: Domain, query: DocumentQuery<T>, operations: DocumentUpdate<T>): Promise<void>

  // Delete documents
  delete<T extends Doc>(domain: Domain, _id: Ref<T>): Promise<void>

  // Create index
  createIndex(domain: Domain, index: Record<string, 1 | -1>): Promise<void>

  // Migration state
  migrateState: Map<string, Set<string>>

  // Logger
  logger: ModelLogger
}
```

### MigrationUpgradeClient

Extended client for upgrade operations:

```typescript
interface MigrationUpgradeClient extends MigrationClient {
  // Traverse large collections efficiently
  traverse<T extends Doc>(domain: Domain, query: DocumentQuery<T>): AsyncIterableIterator<T>

  // Bulk operations
  bulk<T extends Doc>(domain: Domain, operations: BulkOperation<T>[]): Promise<void>
}
```

## Common Migration Patterns

### 1. Adding a New Field

```typescript
{
  state: 'add-description-field',
  func: async (client) => {
    await client.update(
      DOMAIN_TRACKER,
      { _class: tracker.class.Issue },
      { $set: { description: '' } }
    )
  }
}
```

### 2. Renaming a Field

```typescript
{
  state: 'rename-assignee-to-responsible',
  func: async (client) => {
    await client.update(
      DOMAIN_TRACKER,
      { _class: tracker.class.Issue },
      {
        $rename: { assignee: 'responsible' }
      }
    )
  }
}
```

### 3. Transforming Data

```typescript
{
  state: 'convert-priority-to-number',
  func: async (client) => {
    const issues = await client.traverse(
      DOMAIN_TRACKER,
      { _class: tracker.class.Issue }
    )

    for (const issue of issues) {
      const priorityNum = priorityStringToNumber(issue.priority)
      await client.update(
        DOMAIN_TRACKER,
        { _id: issue._id },
        { priority: priorityNum }
      )
    }
  }
}
```

### 4. Creating References

```typescript
{
  state: 'create-default-statuses',
  func: async (client) => {
    const projects = await client.findAll(
      DOMAIN_TRACKER,
      { _class: tracker.class.Project }
    )

    for (const project of projects) {
      // Create default statuses
      const todoStatus = generateId()
      await client.create(DOMAIN_TRACKER, {
        _id: todoStatus,
        _class: tracker.class.IssueStatus,
        space: project._id,
        name: 'Todo',
        category: 'active'
      })

      // Update project
      await client.update(
        DOMAIN_TRACKER,
        { _id: project._id },
        { defaultIssueStatus: todoStatus }
      )
    }
  }
}
```

### 5. Deleting Old Data

```typescript
{
  state: 'remove-obsolete-tags',
  func: async (client) => {
    // Remove all tags with old structure
    const oldTags = await client.findAll(
      DOMAIN_TAGS,
      { version: { $exists: false } }
    )

    for (const tag of oldTags) {
      await client.delete(DOMAIN_TAGS, tag._id)
    }
  }
}
```

### 6. Creating Indexes

```typescript
{
  state: 'add-status-index',
  func: async (client) => {
    await client.createIndex(
      DOMAIN_TRACKER,
      { status: 1, priority: -1 }
    )
  }
}
```

## PostgreSQL-Specific Migrations

### Concurrent-Safe Migrations

PostgreSQL migrations use locking to prevent concurrent execution:

```typescript
async migrate(name: string, ddl: string): Promise<void> {
  let executed = false
  let migrationComplete = false

  while (!migrationComplete) {
    try {
      await this.client.begin(async (tx) => {
        // Lock migration row (NOWAIT prevents waiting)
        const existing = await tx`
          SELECT identifier, applied_at
          FROM _account_applied_migrations
          WHERE identifier = ${name}
          FOR UPDATE NOWAIT
        `

        if (existing.length > 0 && existing[0].applied_at !== null) {
          // Already applied
          migrationComplete = true
          return
        }

        // Execute migration
        await tx.unsafe(ddl)
        executed = true
      })

      if (executed) {
        // Mark as applied
        await this.client`
          UPDATE _account_applied_migrations
          SET applied_at = NOW()
          WHERE identifier = ${name}
        `
        migrationComplete = true
      }
    } catch (err) {
      if (err.code === '55P03') {
        // Lock not available - another worker is running it
        await new Promise(resolve => setTimeout(resolve, 100))
        continue
      }
      throw err
    }
  }
}
```

**Key Features:**

- `FOR UPDATE NOWAIT` - Prevents multiple workers from running same migration
- Automatic retry if lock held by another worker
- Stale timeout detection for crashed workers

## Best Practices

### 1. Make Migrations Idempotent

```typescript
// ✅ Good - idempotent
{
  state: 'add-priority-field',
  func: async (client) => {
    // Only update if priority doesn't exist
    await client.update(
      DOMAIN_TRACKER,
      {
        _class: tracker.class.Issue,
        priority: { $exists: false }
      },
      { $set: { priority: 'normal' } }
    )
  }
}

// ❌ Bad - not idempotent
{
  state: 'add-priority-field',
  func: async (client) => {
    // Overwrites existing values if run twice
    await client.update(
      DOMAIN_TRACKER,
      { _class: tracker.class.Issue },
      { $set: { priority: 'normal' } }
    )
  }
}
```

### 2. Use Descriptive State Names

```
✅ Good: 'convert-status-string-to-ref-2024-01'
❌ Bad: 'migration1', 'fix', 'update'
```

### 3. Test Migrations

```typescript
// Test migration on copy of production data
await testMigration({
  migration: addPriorityField,
  testData: productionDataSample,
  assertions: [(data) => data.every((d) => d.priority !== undefined)]
})
```

### 4. Handle Large Datasets

```typescript
// ✅ Good - process in batches
{
  state: 'update-large-collection',
  func: async (client) => {
    const batchSize = 1000
    let offset = 0

    while (true) {
      const batch = await client.findAll(
        DOMAIN_TRACKER,
        { _class: tracker.class.Issue },
        { limit: batchSize, skip: offset }
      )

      if (batch.length === 0) break

      for (const doc of batch) {
        await client.update(
          DOMAIN_TRACKER,
          { _id: doc._id },
          { /* update */ }
        )
      }

      offset += batchSize
    }
  }
}
```

### 5. Version Your Migrations

```typescript
// Include date or version in state name
{
  state: 'add-tags-v2-2024-03',  // v2 indicates iteration
  func: async (client) => {
    // Migration logic
  }
}
```

## Summary

HULY's migration system provides:

- ✅ Schema evolution support
- ✅ Data transformation capabilities
- ✅ Concurrent-safe execution
- ✅ State tracking
- ✅ Multiple migration modes
- ✅ Plugin-based organization

Migrations enable HULY to evolve over time while preserving existing data!
