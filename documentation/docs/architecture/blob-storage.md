# Blob Storage & File Handling

HULY provides robust file storage capabilities for attachments, images, and other binary data.

## Overview

The blob storage system:

- **Multi-Backend Support** - MinIO, S3, local filesystem
- **Deduplication** - Same file stored only once (SHA-256 hash)
- **Streaming** - Efficient upload/download of large files
- **Metadata Tracking** - File size, content type, etag
- **CDN-Ready** - Direct S3/CloudFront serving
- **Multipart Upload** - For files >64MB

## Architecture

```
┌──────────────┐
│   Client     │
│   (Browser)  │
└──────┬───────┘
       │
       │ HTTP POST (multipart/form-data)
       ▼
┌──────────────┐
│    Front     │ (File upload endpoint)
│    Server    │
└──────┬───────┘
       │
       ├─────────────────┐
       │                 │
       ▼                 ▼
┌──────────────┐  ┌──────────────┐
│  Calculate   │  │   Storage    │
│  SHA-256     │  │   Adapter    │
└──────┬───────┘  └──────┬───────┘
       │                 │
       └────────┬────────┘
                ▼
       ┌────────────────┐
       │   MinIO/S3     │
       │   (Object      │
       │    Storage)    │
       └────────┬───────┘
                │
                ▼
       ┌────────────────┐
       │   Database     │
       │   (Metadata)   │
       └────────────────┘
```

## File Upload

### Client-Side Upload

```typescript
import { uploadFile } from '@hcengineering/presentation'

async function handleFileUpload(file: File): Promise<Ref<Blob>> {
  // Upload file
  const blobRef = await uploadFile(file)

  // Attach to document
  await client.addCollection(attachment.class.Attachment, task.space, task._id, task._class, 'attachments', {
    file: blobRef,
    name: file.name,
    type: file.type,
    size: file.size
  })

  return blobRef
}
```

### Upload Component

```svelte
<script lang="ts">
  import { uploadFile } from '@hcengineering/presentation'
  import { AttachmentPresenter } from '@hcengineering/attachment-resources'

  export let task: Task

  let fileInput: HTMLInputElement
  let uploading = false

  async function handleFiles(event: Event) {
    const target = event.target as HTMLInputElement
    const files = target.files

    if (!files || files.length === 0) return

    uploading = true
    try {
      for (const file of files) {
        // Upload file
        const blobRef = await uploadFile(file)

        // Create attachment
        await client.addCollection(
          attachment.class.Attachment,
          task.space,
          task._id,
          task._class,
          'attachments',
          {
            file: blobRef,
            name: file.name,
            type: file.type,
            size: file.size
          }
        )
      }
    } finally {
      uploading = false
    }
  }
</script>

<input
  bind:this={fileInput}
  type="file"
  multiple
  on:change={handleFiles}
  style="display: none"
/>

<Button
  label="Attach files"
  icon={attachment.icon.Attachment}
  loading={uploading}
  on:click={() => fileInput.click()}
/>
```

### Drag and Drop Upload

```svelte
<script lang="ts">
  let dragOver = false

  function handleDragOver(event: DragEvent) {
    event.preventDefault()
    dragOver = true
  }

  function handleDragLeave() {
    dragOver = false
  }

  async function handleDrop(event: DragEvent) {
    event.preventDefault()
    dragOver = false

    const files = event.dataTransfer?.files
    if (!files) return

    for (const file of files) {
      await uploadFile(file)
      // Attach to document...
    }
  }
</script>

<div
  class="drop-zone"
  class:drag-over={dragOver}
  on:dragover={handleDragOver}
  on:dragleave={handleDragLeave}
  on:drop={handleDrop}
>
  Drop files here or <Button label="Browse" />
</div>

<style>
  .drop-zone {
    border: 2px dashed var(--theme-divider-color);
    padding: 2rem;
    text-align: center;
    transition: border-color 0.2s;
  }

  .drag-over {
    border-color: var(--theme-accent-color);
    background: var(--theme-bg-accent-color);
  }
</style>
```

## File Download

### Direct Download

```typescript
import { getFileUrl } from '@hcengineering/presentation'

// Get download URL
const url = getFileUrl(blobRef, filename)

// Download file
window.open(url, '_blank')

// Or use fetch
const response = await fetch(url)
const blob = await response.blob()
```

### Streaming Large Files

```typescript
// Server-side streaming
async function streamFile(blobRef: Ref<Blob>, response: Response): Promise<void> {
  const stream = await storageAdapter.get(ctx, workspaceId, blobRef)

  // Pipe to response
  stream.pipe(response)
}
```

## Deduplication

Files with same content share storage:

```typescript
// User 1 uploads logo.png
const blob1 = await uploadFile(logoFile)
// SHA-256: abc123...
// Stored at: workspace-123/abc123...

// User 2 uploads same file (different name)
const blob2 = await uploadFile(sameLogoFile)
// SHA-256: abc123... (same!)
// References same storage location
// Only one copy stored!

// Storage savings with deduplication
```

**Benefits:**

- Reduces storage costs
- Faster uploads (file already exists)
- Faster workspace backups

## Storage Backends

### MinIO (Development)

```bash
# docker-compose.yml
minio:
  image: minio/minio
  command: server /data --console-address ":9001"
  environment:
    MINIO_ROOT_USER: minioadmin
    MINIO_ROOT_PASSWORD: minioadmin
  ports:
    - "9000:9000"
    - "9001:9001"
```

**Configuration:**

```bash
export STORAGE_CONFIG=minio
export MINIO_ENDPOINT=localhost
export MINIO_ACCESS_KEY=minioadmin
export MINIO_SECRET_KEY=minioadmin
```

### AWS S3 (Production)

```bash
export STORAGE_CONFIG=s3
export S3_REGION=us-east-1
export S3_BUCKET=huly-production
export AWS_ACCESS_KEY_ID=your-key
export AWS_SECRET_ACCESS_KEY=your-secret
```

### Local Filesystem

```bash
export STORAGE_CONFIG=fs
export STORAGE_PATH=/var/lib/huly/storage
```

## File Types and Limits

### Supported File Types

**Images:**

- JPEG, PNG, GIF, WebP, SVG
- Auto-resize for thumbnails

**Documents:**

- PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- Text extraction for search

**Archives:**

- ZIP, TAR, GZ

**Media:**

- MP4, MOV, AVI (video)
- MP3, WAV (audio)

**Other:**

- Any file type accepted
- MIME type preserved

### Upload Limits

```bash
# Configure in .env
export UPLOAD_SIZE_LIMIT=100  # MB

# Or in code
const MAX_FILE_SIZE = 100 * 1024 * 1024  // 100MB
```

**Defaults:**

- Single file: 100MB
- Total per request: 500MB

## Image Processing

### Automatic Thumbnails

Images are automatically resized:

```typescript
// Upload image
const imageRef = await uploadFile(largeImage)

// Get thumbnail URL
const thumbnailUrl = getFileUrl(imageRef, 'thumbnail') // 256x256
const mediumUrl = getFileUrl(imageRef, 'medium') // 512x512
const largeUrl = getFileUrl(imageRef, 'large') // 1024x1024
const originalUrl = getFileUrl(imageRef) // Original size
```

### Image Upload Preview

```svelte
<script lang="ts">
  import { uploadFile } from '@hcengineering/presentation'

  let preview: string = ''
  let uploading = false

  async function handleImageUpload(file: File) {
    // Show preview immediately
    preview = URL.createObjectURL(file)

    uploading = true
    try {
      const blobRef = await uploadFile(file)
      // Use blobRef...
    } finally {
      uploading = false
      URL.revokeObjectURL(preview)  // Clean up
    }
  }
</script>

{#if preview}
  <img src={preview} alt="Preview" class:uploading />
{/if}
```

## Multipart Upload

For files larger than 64MB, use multipart upload:

```typescript
async function uploadLargeFile(file: File): Promise<Ref<Blob>> {
  const chunkSize = 5 * 1024 * 1024 // 5MB chunks
  const chunks = Math.ceil(file.size / chunkSize)

  // 1. Initiate multipart upload
  const uploadId = await initiateMultipartUpload(file.name)

  // 2. Upload chunks
  const parts = []
  for (let i = 0; i < chunks; i++) {
    const start = i * chunkSize
    const end = Math.min(start + chunkSize, file.size)
    const chunk = file.slice(start, end)

    const part = await uploadPart(uploadId, i + 1, chunk)
    parts.push(part)

    // Update progress
    const progress = ((i + 1) / chunks) * 100
    console.log(`Upload progress: ${progress.toFixed(1)}%`)
  }

  // 3. Complete upload
  const blobRef = await completeMultipartUpload(uploadId, parts)

  return blobRef
}
```

## Security

### File Validation

```typescript
function validateFile(file: File): void {
  // Check file size
  if (file.size > MAX_FILE_SIZE) {
    throw new Error('File too large')
  }

  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword']

  if (!allowedTypes.includes(file.type)) {
    throw new Error('File type not allowed')
  }

  // Check file extension
  const ext = file.name.split('.').pop()?.toLowerCase()
  if (ext === 'exe' || ext === 'sh') {
    throw new Error('Executable files not allowed')
  }
}
```

### Access Control

Files inherit permissions from parent document:

```typescript
// Check if user can access file
async function canAccessFile(user: AccountUuid, attachment: Attachment): Promise<boolean> {
  // Get parent document
  const doc = await client.findOne(attachment.attachedToClass, {
    _id: attachment.attachedTo
  })

  // Check if user has access to document
  return canAccessDocument(user, doc)
}
```

### Virus Scanning

```typescript
// Optional: Scan uploaded files
async function scanFile(file: File): Promise<boolean> {
  const formData = new FormData()
  formData.append('file', file)

  const response = await fetch(VIRUS_SCAN_URL, {
    method: 'POST',
    body: formData
  })

  const result = await response.json()
  return result.clean
}
```

## Performance Optimization

### Lazy Loading

```svelte
<script lang="ts">
  import { IntersectionObserver } from '@hcengineering/ui'

  export let attachment: Attachment

  let visible = false
  let imageUrl = ''

  function loadImage() {
    visible = true
    imageUrl = getFileUrl(attachment.file)
  }
</script>

<IntersectionObserver on:intersect={loadImage}>
  {#if visible}
    <img src={imageUrl} alt={attachment.name} />
  {:else}
    <div class="placeholder">Loading...</div>
  {/if}
</IntersectionObserver>
```

### CDN Integration

```bash
# Use CloudFront/CDN for static files
export FILES_URL=https://cdn.huly.io

# Direct S3 access
export S3_PUBLIC_BUCKET=huly-public
```

### Compression

Compress files before upload:

```typescript
import pako from 'pako'

async function uploadCompressed(file: File): Promise<Ref<Blob>> {
  const buffer = await file.arrayBuffer()
  const compressed = pako.gzip(new Uint8Array(buffer))

  const compressedFile = new File([compressed], file.name + '.gz', {
    type: 'application/gzip'
  })

  return await uploadFile(compressedFile)
}
```

## Backup and Recovery

### Backup Files

```bash
# Backup workspace blobs
rushx workspace-tool backup-blobs \
  --workspace <workspace-id> \
  --output ./blobs-backup.tar.gz
```

### Restore Files

```bash
# Restore workspace blobs
rushx workspace-tool restore-blobs \
  --workspace <workspace-id> \
  --input ./blobs-backup.tar.gz
```

## Advanced Features

### Direct S3 Upload

For very large files, upload directly to S3:

```typescript
// 1. Get pre-signed upload URL
const { uploadUrl, blobRef } = await client.getUploadUrl({
  filename: file.name,
  contentType: file.type,
  size: file.size
})

// 2. Upload directly to S3
await fetch(uploadUrl, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': file.type
  }
})

// 3. Confirm upload
await client.confirmUpload(blobRef)
```

### Image Manipulation

```svelte
<script lang="ts">
  import { getFileUrl } from '@hcengineering/presentation'

  export let imageRef: Ref<Blob>
  export let size: 'thumbnail' | 'medium' | 'large' | 'original' = 'medium'

  $: imageUrl = getFileUrl(imageRef, size)
</script>

<img src={imageUrl} alt="Image" loading="lazy" />
```

### Video Thumbnails

Generate thumbnails from videos:

```typescript
async function getVideoThumbnail(videoFile: File): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.src = URL.createObjectURL(videoFile)

    video.onloadedmetadata = () => {
      video.currentTime = 1 // Get frame at 1 second
    }

    video.onseeked = () => {
      const canvas = document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      const ctx = canvas.getContext('2d')
      ctx?.drawImage(video, 0, 0)

      canvas.toBlob(
        (blob) => {
          URL.revokeObjectURL(video.src)
          if (blob) resolve(blob)
          else reject(new Error('Failed to generate thumbnail'))
        },
        'image/jpeg',
        0.9
      )
    }

    video.onerror = reject
  })
}
```

## Best Practices

### ✅ Do's

- Validate files before upload
- Show upload progress
- Handle upload errors gracefully
- Use lazy loading for images
- Compress large files
- Use CDN for static files
- Clean up temporary URLs
- Limit file sizes

### ❌ Don'ts

- Don't upload files without validation
- Don't load all images at once
- Don't store files in database
- Don't forget to show progress
- Don't ignore upload errors
- Don't allow unlimited file sizes
- Don't expose storage credentials

## Troubleshooting

### Upload fails

**Check:**

1. File size within limit?
2. Valid file type?
3. Storage service running?
   ```bash
   docker-compose ps minio
   ```
4. Network connectivity?
5. Authentication token valid?

### Download fails

**Check:**

1. File exists in storage?
2. User has permission?
3. Storage URL configured correctly?
4. CDN accessible?

### Out of storage space

**Solutions:**

1. Clean up old files
2. Increase storage capacity
3. Enable compression
4. Implement file expiration policy

## Summary

HULY's blob storage provides:

- ✅ Multi-backend support (MinIO, S3, filesystem)
- ✅ Deduplication (SHA-256 hash)
- ✅ Streaming for large files
- ✅ Multipart upload
- ✅ Image processing
- ✅ CDN integration
- ✅ Secure access control
- ✅ Backup and restore

Handle files efficiently and securely! 📁
