# Collaborative Editing

HULY provides real-time collaborative editing for documents, enabling multiple users to edit the same document simultaneously like Google Docs.

## Overview

Collaborative editing enables:

- **Real-time Sync** - See other users' changes instantly
- **Conflict-Free** - CRDT (Conflict-free Replicated Data Type) ensures consistency
- **Cursor Tracking** - See where other users are editing
- **Offline Support** - Continue editing offline, sync when back
- **Undo/Redo** - Full undo/redo history
- **Comments** - Inline comments and discussions

## Technology Stack

HULY uses:

- **Yjs** - CRDT library for conflict-free editing
- **Hocuspocus** - Server for Yjs synchronization
- **TipTap** - Rich text editor based on ProseMirror
- **WebSocket** - Real-time communication

## Architecture

```
┌─────────────────┐         ┌─────────────────┐
│   User 1        │         │   User 2        │
│   Browser       │         │   Browser       │
└────────┬────────┘         └────────┬────────┘
         │                           │
         │ WebSocket                 │ WebSocket
         │                           │
         └──────────┬────────────────┘
                    │
                    ▼
         ┌──────────────────┐
         │   Collaborator   │ (Hocuspocus server)
         │   Server         │
         └─────────┬────────┘
                   │
                   │ Persists to
                   ▼
         ┌──────────────────┐
         │    Storage       │ (PostgreSQL + S3)
         │   (Ydoc blobs)   │
         └──────────────────┘
```

## How It Works

### 1. Document Structure

Collaborative documents use `MarkupBlobRef`:

```typescript
@Model(document.class.Document, core.class.Doc, DOMAIN_DOCUMENT)
export class TDocument extends TDoc implements Document {
  @Prop(TypeString(), document.string.Title)
  title!: string

  @Prop(TypeCollaborativeDoc(), document.string.Content)
  content!: MarkupBlobRef | null // ← Collaborative content

  @Prop(TypeAccountUuid(), document.string.LockedBy)
  @Hidden()
  lockedBy?: AccountUuid // Currently editing user
}
```

### 2. Collaborative Editor Component

```svelte
<script lang="ts">
  import { CollaborativeTextEditor } from '@hcengineering/text-editor-resources'

  export let document: Document

  const client = getClient()

  function handleUpdate() {
    // Content automatically synced!
    console.log('Document updated')
  }
</script>

<CollaborativeTextEditor
  object={document}
  key="content"
  on:update={handleUpdate}
/>
```

### 3. Real-time Synchronization

```
User 1 types "Hello" → Local Yjs doc updated
                     → Changes sent to Hocuspocus server
                     → Server broadcasts to all connected clients
                     → User 2's Yjs doc updates
                     → User 2 sees "Hello"

All in ~50ms!
```

### 4. Conflict Resolution (CRDT)

```
User 1 types: "Hello world"
User 2 types: "Goodbye world" (at same time)

Traditional: Conflict! Which wins?

CRDT: Both merge!
Result: "Hello Goodbye world"

The CRDT algorithm ensures both edits are preserved.
```

## Yjs Document (Ydoc)

### What is Ydoc?

Yjs uses a special document structure:

```typescript
import { Doc as YDoc } from 'yjs'

// Create Ydoc
const ydoc = new YDoc({
  guid: documentId,
  gc: false // Don't garbage collect history
})

// Get XML fragment for field
const contentFragment = ydoc.getXmlFragment('content')

// Apply changes
ydoc.transact(() => {
  contentFragment.insert(0, [new Y.XmlText('Hello world')])
})
```

### Ydoc Persistence

Changes are persisted as binary updates:

```typescript
// Save Ydoc state
const state = Y.encodeStateAsUpdate(ydoc)
await storage.save(documentId, 'content', state)

// Load Ydoc state
const savedState = await storage.load(documentId, 'content')
Y.applyUpdate(ydoc, savedState)
```

## Providers

### Local Provider

Stores document state locally (IndexedDB):

```typescript
import { createLocalProvider } from '@hcengineering/text-editor-resources'

const ydoc = new YDoc()
const localProvider = createLocalProvider(ydoc, documentId)

// Automatically syncs to IndexedDB
await localProvider.loaded // Wait for local data loaded
```

### Remote Provider

Syncs with Hocuspocus server:

```typescript
import { createRemoteProvider } from '@hcengineering/text-editor-resources'

const ydoc = new YDoc()
const remoteProvider = createRemoteProvider(ydoc, documentId, contentRef)

// Automatically connects to server
await remoteProvider.loaded // Wait for server sync

// Provider handles:
// - WebSocket connection
// - Reconnection on disconnect
// - Sending local changes
// - Receiving remote changes
```

## Cursor Tracking

See where other users are editing:

```svelte
<script lang="ts">
  import { Editor } from '@tiptap/core'
  import { Collaboration, CollaborationCursor } from '@tiptap/extension-collaboration'

  const editor = new Editor({
    extensions: [
      Collaboration.configure({
        document: ydoc,
        field: 'content'
      }),
      CollaborationCursor.configure({
        provider: remoteProvider,
        user: {
          name: currentUser.name,
          color: getUserColor(currentUser.uuid)
        }
      })
    ]
  })
</script>

<!-- Other users' cursors shown automatically! -->
```

## Inline Comments

Add comments to specific text:

```typescript
import { InlineComments } from '@hcengineering/text-editor'

const editor = new Editor({
  extensions: [
    // ... other extensions
    InlineComments.configure({
      ydoc,
      boundary: document._id,
      onCommentClick: (commentId) => {
        showCommentPopup(commentId)
      }
    })
  ]
})
```

**Features:**

- Highlight commented text
- Thread conversations
- Resolve comments
- Reply to comments

## Offline Support

### How Offline Works

```
1. User goes offline
   ↓
2. Continue editing locally
   ↓
3. Changes stored in IndexedDB
   ↓
4. User comes back online
   ↓
5. Local changes sync to server
   ↓
6. Server merges with other users' changes
   ↓
7. Final state synced to all users
```

### Conflict-Free Merging

```
Offline User 1: "Hello world"
Offline User 2: "Goodbye world"

Both come online:
CRDT merges → "Hello Goodbye world"

No conflicts, no data loss!
```

## Document Locking

Prevent simultaneous edits to metadata:

```typescript
// Lock document
async function lockDocument(doc: Document): Promise<void> {
  const client = getClient()
  const me = getCurrentAccount()

  // Check if already locked
  if (doc.lockedBy && doc.lockedBy !== me.uuid) {
    throw new Error(`Locked by ${doc.lockedBy}`)
  }

  await client.update(doc, { lockedBy: me.uuid })
}

// Unlock on close/navigate away
async function unlockDocument(doc: Document): Promise<void> {
  const client = getClient()
  await client.update(doc, { lockedBy: undefined })
}

// Auto-unlock on beforeunload
window.addEventListener('beforeunload', () => {
  unlockDocument(currentDoc)
})
```

## Performance

### Lazy Loading

Don't load content until needed:

```svelte
<script lang="ts">
  export let document: Document

  let editing = false
  let editor

  function startEditing() {
    editing = true
    // Editor loads now, not before
  }
</script>

{#if editing}
  <CollaborativeTextEditor
    object={document}
    key="content"
    bind:editor
  />
{:else}
  <!-- Show read-only preview -->
  <div on:click={startEditing}>
    {document.title}
  </div>
{/if}
```

### Debounced Persistence

```typescript
import { debounce } from '@hcengineering/core'

// Save at most once per second
const debouncedSave = debounce((ydoc: YDoc) => {
  const state = Y.encodeStateAsUpdate(ydoc)
  saveToStorage(state)
}, 1000)

// Call on every change
ydoc.on('update', () => {
  debouncedSave(ydoc)
})
```

## Advanced Features

### Version History

Track document versions:

```typescript
// Create snapshot
async function createSnapshot(doc: Document): Promise<void> {
  const client = getClient()

  // Get current Ydoc state
  const ydoc = await loadYdoc(doc.content)
  const state = Y.encodeStateAsUpdate(ydoc)

  // Save snapshot
  await client.addCollection(document.class.DocumentSnapshot, doc.space, doc._id, doc._class, 'snapshots', {
    name: `Version ${Date.now()}`,
    data: state,
    createdBy: getCurrentAccount().uuid,
    createdOn: Date.now()
  })
}

// Restore from snapshot
async function restoreSnapshot(doc: Document, snapshot: DocumentSnapshot): Promise<void> {
  // Load snapshot data
  const ydoc = new YDoc()
  Y.applyUpdate(ydoc, snapshot.data)

  // Apply to current document
  const currentYdoc = await loadYdoc(doc.content)
  Y.applyUpdate(currentYdoc, Y.encodeStateAsUpdate(ydoc))
}
```

### Presence Awareness

Show who's currently viewing/editing:

```svelte
<script lang="ts">
  import { Awareness } from 'y-protocols/awareness'

  const awareness = remoteProvider.awareness

  $: users = Array.from(awareness.getStates().values()).map(state => ({
    name: state.user.name,
    cursor: state.cursor,
    color: state.user.color
  }))
</script>

<div class="presence">
  <span>Editing: </span>
  {#each users as user}
    <span class="user-indicator" style="color: {user.color}">
      {user.name}
    </span>
  {/each}
</div>
```

## Troubleshooting

### Changes not syncing

**Check:**

1. WebSocket connection active?
2. Hocuspocus server running?
3. Network connectivity?
4. Browser console for errors?

**Debug:**

```typescript
remoteProvider.on('sync', (synced) => {
  console.log('Synced:', synced)
})

remoteProvider.on('status', (status) => {
  console.log('Status:', status) // connected, disconnected, connecting
})
```

### Conflicts still occurring

**CRDT should prevent conflicts!** If seeing conflicts:

1. Check Yjs version consistency
2. Verify all clients use same field name
3. Check for manual document modifications

### Slow editing

**Optimize:**

1. Reduce document size (split large docs)
2. Disable unnecessary extensions
3. Increase Hocuspocus capacity
4. Use local provider for offline editing

## Security

### Edit Permissions

Check permissions before allowing edits:

```typescript
async function canEditDocument(user: AccountUuid, doc: Document): Promise<boolean> {
  const client = getClient()

  // Check if document is locked by someone else
  if (doc.lockedBy && doc.lockedBy !== user) {
    return false
  }

  // Check space membership
  const space = await client.findOne(core.class.Space, {
    _id: doc.space
  })

  if (!space?.members.includes(user)) {
    return false
  }

  // Check edit permission
  return await checkPermission(client, document.permission.UpdateDocument, doc.space)
}
```

## Summary

HULY's collaborative editing provides:

- ✅ Real-time synchronization
- ✅ Conflict-free merging (CRDT)
- ✅ Cursor tracking
- ✅ Offline support
- ✅ Version history
- ✅ Inline comments
- ✅ Presence awareness

Build Google Docs-like collaboration! 📝✨
