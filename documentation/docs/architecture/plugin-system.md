# Plugin System

HULY uses a sophisticated plugin architecture that allows the platform to be modular and extensible. Every feature in HULY is implemented as a plugin.

## Overview

The plugin system enables:

- **Modularity** - Features are isolated in separate packages
- **Extensibility** - New functionality can be added without modifying core
- **Lazy Loading** - Plugins are loaded on-demand for better performance
- **Type Safety** - Full TypeScript support with plugin resources

## Plugin Structure

A typical HULY plugin consists of multiple packages:

```
plugins/my-feature/
├── src/
│   └── index.ts           # Type definitions and plugin definition
├── package.json

plugins/my-feature-assets/
├── src/
│   └── index.ts           # String translations
├── lang/
│   ├── en.json
│   └── ru.json
├── package.json

plugins/my-feature-resources/
├── src/
│   ├── index.ts           # Client-side resources
│   ├── components/        # Svelte components
│   └── utils.ts          # Helper functions
├── package.json

models/my-feature/
├── src/
│   └── index.ts           # Data model definitions
├── package.json

server-plugins/my-feature-resources/
├── src/
│   └── index.ts           # Server-side logic
├── package.json
```

### 1. Base Plugin (`plugins/my-feature`)

Defines types, interfaces, and the plugin identifier:

```typescript
import { type Doc, type Ref, plugin, Plugin } from '@hcengineering/platform'

export interface MyDocument extends Doc {
  title: string
  content: string
}

export default plugin(myFeatureId, {
  class: {
    MyDocument: '' as Ref<Class<MyDocument>>
  },
  string: {
    MyFeature: '' as IntlString,
    CreateDocument: '' as IntlString
  },
  icon: {
    MyFeature: '' as Asset
  },
  component: {
    MyDocumentView: '' as AnyComponent
  }
})
```

**Key Points:**

- Defines plugin ID
- Declares classes, strings, icons, components as empty placeholders
- Provides TypeScript types for the plugin

### 2. Assets Plugin (`plugins/my-feature-assets`)

Provides internationalization strings:

```typescript
// src/index.ts
export { default } from './plugin'

// lang/en.json
{
  "MyFeature": "My Feature",
  "CreateDocument": "Create Document",
  "DocumentTitle": "Title"
}
```

### 3. Resources Plugin (`plugins/my-feature-resources`)

Implements client-side functionality:

```typescript
import { type Resources } from '@hcengineering/platform'
import MyDocumentView from './components/MyDocumentView.svelte'

export default async (): Promise<Resources> => ({
  component: {
    MyDocumentView
  },
  actionImpl: {
    CreateDocument: async (doc) => {
      // Implementation
    }
  },
  function: {
    MyHelper: async () => {
      // Helper function
    }
  }
})
```

### 4. Model Plugin (`models/my-feature`)

Defines the data model using the Builder pattern:

```typescript
import { Builder } from '@hcengineering/model'

export function createModel(builder: Builder): void {
  // Define classes
  builder.createModel(TMyDocument)

  // Create space types
  builder.createDoc(core.class.SpaceType, ...)

  // Define permissions
  builder.createDoc(core.class.Permission, ...)
}

@Model(myFeature.class.MyDocument, core.class.Doc, DOMAIN_MY_FEATURE)
export class TMyDocument extends TDoc implements MyDocument {
  @Prop(TypeString(), myFeature.string.Title)
  title!: string

  @Prop(TypeMarkup(), myFeature.string.Content)
  content!: string
}
```

### 5. Server Plugin (`server-plugins/my-feature-resources`)

Implements server-side logic (triggers, API handlers, etc.):

```typescript
import { type TriggerControl } from '@hcengineering/server-core'

export async function OnDocumentCreate(txes: TxCreateDoc<MyDocument>[], control: TriggerControl): Promise<Tx[]> {
  // Server-side logic when document is created
  return []
}

export default async () => ({
  trigger: {
    OnDocumentCreate
  },
  function: {
    MyServerFunction: async (params) => {
      // Server function
    }
  }
})
```

## Plugin Loading

Plugins are loaded lazily on-demand:

### Registration

```typescript
import { addLocation } from '@hcengineering/platform'

// Register plugin location
addLocation(myFeatureId, () => import('@hcengineering/my-feature-resources'))
```

### Loading Process

1. **Plugin Request** - When a resource is requested:

   ```typescript
   const MyComponent = await getResource(myFeature.component.MyDocumentView)
   ```

2. **Load Check** - System checks if plugin is already loaded

3. **Dynamic Import** - If not loaded, dynamically imports the plugin:

   ```typescript
   const plugin = await import('@hcengineering/my-feature-resources')
   ```

4. **Execute Default Export** - Calls the default function to get resources:

   ```typescript
   const resources = await plugin.default()
   ```

5. **Cache** - Resources are cached for future use

6. **Return Resource** - Requested resource is returned

### Plugin Lifecycle

```
┌─────────────────┐
│ Register Plugin │ (addLocation)
│   Location      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Resource        │ (getResource)
│ Requested       │
└────────┬────────┘
         │
    ┌────▼─────┐
    │ Loaded?  │
    └────┬─────┘
         │ No
         ▼
┌─────────────────┐
│ Load Plugin     │ (dynamic import)
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Execute Default │
│ Export Function │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Cache Resources │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Return Resource │
└─────────────────┘
```

## Plugin Configuration

Plugins can be enabled/disabled via PluginConfiguration:

```typescript
builder.createDoc(core.class.PluginConfiguration, core.space.Model, {
  pluginId: myFeatureId,
  transactions: txes.map((it) => it._id),
  label: myFeature.string.MyFeature,
  description: myFeature.string.PluginDescription,
  enabled: true,
  beta: false,
  icon: myFeature.icon.MyFeature,
  classFilter: [workbench.class.Application, view.class.Action, notification.class.NotificationGroup]
})
```

**Configuration Options:**

- `pluginId` - Unique plugin identifier
- `transactions` - List of transactions that define the plugin's model
- `enabled` - Is plugin enabled?
- `beta` - Is this a beta feature?
- `classFilter` - Which classes should be filtered when plugin is disabled

## Resource Types

Plugins can provide various resource types:

### Components (Svelte)

```typescript
export default async (): Promise<Resources> => ({
  component: {
    MyView: MyView,
    MyEditor: MyEditor,
    MyFilter: MyFilter
  }
})
```

### Functions

```typescript
export default async (): Promise<Resources> => ({
  function: {
    CanDeleteDocument: async (doc: MyDocument) => {
      return doc.status === 'draft'
    },
    GetDocumentTitle: async (doc: MyDocument) => {
      return doc.title
    }
  }
})
```

### Action Implementations

```typescript
export default async (): Promise<Resources> => ({
  actionImpl: {
    CreateDocument: async (doc, event, props) => {
      const client = getClient()
      await client.createDoc(myFeature.class.MyDocument, doc.space, {
        title: props.title,
        content: props.content
      })
    }
  }
})
```

### Completion Functions

```typescript
export default async (): Promise<Resources> => ({
  completion: {
    DocumentTitleCompletion: async (client: Client, query: string, filter: DocumentQuery<MyDocument>) => {
      const docs = await client.findAll(myFeature.class.MyDocument, {
        title: { $like: `%${query}%` }
      })
      return docs.map((d) => ({
        label: d.title,
        _id: d._id
      }))
    }
  }
})
```

## Creating a New Plugin

Use the plugin generator script:

```bash
npm run gen:plugin -- --name my-feature --with-server
```

This creates:

1. Base plugin package
2. Assets package
3. Resources package
4. Model package
5. Server plugin package (if --with-server)
6. Updates rush.json
7. Registers server plugin

### Manual Steps After Generation

1. **Install dependencies**:

   ```bash
   rush update
   ```

2. **Define your model** in `models/my-feature/src/index.ts`

3. **Implement components** in `plugins/my-feature-resources/src/components/`

4. **Add translations** in `plugins/my-feature-assets/lang/en.json`

5. **Build**:

   ```bash
   rush build
   ```

6. **Add to workspace configuration** in `models/all/src/index.ts`:

   ```typescript
   import myFeatureModel from '@hcengineering/model-my-feature'

   const builders: BuilderConfig[] = [
     // ... other plugins
     [
       myFeatureModel,
       myFeatureId,
       {
         label: myFeature.string.MyFeature,
         description: myFeature.string.PluginDescription,
         enabled: true,
         beta: false
       }
     ]
   ]
   ```

## Plugin Dependencies

Plugins can depend on other plugins:

```json
{
  "name": "@hcengineering/my-feature",
  "dependencies": {
    "@hcengineering/core": "^0.6.0",
    "@hcengineering/platform": "^0.6.0",
    "@hcengineering/contact": "^0.6.0"
  }
}
```

Import and use dependencies:

```typescript
import contact from '@hcengineering/contact'
import { type Employee } from '@hcengineering/contact'

// Use types from contact plugin
export interface MyDocument extends Doc {
  assignee: Ref<Employee>
}
```

## Best Practices

### 1. Keep Plugins Focused

```
✅ Good: One plugin per feature domain
❌ Bad: Monolithic plugins with multiple unrelated features
```

### 2. Use Proper Naming

```
✅ Good: my-feature, my-feature-resources, model-my-feature
❌ Bad: plugin1, stuff, things
```

### 3. Minimize Dependencies

```
✅ Good: Only depend on plugins you actually use
❌ Bad: Include every plugin as a dependency
```

### 4. Lazy Load Heavy Dependencies

```typescript
// ✅ Good
export default async (): Promise<Resources> => ({
  function: {
    ProcessImage: async (image) => {
      const { processImage } = await import('./heavy-image-processor')
      return await processImage(image)
    }
  }
})

// ❌ Bad - loads heavy dependency immediately
import { processImage } from './heavy-image-processor'
export default async (): Promise<Resources> => ({
  function: {
    ProcessImage: processImage
  }
})
```

### 5. Provide Type Definitions

Always export types from the base plugin for other plugins to use:

```typescript
export interface MyDocument extends Doc {
  // ...
}

export interface MyDocumentSpace extends TypedSpace {
  // ...
}
```

## Plugin Examples

### Minimal Plugin

```typescript
// plugins/hello/src/index.ts
import { plugin } from '@hcengineering/platform'

export default plugin('hello', {
  string: {
    Hello: '' as IntlString
  }
})
```

### Full-Featured Plugin

See existing plugins for examples:

- **tracker** - Issue tracking with projects, issues, components
- **document** - Document collaboration with teamspaces
- **chunter** - Chat with channels and direct messages
- **recruit** - Recruiting with applicants and vacancies

## Summary

The HULY plugin system provides:

- ✅ Modular architecture
- ✅ Lazy loading for performance
- ✅ Type safety
- ✅ Easy extensibility
- ✅ Server and client separation
- ✅ Internationalization support

Plugins are the building blocks of HULY, enabling a flexible and extensible platform!
