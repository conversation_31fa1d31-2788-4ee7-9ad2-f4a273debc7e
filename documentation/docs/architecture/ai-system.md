# AI System

HULY includes an AI-powered bot that can assist users in channels and direct messages using OpenAI's GPT models.

## Overview

The AI bot provides:

- **Chat Assistance** - Answer questions in natural language
- **Context Awareness** - Understands conversation history
- **Tool Integration** - Can call HULY functions to get data
- **Multi-Workspace** - Works across different workspaces
- **Direct Messages** - 1-on-1 conversations with the bot
- **File Understanding** - Processes attachments in conversations

## Architecture

```
┌──────────────┐
│    User      │
│  (Browser)   │
└──────┬───────┘
       │
       │ Send message mentioning @AI
       ▼
┌──────────────┐
│   Server     │
│  (Trigger)   │
└──────┬───────┘
       │
       │ POST /api/v1/ai/message
       ▼
┌──────────────┐
│   AI Bot     │
│   Service    │
└──────┬───────┘
       │
       ├─────────────────┐
       │                 │
       ▼                 ▼
┌──────────────┐  ┌──────────────┐
│   OpenAI     │  │    HULY      │
│     API      │  │    Client    │
│  (GPT-4)     │  │  (Get data)  │
└──────┬───────┘  └──────┬───────┘
       │                 │
       └────────┬────────┘
                ▼
       ┌────────────────┐
       │   Generate     │
       │   Response     │
       └────────┬───────┘
                │
                ▼
       ┌────────────────┐
       │   Post Reply   │
       │   to Channel   │
       └────────────────┘
```

## How AI Bot Works

### 1. Message Detection

When user mentions the AI bot:

```typescript
// Server trigger watches for new messages
async function OnMessageSend(txes: TxCreateDoc<ChatMessage>[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const message = TxProcessor.createDoc2Doc(tx)

    // Check if message is in DM with AI bot
    if (message.attachedToClass === chunter.class.DirectMessage) {
      const dm = await control.findOne(chunter.class.DirectMessage, {
        _id: message.attachedTo
      })

      // Check if bot is a member
      if (dm.members.includes(aiBotAccount)) {
        // Send to AI service
        await sendToAIService({
          messageId: message._id,
          message: message.message,
          user: message.createdBy,
          objectId: dm._id,
          objectClass: dm._class
        })
      }
    }
  }

  return []
}
```

### 2. AI Service Processing

The AI bot service:

```typescript
async function processMessageEvent(event: AIEventRequest): Promise<void> {
  const { user, objectId, message } = event

  // 1. Get conversation history
  const history = await getHistory(objectId)

  // 2. Convert to OpenAI format
  const openAiHistory = toOpenAiHistory(history)

  // 3. Add current message
  const prompt = {
    role: 'user',
    content: message
  }

  // 4. Call OpenAI with tools
  const response = await createChatCompletionWithTools(openai, prompt, user, openAiHistory)

  // 5. Post response back to HULY
  if (response) {
    await client.addCollection(chunter.class.ChatMessage, objectId, objectId, objectClass, 'messages', {
      message: response.completion,
      attachments: 0,
      reactions: 0
    })
  }
}
```

### 3. OpenAI Integration

```typescript
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL // Optional: for custom endpoints
})

async function createChatCompletion(
  message: string,
  history: OpenAI.ChatCompletionMessageParam[]
): Promise<string | undefined> {
  const completion = await openai.chat.completions.create({
    model: 'gpt-4o-mini', // Configurable model
    messages: [
      {
        role: 'system',
        content: 'You are a helpful assistant in the HULY platform.'
      },
      ...history,
      {
        role: 'user',
        content: message
      }
    ],
    max_tokens: 12800,
    temperature: 0.7
  })

  return completion.choices[0]?.message?.content
}
```

## AI Tools

The bot can use tools to interact with HULY:

### Tool Definition

```typescript
const tools: OpenAI.ChatCompletionTool[] = [
  {
    type: 'function',
    function: {
      name: 'search_issues',
      description: 'Search for issues in the current project',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query for issues'
          },
          status: {
            type: 'string',
            enum: ['open', 'in-progress', 'done'],
            description: 'Filter by status'
          }
        },
        required: ['query']
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'create_task',
      description: 'Create a new task in the current project',
      parameters: {
        type: 'object',
        properties: {
          title: { type: 'string' },
          description: { type: 'string' },
          priority: {
            type: 'string',
            enum: ['low', 'medium', 'high', 'urgent']
          }
        },
        required: ['title']
      }
    }
  }
]
```

### Tool Execution

```typescript
async function executeTool(toolName: string, args: any, workspaceClient: WorkspaceClient): Promise<any> {
  switch (toolName) {
    case 'search_issues':
      const issues = await workspaceClient.client.findAll(
        tracker.class.Issue,
        {
          title: { $like: `%${args.query}%` },
          ...(args.status ? { status: args.status } : {})
        },
        { limit: 10 }
      )
      return issues.map((i) => ({
        id: i._id,
        title: i.title,
        status: i.status,
        assignee: i.assignee
      }))

    case 'create_task':
      const taskId = await workspaceClient.client.createDoc(tracker.class.Issue, args.space, {
        title: args.title,
        description: args.description || '',
        priority: args.priority || 'medium',
        status: 'open'
      })
      return { taskId, success: true }

    default:
      throw new Error(`Unknown tool: ${toolName}`)
  }
}
```

### Tool-Based Conversation

```
User: "Show me all high priority issues"

AI: (Calls search_issues tool with query="", priority="high")
    (Gets results from HULY)

AI Response: "Here are the high priority issues:
1. Fix login bug (#123)
2. Database migration error (#124)
3. Performance issue on dashboard (#125)"

User: "Create a task to fix the login bug"

AI: (Calls create_task tool with title="Fix login bug")
    (Creates task in HULY)

AI Response: "I've created task #126: Fix login bug"
```

## Configuration

### Environment Variables

```bash
# AI Bot Service Configuration

# OpenAI API
OPENAI_API_KEY=sk-...                    # Your OpenAI API key
OPENAI_MODEL=gpt-4o-mini                 # Model to use
OPENAI_BASE_URL=https://api.openai.com/v1  # API endpoint (optional)

# Alternative: Use Together AI
# OPENAI_BASE_URL=https://api.together.xyz/v1
# OPENAI_MODEL=meta-llama/Llama-3-70b-chat-hf

# Token limits
MAX_CONTENT_TOKENS=12800                 # Max tokens in response
MAX_HISTORY_RECORDS=500                  # Max conversation history

# Bot account
FIRST_NAME=HULY
LAST_NAME=AI
PASSWORD=secure-bot-password
AVATAR_PATH=./assets/avatar.png

# Service
SERVICE_ID=ai-bot-service
PORT=4010
```

### Bot Account Creation

The AI bot needs an account:

```bash
# Create bot account
rushx workspace-tool create-account \
  --workspace <workspace-id> \
  --email <EMAIL> \
  --password secure-password \
  --first-name HULY \
  --last-name AI
```

## Conversation History

### History Management

```typescript
interface HistoryRecord {
  role: 'user' | 'assistant' | 'system'
  content: string
  tokens: number
  personUuid: PersonUuid
  timestamp: Timestamp
}

// Store history in database
await storage.pushHistory({
  objectId, // DM or channel ID
  objectClass,
  role: 'user',
  content: message,
  tokens: tokenCount,
  personUuid: user,
  timestamp: Date.now()
})

// Retrieve history
const history = await storage.getHistory(objectId, (limit = 500))
```

### Token Management

Limit context window to fit model limits:

```typescript
function toOpenAiHistory(rawHistory: HistoryRecord[], promptTokens: number): OpenAI.ChatCompletionMessageParam[] {
  const maxTokens = 128000 // GPT-4 limit
  const reservedForResponse = 12800
  const availableForHistory = maxTokens - promptTokens - reservedForResponse

  let totalTokens = 0
  const result: OpenAI.ChatCompletionMessageParam[] = []

  // Add messages from newest to oldest until limit
  for (let i = rawHistory.length - 1; i >= 0; i--) {
    const record = rawHistory[i]

    if (totalTokens + record.tokens > availableForHistory) {
      break // Would exceed limit
    }

    result.unshift({
      role: record.role,
      content: record.content
    })

    totalTokens += record.tokens
  }

  return result
}
```

### Conversation Summarization

When history gets too long, summarize it:

```typescript
async function summarizeHistory(history: HistoryRecord[]): Promise<string> {
  const messages = history.map((h) => ({
    role: h.role,
    content: h.content
  }))

  const summary = await openai.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: [
      {
        role: 'system',
        content: 'Summarize this conversation concisely.'
      },
      ...messages
    ],
    max_tokens: 500
  })

  return summary.choices[0]?.message?.content || ''
}
```

## Attachment Processing

AI bot can process file attachments:

```typescript
async function processMessageWithAttachments(message: string, attachments: Attachment[]): Promise<string> {
  let prompt = message

  // Add attachment information
  if (attachments.length > 0) {
    prompt += '\n\nAttachments:\n'

    for (const attachment of attachments) {
      prompt += `- Name: ${attachment.name}\n`
      prompt += `  Type: ${attachment.type}\n`
      prompt += `  Size: ${attachment.size} bytes\n`

      // For text files, include content
      if (attachment.type.startsWith('text/')) {
        const content = await downloadAttachment(attachment.file)
        prompt += `  Content:\n${content}\n`
      }
    }
  }

  return prompt
}
```

## Cost Management

Track and limit OpenAI API costs:

```typescript
interface Usage {
  workspace: WorkspaceUuid
  user: PersonUuid
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost: number // Estimated cost in USD
  timestamp: Timestamp
}

// Calculate cost
function calculateCost(promptTokens: number, completionTokens: number, model: string): number {
  // GPT-4o-mini pricing (example)
  const costPer1kPrompt = 0.00015 // $0.15 per 1M tokens
  const costPer1kCompletion = 0.0006 // $0.60 per 1M tokens

  const promptCost = (promptTokens / 1000) * costPer1kPrompt
  const completionCost = (completionTokens / 1000) * costPer1kCompletion

  return promptCost + completionCost
}

// Check usage limits
async function checkUsageLimit(workspace: WorkspaceUuid, user: PersonUuid): Promise<boolean> {
  const usage = await getMonthlyUsage(workspace, user)
  const limit = await getUsageLimit(workspace)

  return usage.cost < limit
}
```

## Customizing the AI Bot

### Custom System Prompt

```typescript
const SYSTEM_PROMPTS = {
  default: 'You are a helpful assistant in the HULY platform.',

  technical: `You are a technical support AI for HULY. 
    Help users with:
    - Creating and managing tasks
    - Finding documents
    - Understanding project status
    - Technical troubleshooting`,

  casual: `You are a friendly team assistant.
    Be conversational and helpful.
    Use emojis occasionally. 😊`
}

// Use workspace-specific prompt
const workspace = await getWorkspace(workspaceId)
const systemPrompt = workspace.aiPrompt || SYSTEM_PROMPTS.default
```

### Custom Tools

Add workspace-specific tools:

```typescript
function getTools(workspaceClient: WorkspaceClient, user: AccountUuid): OpenAI.ChatCompletionTool[] {
  const baseTools = [
    searchIssuesTool,
    createTaskTool
    // ... standard tools
  ]

  // Add custom tools based on workspace plugins
  const workspace = workspaceClient.workspace

  if (workspace.hasPlugin('hr')) {
    baseTools.push(searchEmployeesTool)
    baseTools.push(getOrgChartTool)
  }

  if (workspace.hasPlugin('sales')) {
    baseTools.push(searchLeadsTool)
    baseTools.push(createOpportunityTool)
  }

  return baseTools
}
```

## Using AI in Plugins

### Trigger AI Response

```typescript
// From your plugin, trigger AI assistance
async function askAI(question: string, context?: any): Promise<string> {
  const response = await fetch(`${AI_BOT_URL}/api/v1/ai/ask`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    },
    body: JSON.stringify({
      workspace: workspaceId,
      question,
      context
    })
  })

  const data = await response.json()
  return data.answer
}

// Example usage
const summary = await askAI('Summarize this document', { documentId: doc._id })
```

### AI-Powered Features

```typescript
// AI-powered document summarization
async function summarizeDocument(doc: Document): Promise<string> {
  const content = await getDocumentContent(doc.content)

  const summary = await openai.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: [
      {
        role: 'system',
        content: 'Summarize this document in 2-3 sentences.'
      },
      {
        role: 'user',
        content
      }
    ]
  })

  return summary.choices[0]?.message?.content || ''
}

// AI-powered task prioritization
async function suggestPriority(task: Task): Promise<'low' | 'medium' | 'high' | 'urgent'> {
  const prompt = `
    Task: ${task.title}
    Description: ${task.description}
    Due date: ${task.dueDate}
    
    Suggest priority level (low/medium/high/urgent) based on the information.
    Respond with just the priority level.
  `

  const response = await openai.chat.completions.create({
    model: 'gpt-4o-mini',
    messages: [{ role: 'user', content: prompt }],
    max_tokens: 10
  })

  const priority = response.choices[0]?.message?.content?.toLowerCase()

  if (['low', 'medium', 'high', 'urgent'].includes(priority)) {
    return priority as any
  }

  return 'medium' // Default
}
```

## Best Practices

### ✅ Do's

- Sanitize user input before sending to AI
- Limit context window size (token management)
- Cache common responses
- Monitor API costs
- Set rate limits per user/workspace
- Handle API failures gracefully
- Provide fallback responses
- Log AI interactions for debugging

### ❌ Don'ts

- Don't send sensitive data to AI without user consent
- Don't trust AI responses blindly (validate outputs)
- Don't skip error handling
- Don't expose API keys in client code
- Don't allow unlimited AI usage
- Don't ignore cost monitoring
- Don't forget to handle rate limits

## Troubleshooting

### AI bot not responding

**Check:**

1. AI bot service running?
   ```bash
   docker-compose ps ai-bot
   ```
2. OpenAI API key configured?
   ```bash
   echo $OPENAI_API_KEY
   ```
3. Bot account exists in workspace?
4. Bot is member of the channel/DM?

### API rate limit exceeded

**Solutions:**

1. Implement caching for common queries
2. Add rate limiting per user
3. Use cheaper model (gpt-3.5-turbo)
4. Batch requests

### High costs

**Optimize:**

1. Reduce max_tokens
2. Use cheaper models
3. Summarize long histories
4. Cache responses
5. Limit usage per user/workspace

## Summary

HULY's AI system provides:

- ✅ OpenAI-powered chat assistant
- ✅ Tool integration with HULY
- ✅ Conversation history
- ✅ Attachment processing
- ✅ Multi-workspace support
- ✅ Customizable prompts and tools
- ✅ Cost management

Add AI-powered features to your HULY workspace! 🤖✨
