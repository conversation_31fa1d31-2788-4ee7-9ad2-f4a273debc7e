# Server Triggers

Server triggers are server-side functions that execute automatically when specific transactions occur, enabling business logic, validation, and automated workflows.

## Overview

Triggers provide:

- **Automatic Execution** - Run code when transactions match patterns
- **Business Logic** - Enforce rules and workflows
- **Validation** - Verify data before committing
- **Side Effects** - Send notifications, update related docs
- **Async Processing** - Handle long-running operations

## Trigger Basics

### What is a Trigger?

```typescript
interface Trigger extends Doc {
  trigger: Resource<TriggerFunc> // Function to execute
  isAsync?: boolean // Run async after tx committed?
  txMatch?: DocumentQuery<Tx> // Which transactions trigger this?
}

type TriggerFunc = (tx: Tx[], ctrl: TriggerControl) => Promise<Tx[]> // Returns additional transactions to apply
```

**Key concepts:**

- Triggers watch for matching transactions
- Execute custom server-side logic
- Can generate additional transactions
- Can be synchronous or asynchronous

### Simple Trigger Example

```typescript
// Server plugin: server-plugins/my-feature-resources/src/index.ts

async function OnTaskCreate(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    control.ctx.info('Task created', {
      task: task.title,
      space: task.space
    })

    // Generate additional transaction
    // e.g., send notification
    if (task.assignee) {
      const notificationTx = control.txFactory.createTxCreateDoc(notification.class.InboxNotification, task.space, {
        user: task.assignee,
        header: 'Task assigned to you',
        objectId: task._id,
        objectClass: task._class,
        isViewed: false
      })
      result.push(notificationTx)
    }
  }

  return result
}

// Register trigger
export default async () => ({
  trigger: {
    OnTaskCreate
  }
})
```

### Registering Triggers

Define triggers in model:

```typescript
// models/server-my-feature/src/index.ts

export function createModel(builder: Builder): void {
  builder.createDoc(serverCore.class.Trigger, core.space.Model, {
    trigger: serverMyFeature.trigger.OnTaskCreate,
    txMatch: {
      _class: core.class.TxCreateDoc,
      objectClass: myFeature.class.Task
    },
    isAsync: false // Run synchronously
  })
}
```

## Transaction Matching

### Match Specific Transaction Types

```typescript
// Match only creates
txMatch: {
  _class: core.class.TxCreateDoc,
  objectClass: myFeature.class.Task
}

// Match only updates
txMatch: {
  _class: core.class.TxUpdateDoc,
  objectClass: myFeature.class.Task
}

// Match creates and updates
txMatch: {
  _class: { $in: [core.class.TxCreateDoc, core.class.TxUpdateDoc] },
  objectClass: myFeature.class.Task
}

// Match removes
txMatch: {
  _class: core.class.TxRemoveDoc,
  objectClass: myFeature.class.Task
}
```

### Match Specific Operations

```typescript
// Match when specific field is updated
txMatch: {
  _class: core.class.TxUpdateDoc,
  objectClass: myFeature.class.Task,
  'operations.status': { $exists: true }  // Status changed
}

// Match specific value changes
txMatch: {
  _class: core.class.TxUpdateDoc,
  objectClass: myFeature.class.Task,
  'operations.status': 'completed'  // Status set to completed
}

// Match collection updates
txMatch: {
  _class: core.class.TxCreateDoc,
  objectClass: chunter.class.ChatMessage,
  collection: 'comments'  // Comment added
}
```

## Synchronous vs Asynchronous

### Synchronous Triggers

Execute **before** transaction is committed:

```typescript
builder.createDoc(serverCore.class.Trigger, core.space.Model, {
  trigger: serverMyFeature.trigger.ValidateTask,
  txMatch: {
    _class: core.class.TxCreateDoc,
    objectClass: myFeature.class.Task
  },
  isAsync: false // Synchronous
})

// Can prevent transaction from committing
async function ValidateTask(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    // Validation
    if (task.title.length < 3) {
      throw new Error('Title must be at least 3 characters')
    }

    if (task.dueDate && task.dueDate < Date.now()) {
      throw new Error('Due date must be in the future')
    }
  }

  return [] // No additional transactions
}
```

**Use cases:**

- Data validation
- Enforcing business rules
- Preventing invalid data
- Modifying transaction before commit

### Asynchronous Triggers

Execute **after** transaction is committed:

```typescript
builder.createDoc(serverCore.class.Trigger, core.space.Model, {
  trigger: serverMyFeature.trigger.OnTaskComplete,
  txMatch: {
    _class: core.class.TxUpdateDoc,
    objectClass: myFeature.class.Task,
    'operations.completed': true
  },
  isAsync: true // Asynchronous
})

// Runs after transaction committed
async function OnTaskComplete(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })

    if (!task) continue

    // Send notifications
    // Update related documents
    // Call external APIs
    // etc.
  }

  return result
}
```

**Use cases:**

- Sending notifications
- Updating related documents
- Calling external APIs
- Long-running operations

## TriggerControl API

The trigger function receives a `TriggerControl` object:

```typescript
interface TriggerControl {
  // Context for logging and metrics
  ctx: MeasureContext

  // Transaction factory for creating new transactions
  txFactory: TxFactory

  // Workspace info
  workspace: WorkspaceIds

  // Hierarchy for type checking
  hierarchy: Hierarchy

  // Find documents
  findAll<T extends Doc>(_class: Ref<Class<T>>, query: DocumentQuery<T>, options?: FindOptions<T>): Promise<T[]>

  findOne<T extends Doc>(_class: Ref<Class<T>>, query: DocumentQuery<T>): Promise<T | undefined>

  // Apply transactions
  apply(ctx: MeasureContext, tx: Tx[]): Promise<void>

  // Model database
  modelDb: ModelDb

  // Storage adapter
  storageAdapter: StorageAdapter

  // Cache for performance
  contextCache: Map<string, any>
}
```

## Common Trigger Patterns

### Pattern: Cascade Delete

Delete related documents when parent is deleted:

```typescript
async function OnTaskDelete(txes: TxRemoveDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    // Find all subtasks
    const subtasks = await control.findAll(myFeature.class.Task, {
      parent: tx.objectId
    })

    // Delete each subtask
    for (const subtask of subtasks) {
      const removeTx = control.txFactory.createTxRemoveDoc(subtask._class, subtask.space, subtask._id)
      result.push(removeTx)
    }
  }

  return result
}
```

### Pattern: Auto-Update Related Documents

```typescript
async function OnTaskComplete(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    if (!tx.operations.completed) continue

    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })

    if (!task || !task.parent) continue

    // Check if all sibling tasks are completed
    const siblings = await control.findAll(myFeature.class.Task, {
      parent: task.parent
    })

    const allCompleted = siblings.every((t) => t.completed)

    // Auto-complete parent if all children done
    if (allCompleted) {
      const updateTx = control.txFactory.createTxUpdateDoc(myFeature.class.Task, task.space, task.parent, {
        completed: true
      })
      result.push(updateTx)
    }
  }

  return result
}
```

### Pattern: External API Integration

```typescript
async function OnTaskUpdate(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })

    if (!task?.githubIssueUrl) continue

    // Sync to GitHub
    try {
      await fetch(GITHUB_API_URL, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${GITHUB_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: task.title,
          body: task.description,
          state: task.completed ? 'closed' : 'open'
        })
      })
    } catch (err) {
      control.ctx.error('GitHub sync failed', { task: task._id, err })
    }
  }

  return []
}
```

### Pattern: Auto-Assignment

```typescript
async function OnTaskCreate(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    // Auto-assign if no assignee set
    if (!task.assignee) {
      // Get space members
      const space = await control.findOne(core.class.Space, {
        _id: task.space
      })

      if (!space) continue

      // Find member with least tasks
      const memberTaskCounts = new Map()

      for (const member of space.members) {
        const tasks = await control.findAll(myFeature.class.Task, {
          space: task.space,
          assignee: member,
          completed: false
        })
        memberTaskCounts.set(member, tasks.length)
      }

      // Find member with minimum tasks
      let minMember = null
      let minCount = Infinity

      for (const [member, count] of memberTaskCounts) {
        if (count < minCount) {
          minCount = count
          minMember = member
        }
      }

      // Auto-assign
      if (minMember) {
        const updateTx = control.txFactory.createTxUpdateDoc(task._class, task.space, task._id, { assignee: minMember })
        result.push(updateTx)
      }
    }
  }

  return result
}
```

### Pattern: Audit Trail

```typescript
async function OnTaskUpdate(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })

    if (!task) continue

    // Create audit log entry
    const auditTx = control.txFactory.createTxCreateDoc(myFeature.class.AuditLog, task.space, {
      objectId: task._id,
      objectClass: task._class,
      action: 'update',
      changes: tx.operations,
      performedBy: tx.modifiedBy,
      performedAt: tx.modifiedOn
    })

    result.push(auditTx)
  }

  return result
}
```

## Advanced Trigger Techniques

### Caching in Triggers

Use `contextCache` for performance:

```typescript
async function OnTaskUpdate(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  // Get cache
  let cache = control.contextCache.get('task-spaces') as Map<Ref<Space>, Space>

  if (!cache) {
    cache = new Map()
    control.contextCache.set('task-spaces', cache)
  }

  for (const tx of txes) {
    // Use cache instead of querying every time
    let space = cache.get(tx.objectSpace)

    if (!space) {
      space = await control.findOne(core.class.Space, {
        _id: tx.objectSpace
      })
      if (space) cache.set(tx.objectSpace, space)
    }

    // Use space...
  }

  return []
}
```

### Batch Processing

Process multiple transactions efficiently:

```typescript
async function OnTaskBulkUpdate(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  // Batch by space
  const bySpace = new Map<Ref<Space>, TxUpdateDoc<Task>[]>()

  for (const tx of txes) {
    const existing = bySpace.get(tx.objectSpace) || []
    existing.push(tx)
    bySpace.set(tx.objectSpace, existing)
  }

  const result: Tx[] = []

  // Process each space
  for (const [spaceId, spaceTxes] of bySpace) {
    const space = await control.findOne(core.class.Space, { _id: spaceId })

    // Bulk update space statistics
    const updateTx = control.txFactory.createTxUpdateDoc(space._class, space.space, space._id, {
      taskCount: { $inc: spaceTxes.length }
    })
    result.push(updateTx)
  }

  return result
}
```

### Conditional Execution

Only execute under certain conditions:

```typescript
async function OnHighPriorityTask(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    // Only process high priority tasks
    if (task.priority !== 'high') continue

    // Notify managers
    const managers = await getSpaceManagers(control, task.space)

    for (const manager of managers) {
      const notificationTx = control.txFactory.createTxCreateDoc(notification.class.InboxNotification, task.space, {
        user: manager,
        header: 'High priority task created',
        message: task.title,
        objectId: task._id,
        objectClass: task._class
      })
      result.push(notificationTx)
    }
  }

  return result
}
```

## Synchronous vs Asynchronous

### When to Use Synchronous

```typescript
isAsync: false
```

**Use for:**

- ✅ Data validation
- ✅ Enforcing constraints
- ✅ Modifying the transaction
- ✅ Fast operations (&lt; 100 ms)
- ✅ Must complete before commit

**Example:**

```typescript
async function ValidateEmail(txes: TxCreateDoc<Contact>[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const contact = TxProcessor.createDoc2Doc(tx)

    // Validation - must pass before commit
    if (!isValidEmail(contact.email)) {
      throw new Error('Invalid email address')
    }
  }

  return []
}
```

### When to Use Asynchronous

```typescript
isAsync: true
```

**Use for:**

- ✅ Sending notifications
- ✅ External API calls
- ✅ Long-running operations
- ✅ Operations that can fail without blocking
- ✅ Non-critical side effects

**Example:**

```typescript
async function SendWelcomeEmail(txes: TxCreateDoc<Employee>[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const employee = TxProcessor.createDoc2Doc(tx)

    // Send email asynchronously
    // Doesn't block if email service is down
    try {
      await sendEmail(employee.email, 'Welcome!', emailBody)
    } catch (err) {
      control.ctx.error('Failed to send welcome email', { err })
      // Don't throw - allow transaction to succeed anyway
    }
  }

  return []
}
```

## Error Handling

### Graceful Error Handling

```typescript
async function OnTaskUpdate(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    try {
      // Try to process
      const processed = await processTask(tx, control)
      result.push(...processed)
    } catch (err: any) {
      // Log but don't fail entire batch
      control.ctx.error('Failed to process task', {
        task: tx.objectId,
        error: err.message
      })

      // Continue with next task
      continue
    }
  }

  return result
}
```

### Retry Logic

```typescript
async function OnTaskCreate(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    // Retry external API call
    let attempts = 0
    const maxAttempts = 3

    while (attempts < maxAttempts) {
      try {
        await callExternalAPI(task)
        break // Success
      } catch (err) {
        attempts++
        if (attempts >= maxAttempts) {
          control.ctx.error('API call failed after retries', {
            attempts,
            task: task._id
          })
        } else {
          // Wait before retry
          await new Promise((resolve) => setTimeout(resolve, 1000 * attempts))
        }
      }
    }
  }

  return result
}
```

## Debugging Triggers

### Add Logging

```typescript
async function OnTaskCreate(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  control.ctx.info('Trigger fired', {
    trigger: 'OnTaskCreate',
    count: txes.length
  })

  for (const tx of txes) {
    control.ctx.info('Processing task', {
      task: tx.objectId,
      title: tx.attributes.title
    })

    // Process...

    control.ctx.info('Task processed', {
      task: tx.objectId,
      generated: result.length
    })
  }

  return result
}
```

### Test Triggers Locally

```typescript
// Create test control
const testControl: TriggerControl = {
  ctx: createTestContext(),
  txFactory: new TxFactory(account),
  workspace: { uuid: testWorkspaceId },
  hierarchy: testHierarchy,
  findAll: async (_class, query) => {
    return testData.filter(/* match query */)
  },
  findOne: async (_class, query) => {
    return testData.find(/* match query */)
  },
  contextCache: new Map()
}

// Test trigger
const testTx = createTestTransaction()
const result = await OnTaskCreate([testTx], testControl)

expect(result.length).toBe(1)
expect(result[0]._class).toBe(notification.class.InboxNotification)
```

## Performance Optimization

### Avoid N+1 Queries

```typescript
// ❌ Bad - queries in loop
async function SlowTrigger(txes, control) {
  for (const tx of txes) {
    const task = await control.findOne(myFeature.class.Task, {
      _id: tx.objectId
    })
    const space = await control.findOne(core.class.Space, {
      _id: task.space
    })
    // ...
  }
}

// ✅ Good - batch queries
async function FastTrigger(txes, control) {
  // Get all task IDs
  const taskIds = txes.map((tx) => tx.objectId)

  // Single query for all tasks
  const tasks = await control.findAll(myFeature.class.Task, {
    _id: { $in: taskIds }
  })

  // Single query for all spaces
  const spaceIds = [...new Set(tasks.map((t) => t.space))]
  const spaces = await control.findAll(core.class.Space, {
    _id: { $in: spaceIds }
  })

  // Create lookup map
  const spaceMap = new Map(spaces.map((s) => [s._id, s]))

  // Process with cached data
  for (const task of tasks) {
    const space = spaceMap.get(task.space)
    // ...
  }
}
```

### Use Context Cache

```typescript
async function CachedTrigger(txes: TxUpdateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  // Check cache
  let employees = control.contextCache.get('all-employees') as Employee[]

  if (!employees) {
    // Load once
    employees = await control.findAll(contact.class.Employee, {})
    control.contextCache.set('all-employees', employees)
  }

  // Use cached data for all transactions
  // ...
}
```

## Best Practices

### ✅ Do's

- Log trigger execution for debugging
- Handle errors gracefully
- Use async triggers for non-critical operations
- Cache frequently accessed data
- Batch database queries
- Return only necessary transactions
- Test triggers thoroughly
- Document trigger behavior

### ❌ Don'ts

- Don't use sync triggers for slow operations
- Don't ignore errors in async triggers
- Don't create infinite trigger loops
- Don't query in loops (N+1 problem)
- Don't modify input transactions
- Don't throw errors in async triggers
- Don't forget to clean up resources

## Common Pitfalls

### Infinite Loop

```typescript
// ❌ BAD - creates infinite loop!
async function BadTrigger(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    // This creates another Task
    // Which triggers this same trigger again!
    const newTaskTx = control.txFactory.createTxCreateDoc(
      myFeature.class.Task, // ← Creates same class
      tx.objectSpace,
      { title: 'Related task' }
    )
    result.push(newTaskTx)
  }

  return result
}

// ✅ GOOD - prevents loop
async function GoodTrigger(txes: TxCreateDoc<Task>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []

  for (const tx of txes) {
    const task = TxProcessor.createDoc2Doc(tx)

    // Only create related task if parent doesn't exist
    if (!task.parent) {
      const newTaskTx = control.txFactory.createTxCreateDoc(myFeature.class.Task, tx.objectSpace, {
        title: 'Related task',
        parent: task._id // ← Has parent, won't trigger again
      })
      result.push(newTaskTx)
    }
  }

  return result
}
```

## Summary

Server triggers provide:

- ✅ Automatic execution on transactions
- ✅ Business logic enforcement
- ✅ Data validation
- ✅ Side effect handling
- ✅ Sync and async modes
- ✅ Transaction matching patterns
- ✅ Full database access

Key concepts:

- Triggers execute server-side
- Match transactions with `txMatch`
- Return additional transactions to apply
- Sync triggers can block, async triggers can't
- Cache data for performance
- Handle errors gracefully

Build powerful automated workflows with triggers! ⚡
