---
class: tracker:class:Issue
title: Explore Cards 🗂️
status: Backlog
assignee: ${creatorName@global}
---

**Cards** are a flexible framework for structuring information efficiently. You can use Cards to create an efficient knowledge management system. 

You may have already explored some of the Cards linked to the issues we just looked at. From here, feel free to leave the Tracker and head over to Cards. You can find the Cards module in the left side navigation, or dive right in: [Main Story Arc](../card-types-Document/Game%20Narrative/The%20Hero's%20Journey.md)

---

### Need some more support?

* [Huly Docs](https://docs.huly.io/) - Our resource for step-by-step tutorials! Learn how to customize the Huly platform to your team’s needs through detailed instructions and video demonstrations.
* [Slack](https://huly.link/slack) - Our community on Slack is not only an excellent resource for getting in touch directly with our engineers for support, but also a great space for sharing feedback and feature suggestions!
* [YouTube](https://www.youtube.com/@huly_io) - Dive deeper into <PERSON><PERSON>’s features by watching our demo videos, tutorials and other video content.
* Reach out through [LinkedIn](https://www.linkedin.com/company/hardcoreeng/) and [Twitter](https://x.com/huly_io) - we’d love to see what you’re building with Huly!

---

🎉 Congratulations! You’ve completed all of the tasks! ✅ 

Now that you know the basic functions of the Huly Platform, you’re ready to get your workspace set up just the way you like. Feel free to delete our starter examples, or use them as inspiration as you build your own systems. <PERSON>ly has so much more to offer than what we’ve covered in this short tutorial — from video conferencing to chat, documents and more. Have fun exploring!