{"name": "@hcengineering/pod-server", "version": "0.6.0", "main": "lib/index.js", "svelte": "src/index.ts", "types": "types/index.d.ts", "author": "Anticrm Platform Contributors", "template": "@hcengineering/node-package", "license": "EPL-2.0", "scripts": {"start": "rush bundle --to @hcengineering/pod-server && cross-env NODE_ENV=production MODEL_VERSION=$(node ../../common/scripts/show_version.js) ACCOUNTS_URL=http://localhost:3000 REKONI_URL=http://localhost:4004 MONGO_URL=mongodb://localhost:27017 DB_URL=mongodb://localhost:27017 FRONT_URL=http://localhost:8087 UPLOAD_URL=/upload MINIO_ENDPOINT=localhost MINIO_ACCESS_KEY=minioadmin MINIO_SECRET_KEY=minioadmin METRICS_CONSOLE=true SERVER_SECRET=secret OPERATION_PROFILING=false MODEL_JSON=../../models/all/bundle/model.json STATS_URL=http://huly.local:4900 node --inspect bundle/bundle.js", "start-cr": "rush bundle --to @hcengineering/pod-server && cross-env NODE_ENV=production MODEL_VERSION=$(node ../../common/scripts/show_version.js) ACCOUNTS_URL=http://localhost:3000 REKONI_URL=http://localhost:4004 DB_URL=postgresql://<EMAIL>:26257/defaultdb?sslmode=disable FRONT_URL=http://localhost:8087 UPLOAD_URL=/upload MINIO_ENDPOINT=localhost MINIO_ACCESS_KEY=minioadmin MINIO_SECRET_KEY=minioadmin METRICS_CONSOLE=true SERVER_SECRET=secret OPERATION_PROFILING=false MODEL_JSON=../../models/all/bundle/model.json STATS_URL=http://huly.local:4900 FULLTEXT_URL=http://huly.local:4702 SERVER_PORT=3332 node --inspect bundle/bundle.js", "start-flame": "rush bundle --to @hcengineering/pod-server && cross-env NODE_ENV=production MODEL_VERSION=$(node ../../common/scripts/show_version.js) ACCOUNTS_URL=http://localhost:3000 REKONI_URL=http://localhost:4004 MONGO_URL=mongodb://localhost:27017 FRONT_URL=http://localhost:8087 UPLOAD_URL=/upload MINIO_ENDPOINT=localhost MINIO_ACCESS_KEY=minioadmin MINIO_SECRET_KEY=minioadmin METRICS_CONSOLE=true SERVER_SECRET=secret MODEL_JSON=../../models/all/bundle/model.json clinic flame --dest ./out -- node --nolazy -r ts-node/register --enable-source-maps src/__start.ts", "bundle-local": "rush bundle --to @hcengineering/pod-server", "run-local": "cross-env ACCOUNTS_URL=http://localhost:3000 REKONI_URL=http://localhost:4004 DB_URL=postgresql://postgres:postgres@localhost:26257/defaultdb?sslmode=disable ACCOUNTS_DB_URL=postgresql://postgres:postgres@localhost:26257/defaultdb?sslmode=disable FRONT_URL=http://localhost:8087 UPLOAD_URL=/upload \"STORAGE_CONFIG=minio|localhost:9000?accessKey=minioadmin&secretKey=minioadmin&useSSL=false\" METRICS_CONSOLE=true SERVER_SECRET=secret OPERATION_PROFILING=false MODEL_JSON=../../models/all/bundle/model.json STATS_URL=http://localhost:4900 FULLTEXT_URL=http://localhost:4702 QUEUE_CONFIG=localhost:19092 SERVER_PORT=3332 ts-node src/__start.ts", "build": "compile", "_phase:bundle": "rushx bundle", "_phase:docker-build": "rushx docker:build", "_phase:docker-staging": "rushx docker:staging", "get-model": "node ../../common/scripts/esbuild.js --entry=src/get-model.ts -keep-names=true --define=MODEL_VERSION --define=VERSION --define=GIT_REVISION --bundle=true && node ./bundle/bundle.js > ./bundle/model.json", "bundle": "rushx get-model && node ../../common/scripts/esbuild.js --entry=src/__start.ts --keep-names=true --bundle=true --sourcemap=external --external=*.node --external=bufferutil --external=snappy --define=MODEL_VERSION --define=VERSION --define=GIT_REVISION --external=utf-8-validate --external=msgpackr-extract", "docker:build": "../../common/scripts/docker_build.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor", "docker:tbuild": "docker build -t us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor . --platform=linux/amd64 && ../../common/scripts/docker_tag_push.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor", "docker:abuild": "docker build -t us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor . --platform=linux/arm64 && ../../common/scripts/docker_tag_push.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor", "docker:staging": "../../common/scripts/docker_tag.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor staging", "docker:push": "../../common/scripts/docker_tag.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor", "build:watch": "compile", "format": "format src", "test": "jest --passWithNoTests --silent --forceExit", "_phase:build": "compile transpile src", "_phase:test": "jest --passWithNoTests --silent --forceExit", "_phase:format": "format src", "_phase:validate": "compile validate"}, "devDependencies": {"cross-env": "~7.0.3", "@hcengineering/platform-rig": "^0.6.0", "@types/node": "^22.15.29", "@typescript-eslint/eslint-plugin": "^6.11.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-n": "^15.4.0", "eslint": "^8.54.0", "eslint-config-prettier": "^8.8.0", "@types/ws": "^8.5.11", "ts-node": "^10.8.0", "esbuild": "^0.25.9", "@typescript-eslint/parser": "^6.11.0", "eslint-config-standard-with-typescript": "^40.0.0", "prettier": "^3.1.0", "typescript": "^5.8.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/jest": "^29.5.5", "@hcengineering/model-all": "^0.6.0", "snappyjs": "^0.7.0", "@types/body-parser": "~1.19.2", "@types/morgan": "~1.9.9", "@types/cors": "^2.8.12", "@types/express": "^4.17.13"}, "dependencies": {"@hcengineering/analytics": "^0.6.0", "@hcengineering/analytics-service": "^0.6.0", "@hcengineering/communication-server": "^0.1.0", "@hcengineering/contact": "^0.6.24", "@hcengineering/core": "^0.6.32", "@hcengineering/kafka": "^0.6.0", "@hcengineering/middleware": "^0.6.0", "@hcengineering/minio": "^0.6.0", "@hcengineering/mongo": "^0.6.1", "@hcengineering/notification": "^0.6.23", "@hcengineering/platform": "^0.6.11", "@hcengineering/pod-telegram-bot": "^0.6.0", "@hcengineering/postgres": "^0.6.0", "@hcengineering/rpc": "^0.6.5", "@hcengineering/server": "^0.6.4", "@hcengineering/server-ai-bot": "^0.6.0", "@hcengineering/server-calendar": "^0.6.0", "@hcengineering/server-core": "^0.6.1", "@hcengineering/server-notification": "^0.6.1", "@hcengineering/server-pipeline": "^0.6.0", "@hcengineering/server-storage": "^0.6.0", "@hcengineering/server-telegram": "^0.6.0", "@hcengineering/server-token": "^0.6.11", "utf-8-validate": "^6.0.4", "bufferutil": "^4.0.8", "msgpackr": "^1.11.2", "msgpackr-extract": "^3.0.3", "snappy": "^7.2.2", "ws": "^8.18.2", "@hcengineering/account-client": "^0.6.0", "morgan": "^1.10.0", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2"}}