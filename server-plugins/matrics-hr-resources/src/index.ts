//
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import contact, { Contact, Employee, formatName, getName, Person } from '@hcengineering/contact'
import core, {
  Doc,
  PersonId,
  Ref,
  SortingOrder,
  toIdMap,
  Tx,
  TxCreateDoc,
  TxFactory,
  TxCUD,
  TxMixin,
  TxProcessor,
  TxRemoveDoc,
  TxUpdateDoc,
  UserStatus
} from '@hcengineering/core'
import gmail from '@hcengineering/gmail'
import hr, { Department, fromTzDate, PublicHoliday, Request, RequestStatus, Staff, TimeOffTransactionKind, tzDateEqual } from '@hcengineering/matrics-hr'
import notification, { NotificationType } from '@hcengineering/notification'
import { translate } from '@hcengineering/platform'
import { TriggerControl } from '@hcengineering/server-core'
import { getEmployee, getSocialStrings } from '@hcengineering/server-contact'
import { sendEmailNotification } from '@hcengineering/server-gmail-resources'
import {
  getContentByTemplate,
  getNotificationProviderControl,
  isAllowed
} from '@hcengineering/server-notification-resources'

async function getOldDepartment (
  currentTx: TxMixin<Employee, Staff>,
  control: TriggerControl
): Promise<Ref<Department> | undefined> {
  const txes = await control.findAll<TxMixin<Employee, Staff>>(
    control.ctx,
    core.class.TxMixin,
    {
      objectId: currentTx.objectId
    },
    { sort: { modifiedOn: SortingOrder.Ascending } }
  )
  let lastDepartment: Ref<Department> | undefined
  for (const tx of txes) {
    if (tx._id === currentTx._id) continue
    if (tx.attributes?.department !== undefined) {
      lastDepartment = tx.attributes.department
    }
  }
  return lastDepartment
}

async function buildHierarchy (_id: Ref<Department>, control: TriggerControl): Promise<Department[]> {
  const res: Department[] = []
  const ancestors = new Map<Ref<Department>, Ref<Department>>()
  const departments = await control.queryFind(control.ctx, hr.class.Department, {})
  for (const department of departments) {
    if (department._id === hr.ids.Head || department.parent === undefined) continue
    ancestors.set(department._id, department.parent)
  }
  const departmentsMap = toIdMap(departments)
  while (true) {
    const department = departmentsMap.get(_id)
    if (department === undefined) return res
    res.push(department)
    const next = ancestors.get(department._id)
    if (next === undefined) return res
    _id = next
  }
}

function exlude (first: Ref<Department>[], second: Ref<Department>[]): Ref<Department>[] {
  const set = new Set(first)
  const res: Ref<Department>[] = []
  for (const department of second) {
    if (!set.has(department)) {
      res.push(department)
    }
  }
  return res
}

function getTxes (
  factory: TxFactory,
  employees: Ref<Employee>[],
  added: Ref<Department>[],
  removed?: Ref<Department>[]
): Tx[] {
  const pushTxes = added
    .map((dep) =>
      employees.map((emp) =>
        factory.createTxUpdateDoc(hr.class.Department, core.space.Workspace, dep, {
          $push: { members: emp }
        })
      )
    )
    .flat()
  if (removed === undefined) return pushTxes
  const pullTxes = removed
    .map((dep) =>
      employees.map((emp) =>
        factory.createTxUpdateDoc(hr.class.Department, core.space.Workspace, dep, {
          $pull: { members: emp }
        })
      )
    )
    .flat()
  return [...pullTxes, ...pushTxes]
}

/**
 * @public
 */
export async function OnDepartmentStaff (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    const ctx = tx as TxMixin<Employee, Staff>
    const employee = ctx.objectId

    if (ctx.attributes.department !== undefined) {
      const lastDepartment = await getOldDepartment(ctx, control)

      const departmentId = ctx.attributes.department
      if (departmentId === null) {
        if (lastDepartment !== undefined) {
          const removed = await buildHierarchy(lastDepartment, control)
          result.push(
            ...getTxes(
              control.txFactory,
              [employee],
              [],
              removed.map((p) => p._id)
            )
          )
        }
      }
      const push = (await buildHierarchy(departmentId, control)).map((p) => p._id)

      if (lastDepartment === undefined) {
        result.push(...getTxes(control.txFactory, [employee], push))
      } else {
        let removed = (await buildHierarchy(lastDepartment, control)).map((p) => p._id)
        const added = exlude(removed, push)
        removed = exlude(push, removed)
        result.push(...getTxes(control.txFactory, [employee], added, removed))
      }
    }
  }
  return result
}

/**
 * @public
 */
export async function OnDepartmentRemove (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    const ctx = tx as TxRemoveDoc<Department>

    const department = control.removedMap.get(ctx.objectId) as Department
    if (department === undefined) {
      continue
    }
    const nested = await control.findAll(control.ctx, hr.class.Department, { parent: department._id })
    for (const dep of nested) {
      result.push(control.txFactory.createTxRemoveDoc(dep._class, dep.space, dep._id))
    }
    const employeeIds = department.members
    const employees = await control.findAll(control.ctx, contact.mixin.Employee, {
      _id: { $in: employeeIds }
    })
    const removed = await buildHierarchy(department._id, control)
    employees.forEach((em) => {
      result.push(
        control.txFactory.createTxMixin(em._id, em._class, em.space, hr.mixin.Staff, { department: undefined })
      )
    })
    result.push(
      ...getTxes(
        control.txFactory,
        employeeIds,
        [],
        removed.map((p) => p._id)
      )
    )
  }
  return result
}

/**
 * @public
 */
export async function OnEmployee (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    const ctx = tx as TxMixin<Person, Employee>

    const person = (await control.findAll(control.ctx, contact.class.Person, { _id: ctx.objectId }))[0]
    if (person === undefined) {
      continue
    }

    const employee = control.hierarchy.as(person, ctx.mixin)
    if (control.hierarchy.hasMixin(person, hr.mixin.Staff) || !employee.active) {
      continue
    }

    if (employee.role === 'GUEST') continue

    result.push(
      control.txFactory.createTxMixin(ctx.objectId, ctx.objectClass, ctx.objectSpace, hr.mixin.Staff, {
        department: hr.ids.Head
      })
    )
  }
  return result
}

/**
 * @public
 */
export async function OnEmployeeDeactivate (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    if (core.class.TxMixin !== tx._class) {
      continue
    }
    const ctx = tx as TxMixin<Person, Employee>
    if (ctx.mixin !== contact.mixin.Employee || ctx.attributes.active !== false) {
      continue
    }
    const employee = ctx.objectId as Ref<Employee>
    const departments = await control.queryFind(control.ctx, hr.class.Department, {})
    const removed = departments.filter((dep) => dep.members.some((p) => p === employee))
    result.push(
      ...getTxes(
        control.txFactory,
        [employee],
        [],
        removed.map((p) => p._id)
      )
    )
  }

  return result
}

// TODO: why we need specific email notifications instead of using general flow?
async function sendEmailNotifications (
  control: TriggerControl,
  sender: PersonId,
  doc: Request | PublicHoliday,
  space: Ref<Department>,
  typeId: Ref<NotificationType>
): Promise<void> {
  const contacts = new Set<Ref<Contact>>()
  const departments = await buildHierarchy(space, control)
  for (const department of departments) {
    if (department.subscribers === undefined) continue
    for (const subscriber of department.subscribers) {
      contacts.add(subscriber)
    }
  }

  // should respect employee settings
  const type = await control.modelDb.findOne(notification.class.NotificationType, { _id: typeId })
  if (type === undefined) return
  const provider = await control.modelDb.findOne(notification.class.NotificationProvider, {
    _id: gmail.providers.EmailNotificationProvider
  })
  if (provider === undefined) return

  const notificationControl = await getNotificationProviderControl(control.ctx, control)
  for (const contact of contacts.values()) {
    const socialStrings = await getSocialStrings(control, contact as Ref<Person>)
    const allowed = isAllowed(control, socialStrings, type, provider, notificationControl)
    if (!allowed) {
      contacts.delete(contact)
    }
  }

  const channels = await control.findAll(control.ctx, contact.class.Channel, {
    provider: contact.channelProvider.Email,
    attachedTo: { $in: Array.from(contacts) }
  })

  const socialId = (await control.findAll(control.ctx, contact.class.SocialIdentity, { key: sender }))[0]
  if (socialId === undefined) return

  const senderPerson = (await control.findAll(control.ctx, contact.class.Person, { _id: socialId.attachedTo }))[0]

  const senderName = senderPerson !== undefined ? formatName(senderPerson.name, control.branding?.lastNameFirst) : ''
  const content = await getContentByTemplate(doc, senderName, type._id, control, '')
  if (content === undefined) return

  for (const channel of channels) {
    await sendEmailNotification(control.ctx, content.text, content.html, content.subject, channel.value)
  }
}

/**
 * @public
 */
export async function OnRequestCreate (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const ctx = tx as TxCreateDoc<Request>
    const request = TxProcessor.createDoc2Doc(ctx)

    await sendEmailNotifications(control, ctx.modifiedBy, request, request.department, hr.ids.CreateRequestNotification)
  }

  return []
}

export async function OnRequestUpdate (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const ctx = tx as TxUpdateDoc<Request>
    const request = (await control.findAll(control.ctx, hr.class.Request, { _id: ctx.objectId }))[0] as Request
    if (request === undefined) continue

    await sendEmailNotifications(control, ctx.modifiedBy, request, request.department, hr.ids.UpdateRequestNotification)
  }
  return []
}

export async function OnRequestRemove (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  for (const tx of txes) {
    const ctx = tx as TxCreateDoc<Request>
    const request = control.removedMap.get(ctx.objectId) as Request
    if (request === undefined) continue

    await sendEmailNotifications(control, ctx.modifiedBy, request, request.department, hr.ids.RemoveRequestNotification)
  }
  return []
}

/**
 * @public
 */
export async function RequestHTMLPresenter (doc: Doc, control: TriggerControl): Promise<string> {
  const request = doc as Request
  const employee = (await control.findAll(control.ctx, contact.mixin.Employee, { _id: request.attachedTo }))[0]
  const who = getName(control.hierarchy, employee, control.branding?.lastNameFirst)
  const type = await translate(control.modelDb.getObject(request.type).label, {})

  const date = tzDateEqual(request.tzDate, request.tzDueDate)
    ? `on ${new Date(fromTzDate(request.tzDate)).toLocaleDateString()}`
    : `from ${new Date(fromTzDate(request.tzDate)).toLocaleDateString()} to ${new Date(
        fromTzDate(request.tzDueDate)
      ).toLocaleDateString()}`

  return `${who} - ${type.toLowerCase()} ${date}`
}

/**
 * @public
 */
export async function RequestTextPresenter (doc: Doc, control: TriggerControl): Promise<string> {
  const request = doc as Request
  const employee = (await control.findAll(control.ctx, contact.mixin.Employee, { _id: request.attachedTo }))[0]
  const who = getName(control.hierarchy, employee, control.branding?.lastNameFirst)
  const type = await translate(control.modelDb.getObject(request.type).label, {})

  const date = tzDateEqual(request.tzDate, request.tzDueDate)
    ? `on ${new Date(fromTzDate(request.tzDate)).toLocaleDateString()}`
    : `from ${new Date(fromTzDate(request.tzDate)).toLocaleDateString()} to ${new Date(
        fromTzDate(request.tzDueDate)
      ).toLocaleDateString()}`

  return `${who} - ${type.toLowerCase()} ${date}`
}

export async function OnPublicHolidayCreate (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    const ctx = tx as TxCreateDoc<PublicHoliday>
    const employee = await getEmployee(control, ctx.modifiedBy)
    if (employee === undefined) continue

    const publicHoliday = TxProcessor.createDoc2Doc(ctx)
    await sendEmailNotifications(
      control,
      ctx.modifiedBy,
      publicHoliday,
      publicHoliday.department,
      hr.ids.CreatePublicHolidayNotification
    )
  }
  return result
}

/**
 * @public
 */
export async function PublicHolidayHTMLPresenter (doc: Doc, control: TriggerControl): Promise<string> {
  const holiday = doc as PublicHoliday
  const employee = await getEmployee(control, holiday.modifiedBy)
  if (employee === undefined) return ''
  const who = formatName(employee.name, control.branding?.lastNameFirst)

  const date = `on ${new Date(fromTzDate(holiday.date)).toLocaleDateString()}`

  return `${holiday.title} ${date}<br/>${holiday.description}<br/>Set by ${who}`
}

/**
 * @public
 */
export async function PublicHolidayTextPresenter (doc: Doc, control: TriggerControl): Promise<string> {
  const holiday = doc as PublicHoliday
  const employee = await getEmployee(control, holiday.modifiedBy)
  if (employee === undefined) return ''
  const who = formatName(employee.name, control.branding?.lastNameFirst)

  const date = `on ${new Date(fromTzDate(holiday.date)).toLocaleDateString()}`

  return `${holiday.title} ${date}. ${holiday.description}. Set by ${who}`
}

/**
 * Check if employee can approve requests for a department
 */
async function canApproveRequest(
  employeeId: Ref<Employee>,
  departmentId: Ref<Department>,
  control: TriggerControl
): Promise<boolean> {
  const department = (await control.findAll(control.ctx, hr.class.Department, { _id: departmentId }))[0]
  if (!department) return false
  
  const isManager = department.managers?.includes(employeeId) ?? false
  const isTeamLead = department.teamLead === employeeId
  
  return isManager || isTeamLead
}

/**
 * Get list of approvers for a request (department managers + team lead)
 */
async function getApprovers(
  departmentId: Ref<Department>,
  control: TriggerControl
): Promise<Ref<Employee>[]> {
  const department = (await control.findAll(control.ctx, hr.class.Department, { _id: departmentId }))[0]
  if (!department) return []
  
  const approvers: Set<Ref<Employee>> = new Set()
  
  if (department.managers) {
    department.managers.forEach(m => approvers.add(m))
  }
  
  if (department.teamLead) {
    approvers.add(department.teamLead)
  }
  
  return Array.from(approvers)
}

/**
 * Send notification to approvers when request is submitted
 */
async function notifyApprovers(
  request: Request,
  control: TriggerControl,
  sender: PersonId
): Promise<void> {
  const approvers = await getApprovers(request.department, control)
  if (approvers.length === 0) return

  const type = await control.modelDb.findOne(notification.class.NotificationType, { 
    _id: hr.ids.RequestSubmittedNotification 
  })
  if (type === undefined) return

  const provider = await control.modelDb.findOne(notification.class.NotificationProvider, {
    _id: gmail.providers.EmailNotificationProvider
  })
  if (provider === undefined) return

  const notificationControl = await getNotificationProviderControl(control.ctx, control)
  
  for (const approverId of approvers) {
    const socialStrings = await getSocialStrings(control, approverId as Ref<Person>)
    const allowed = isAllowed(control, socialStrings, type, provider, notificationControl)
    if (!allowed) continue

    const channels = await control.findAll(control.ctx, contact.class.Channel, {
      provider: contact.channelProvider.Email,
      attachedTo: approverId
    })

    const socialId = (await control.findAll(control.ctx, contact.class.SocialIdentity, { key: sender }))[0]
    if (socialId === undefined) continue

    const senderPerson = (await control.findAll(control.ctx, contact.class.Person, { _id: socialId.attachedTo }))[0]

    const senderName = senderPerson !== undefined ? formatName(senderPerson.name, control.branding?.lastNameFirst) : ''
    const content = await getContentByTemplate(request, senderName, type._id, control, '')
    if (content === undefined) continue

    for (const channel of channels) {
      await sendEmailNotification(control.ctx, content.text, content.html, content.subject, channel.value)
    }
  }
}

/**
 * Trigger when request is approved
 */
export async function OnRequestApproved (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    const ctx = tx as TxUpdateDoc<Request>

    if (ctx.operations.status !== RequestStatus.Approved) continue

    const request = (await control.findAll(control.ctx, hr.class.Request, { _id: ctx.objectId }))[0]
    if (request === undefined) continue

    // Validate approver has permission
    const approver = ctx.operations.approvedBy
    if (approver) {
      const canApprove = await canApproveRequest(approver, request.department, control)
      if (!canApprove) {
        console.warn(`Employee ${approver} is not authorized to approve requests for department ${request.department}`)
      }
    }

    // Auto-apply changes based on request type
    try {
      const payloadRaw = (request as any).payload as string | undefined
      const payload = payloadRaw ? (JSON.parse(payloadRaw) as any) : undefined

      // Update Staff profile fields
      if (request.type === hr.ids.ProfileUpdate && payload !== undefined) {
        const employee = (await control.findAll(control.ctx, contact.mixin.Employee, { _id: request.attachedTo }))[0]
        if (employee !== undefined) {
          const toUpdate: Record<string, any> = {}
          if (payload.jobTitle !== undefined) toUpdate.jobTitle = payload.jobTitle || null
          if (payload.employmentType !== undefined) toUpdate.employmentType = payload.employmentType || null
          if (payload.location !== undefined) toUpdate.location = payload.location || null
          if (payload.workHoursPerWeek !== undefined) {
            const n = Number(payload.workHoursPerWeek)
            toUpdate.workHoursPerWeek = Number.isFinite(n) ? n : null
          }
          const toTs = (s: any) => (s ? new Date(s).getTime() : null)
          if (payload.hireDate !== undefined) toUpdate.hireDate = toTs(payload.hireDate)
          if (payload.terminationDate !== undefined) toUpdate.terminationDate = toTs(payload.terminationDate)
          if (payload.probationEndDate !== undefined) toUpdate.probationEndDate = toTs(payload.probationEndDate)
          if (Object.keys(toUpdate).length > 0) {
            result.push(
              control.txFactory.createTxMixin(employee._id, employee._class, employee.space, hr.mixin.Staff, toUpdate)
            )
          }
        }
      }

      // Update Staff emergency info
      if (request.type === hr.ids.EmergencyInfoUpdate && payload !== undefined) {
        const employee = (await control.findAll(control.ctx, contact.mixin.Employee, { _id: request.attachedTo }))[0]
        if (employee !== undefined) {
          const toUpdate: Record<string, any> = {}
          if (payload.emergencyContact !== undefined) toUpdate.emergencyContact = payload.emergencyContact || null
          if (payload.emergencyPhone !== undefined) toUpdate.emergencyPhone = payload.emergencyPhone || null
          if (payload.emergencyEmail !== undefined) toUpdate.emergencyEmail = payload.emergencyEmail || null
          if (payload.emergencyRelationship !== undefined) toUpdate.emergencyRelationship = payload.emergencyRelationship || null
          if (Object.keys(toUpdate).length > 0) {
            result.push(
              control.txFactory.createTxMixin(employee._id, employee._class, employee.space, hr.mixin.Staff, toUpdate)
            )
          }
        }
      }

      // Create new AssignedAsset on assignment
      if (request.type === hr.ids.AssetAssignment && payload !== undefined) {
        const now = Date.now()
        const attrs: Record<string, any> = {
          assetName: payload.assetName,
          assetTag: payload.assetTag,
          serialNumber: payload.serialNumber,
          condition: payload.condition,
          assignedDate: now
        }
        const inner = control.txFactory.createTxCreateDoc(hr.class.AssignedAsset, request.space, attrs) as any
        const outer = control.txFactory.createTxCollectionCUD(
          request.attachedToClass,
          request.attachedTo,
          request.space,
          'assets',
          inner
        )
        result.push(outer)
      }

      // Set returnDate on existing AssignedAsset
      if (request.type === hr.ids.AssetReturn && payload !== undefined) {
        const assetId = payload.assignedAssetId as Ref<Doc> | undefined
        if (assetId) {
          const asset = (await control.findAll(control.ctx, hr.class.AssignedAsset, { _id: assetId }))[0]
          if (asset !== undefined) {
            const inner = control.txFactory.createTxUpdateDoc(
              hr.class.AssignedAsset,
              (asset as any).space,
              (asset as any)._id as any,
              ({ returnDate: Date.now() } as any)
            ) as any
            const outer = control.txFactory.createTxCollectionCUD(
              (asset as any).attachedToClass,
              (asset as any).attachedTo,
              (asset as any).space,
              (asset as any).collection,
              inner as any
            ) as any
            result.push(outer)
          }
        }
      }

      // Handle time-off request approval - deduct from balance
      const { isTimeOffRequestType, getPolicyForRequestType, getOrCreateBalance, postTransaction, updateBalance, hassSufficientBalance } = await import('./timeoff-helpers')
      
      if (isTimeOffRequestType(request.type)) {
        const policy = await getPolicyForRequestType(request.type, control)
        
        if (policy !== undefined) {
          const balance = await getOrCreateBalance(request.attachedTo, policy._id, control)
          
          if (balance !== undefined && request.requestedDays != null) {
            const daysToDeduct = request.requestedDays
            
            // Enforce waiting period before approving
            try {
              const staff = (await control.findAll(control.ctx, hr.mixin.Staff, { _id: request.attachedTo as any }))[0] as Staff | undefined
              if (staff?.hireDate != null && policy.waitingPeriodDays != null) {
                const daysSinceHire = Math.floor((Date.now() - staff.hireDate) / (1000 * 60 * 60 * 24))
                if (daysSinceHire < policy.waitingPeriodDays) {
                  console.warn(
                    `Time-off request ${request._id} is within waiting period: ${daysSinceHire} < ${policy.waitingPeriodDays}`
                  )
                  return result
                }
              }
            } catch (err) {
              console.warn('Failed to evaluate waiting period for time-off request', { requestId: request._id, err })
            }
            
            // Check if sufficient balance (respects allowNegativeBalance policy)
            if (!hassSufficientBalance(balance, daysToDeduct, policy)) {
              console.warn(`Insufficient balance for request ${request._id}: has ${balance.balance}, needs ${daysToDeduct}`)
            } else {
              // Post usage transaction
              const txn = await postTransaction(
                request.attachedTo,
                policy._id,
                TimeOffTransactionKind.Usage,
                -daysToDeduct,  // Negative for usage
                fromTzDate(request.tzDate),
                control,
                {
                  sourceRequest: request._id,
                  note: `Time-off usage: ${request.description}`,
                  recordedBy: ctx.operations.approvedBy
                }
              )
              result.push(txn)
              
              // Update balance - deduct from both balance and pending
              const balanceUpdateTx = await updateBalance(
                balance,
                -daysToDeduct,  // Reduce balance
                -daysToDeduct,  // Reduce pending
                control
              )
              result.push(balanceUpdateTx)
            }
          }
        }
      }
    } catch (err) {
      console.warn('Failed to auto-apply approved request', { requestId: ctx.objectId, err })
    }

    // Notify requester
    await sendEmailNotifications(control, ctx.modifiedBy, request, request.department, hr.ids.RequestApprovedNotification)
  }

  return result
}

/**
 * Trigger when request is rejected
 */
export async function OnRequestRejected (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  
  for (const tx of txes) {
    const ctx = tx as TxUpdateDoc<Request>
    
    if (ctx.operations.status !== RequestStatus.Rejected) continue
    
    const request = (await control.findAll(control.ctx, hr.class.Request, { _id: ctx.objectId }))[0]
    if (request === undefined) continue

    // Validate approver has permission
    const approver = ctx.operations.approvedBy
    if (approver) {
      const canApprove = await canApproveRequest(approver, request.department, control)
      if (!canApprove) {
        console.warn(`Employee ${approver} is not authorized to reject requests for department ${request.department}`)
      }
    }

    // Send notification to requester
    await sendEmailNotifications(control, ctx.modifiedBy, request, request.department, hr.ids.RequestRejectedNotification)
    
    // Restore pending balance for time-off requests
    try {
      const { isTimeOffRequestType, getPolicyForRequestType, getOrCreateBalance, updateBalance } = await import('./timeoff-helpers')
      
      if (isTimeOffRequestType(request.type) && request.requestedDays != null) {
        const policy = await getPolicyForRequestType(request.type, control)
        
        if (policy !== undefined) {
          const balance = await getOrCreateBalance(request.attachedTo, policy._id, control)
          
          if (balance !== undefined) {
            // Remove from pending balance (request was rejected, not deducted)
            const balanceUpdateTx = await updateBalance(
              balance,
              0,  // Don't change actual balance
              -request.requestedDays,  // Decrease pending
              control
            )
            result.push(balanceUpdateTx)
          }
        }
      }
    } catch (err) {
      console.warn('Failed to restore pending balance for rejected request', { requestId: ctx.objectId, err })
    }
  }

  return result
}

/**
 * Trigger when request status changes to pending (submitted)
 */
export async function OnRequestSubmitted (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  
  for (const tx of txes) {
    const ctx = tx as TxUpdateDoc<Request>
    
    if (ctx.operations.status !== RequestStatus.Pending) continue
    
    const request = (await control.findAll(control.ctx, hr.class.Request, { _id: ctx.objectId }))[0]
    if (request === undefined) continue

    // Notify approvers
    await notifyApprovers(request, control, ctx.modifiedBy)
    
    // Track pending balance for time-off requests and auto-approve when allowed by policy
    try {
      const { isTimeOffRequestType, getPolicyForRequestType, getOrCreateBalance, updateBalance, hassSufficientBalance } = await import('./timeoff-helpers')
      
      if (isTimeOffRequestType(request.type) && request.requestedDays != null) {
        const policy = await getPolicyForRequestType(request.type, control)
        
        if (policy !== undefined) {
          const balance = await getOrCreateBalance(request.attachedTo, policy._id, control)
          
          if (balance !== undefined) {
            const requestedDays = request.requestedDays

            // Enforce sufficient balance (unless policy allows negative)
            if (!hassSufficientBalance(balance, requestedDays, policy)) {
              console.warn(
                `Insufficient balance to reserve pending for request ${request._id}: has ${balance.balance}, pending ${balance.pending}, needs ${requestedDays}`
              )
            } else {
              // Increase pending balance (reserved but not yet deducted)
              const balanceUpdateTx = await updateBalance(
                balance,
                0,  // Don't change actual balance yet
                requestedDays,  // Increase pending
                control
              )
              result.push(balanceUpdateTx)

              // Auto-approve small requests when policy allows it
              if (policy.autoApprove && requestedDays != null) {
                const maxDays = policy.autoApproveMaxDays ?? requestedDays
                if (requestedDays <= maxDays) {
                  const approveTx = control.txFactory.createTxUpdateDoc(
                    hr.class.Request,
                    core.space.Workspace,
                    ctx.objectId,
                    ({ status: RequestStatus.Approved } as any)
                  )
                  result.push(approveTx)
                }
              }
            }
          }
        }
      }
    } catch (err) {
      console.warn('Failed to update pending balance for request', { requestId: ctx.objectId, err })
    }
  }

  return result
}

/**
 * Trigger when a TimeOffPolicy is created - create balances for all eligible staff
 */
export async function OnTimeOffPolicyCreate (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  
  for (const tx of txes) {
    const ctx = tx as TxCreateDoc<any>
    const policy = TxProcessor.createDoc2Doc(ctx)
    
    try {
      // Find eligible staff (by department if specified, else all active staff)
      const query = policy.department != null
        ? { department: policy.department }
        : {}
      
      const staff = await control.findAll(control.ctx, hr.mixin.Staff, query)
      
      for (const employee of staff) {
        // Check if balance already exists
        const existing = await control.findAll(control.ctx, hr.class.TimeOffBalance, {
          staff: employee._id,
          policy: policy._id
        })
        
        if (existing.length === 0) {
          // Create new TimeOffBalance
          const balanceTx = control.txFactory.createTxCreateDoc(
            hr.class.TimeOffBalance,
            core.space.Workspace,
            {
              staff: employee._id,
              policy: policy._id,
              balance: 0,  // Will be set by first accrual
              pending: 0,
              carryover: 0,
              effectiveDate: Date.now()
            }
          )
          result.push(balanceTx)
        }
      }
    } catch (err) {
      console.warn('Failed to create balances for new TimeOffPolicy', { policyId: policy._id, err })
    }
  }
  
  return result
}

/**
 * Trigger when Staff mixin is created - create balances for all active policies
 */
export async function OnStaffCreated (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  
  for (const tx of txes) {
    const ctx = tx as TxMixin<any, any>
    
    // Only handle new Staff mixin creation
    if (ctx.mixin !== hr.mixin.Staff) continue
    
    const staffId = ctx.objectId
    const staffData = ctx.attributes as any
    const department = staffData.department
    
    try {
      // Find all active policies
      const allPolicies = await control.findAll(control.ctx, hr.class.TimeOffPolicy, { active: true })
      
      // Filter policies that apply to this employee
      const applicablePolicies = allPolicies.filter(policy => {
        // If policy has no department restriction, it applies to all
        if (policy.department == null) return true
        // If policy is for employee's department
        if (policy.department === department) return true
        return false
      })
      
      for (const policy of applicablePolicies) {
        // Check if balance already exists
        const existing = await control.findAll(control.ctx, hr.class.TimeOffBalance, {
          staff: staffId,
          policy: policy._id
        })
        
        if (existing.length === 0) {
          // Create new TimeOffBalance
          const balanceTx = control.txFactory.createTxCreateDoc(
            hr.class.TimeOffBalance,
            core.space.Workspace,
            {
              staff: staffId,
              policy: policy._id,
              balance: 0,  // Will be set by first accrual
              pending: 0,
              carryover: 0,
              effectiveDate: Date.now()
            }
          )
          result.push(balanceTx)
        }
      }
    } catch (err) {
      console.warn('Failed to create balances for new staff', { staffId, err })
    }
  }
  
  return result
}

export async function OnUserStatus (txes: Tx[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  
  // We only need to run this once per batch, and only if there's a relevant status change
  let shouldRun = false
  
  for (const tx of txes) {
    const cud = tx as TxCUD<any>
    if (cud.objectClass !== core.class.UserStatus) continue
    
    // Run on any status change (online/offline) to ensure we catch the first activity of the day
    shouldRun = true
    break
  }
  
  if (shouldRun) {
    try {
      const { runDailyAccruals, runYearEndCarryover } = await import('./timeoff-helpers')
      
      // Run daily accruals
      const accrualTxes = await runDailyAccruals(control)
      result.push(...accrualTxes)
      
      // Run year-end carryover (checks date internally)
      const carryoverTxes = await runYearEndCarryover(control)
      result.push(...carryoverTxes)
      
    } catch (err) {
      console.error('Failed to run scheduled time-off tasks', err)
    }
  }
  
  return result
}

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export default async () => ({
  trigger: {
    OnEmployee,
    OnRequestCreate,
    OnRequestUpdate,
    OnRequestRemove,
    OnDepartmentStaff,
    OnDepartmentRemove,
    OnEmployeeDeactivate,
    OnPublicHolidayCreate,
    OnRequestApproved,
    OnRequestRejected,
    OnRequestSubmitted,
    OnTimeOffPolicyCreate,
    OnStaffCreated,
    OnUserStatus
  },
  function: {
    RequestHTMLPresenter,
    RequestTextPresenter,
    PublicHolidayHTMLPresenter,
    PublicHolidayTextPresenter
  }
})
