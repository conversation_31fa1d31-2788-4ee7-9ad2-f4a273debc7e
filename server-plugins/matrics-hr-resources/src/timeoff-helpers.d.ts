import type { Ref, Tx } from '@hcengineering/core';
import type { TriggerControl } from '@hcengineering/server-core';
import { type Staff, type TimeOffPolicy, type TimeOffBalance, TimeOffTransactionKind } from '@hcengineering/matrics-hr';
/**
 * Get or create a TimeOffBalance for a staff member and policy
 */
export declare function getOrCreateBalance(staffId: Ref<Staff>, policyId: Ref<TimeOffPolicy>, control: TriggerControl): Promise<TimeOffBalance | undefined>;
/**
 * Post a time-off transaction
 */
export declare function postTransaction(staffId: Ref<Staff>, policyId: Ref<TimeOffPolicy>, kind: TimeOffTransactionKind, amount: number, effectiveDate: number, control: TriggerControl, opts?: {
    sourceRequest?: Ref<any>;
    note?: string;
    recordedBy?: Ref<any>;
}): Promise<Tx>;
/**
 * Update a balance by amount
 */
export declare function updateBalance(balance: TimeOffBalance, deltaBalance: number, deltaPending: number, control: TriggerControl): Promise<Tx>;
/**
 * Check if a request type is a time-off request
 */
export declare function isTimeOffRequestType(typeId: Ref<any>): boolean;
/**
 * Find the matching TimeOffPolicy for a request type
 */
export declare function getPolicyForRequestType(requestTypeId: Ref<any>, control: TriggerControl): Promise<TimeOffPolicy | undefined>;
/**
 * Calculate accrual amount for an employee based on policy
 */
export declare function calculateAccrualAmount(employee: Staff, policy: TimeOffPolicy, currentDate: Date): number;
/**
 * Check if balance is sufficient for request (respecting policy)
 */
export declare function hassSufficientBalance(balance: TimeOffBalance, requestedDays: number, policy: TimeOffPolicy): boolean;
/**
 * Apply carryover limits at year-end
 */
export declare function applyCarryoverLimits(currentBalance: number, policy: TimeOffPolicy): {
    carryover: number;
    expired: number;
};
/**
 * Run daily accruals across all active policies and eligible staff
 */
export declare function runDailyAccruals(control: TriggerControl, now?: Date): Promise<Tx[]>;
/**
 * Run year-end carryover processing across all balances and policies
 */
export declare function runYearEndCarryover(control: TriggerControl, year?: number): Promise<Tx[]>;
//# sourceMappingURL=timeoff-helpers.d.ts.map