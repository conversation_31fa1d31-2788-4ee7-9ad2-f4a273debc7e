//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import type { Ref, Tx } from '@hcengineering/core'
import type { TriggerControl } from '@hcengineering/server-core'
import hr, {
  type Staff,
  type TimeOffPolicy,
  type TimeOffBalance,
  type TimeOffTransaction,
  TimeOffAccrualMethod,
  TimeOffAccrualFrequency,
  TimeOffTransactionKind
} from '@hcengineering/matrics-hr'
import core from '@hcengineering/core'

/**
 * Get or create a TimeOffBalance for a staff member and policy
 */
export async function getOrCreateBalance (
  staffId: Ref<Staff>,
  policyId: Ref<TimeOffPolicy>,
  control: TriggerControl
): Promise<TimeOffBalance | undefined> {
  const existing = (
    await control.findAll(control.ctx, hr.class.TimeOffBalance, {
      staff: staffId,
      policy: policyId
    })
  )[0]

  if (existing !== undefined) return existing

  // Create new balance
  const now = Date.now()
  return {
    _id: `${staffId}_${policyId}` as Ref<TimeOffBalance>,
    _class: hr.class.TimeOffBalance,
    space: core.space.Workspace,
    modifiedOn: now,
    modifiedBy: control.txFactory.account,
    createdOn: now,
    createdBy: control.txFactory.account,
    staff: staffId,
    policy: policyId,
    balance: 0,
    pending: 0,
    carryover: 0,
    effectiveDate: now
  } as TimeOffBalance
}

/**
 * Post a time-off transaction
 */
export async function postTransaction (
  staffId: Ref<Staff>,
  policyId: Ref<TimeOffPolicy>,
  kind: TimeOffTransactionKind,
  amount: number,
  effectiveDate: number,
  control: TriggerControl,
  opts?: {
    sourceRequest?: Ref<any>
    note?: string
    recordedBy?: Ref<any>
  }
): Promise<Tx> {
  return control.txFactory.createTxCreateDoc(
    hr.class.TimeOffTransaction,
    core.space.Workspace,
    {
      staff: staffId,
      policy: policyId,
      kind,
      amount,
      effectiveDate,
      sourceRequest: opts?.sourceRequest,
      note: opts?.note,
      recordedBy: opts?.recordedBy
    }
  )
}

/**
 * Update a balance by amount
 */
export async function updateBalance (
  balance: TimeOffBalance,
  deltaBalance: number,
  deltaPending: number,
  control: TriggerControl
): Promise<Tx> {
  return control.txFactory.createTxUpdateDoc(
    hr.class.TimeOffBalance,
    core.space.Workspace,
    balance._id,
    {
      balance: balance.balance + deltaBalance,
      pending: Math.max(0, balance.pending + deltaPending)
    }
  )
}

/**
 * Check if a request type is a time-off request
 */
export function isTimeOffRequestType (typeId: Ref<any>): boolean {
  return [
    hr.ids.PTO,
    hr.ids.PTO2,
    hr.ids.Vacation,
    hr.ids.Sick,
    hr.ids.Remote,
    hr.ids.Overtime,
    hr.ids.Overtime2
  ].includes(typeId)
}

/**
 * Find the matching TimeOffPolicy for a request type
 */
export async function getPolicyForRequestType (
  requestTypeId: Ref<any>,
  control: TriggerControl
): Promise<TimeOffPolicy | undefined> {
  const policies = await control.findAll(control.ctx, hr.class.TimeOffPolicy, {
    requestType: requestTypeId,
    active: true
  })
  return policies[0]
}

/**
 * Calculate accrual amount for an employee based on policy
 */
export function calculateAccrualAmount (
  employee: Staff,
  policy: TimeOffPolicy,
  currentDate: Date
): number {
  if (policy.accrualMethod === TimeOffAccrualMethod.None) {
    return 0
  }

  // Check waiting period
  if (policy.waitingPeriodDays != null && employee.hireDate != null) {
    const daysSinceHire = Math.floor((currentDate.getTime() - employee.hireDate) / (1000 * 60 * 60 * 24))
    if (daysSinceHire < policy.waitingPeriodDays) {
      return 0
    }
  }

  // For lump_sum, grant full amount on anniversary
  if (policy.accrualMethod === TimeOffAccrualMethod.LumpSum) {
    if (!shouldAccrueThisPeriod(employee, policy, currentDate)) {
      return 0
    }
    return policy.accrualRate ?? 0
  }

  // For periodic, check if this period should accrue
  if (policy.accrualMethod === TimeOffAccrualMethod.Periodic) {
    if (!shouldAccrueThisPeriod(employee, policy, currentDate)) {
      return 0
    }
    return policy.accrualRate ?? 0
  }

  // For hourly, calculate based on hours worked
  // This would need time tracking integration - simplified for now
  if (policy.accrualMethod === TimeOffAccrualMethod.Hourly) {
    const hoursPerWeek = employee.workHoursPerWeek ?? 40
    const accrualPerHour = (policy.accrualRate ?? 0) / 2080 // Assuming 2080 work hours/year
    const hoursThisPeriod = getHoursForPeriod(policy.accrualFrequency, hoursPerWeek)
    return accrualPerHour * hoursThisPeriod
  }

  return 0
}

/**
 * Check if accrual should happen this period
 */
function shouldAccrueThisPeriod (employee: Staff, policy: TimeOffPolicy, currentDate: Date): boolean {
  // Use employee hire date as default start if no policy start configured
  const startMonth = policy.accrualStartMonth ?? (employee.hireDate ? new Date(employee.hireDate).getMonth() : 0)
  const startDay = policy.accrualStartDay ?? (employee.hireDate ? new Date(employee.hireDate).getDate() : 1)
  
  if (employee.hireDate == null) return false
  const start = new Date(employee.hireDate)
  const frequency = policy.accrualFrequency

  if (frequency === TimeOffAccrualFrequency.Anniversary) {
    // Check if it's the anniversary month
    return currentDate.getMonth() === start.getMonth() && currentDate.getDate() === start.getDate()
  }

  if (frequency === TimeOffAccrualFrequency.Yearly) {
    // Accrue on January 1st
    return currentDate.getMonth() === 0 && currentDate.getDate() === 1
  }

  if (frequency === TimeOffAccrualFrequency.Monthly) {
    // Accrue on the 1st of each month
    return currentDate.getDate() === 1
  }

  if (frequency === TimeOffAccrualFrequency.Quarterly) {
    // Accrue on Jan 1, Apr 1, Jul 1, Oct 1
    const month = currentDate.getMonth()
    return currentDate.getDate() === 1 && (month === 0 || month === 3 || month === 6 || month === 9)
  }

  // For per_pay_period, would need payroll integration
  // Defaulting to bi-weekly for now
  if (frequency === TimeOffAccrualFrequency.PerPayPeriod) {
    const daysSinceStart = Math.floor((currentDate.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    return daysSinceStart % 14 === 0 // Bi-weekly
  }

  return false
}

/**
 * Get hours for accrual period
 */
function getHoursForPeriod (frequency: TimeOffAccrualFrequency | undefined, hoursPerWeek: number): number {
  switch (frequency) {
    case TimeOffAccrualFrequency.Monthly:
      return (hoursPerWeek * 52) / 12 // Average hours per month
    case TimeOffAccrualFrequency.Quarterly:
      return (hoursPerWeek * 52) / 4 // Hours per quarter
    case TimeOffAccrualFrequency.Yearly:
    case TimeOffAccrualFrequency.Anniversary:
      return hoursPerWeek * 52 // Hours per year
    case TimeOffAccrualFrequency.PerPayPeriod:
      return hoursPerWeek * 2 // Assuming bi-weekly
    default:
      return hoursPerWeek * 2 // Default bi-weekly
  }
}

/**
 * Check if balance is sufficient for request (respecting policy)
 */
export function hassSufficientBalance (
  balance: TimeOffBalance,
  requestedDays: number,
  policy: TimeOffPolicy
): boolean {
  const availableBalance = balance.balance - balance.pending
  if (policy.allowNegativeBalance === true) {
    return true // Allow any amount
  }
  return availableBalance >= requestedDays
}

/**
 * Apply carryover limits at year-end
 */
export function applyCarryoverLimits (
  currentBalance: number,
  policy: TimeOffPolicy
): { carryover: number; expired: number } {
  if (policy.carryoverLimit == null) {
    // No limit, carry over everything
    return { carryover: currentBalance, expired: 0 }
  }

  const carryover = Math.min(currentBalance, policy.carryoverLimit)
  const expired = Math.max(0, currentBalance - policy.carryoverLimit)

  return { carryover, expired }
}

export async function runDailyAccruals (
  control: TriggerControl,
  now: Date = new Date()
): Promise<Tx[]> {
  const result: Tx[] = []

  const policies = await control.findAll(control.ctx, hr.class.TimeOffPolicy, { active: true })
  if (policies.length === 0) return result

  const policyById = new Map<Ref<TimeOffPolicy>, TimeOffPolicy>()
  for (const policy of policies) {
    policyById.set(policy._id as Ref<TimeOffPolicy>, policy)
  }

  const balances = await control.findAll(control.ctx, hr.class.TimeOffBalance, {})
  if (balances.length === 0) return result

  const staffIds = Array.from(new Set(balances.map((b) => b.staff)))
  const staff = await control.findAll(control.ctx, hr.mixin.Staff, { _id: { $in: staffIds as any[] } })
  const staffById = new Map<Ref<Staff>, Staff>()
  for (const s of staff) {
    staffById.set(s._id as Ref<Staff>, s)
  }

  const nowMs = now.getTime()

  for (const balance of balances) {
    const policy = policyById.get(balance.policy as Ref<TimeOffPolicy>)
    if (policy === undefined || policy.active !== true) continue

    const employee = staffById.get(balance.staff as Ref<Staff>)
    if (employee === undefined) continue

    if (balance.lastAccruedAt != null) {
      const last = new Date(balance.lastAccruedAt)
      if (
        last.getFullYear() === now.getFullYear() &&
        last.getMonth() === now.getMonth() &&
        last.getDate() === now.getDate()
      ) {
        continue
      }
    }

    let amount = calculateAccrualAmount(employee, policy, now)
    if (amount <= 0) continue

    if (policy.accrualCap != null) {
      const max = policy.accrualCap
      const current = balance.balance
      if (current >= max) continue
      const allowed = max - current
      if (allowed <= 0) continue
      if (amount > allowed) {
        amount = allowed
      }
    }

    const txn = await postTransaction(
      balance.staff as Ref<Staff>,
      balance.policy as Ref<TimeOffPolicy>,
      TimeOffTransactionKind.Accrual,
      amount,
      nowMs,
      control,
      {
        note: 'Automatic time-off accrual'
      }
    )
    result.push(txn)

    const updateTx = control.txFactory.createTxUpdateDoc(
      hr.class.TimeOffBalance,
      core.space.Workspace,
      balance._id,
      {
        balance: balance.balance + amount,
        pending: balance.pending,
        carryover: balance.carryover,
        lastAccruedAt: nowMs
      } as any
    )
    result.push(updateTx)
  }

  return result
}

export async function runYearEndCarryover (
  control: TriggerControl,
  year?: number
): Promise<Tx[]> {
  const result: Tx[] = []
  const now = new Date()
  const targetYear = year ?? now.getFullYear()
  const effectiveDate = new Date(targetYear + 1, 0, 1).getTime()

  const policies = await control.findAll(control.ctx, hr.class.TimeOffPolicy, { active: true })
  if (policies.length === 0) return result

  const policyById = new Map<Ref<TimeOffPolicy>, TimeOffPolicy>()
  for (const policy of policies) {
    policyById.set(policy._id as Ref<TimeOffPolicy>, policy)
  }

  const balances = await control.findAll(control.ctx, hr.class.TimeOffBalance, {})
  if (balances.length === 0) return result

  for (const balance of balances) {
    const policy = policyById.get(balance.policy as Ref<TimeOffPolicy>)
    if (policy === undefined || policy.active !== true) continue

    const current = balance.balance
    if (current <= 0) continue

    const { carryover, expired } = applyCarryoverLimits(current, policy)

    if (expired > 0) {
      const expirationTx = await postTransaction(
        balance.staff as Ref<Staff>,
        balance.policy as Ref<TimeOffPolicy>,
        TimeOffTransactionKind.Expiration,
        -expired,
        effectiveDate,
        control,
        { note: 'Year-end expiration' }
      )
      result.push(expirationTx)
    }

    if (carryover !== current || balance.carryover !== carryover) {
      const carryoverTx = await postTransaction(
        balance.staff as Ref<Staff>,
        balance.policy as Ref<TimeOffPolicy>,
        TimeOffTransactionKind.Carryover,
        carryover,
        effectiveDate,
        control,
        { note: 'Year-end carryover' }
      )
      result.push(carryoverTx)
    }

    const updateTx = control.txFactory.createTxUpdateDoc(
      hr.class.TimeOffBalance,
      core.space.Workspace,
      balance._id,
      {
        balance: carryover,
        pending: balance.pending,
        carryover,
        lastAccruedAt: balance.lastAccruedAt
      } as any
    )
    result.push(updateTx)
  }

  return result
}
