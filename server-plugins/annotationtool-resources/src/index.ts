//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import chunter, { ChatMessage } from '@hcengineering/chunter'
import core, {
  concatLink,
  Doc,
  Ref,
  Tx,
  TxCreateDoc,
  TxCUD,
  TxProcessor,
  type MeasureContext
} from '@hcengineering/core'
import notification, { getClassCollaborators, NotificationContent } from '@hcengineering/notification'
import { IntlString } from '@hcengineering/platform'
import { getAccountBySocialId } from '@hcengineering/server-contact'
import { TriggerControl } from '@hcengineering/server-core'
import { NOTIFICATION_BODY_SIZE } from '@hcengineering/server-notification'
import {
  createCollaboratorNotifications,
  getAddCollaboratTxes,
  getDocCollaborators
} from '@hcengineering/server-notification-resources'
import { stripTags } from '@hcengineering/text-core'
import annotationTool, { AnnotationActivity } from '@hcengineering/annotationtool'
import tracker, { Project } from '@hcengineering/tasker'
import { workbenchId } from '@hcengineering/workbench'

/**
 * @public
 */
export function annotationActivityHTMLPresenter (doc: Doc, control: TriggerControl): string {
  const annotationActivity = doc as AnnotationActivity
  const front = control.branding?.front ?? ''
  const path = `${workbenchId}/${control.workspace.url}/${annotationTool.app.AnnotationToolApp}/${annotationActivity._id}`
  const link = concatLink(front, path)
  return `<a href="${link}">${annotationActivity.episodeId ?? annotationActivity._id}</a>`
}

/**
 * @public
 */
export function annotationActivityTextPresenter (doc: Doc): { episodeOrStrip: string, id: string } {
  const annotationActivity = doc as AnnotationActivity
  return {
    episodeOrStrip: annotationActivity?.stripId != null ? 'strip' : 'episode',
    id: annotationActivity?.stripId ?? annotationActivity?.episodeId ?? ''
  }
}

/**
 * @public
 */
export async function getAnnotationActivityNotificationContent (
  doc: Doc,
  tx: TxCUD<Doc>,
  target: Ref<any>,
  control: TriggerControl
): Promise<NotificationContent> {
  const title = annotationTool.string.AnnotationActivityTitle
  const body = annotationTool.string.AnnotationActivityNotificationBody
  const intlParams: Record<string, string | number> = {
    ...annotationActivityTextPresenter(doc)
  }
  const intlParamsNotLocalized: Record<string, IntlString> = {}

  if (tx._class === core.class.TxCreateDoc) {
    if (tx.objectClass === chunter.class.ChatMessage) {
      const createTx = tx as TxCreateDoc<ChatMessage>
      const message = createTx.attributes.message
      const plainTextMessage = stripTags(message, NOTIFICATION_BODY_SIZE)
      intlParams.message = plainTextMessage
    }
  }

  return {
    title,
    body,
    intlParams,
    intlParamsNotLocalized
  }
}

/**
 * @public
 * Auto-add all project members as collaborators when AnnotationActivity is created
 */
export async function OnAnnotationActivityCreate (txes: TxCreateDoc<AnnotationActivity>[], control: TriggerControl): Promise<Tx[]> {
  const res: Tx[] = []

  for (const tx of txes) {
    const activity = TxProcessor.createDoc2Doc(tx)

    if (activity.episodeId == null && activity.stripId == null) {
      return []
    }

    // Get the project (space) for this activity
    const project = await control.findAll(control.ctx, tracker.class.Project, { _id: activity.projectId }, { limit: 1 })
    if (project.length === 0) continue

    const projectSpace = project[0] as Project

    // Add all project members as Collaborator documents (not just mixin)
    if (projectSpace.members.length > 0) {
      // Create Collaborator documents for each member
      res.push(...getAddCollaboratTxes(activity._id, activity._class, activity.space, control, projectSpace.members))

      // Also update the Collaborators mixin for compatibility
      res.push(
        control.txFactory.createTxMixin(
          activity._id,
          activity._class,
          activity.space,
          notification.mixin.Collaborators,
          {
            $push: {
              collaborators: { $each: projectSpace.members }
            }
          }
        )
      )

      // Try to find person by account first
      // const { contextData } = control.ctx
      // const account: AccountUuid | undefined = contextData.account.socialIds.includes(tx.modifiedBy)
      //   ? contextData.account.uuid
      //   : contextData.socialStringsToUsers.get(tx.modifiedBy)?.accontUuid

      // let senderPerson: Person | undefined

      // if (account !== undefined) {
      //   senderPerson = (await control.findAll(control.ctx, contact.class.Person, { personUuid: account }, { limit: 1 }))[0]
      // } else {
      //   // Try to find by social identity
      //   const socialId = (await control.findAll(control.ctx, contact.class.SocialIdentity, { key: tx.modifiedBy }, { limit: 1 }))[0]

      //   if (socialId !== undefined) {
      //     senderPerson = (await control.findAll(control.ctx, contact.class.Person, { _id: socialId.attachedTo }, { limit: 1 }))[0]
      //   }
      // }

      // const senderName = senderPerson?.name?.split(',')?.reverse()?.join(' ') ?? 'System'

      // // Create notifications for each project member
      // for (const memberId of projectSpace.members) {
      //   // Create DocNotifyContext for each member
      //   const docNotifyContextTx = control.txFactory.createTxCreateDoc(
      //     notification.class.DocNotifyContext,
      //     activity.space,
      //     {
      //       user: memberId,
      //       objectId: activity._id,
      //       objectClass: activity._class,
      //       objectSpace: activity.space,
      //       isPinned: false,
      //       hidden: false
      //     }
      //   )
      //   res.push(docNotifyContextTx)

      //   // Create CommonInboxNotification for each member
      //   const notificationTx = control.txFactory.createTxCreateDoc(
      //     notification.class.CommonInboxNotification,
      //     activity.space,
      //     {
      //       user: memberId,
      //       objectId: activity._id,
      //       objectClass: activity._class,
      //       docNotifyContext: docNotifyContextTx.objectId,
      //       header: annotationTool.string.AnnotationActivityTitle,
      //       message: annotationTool.string.AnnotationActivityNotificationBody,
      //       intlParams: annotationActivityTextPresenter(activity),
      //       props: {
      //         senderName
      //       },
      //       intlParamsNotLocalized: {},
      //       isViewed: false,
      //       archived: false
      //     }
      //   )
      //   res.push(notificationTx)
      // }
    }
  }

  return res
}

/**
 * @public
 * Handle adding collaborators from chat messages on AnnotationActivity
 */
async function OnChatMessageCreated (tx: TxCUD<Doc>, control: TriggerControl): Promise<Tx[]> {
  const hierarchy = control.hierarchy
  const actualTx = tx as TxCreateDoc<ChatMessage>

  const message = TxProcessor.createDoc2Doc(actualTx)

  // Only process messages attached to AnnotationActivity
  if (!hierarchy.isDerived(message.attachedToClass, annotationTool.class.AnnotationActivity)) {
    return []
  }

  if (message.modifiedBy === core.account.System) return []

  const mixin = getClassCollaborators(control.modelDb, hierarchy, message.attachedToClass)
  if (mixin === undefined) return []

  const targetDoc = (await control.findAll(control.ctx, message.attachedToClass, { _id: message.attachedTo }, { limit: 1 }))[0]
  if (targetDoc === undefined) return []

  const res: Tx[] = []
  const account = await getAccountBySocialId(control, message.modifiedBy)

  // Check if sender is already a collaborator
  let currentCollaborators = (
    await control.findAll(control.ctx, core.class.Collaborator, {
      attachedTo: targetDoc._id
    })
  ).map((it) => it.collaborator)

  // If no collaborators exist yet, initialize them
  if (currentCollaborators.length === 0) {
    const mixin = getClassCollaborators(control.modelDb, control.hierarchy, targetDoc._class)
    if (mixin !== undefined) {
      const collaborators = await getDocCollaborators(control.ctx, targetDoc, mixin, control)
      currentCollaborators = collaborators
      res.push(...getAddCollaboratTxes(tx.objectId, tx.objectClass, tx.objectSpace, control, collaborators))
    }
  }

  // Add sender as collaborator if not already
  if (account !== undefined && !currentCollaborators.includes(account as any)) {
    res.push(...getAddCollaboratTxes(tx.objectId, tx.objectClass, tx.objectSpace, control, [account as any]))
  }

  return res
}

/**
 * @public
 * Handle chat message notifications for AnnotationActivity
 */
export async function ChatNotificationsHandler (txes: TxCUD<Doc>[], control: TriggerControl): Promise<Tx[]> {
  const result: Tx[] = []
  for (const tx of txes) {
    const actualTx = tx as TxCreateDoc<ChatMessage>

    if (actualTx._class !== core.class.TxCreateDoc) {
      continue
    }

    const chatMessage = TxProcessor.createDoc2Doc(actualTx)

    // Only process messages attached to AnnotationActivity
    if (!control.hierarchy.isDerived(chatMessage.attachedToClass, annotationTool.class.AnnotationActivity)) {
      continue
    }

    // First, handle collaborator management
    const collaboratorTxes = await OnChatMessageCreated(tx, control)
    result.push(...collaboratorTxes)

    // Update existing DocNotifyContext timestamps for the AnnotationActivity
    const existingContexts = await control.findAll(
      control.ctx,
      notification.class.DocNotifyContext,
      { objectId: chatMessage.attachedTo }
    )

    if (existingContexts.length > 0) {
      const modifiedByAccount = await getAccountBySocialId(control, tx.modifiedBy)

      for (const context of existingContexts) {
        // Check if this context was previously viewed
        const isViewed =
          context.lastViewedTimestamp !== undefined &&
          (context.lastUpdateTimestamp ?? 0) <= context.lastViewedTimestamp

        // Update the context timestamp, and keep it viewed if the sender is viewing their own message
        const updateTx = control.txFactory.createTxUpdateDoc(context._class, context.space, context._id, {
          hidden: false,
          lastUpdateTimestamp: tx.modifiedOn,
          ...(isViewed && context.user === modifiedByAccount
            ? {
                lastViewedTimestamp: tx.modifiedOn
              }
            : {})
        })

        result.push(updateTx)
      }
    }

    // Then create notifications for collaborators
    const notifications = await createCollaboratorNotifications(control.ctx, tx, control, [chatMessage])
    result.push(...notifications)
  }
  return result
}

// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export default async () => ({
  function: {
    AnnotationActivityHTMLPresenter: annotationActivityHTMLPresenter,
    AnnotationActivityTextPresenter: annotationActivityTextPresenter,
    AnnotationActivityNotificationContentProvider: getAnnotationActivityNotificationContent
  },
  trigger: {
    OnAnnotationActivityCreate,
    ChatNotificationsHandler
  }
})
