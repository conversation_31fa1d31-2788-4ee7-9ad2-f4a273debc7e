#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

PROJECT_ROOT="$(git rev-parse --show-toplevel)"
cd "$PROJECT_ROOT" || exit 1

echo "Running rush validate before commit..."

# Ensure Git detection works in hook environment
export GIT_DIR="$(git rev-parse --git-dir)"
export GIT_WORK_TREE="$(git rev-parse --show-toplevel)"

# Run rush validate
if node common/scripts/install-run-rush.js validate --ignore-hooks; then
    echo "✓ Validation passed!"
    VALIDATION_RESULT=0
else
    echo "✗ Validation failed! Please fix TypeScript errors before committing."
    VALIDATION_RESULT=1
fi

exit $VALIDATION_RESULT
