/**
 * This is the main configuration file for <PERSON>.
 * For full documentation, please see https://rushjs.io
 */
{
  "$schema": "https://developer.microsoft.com/json-schemas/rush/v5/rush.schema.json",
  /**
   * (Required) This specifies the version of the Rush engine to be used in this repo.
   * <PERSON>'s "version selector" feature ensures that the globally installed tool will
   * behave like this release, regardless of which version is installed globally.
   *
   * The common/scripts/install-run-rush.js automation script also uses this version.
   *
   * NOTE: If you upgrade to a new major version of Rush, you should replace the "v5"
   * path segment in the "$schema" field for all your Rush config files.  This will ensure
   * correct error-underlining and tab-completion for editors such as VS Code.
   */
  "rushVersion": "5.158.1",
  /**
   * The next field selects which package manager should be installed and determines its version.
   * Rush installs its own local copy of the package manager to ensure that your build process
   * is fully isolated from whatever tools are present in the local environment.
   *
   * Specify one of: "pnpmVersion", "npmVersion", or "yarnVersion".  See the Rush documentation
   * for details about these alternatives.
   */
  "pnpmVersion": "10.15.1",
  // "npmVersion": "4.5.0",
  // "yarnVersion": "1.9.4",
  /**
   * Older releases of the Node.js engine may be missing features required by your system.
   * Other releases may have bugs.  In particular, the "latest" version will not be a
   * Long Term Support (LTS) version and is likely to have regressions.
   *
   * Specify a SemVer range to ensure developers use a Node.js version that is appropriate
   * for your repo.
   *
   * LTS schedule: https://nodejs.org/en/about/releases/
   * LTS versions: https://nodejs.org/en/download/releases/
   */
  "nodeSupportedVersionRange": ">=20.0.0 <25.0.0",
  /**
   * Odd-numbered major versions of Node.js are experimental.  Even-numbered releases
   * spend six months in a stabilization period before the first Long Term Support (LTS) version.
   * For example, 8.9.0 was the first LTS version of Node.js 8.  Pre-LTS versions are not recommended
   * for production usage because they frequently have bugs.  They may cause Rush itself
   * to malfunction.
   *
   * Rush normally prints a warning if it detects a pre-LTS Node.js version.  If you are testing
   * pre-LTS versions in preparation for supporting the first LTS version, you can use this setting
   * to disable Rush's warning.
   */
  "suppressNodeLtsWarning": true,
  /**
   * If you would like the version specifiers for your dependencies to be consistent, then
   * uncomment this line. This is effectively similar to running "rush check" before any
   * of the following commands:
   *
   *   rush install, rush update, rush link, rush version, rush publish
   *
   * In some cases you may want this turned on, but need to allow certain packages to use a different
   * version. In those cases, you will need to add an entry to the "allowedAlternativeVersions"
   * section of the common-versions.json.
   */
  "ensureConsistentVersions": false,
  /**
   * Large monorepos can become intimidating for newcomers if project folder paths don't follow
   * a consistent and recognizable pattern.  When the system allows nested folder trees,
   * we've found that teams will often use subfolders to create islands that isolate
   * their work from others ("shipping the org").  This hinders collaboration and code sharing.
   *
   * The Rush developers recommend a "category folder" model, where buildable project folders
   * must always be exactly two levels below the repo root.  The parent folder acts as the category.
   * This provides a basic facility for grouping related projects (e.g. "apps", "libraries",
   * "tools", "prototypes") while still encouraging teams to organize their projects into
   * a unified taxonomy.  Limiting to 2 levels seems very restrictive at first, but if you have
   * 20 categories and 20 projects in each category, this scheme can easily accommodate hundreds
   * of projects.  In practice, you will find that the folder hierarchy needs to be rebalanced
   * occasionally, but if that's painful, it's a warning sign that your development style may
   * discourage refactoring.  Reorganizing the categories should be an enlightening discussion
   * that brings people together, and maybe also identifies poor coding practices (e.g. file
   * references that reach into other project's folders without using Node.js module resolution).
   *
   * The defaults are projectFolderMinDepth=1 and projectFolderMaxDepth=2.
   *
   * To remove these restrictions, you could set projectFolderMinDepth=1
   * and set projectFolderMaxDepth to a large number.
   */
  // "projectFolderMinDepth": 2,
  "projectFolderMaxDepth": 3,
  /**
   * Today the npmjs.com registry enforces fairly strict naming rules for packages, but in the early
   * days there was no standard and hardly any enforcement.  A few large legacy projects are still using
   * nonstandard package names, and private registries sometimes allow it.  Set "allowMostlyStandardPackageNames"
   * to true to relax Rush's enforcement of package names.  This allows upper case letters and in the future may
   * relax other rules, however we want to minimize these exceptions.  Many popular tools use certain punctuation
   * characters as delimiters, based on the assumption that they will never appear in a package name; thus if we relax
   * the rules too much it is likely to cause very confusing malfunctions.
   *
   * The default value is false.
   */
  // "allowMostlyStandardPackageNames": true,
  /**
   * This feature helps you to review and approve new packages before they are introduced
   * to your monorepo.  For example, you may be concerned about licensing, code quality,
   * performance, or simply accumulating too many libraries with overlapping functionality.
   * The approvals are tracked in two config files "browser-approved-packages.json"
   * and "nonbrowser-approved-packages.json".  See the Rush documentation for details.
   */
  // "approvedPackagesPolicy": {
  //   /**
  //    * The review categories allow you to say for example "This library is approved for usage
  //    * in prototypes, but not in production code."
  //    *
  //    * Each project can be associated with one review category, by assigning the "reviewCategory" field
  //    * in the "projects" section of rush.json.  The approval is then recorded in the files
  //    * "common/config/rush/browser-approved-packages.json" and "nonbrowser-approved-packages.json"
  //    * which are automatically generated during "rush update".
  //    *
  //    * Designate categories with whatever granularity is appropriate for your review process,
  //    * or you could just have a single category called "default".
  //    */
  //   "reviewCategories": [
  //     // Some example categories:
  //     "production", // projects that ship to production
  //     "tools",      // non-shipping projects that are part of the developer toolchain
  //     "prototypes"  // experiments that should mostly be ignored by the review process
  //   ],
  //
  //   /**
  //    * A list of NPM package scopes that will be excluded from review.
  //    * We recommend to exclude TypeScript typings (the "@types" scope), because
  //    * if the underlying package was already approved, this would imply that the typings
  //    * are also approved.
  //    */
  //   // "ignoredNpmScopes": ["@types"]
  // },
  /**
   * If you use Git as your version control system, this section has some additional
   * optional features you can use.
   */
  "gitPolicy": {
    /**
     * Work at a big company?  Tired of finding Git commits at work with unprofessional Git
     * emails such as "<EMAIL>"?  Rush can validate people's Git email address
     * before they get started.
     *
     * Define a list of regular expressions describing allowable e-mail patterns for Git commits.
     * They are case-insensitive anchored JavaScript RegExps.  Example: ".*@example\.com"
     *
     * IMPORTANT: Because these are regular expressions encoded as JSON string literals,
     * RegExp escapes need two backslashes, and ordinary periods should be "\\.".
     */
    // "allowedEmailRegExps": [
    //   "[^@]+@users\\.noreply\\.github\\.com",
    //   "travis@example\\.org"
    // ],
    /**
     * When Rush reports that the address is malformed, the notice can include an example
     * of a recommended email.  Make sure it conforms to one of the allowedEmailRegExps
     * expressions.
     */
    // "sampleEmail": "<EMAIL>",
    /**
     * The commit message to use when committing changes during 'rush publish'.
     *
     * For example, if you want to prevent these commits from triggering a CI build,
     * you might configure your system's trigger to look for a special string such as "[skip-ci]"
     * in the commit message, and then customize Rush's message to contain that string.
     */
    // "versionBumpCommitMessage": "Applying package updates. [skip-ci]",
    /**
     * The commit message to use when committing changes during 'rush version'.
     *
     * For example, if you want to prevent these commits from triggering a CI build,
     * you might configure your system's trigger to look for a special string such as "[skip-ci]"
     * in the commit message, and then customize Rush's message to contain that string.
     */
    // "changeLogUpdateCommitMessage": "Deleting change files and updating change logs for package updates. [skip-ci]"
  },
  "repository": {
    /**
     * The URL of this Git repository, used by "rush change" to determine the base branch for your PR.
     *
     * The "rush change" command needs to determine which files are affected by your PR diff.
     * If you merged or cherry-picked commits from the master branch into your PR branch, those commits
     * should be excluded from this diff (since they belong to some other PR).  In order to do that,
     * Rush needs to know where to find the base branch for your PR.  This information cannot be
     * determined from Git alone, since the "pull request" feature is not a Git concept.  Ideally
     * Rush would use a vendor-specific protocol to query the information from GitHub, Azure DevOps, etc.
     * But to keep things simple, "rush change" simply assumes that your PR is against the "master" branch
     * of the Git remote indicated by the repository.url setting in rush.json.  If you are working in
     * a GitHub "fork" of the real repo, this setting will be different from the repository URL of your
     * your PR branch, and in this situation "rush change" will also automatically invoke "git fetch"
     * to retrieve the latest activity for the remote master branch.
     */
    "url": "https://github.com/hcengineering/anticrm",
    /**
     * The default branch name. This tells "rush change" which remote branch to compare against.
     * The default value is "master"
     */
    "defaultBranch": "main",
    /**
     * The default remote. This tells "rush change" which remote to compare against if the remote URL is
     * not set or if a remote matching the provided remote URL is not found.
     */
    "defaultRemote": "origin"
  },
  /**
   * Event hooks are customized script actions that Rush executes when specific events occur
   */
  "eventHooks": {
    /**
     * The list of shell commands to run before the Rush installation starts
     */
    "preRushInstall": [
      // "common/scripts/pre-rush-install.js"
    ],
    /**
     * The list of shell commands to run after the Rush installation finishes
     */
    "postRushInstall": [
      "node common/scripts/setup-husky.js"
    ],
    /**
     * The list of shell commands to run before the Rush build command starts
     */
    "preRushBuild": [],
    /**
     * The list of shell commands to run after the Rush build command finishes
     */
    "postRushBuild": []
  },
  /**
   * Rush can collect anonymous telemetry about everyday developer activity such as
   * success/failure of installs, builds, and other operations.  You can use this to identify
   * problems with your toolchain or Rush itself.  THIS TELEMETRY IS NOT SHARED WITH MICROSOFT.
   * It is written into JSON files in the common/temp folder.  It's up to you to write scripts
   * that read these JSON files and do something with them.  These scripts are typically registered
   * in the "eventHooks" section.
   */
  // "telemetryEnabled": false,
  /**
   * Allows creation of hotfix changes. This feature is experimental so it is disabled by default.
   * If this is set, 'rush change' only allows a 'hotfix' change type to be specified. This change type
   * will be used when publishing subsequent changes from the monorepo.
   */
  // "hotfixChangeEnabled": false,
  /**
   * (Required) This is the inventory of projects to be managed by Rush.
   *
   * Rush does not automatically scan for projects using wildcards, for a few reasons:
   * 1. Depth-first scans are expensive, particularly when tools need to repeatedly collect the list.
   * 2. On a caching CI machine, scans can accidentally pick up files left behind from a previous build.
   * 3. It's useful to have a centralized inventory of all projects and their important metadata.
   */
  "projects": [
    // {
    //   /**
    //    * The NPM package name of the project (must match package.json)
    //    */
    //   "packageName": "my-app",
    //
    //   /**
    //    * The path to the project folder, relative to the rush.json config file.
    //    */
    //   "projectFolder": "apps/my-app",
    //
    //   /**
    //    * An optional category for usage in the "browser-approved-packages.json"
    //    * and "nonbrowser-approved-packages.json" files.  The value must be one of the
    //    * strings from the "reviewCategories" defined above.
    //    */
    //   "reviewCategory": "production",
    //
    //   /**
    //    * A list of local projects that appear as devDependencies for this project, but cannot be
    //    * locally linked because it would create a cyclic dependency; instead, the last published
    //    * version will be installed in the Common folder.
    //    */
    //   "cyclicDependencyProjects": [
    //     // "my-toolchain"
    //   ],
    //
    //   /**
    //    * If true, then this project will be ignored by the "rush check" command.
    //    * The default value is false.
    //    */
    //   // "skipRushCheck": false,
    //
    //   /**
    //    * A flag indicating that changes to this project will be published to npm, which affects
    //    * the Rush change and publish workflows. The default value is false.
    //    * NOTE: "versionPolicyName" and "shouldPublish" are alternatives; you cannot specify them both.
    //    */
    //   // "shouldPublish": false,
    //
    //   /**
    //    * Facilitates postprocessing of a project's files prior to publishing.
    //    *
    //    * If specified, the "publishFolder" is the relative path to a subfolder of the project folder.
    //    * The "rush publish" command will publish the subfolder instead of the project folder.  The subfolder
    //    * must contain its own package.json file, which is typically a build output.
    //    */
    //   // "publishFolder": "temp/publish",
    //
    //   /**
    //    * An optional version policy associated with the project.  Version policies are defined
    //    * in "version-policies.json" file.  See the "rush publish" documentation for more info.
    //    * NOTE: "versionPolicyName" and "shouldPublish" are alternatives; you cannot specify them both.
    //    */
    //   // "versionPolicyName": ""
    // },
    //
    {
      "packageName": "@hcengineering/platform-rig",
      "projectFolder": "packages/platform-rig",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/platform",
      "projectFolder": "packages/platform",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/measurements",
      "projectFolder": "packages/measurements",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/measurements-otlp",
      "projectFolder": "packages/measurements-otlp",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/core",
      "projectFolder": "packages/core",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/analytics",
      "projectFolder": "packages/analytics",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/analytics-providers",
      "projectFolder": "packages/analytics-providers",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/analytics-service",
      "projectFolder": "packages/analytics-service",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/storage",
      "projectFolder": "packages/storage",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/highlight",
      "projectFolder": "packages/highlight",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/api-client",
      "projectFolder": "packages/api-client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/api-tests",
      "projectFolder": "ws-tests/api-tests",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/importer",
      "projectFolder": "packages/importer",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/collaboration",
      "projectFolder": "server/collaboration",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/hls",
      "projectFolder": "packages/hls",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/theme",
      "projectFolder": "packages/theme",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/text-core",
      "projectFolder": "packages/text-core",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/text-markdown",
      "projectFolder": "packages/text-markdown",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/text-html",
      "projectFolder": "packages/text-html",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/text",
      "projectFolder": "packages/text",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/text-ydoc",
      "projectFolder": "packages/text-ydoc",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/ui",
      "projectFolder": "packages/ui",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/uploader",
      "projectFolder": "plugins/uploader",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/uploader-assets",
      "projectFolder": "plugins/uploader-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/uploader-resources",
      "projectFolder": "plugins/uploader-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-uploader",
      "projectFolder": "models/uploader",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/media",
      "projectFolder": "plugins/media",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/media-resources",
      "projectFolder": "plugins/media-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/media-assets",
      "projectFolder": "plugins/media-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-media",
      "projectFolder": "models/media",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/recorder",
      "projectFolder": "plugins/recorder",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/recorder-resources",
      "projectFolder": "plugins/recorder-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/recorder-assets",
      "projectFolder": "plugins/recorder-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-recorder",
      "projectFolder": "models/recorder",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/presence",
      "projectFolder": "plugins/presence",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/presence-resources",
      "projectFolder": "plugins/presence-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-presence",
      "projectFolder": "models/presence",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/collaborator-client",
      "projectFolder": "packages/collaborator-client",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/account-client",
      "projectFolder": "packages/account-client",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/billing-client",
      "projectFolder": "packages/billing-client",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/prod",
      "projectFolder": "dev/prod",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-core",
      "projectFolder": "server/core",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-indexer",
      "projectFolder": "server/indexer",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-token",
      "projectFolder": "server/token",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/server",
      "projectFolder": "server/server",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-storage",
      "projectFolder": "server/server-storage",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-pipeline",
      "projectFolder": "server/server-pipeline",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/login",
      "projectFolder": "plugins/login",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/login-assets",
      "projectFolder": "plugins/login-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/login-resources",
      "projectFolder": "plugins/login-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/onboard",
      "projectFolder": "plugins/onboard",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/onboard-assets",
      "projectFolder": "plugins/onboard-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/onboard-resources",
      "projectFolder": "plugins/onboard-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/workbench",
      "projectFolder": "plugins/workbench",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/workbench-assets",
      "projectFolder": "plugins/workbench-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/workbench-resources",
      "projectFolder": "plugins/workbench-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/presentation",
      "projectFolder": "packages/presentation",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-presentation",
      "projectFolder": "models/presentation",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/client",
      "projectFolder": "plugins/client",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/client-resources",
      "projectFolder": "plugins/client-resources",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/query",
      "projectFolder": "packages/query",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/rank",
      "projectFolder": "packages/rank",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/view",
      "projectFolder": "plugins/view",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/view-assets",
      "projectFolder": "plugins/view-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/view-resources",
      "projectFolder": "plugins/view-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model",
      "projectFolder": "packages/model",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/model-core",
      "projectFolder": "models/core",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-all",
      "projectFolder": "models/all",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-view",
      "projectFolder": "models/view",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/contact",
      "projectFolder": "plugins/contact",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/contact-assets",
      "projectFolder": "plugins/contact-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/contact-resources",
      "projectFolder": "plugins/contact-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/task",
      "projectFolder": "plugins/task",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/task-assets",
      "projectFolder": "plugins/task-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/task-resources",
      "projectFolder": "plugins/task-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-workbench",
      "projectFolder": "models/workbench",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-task",
      "projectFolder": "models/task",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-contact",
      "projectFolder": "models/contact",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/text-editor",
      "projectFolder": "plugins/text-editor",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/text-editor-assets",
      "projectFolder": "plugins/text-editor-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/text-editor-resources",
      "projectFolder": "plugins/text-editor-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/chunter",
      "projectFolder": "plugins/chunter",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/chunter-assets",
      "projectFolder": "plugins/chunter-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/chunter-resources",
      "projectFolder": "plugins/chunter-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-chunter",
      "projectFolder": "models/chunter",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/recruit",
      "projectFolder": "plugins/recruit",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/recruit-assets",
      "projectFolder": "plugins/recruit-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/recruit-resources",
      "projectFolder": "plugins/recruit-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-recruit",
      "projectFolder": "models/recruit",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/image-cropper",
      "projectFolder": "plugins/image-cropper",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/image-cropper-resources",
      "projectFolder": "plugins/image-cropper-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-core",
      "projectFolder": "models/server-core",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-attachment",
      "projectFolder": "server-plugins/attachment",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-attachment",
      "projectFolder": "models/server-attachment",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-attachment-resources",
      "projectFolder": "server-plugins/attachment-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-collaboration",
      "projectFolder": "server-plugins/collaboration",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-collaboration",
      "projectFolder": "models/server-collaboration",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-collaboration-resources",
      "projectFolder": "server-plugins/collaboration-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-contact",
      "projectFolder": "server-plugins/contact",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-contact",
      "projectFolder": "models/server-contact",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-contact-resources",
      "projectFolder": "server-plugins/contact-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/mongo",
      "projectFolder": "server/mongo",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/postgres",
      "projectFolder": "server/postgres",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/postgres-base",
      "projectFolder": "server/postgres-base",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/elastic",
      "projectFolder": "server/elastic",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/rpc",
      "projectFolder": "server/rpc",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/pod-front",
      "projectFolder": "pods/front",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-stats",
      "projectFolder": "pods/stats",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-server",
      "projectFolder": "pods/server",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-external",
      "projectFolder": "pods/external",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/front",
      "projectFolder": "server/front",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/account",
      "projectFolder": "server/account",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/account-service",
      "projectFolder": "server/account-service",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/workspace-service",
      "projectFolder": "server/workspace-service",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/collaborator",
      "projectFolder": "server/collaborator",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tool",
      "projectFolder": "dev/tool",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/import-tool",
      "projectFolder": "dev/import-tool",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-account",
      "projectFolder": "pods/account",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-workspace",
      "projectFolder": "pods/workspace",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-collaborator",
      "projectFolder": "pods/collaborator",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/panel",
      "projectFolder": "packages/panel",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/activity",
      "projectFolder": "plugins/activity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/activity-assets",
      "projectFolder": "plugins/activity-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/activity-resources",
      "projectFolder": "plugins/activity-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-activity",
      "projectFolder": "models/activity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/setting",
      "projectFolder": "plugins/setting",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/setting-assets",
      "projectFolder": "plugins/setting-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/setting-resources",
      "projectFolder": "plugins/setting-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-setting",
      "projectFolder": "models/setting",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/telegram",
      "projectFolder": "plugins/telegram",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/telegram-assets",
      "projectFolder": "plugins/telegram-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/telegram-resources",
      "projectFolder": "plugins/telegram-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-telegram",
      "projectFolder": "models/telegram",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/devmodel-resources",
      "projectFolder": "plugins/devmodel-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/devmodel",
      "projectFolder": "plugins/devmodel",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/attachment",
      "projectFolder": "plugins/attachment",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/attachment-assets",
      "projectFolder": "plugins/attachment-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/attachment-resources",
      "projectFolder": "plugins/attachment-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-attachment",
      "projectFolder": "models/attachment",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/lead",
      "projectFolder": "plugins/lead",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/lead-assets",
      "projectFolder": "plugins/lead-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/lead-resources",
      "projectFolder": "plugins/lead-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-lead",
      "projectFolder": "models/lead",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-gmail",
      "projectFolder": "models/gmail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/gmail",
      "projectFolder": "plugins/gmail",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/gmail-assets",
      "projectFolder": "plugins/gmail-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/gmail-resources",
      "projectFolder": "plugins/gmail-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-inventory",
      "projectFolder": "models/inventory",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/inventory",
      "projectFolder": "plugins/inventory",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/inventory-assets",
      "projectFolder": "plugins/inventory-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/inventory-resources",
      "projectFolder": "plugins/inventory-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/templates",
      "projectFolder": "plugins/templates",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/templates-assets",
      "projectFolder": "plugins/templates-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/templates-resources",
      "projectFolder": "plugins/templates-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-templates",
      "projectFolder": "models/templates",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-templates",
      "projectFolder": "server-plugins/templates",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-templates",
      "projectFolder": "models/server-templates",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-text-editor",
      "projectFolder": "models/text-editor",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-tool",
      "projectFolder": "server/tool",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-client",
      "projectFolder": "server/client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tests-sanity",
      "projectFolder": "tests/sanity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tests-ws-sanity",
      "projectFolder": "ws-tests/sanity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/qms-tests-sanity",
      "projectFolder": "qms-tests/sanity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/rekoni",
      "projectFolder": "packages/rekoni",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-notification",
      "projectFolder": "models/notification",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/notification",
      "projectFolder": "plugins/notification",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/notification-assets",
      "projectFolder": "plugins/notification-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/notification-resources",
      "projectFolder": "plugins/notification-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-notification",
      "projectFolder": "server-plugins/notification",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-notification",
      "projectFolder": "models/server-notification",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-notification-resources",
      "projectFolder": "server-plugins/notification-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tags",
      "projectFolder": "plugins/tags",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/tags-assets",
      "projectFolder": "plugins/tags-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tags-resources",
      "projectFolder": "plugins/tags-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-tags",
      "projectFolder": "models/tags",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-chunter",
      "projectFolder": "server-plugins/chunter",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-chunter",
      "projectFolder": "models/server-chunter",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-chunter-resources",
      "projectFolder": "server-plugins/chunter-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-inventory",
      "projectFolder": "server-plugins/inventory",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-inventory",
      "projectFolder": "models/server-inventory",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-inventory-resources",
      "projectFolder": "server-plugins/inventory-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-lead",
      "projectFolder": "server-plugins/lead",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-lead",
      "projectFolder": "models/server-lead",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-lead-resources",
      "projectFolder": "server-plugins/lead-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-recruit",
      "projectFolder": "server-plugins/recruit",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-recruit",
      "projectFolder": "models/server-recruit",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-recruit-resources",
      "projectFolder": "server-plugins/recruit-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-task",
      "projectFolder": "server-plugins/task",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-task",
      "projectFolder": "models/server-task",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-tracker",
      "projectFolder": "server-plugins/tasker",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-tracker",
      "projectFolder": "models/server-tasker",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-tracker-resources",
      "projectFolder": "server-plugins/tasker-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-task-resources",
      "projectFolder": "server-plugins/task-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-setting",
      "projectFolder": "server-plugins/setting",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-setting",
      "projectFolder": "models/server-setting",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-setting-resources",
      "projectFolder": "server-plugins/setting-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/calendar",
      "projectFolder": "plugins/calendar",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/calendar-assets",
      "projectFolder": "plugins/calendar-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/calendar-resources",
      "projectFolder": "plugins/calendar-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-calendar",
      "projectFolder": "models/calendar",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-tags",
      "projectFolder": "server-plugins/tags",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-tags",
      "projectFolder": "models/server-tags",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-tags-resources",
      "projectFolder": "server-plugins/tags-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-calendar",
      "projectFolder": "server-plugins/calendar",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-calendar",
      "projectFolder": "models/server-calendar",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-calendar-resources",
      "projectFolder": "server-plugins/calendar-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-gmail",
      "projectFolder": "server-plugins/gmail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-gmail",
      "projectFolder": "models/server-gmail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-gmail-resources",
      "projectFolder": "server-plugins/gmail-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-telegram",
      "projectFolder": "server-plugins/telegram",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-telegram",
      "projectFolder": "models/server-telegram",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-telegram-resources",
      "projectFolder": "server-plugins/telegram-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tasker",
      "projectFolder": "plugins/tasker",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/tasker-assets",
      "projectFolder": "plugins/tasker-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/tasker-resources",
      "projectFolder": "plugins/tasker-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-tracker",
      "projectFolder": "models/tasker",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/board",
      "projectFolder": "plugins/board",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/board-assets",
      "projectFolder": "plugins/board-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/board-resources",
      "projectFolder": "plugins/board-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-board",
      "projectFolder": "models/board",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/kanban",
      "projectFolder": "packages/kanban",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/preference",
      "projectFolder": "plugins/preference",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/preference-assets",
      "projectFolder": "plugins/preference-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-preference",
      "projectFolder": "models/preference",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-preference",
      "projectFolder": "server-plugins/preference",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/middleware",
      "projectFolder": "server/middleware",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-backup",
      "projectFolder": "server/backup",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/backup-service",
      "projectFolder": "server/backup-service",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-backup",
      "projectFolder": "pods/backup",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-fulltext",
      "projectFolder": "pods/fulltext",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-media",
      "projectFolder": "pods/media",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-preview",
      "projectFolder": "pods/preview",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/matrics-hr",
      "projectFolder": "plugins/matrics-hr",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/matrics-hr-assets",
      "projectFolder": "plugins/matrics-hr-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/matrics-hr-resources",
      "projectFolder": "plugins/matrics-hr-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-matrics-hr",
      "projectFolder": "models/matrics-hr",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-matrics-hr",
      "projectFolder": "server-plugins/matrics-hr",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-matrics-hr",
      "projectFolder": "models/server-matrics-hr",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-matrics-hr-resources",
      "projectFolder": "server-plugins/matrics-hr-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/minio",
      "projectFolder": "server/minio",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/s3",
      "projectFolder": "server/s3",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/datalake",
      "projectFolder": "server/datalake",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/bitrix",
      "projectFolder": "plugins/bitrix",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/bitrix-assets",
      "projectFolder": "plugins/bitrix-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/bitrix-resources",
      "projectFolder": "plugins/bitrix-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-bitrix",
      "projectFolder": "models/bitrix",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/request",
      "projectFolder": "plugins/request",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/request-assets",
      "projectFolder": "plugins/request-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/request-resources",
      "projectFolder": "plugins/request-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-request",
      "projectFolder": "models/request",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-request",
      "projectFolder": "server-plugins/request",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-request",
      "projectFolder": "models/server-request",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-request-resources",
      "projectFolder": "server-plugins/request-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-view",
      "projectFolder": "server-plugins/view",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-view",
      "projectFolder": "models/server-view",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-view-resources",
      "projectFolder": "server-plugins/view-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-activity",
      "projectFolder": "server-plugins/activity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-activity",
      "projectFolder": "models/server-activity",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-activity-resources",
      "projectFolder": "server-plugins/activity-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-support",
      "projectFolder": "models/support",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/support",
      "projectFolder": "plugins/support",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/support-assets",
      "projectFolder": "plugins/support-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/support-resources",
      "projectFolder": "plugins/support-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/guest",
      "projectFolder": "plugins/guest",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/guest-assets",
      "projectFolder": "plugins/guest-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/guest-resources",
      "projectFolder": "plugins/guest-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-guest",
      "projectFolder": "models/guest",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-guest",
      "projectFolder": "server-plugins/guest",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-guest",
      "projectFolder": "models/server-guest",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-guest-resources",
      "projectFolder": "server-plugins/guest-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/auth-providers",
      "projectFolder": "pods/authProviders",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/time",
      "projectFolder": "plugins/time",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/time-assets",
      "projectFolder": "plugins/time-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/time-resources",
      "projectFolder": "plugins/time-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-time",
      "projectFolder": "models/time",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-time",
      "projectFolder": "server-plugins/time",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-time-resources",
      "projectFolder": "server-plugins/time-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-time",
      "projectFolder": "models/server-time",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/document",
      "projectFolder": "plugins/document",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/document-assets",
      "projectFolder": "plugins/document-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/document-resources",
      "projectFolder": "plugins/document-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-document",
      "projectFolder": "models/document",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-document",
      "projectFolder": "server-plugins/document",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-document-resources",
      "projectFolder": "server-plugins/document-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-document",
      "projectFolder": "models/server-document",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/love",
      "projectFolder": "plugins/love",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/love-assets",
      "projectFolder": "plugins/love-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/love-resources",
      "projectFolder": "plugins/love-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-love",
      "projectFolder": "models/love",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-love",
      "projectFolder": "server-plugins/love",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-love-resources",
      "projectFolder": "server-plugins/love-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-love",
      "projectFolder": "models/server-love",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/drive",
      "projectFolder": "plugins/drive",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/drive-assets",
      "projectFolder": "plugins/drive-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/drive-resources",
      "projectFolder": "plugins/drive-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-drive",
      "projectFolder": "models/drive",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-drive",
      "projectFolder": "server-plugins/drive",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-drive",
      "projectFolder": "models/server-drive",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-drive-resources",
      "projectFolder": "server-plugins/drive-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/controlled-documents",
      "projectFolder": "plugins/controlled-documents",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/controlled-documents-assets",
      "projectFolder": "plugins/controlled-documents-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/controlled-documents-resources",
      "projectFolder": "plugins/controlled-documents-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-controlled-documents",
      "projectFolder": "models/controlled-documents",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-controlled-documents",
      "projectFolder": "server-plugins/controlled-documents",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-controlled-documents-resources",
      "projectFolder": "server-plugins/controlled-documents-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-controlled-documents",
      "projectFolder": "models/server-controlled-documents",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/print",
      "projectFolder": "plugins/print",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/print-assets",
      "projectFolder": "plugins/print-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/print-resources",
      "projectFolder": "plugins/print-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-print",
      "projectFolder": "models/print",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-export",
      "projectFolder": "models/export",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/export",
      "projectFolder": "plugins/export",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/export-assets",
      "projectFolder": "plugins/export-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/export-resources",
      "projectFolder": "plugins/export-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/sign",
      "projectFolder": "plugins/sign",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/products",
      "projectFolder": "plugins/products",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/products-assets",
      "projectFolder": "plugins/products-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/products-resources",
      "projectFolder": "plugins/products-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-products",
      "projectFolder": "models/products",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-products",
      "projectFolder": "models/server-products",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/questions",
      "projectFolder": "plugins/questions",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/questions-assets",
      "projectFolder": "plugins/questions-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/questions-resources",
      "projectFolder": "plugins/questions-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-questions",
      "projectFolder": "models/questions",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/training",
      "projectFolder": "plugins/training",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/training-assets",
      "projectFolder": "plugins/training-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/training-resources",
      "projectFolder": "plugins/training-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-training",
      "projectFolder": "server-plugins/training",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-training-resources",
      "projectFolder": "server-plugins/training-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-training",
      "projectFolder": "models/training",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-training",
      "projectFolder": "models/server-training",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop",
      "projectFolder": "desktop",
      "shouldPublish": false
    },
    {
      "packageName": "desktop",
      "projectFolder": "desktop-package",
      "shouldPublish": false
    },
    {
      "packageName": "qms-desktop",
      "projectFolder": "qms-desktop-package",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/diffview",
      "projectFolder": "plugins/diffview",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/diffview-assets",
      "projectFolder": "plugins/diffview-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/diffview-resources",
      "projectFolder": "plugins/diffview-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/github",
      "projectFolder": "services/github/github",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/github-assets",
      "projectFolder": "services/github/github-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/github-resources",
      "projectFolder": "services/github/github-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-github",
      "projectFolder": "services/github/model-github",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-github",
      "projectFolder": "services/github/pod-github",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-github",
      "projectFolder": "services/github/server-github",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-github-resources",
      "projectFolder": "services/github/server-github-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-github-model",
      "projectFolder": "services/github/server-github-model",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop-preferences",
      "projectFolder": "plugins/desktop-preferences",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop-preferences-assets",
      "projectFolder": "plugins/desktop-preferences-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop-preferences-resources",
      "projectFolder": "plugins/desktop-preferences-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-desktop-preferences",
      "projectFolder": "models/desktop-preferences",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop-downloads",
      "projectFolder": "plugins/desktop-downloads",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop-downloads-assets",
      "projectFolder": "plugins/desktop-downloads-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/desktop-downloads-resources",
      "projectFolder": "plugins/desktop-downloads-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-desktop-downloads",
      "projectFolder": "models/desktop-downloads",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-notification",
      "projectFolder": "services/notification/pod-notification",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-telegram",
      "projectFolder": "services/telegram/pod-telegram",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-gmail",
      "projectFolder": "services/gmail/pod-gmail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-calendar",
      "projectFolder": "services/calendar/pod-calendar",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-calendar-mailer",
      "projectFolder": "services/calendar/pod-calendar-mailer",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/qms-doc-import-tool",
      "projectFolder": "dev/doc-import-tool",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-datalake",
      "projectFolder": "services/datalake/pod-datalake",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-billing",
      "projectFolder": "services/billing/pod-billing",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-love",
      "projectFolder": "services/love",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-print",
      "projectFolder": "services/print/pod-print",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-export",
      "projectFolder": "services/export/pod-export",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-sign",
      "projectFolder": "services/sign/pod-sign",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/rekoni-service",
      "projectFolder": "services/rekoni",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-analytics-collector",
      "projectFolder": "services/analytics-collector/pod-analytics-collector",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/analytics-collector",
      "projectFolder": "plugins/analytics-collector",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/analytics-collector-assets",
      "projectFolder": "plugins/analytics-collector-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/analytics-collector-resources",
      "projectFolder": "plugins/analytics-collector-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-analytics-collector",
      "projectFolder": "models/analytics-collector",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-analytics-collector",
      "projectFolder": "server-plugins/analytics-collector",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-analytics-collector-resources",
      "projectFolder": "server-plugins/analytics-collector-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-ai-bot",
      "projectFolder": "services/ai-bot/pod-ai-bot",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/ai-bot",
      "projectFolder": "plugins/ai-bot",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/ai-bot-resources",
      "projectFolder": "plugins/ai-bot-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-ai-bot",
      "projectFolder": "models/ai-bot",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-ai-bot",
      "projectFolder": "models/server-ai-bot",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-ai-bot",
      "projectFolder": "server-plugins/ai-bot",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-ai-bot-resources",
      "projectFolder": "server-plugins/ai-bot-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-telegram-bot",
      "projectFolder": "services/telegram-bot/pod-telegram-bot",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/openai",
      "projectFolder": "plugins/openai",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/scripts",
      "projectFolder": "common/scripts",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-test-management",
      "projectFolder": "models/test-management",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/test-management",
      "projectFolder": "plugins/test-management",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/test-management-resources",
      "projectFolder": "plugins/test-management-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/test-management-assets",
      "projectFolder": "plugins/test-management-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/survey",
      "projectFolder": "plugins/survey",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-survey",
      "projectFolder": "models/survey",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/survey-assets",
      "projectFolder": "plugins/survey-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/survey-resources",
      "projectFolder": "plugins/survey-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/card",
      "projectFolder": "plugins/card",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-card",
      "projectFolder": "models/card",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/card-assets",
      "projectFolder": "plugins/card-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/card-resources",
      "projectFolder": "plugins/card-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-card",
      "projectFolder": "server-plugins/card",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-card-resources",
      "projectFolder": "server-plugins/card-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-card",
      "projectFolder": "models/server-card",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/mail",
      "projectFolder": "plugins/mail",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/model-mail",
      "projectFolder": "models/mail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/mail-assets",
      "projectFolder": "plugins/mail-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-mail",
      "projectFolder": "services/mail/pod-mail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/chat",
      "projectFolder": "plugins/chat",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/chat-assets",
      "projectFolder": "plugins/chat-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/chat-resources",
      "projectFolder": "plugins/chat-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-chat",
      "projectFolder": "models/chat",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/process",
      "projectFolder": "plugins/process",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/process-assets",
      "projectFolder": "plugins/process-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/process-resources",
      "projectFolder": "plugins/process-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-process",
      "projectFolder": "models/process",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-process",
      "projectFolder": "server-plugins/process",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-process",
      "projectFolder": "models/server-process",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-process-resources",
      "projectFolder": "server-plugins/process-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/kafka",
      "projectFolder": "server/kafka",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-mail-worker",
      "projectFolder": "services/mail/pod-mail-worker",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/achievement",
      "projectFolder": "plugins/achievement",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/achievement-assets",
      "projectFolder": "plugins/achievement-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/achievement-resources",
      "projectFolder": "plugins/achievement-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-achievement",
      "projectFolder": "models/achievement",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/kvs-client",
      "projectFolder": "packages/kvs-client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/hulypulse-client",
      "projectFolder": "packages/hulypulse-client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication",
      "projectFolder": "plugins/communication",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-assets",
      "projectFolder": "plugins/communication-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-resources",
      "projectFolder": "plugins/communication-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-communication",
      "projectFolder": "models/communication",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/mail-common",
      "projectFolder": "services/mail/mail-common",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/emoji",
      "projectFolder": "plugins/emoji",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/emoji-assets",
      "projectFolder": "plugins/emoji-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/emoji-resources",
      "projectFolder": "plugins/emoji-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-emoji",
      "projectFolder": "models/emoji",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/retry",
      "projectFolder": "packages/retry",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-types",
      "projectFolder": "communication/packages/types",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-sdk-types",
      "projectFolder": "communication/packages/sdk-types",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-shared",
      "projectFolder": "communication/packages/shared",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-rest-client",
      "projectFolder": "communication/packages/rest-client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-cockroach",
      "projectFolder": "communication/packages/cockroach",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-server",
      "projectFolder": "communication/packages/server",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-query",
      "projectFolder": "communication/packages/query",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/communication-client-query",
      "projectFolder": "communication/packages/client-query",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/backup-api-pod",
      "projectFolder": "services/backup/backup-api-pod",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/billing",
      "projectFolder": "plugins/billing",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/billing-assets",
      "projectFolder": "plugins/billing-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/billing-resources",
      "projectFolder": "plugins/billing-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-billing",
      "projectFolder": "models/billing",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-process",
      "projectFolder": "services/process",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/integration-client",
      "projectFolder": "packages/integration-client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-huly-mail",
      "projectFolder": "models/huly-mail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/huly-mail",
      "projectFolder": "plugins/huly-mail",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/huly-mail-assets",
      "projectFolder": "plugins/huly-mail-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/huly-mail-resources",
      "projectFolder": "plugins/huly-mail-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-ai-assistant",
      "projectFolder": "models/ai-assistant",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/ai-assistant",
      "projectFolder": "plugins/ai-assistant",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/ai-assistant-assets",
      "projectFolder": "plugins/ai-assistant-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/ai-assistant-resources",
      "projectFolder": "plugins/ai-assistant-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/pod-worker",
      "projectFolder": "services/worker",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/hulylake-client",
      "projectFolder": "packages/hulylake-client",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/dailypriorities",
      "projectFolder": "plugins/dailypriorities",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/dailypriorities-assets",
      "projectFolder": "plugins/dailypriorities-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/dailypriorities-resources",
      "projectFolder": "plugins/dailypriorities-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-dailypriorities",
      "projectFolder": "models/dailypriorities",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-dailypriorities",
      "projectFolder": "server-plugins/dailypriorities",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-server-dailypriorities",
      "projectFolder": "models/server-dailypriorities",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-dailypriorities-resources",
      "projectFolder": "server-plugins/dailypriorities-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/annotationtool",
      "projectFolder": "plugins/annotationtool"
    },
    {
      "packageName": "@hcengineering/annotationtool-assets",
      "projectFolder": "plugins/annotationtool-assets"
    },
    {
      "packageName": "@hcengineering/annotationtool-resources",
      "projectFolder": "plugins/annotationtool-resources"
    },
    {
      "packageName": "@hcengineering/model-annotationtool",
      "projectFolder": "models/annotationtool"
    },
    {
      "packageName": "@hcengineering/model-server-annotationtool",
      "projectFolder": "models/server-annotationtool"
    },
    {
      "packageName": "@hcengineering/server-annotationtool",
      "projectFolder": "server-plugins/annotationtool"
    },
    {
      "packageName": "@hcengineering/server-annotationtool-resources",
      "projectFolder": "server-plugins/annotationtool-resources"
    },
    {
      "packageName": "@hcengineering/datacatalog",
      "projectFolder": "plugins/datacatalog",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/datacatalog-assets",
      "projectFolder": "plugins/datacatalog-assets",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/datacatalog-resources",
      "projectFolder": "plugins/datacatalog-resources",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/model-datacatalog",
      "projectFolder": "models/datacatalog",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/go-analytics-microservice",
      "projectFolder": "go/go-microservices/analytics-microservice",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/go-webhook-microservice",
      "projectFolder": "go/go-microservices/webhook-microservice",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/go-annotationtool-microservice",
      "projectFolder": "go/go-microservices/annotationtool-microservice",
      "shouldPublish": true
    },
    {
      "packageName": "@hcengineering/kpis",
      "projectFolder": "plugins/kpis",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/kpis-assets",
      "projectFolder": "plugins/kpis-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/kpis-resources",
      "projectFolder": "plugins/kpis-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-kpis",
      "projectFolder": "models/kpis",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-kpis",
      "projectFolder": "server-plugins/kpis",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-kpis-resources",
      "projectFolder": "server-plugins/kpis-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/rd",
      "projectFolder": "plugins/rd",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/rd-assets",
      "projectFolder": "plugins/rd-assets",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/rd-resources",
      "projectFolder": "plugins/rd-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/model-rd",
      "projectFolder": "models/rd",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-rd",
      "projectFolder": "server-plugins/rd",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/server-rd-resources",
      "projectFolder": "server-plugins/rd-resources",
      "shouldPublish": false
    },
    {
      "packageName": "@hcengineering/opensearch",
      "projectFolder": "server/opensearch",
      "shouldPublish": false
    }
  ]
}